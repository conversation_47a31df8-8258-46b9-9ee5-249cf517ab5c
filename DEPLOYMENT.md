# DigitalOcean App Platform Deployment Guide

This guide walks you through deploying the Goali Django application to DigitalOcean App Platform.

## Prerequisites

1. A DigitalOcean account
2. Your code pushed to a GitHub repository
3. A PostgreSQL database (will be created automatically)

## Environment Variables

The following environment variables need to be configured in the App Platform dashboard:

### Required Environment Variables

1. **DJANGO_SECRET_KEY** (Secret)
   - Generate a secure secret key for Django
   - Example: `python -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())'`

2. **DATABASE_URL** (Secret)
   - This will be automatically provided by the managed database
   - Format: `postgres://username:password@host:port/database`

3. **DJANGO_ALLOWED_HOSTS** 
   - Set to your app domain
   - Will be automatically set to `${APP_DOMAIN}` in the configuration

4. **APP_DOMAIN** (Secret)
   - Your app's domain (e.g., `your-app-name.ondigitalocean.app`)

### Optional Environment Variables

5. **DEBUG** (default: False)
   - Set to "True" only for debugging (not recommended in production)

6. **REDIS_URL** (optional)
   - If you want to use Redis for Channels instead of in-memory
   - Format: `redis://host:port/db`

7. **MISTRAL_API_KEY** (Secret, if using AI features)
   - Your Mistral AI API key

## Deployment Steps

### Step 1: Prepare Your Repository

1. Ensure your code is pushed to GitHub
2. Make sure the `.do/app.yaml` file is in your repository root
3. Verify that `backend/build.sh` is executable

### Step 2: Create App Platform App

1. Go to DigitalOcean App Platform dashboard
2. Click "Create App"
3. Connect your GitHub repository
4. Select the repository and branch (usually `main`)

### Step 3: Configure Environment Variables

In the App Platform dashboard:

1. Go to your app's Settings
2. Navigate to "App-Level Environment Variables"
3. Add the required environment variables listed above

### Step 4: Deploy

1. Click "Deploy" in the App Platform dashboard
2. Monitor the build logs for any issues
3. Once deployed, your app will be available at the provided URL

## Database Setup

The managed PostgreSQL database will be automatically:
- Created with the specifications in `app.yaml`
- Connected to your Django app via `DATABASE_URL`
- Migrated during the build process

## Static Files

Static files are handled by:
- WhiteNoise middleware for serving static files
- Automatic collection during build process
- Compressed and cached for performance

## Troubleshooting

### Common Issues

1. **Build fails during collectstatic**
   - Check that all static file directories exist
   - Verify STATIC_ROOT is properly configured

2. **Database connection errors**
   - Ensure DATABASE_URL is properly set
   - Check that the database service is running

3. **Static files not loading**
   - Verify WhiteNoise is in MIDDLEWARE
   - Check STATIC_URL and STATIC_ROOT settings

### Logs

View logs in the App Platform dashboard:
1. Go to your app
2. Click on "Runtime Logs"
3. Select the service (web, database, etc.)

## Scaling

To scale your application:
1. Go to App Platform dashboard
2. Select your app
3. Adjust instance count and size in the web service settings

## Custom Domain

To use a custom domain:
1. Go to Settings > Domains
2. Add your custom domain
3. Update DNS records as instructed
4. Update DJANGO_ALLOWED_HOSTS to include your custom domain
