<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
  <meta name="description" content="Goali - Life coaching app with personalized activity recommendations" />
  <meta name="theme-color" content="#4A90E2" />
  
  <!-- Preconnect to improve performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  
  <!-- Apple Touch Icon for iOS -->
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  
  <!-- Manifest for PWA (disabled for demo) -->
  <!-- <link rel="manifest" href="/manifest.json" /> -->
  
  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  
  <title>Goali - Your Life Coaching Companion</title>
  
  <!-- Critical CSS for initial render -->
  <style>
    /* Reset and base styles */
    *, *::before, *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    html {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.5;
      -webkit-text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    body {
      margin: 0;
      padding: 0;
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      overflow-x: hidden;
      /* Prevent zoom on iOS */
      touch-action: manipulation;
    }
    
    /* Loading screen */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease-out;
    }
    
    .loading-screen.hidden {
      opacity: 0;
      pointer-events: none;
    }
    
    .loading-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 24px;
      animation: pulse 2s ease-in-out infinite;
    }
    
    .loading-text {
      color: white;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 16px;
    }
    
    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* App container */
    #app {
      width: 100%;
      min-height: 100vh;
      position: relative;
    }
    
    /* Hide app initially */
    app-shell {
      opacity: 0;
      transition: opacity 0.5s ease-in;
    }
    
    app-shell.loaded {
      opacity: 1;
    }
    
    /* Error fallback */
    .error-fallback {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: white;
      padding: 20px;
      max-width: 400px;
    }
    
    .error-fallback h1 {
      font-size: 24px;
      margin-bottom: 16px;
    }
    
    .error-fallback p {
      font-size: 16px;
      margin-bottom: 20px;
      opacity: 0.9;
    }
    
    .error-fallback button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      transition: background 0.2s ease;
    }
    
    .error-fallback button:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  </style>
  <script type="module" crossorigin src="./assets/main-BQgKOwp9.js"></script>
  <link rel="modulepreload" crossorigin href="./assets/lit-KavRT6Ey.js">
  <link rel="modulepreload" crossorigin href="./assets/pixi-DRyRoF6D.js">
  <link rel="modulepreload" crossorigin href="./assets/matter-4D3s4xTp.js">
  <link rel="stylesheet" crossorigin href="./assets/main-Dd2MvPK5.css">
</head>
<body>
  <!-- Loading screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-logo">
      <!-- Placeholder for logo SVG -->
      <svg viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="40" cy="40" r="35" stroke="white" stroke-width="3" fill="none" opacity="0.3"/>
        <circle cx="40" cy="40" r="25" stroke="white" stroke-width="2" fill="none"/>
        <circle cx="40" cy="40" r="8" fill="white"/>
      </svg>
    </div>
    <div class="loading-text">Loading Goali...</div>
    <div class="loading-spinner"></div>
  </div>

  <!-- Main application -->
  <div id="app">
    <app-shell></app-shell>
  </div>

  <!-- Error fallback (hidden by default) -->
  <div id="error-fallback" class="error-fallback" style="display: none;">
    <h1>🎯 Goali Demo Mode</h1>
    <p>The backend server is not available, but you can still try the spinning wheel with sample activities!</p>
    <button onclick="window.location.reload()">Try Again</button>
  </div>

  <!-- Main application script -->
  
  <!-- Service worker registration (disabled for demo) -->
  <!--
  <script>
    // Register service worker for PWA functionality
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
  -->
</body>
</html>
