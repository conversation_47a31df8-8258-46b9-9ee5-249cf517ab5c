/**
 * Configuration Service
 * Manages application configuration based on environment and mode
 */

import type { AppConfig, AppMode, DebugConfig, SecurityConfig } from '../types/app-types';

export class ConfigService {
  private static instance: ConfigService;
  private config: AppConfig;

  private constructor() {
    this.config = this.buildConfig();
  }

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  /**
   * Get the current application configuration
   */
  public getConfig(): AppConfig {
    return this.config;
  }

  /**
   * Get the current application mode
   */
  public getMode(): AppMode {
    return this.config.mode;
  }

  /**
   * Check if debug mode is enabled
   */
  public isDebugMode(): boolean {
    return this.config.mode === 'debug';
  }

  /**
   * Check if production mode is enabled
   */
  public isProductionMode(): boolean {
    return this.config.mode === 'production';
  }

  /**
   * Update configuration at runtime (debug mode only)
   */
  public updateConfig(updates: Partial<AppConfig>): void {
    if (!this.isDebugMode()) {
      console.warn('Configuration updates are only allowed in debug mode');
      return;
    }
    
    this.config = { ...this.config, ...updates };
    this.notifyConfigChange();
  }

  /**
   * Update WebSocket URL (debug mode only)
   */
  public updateWebSocketUrl(url: string): void {
    if (!this.isDebugMode() || !this.config.debug.allowBackendUrlChange) {
      console.warn('WebSocket URL changes are not allowed in this mode');
      return;
    }
    
    this.config.websocket.url = url;
    this.notifyConfigChange();
  }

  /**
   * Build configuration from environment variables
   */
  private buildConfig(): AppConfig {
    const env = import.meta.env;
    const mode: AppMode = (env.VITE_APP_MODE as AppMode) || 'debug';

    return {
      mode,
      api: {
        baseUrl: env.VITE_API_BASE_URL || this.deriveApiBaseUrl(env.VITE_WS_URL || 'ws://localhost:8000/ws/game/'),
      },
      websocket: {
        url: env.VITE_WS_URL || 'ws://localhost:8000/ws/game/',
        reconnectAttempts: 5,
        reconnectDelay: 1000,
        heartbeatInterval: 30000,
        messageTimeout: 10000,
        maxQueueSize: 100,
      },
      wheel: {
        defaultSize: '80vw',
        spinDuration: 5000,
        maxSpinDuration: 10000,
        ballRadius: 6,
        nailRadius: 4,
      },
      performance: {
        targetFPS: 60,
        maxMemoryMB: 50,
      },
      debug: this.buildDebugConfig(env, mode),
      security: this.buildSecurityConfig(env, mode),
    };
  }

  /**
   * Build debug configuration
   */
  private buildDebugConfig(env: any, mode: AppMode): DebugConfig {
    return {
      enabled: mode === 'debug' && (env.VITE_DEBUG_ENABLED === 'true'),
      showPerformanceMetrics: env.VITE_DEBUG_SHOW_PERFORMANCE === 'true',
      showNetworkLogs: env.VITE_DEBUG_SHOW_NETWORK_LOGS === 'true',
      showStateInspector: env.VITE_DEBUG_SHOW_STATE_INSPECTOR === 'true',
      allowUserSelection: env.VITE_DEBUG_ALLOW_USER_SELECTION === 'true',
      allowLLMConfigSelection: env.VITE_DEBUG_ALLOW_LLM_CONFIG_SELECTION === 'true',
      allowBackendUrlChange: env.VITE_DEBUG_ALLOW_BACKEND_URL_CHANGE === 'true',
      mockDataEnabled: env.VITE_DEBUG_MOCK_DATA_ENABLED === 'true',
    };
  }

  /**
   * Build security configuration
   */
  private buildSecurityConfig(env: any, mode: AppMode): SecurityConfig {
    return {
      // TEMPORARY: Force authentication for testing production login modal
      requireAuthentication: true, // mode === 'production' && (env.VITE_SECURITY_REQUIRE_AUTH === 'true'),
      tokenValidation: env.VITE_SECURITY_TOKEN_VALIDATION === 'true',
      allowedOrigins: env.VITE_SECURITY_ALLOWED_ORIGINS?.split(',') || [],
      sessionTimeout: parseInt(env.VITE_SECURITY_SESSION_TIMEOUT) || 1800000,
    };
  }

  /**
   * Derive API base URL from WebSocket URL
   */
  private deriveApiBaseUrl(wsUrl: string): string {
    let baseUrl = wsUrl;

    // Convert WebSocket URL to HTTP URL
    if (baseUrl.startsWith('ws://')) {
      baseUrl = baseUrl.replace('ws://', 'http://');
    } else if (baseUrl.startsWith('wss://')) {
      baseUrl = baseUrl.replace('wss://', 'https://');
    }

    // Remove WebSocket path
    baseUrl = baseUrl.replace('/ws/game/', '');

    return baseUrl;
  }

  /**
   * Notify listeners of configuration changes
   */
  private notifyConfigChange(): void {
    window.dispatchEvent(new CustomEvent('config-changed', {
      detail: { config: this.config }
    }));
  }
}

export default ConfigService;
