/**
 * Core application type definitions
 */

/**
 * Application mode types
 */
export type AppMode = 'debug' | 'production';

/**
 * Application configuration interface
 */
export interface AppConfig {
  mode: AppMode;
  api: ApiConfig;
  websocket: WebSocketConfig;
  wheel: WheelConfig;
  performance: PerformanceConfig;
  debug: DebugConfig;
  security: SecurityConfig;
}

/**
 * API configuration
 */
export interface ApiConfig {
  baseUrl: string;
}

/**
 * Debug configuration for development mode
 */
export interface DebugConfig {
  enabled: boolean;
  showPerformanceMetrics: boolean;
  showNetworkLogs: boolean;
  showStateInspector: boolean;
  allowUserSelection: boolean;
  allowLLMConfigSelection: boolean;
  allowBackendUrlChange: boolean;
  mockDataEnabled: boolean;
}

/**
 * Security configuration for production mode
 */
export interface SecurityConfig {
  requireAuthentication: boolean;
  tokenValidation: boolean;
  allowedOrigins: string[];
  sessionTimeout: number;
}

/**
 * WebSocket configuration
 */
export interface WebSocketConfig {
  url: string;
  reconnectAttempts: number;
  reconnectDelay: number;
  heartbeatInterval: number;
  messageTimeout: number;
  maxQueueSize: number;
}

/**
 * Wheel component configuration
 */
export interface WheelConfig {
  defaultSize: string;
  spinDuration: number;
  maxSpinDuration: number;
  ballRadius: number;
  nailRadius: number;
}

/**
 * Performance configuration
 */
export interface PerformanceConfig {
  targetFPS: number;
  maxMemoryMB: number;
}

/**
 * Application state interface
 */
export interface AppState {
  isConnected: boolean;
  isLoading: boolean;
  currentUser: UserProfile | null;
  currentWheel: WheelData | null;
  chatMessages: ChatMessage[];
  error: string | null;
  lastActivity: number;
}

/**
 * User profile interface
 */
export interface UserProfile {
  id: string;
  name: string;
  username?: string;
  email?: string;
  is_staff?: boolean;
  trustLevel: number;
  preferences: UserPreferences;
  is_fake?: boolean;
}

/**
 * LLM Configuration interface for debug mode
 */
export interface LLMConfig {
  id: string;
  name: string;
  model_name: string;
  temperature: number;
  is_default: boolean;
}

/**
 * Authentication token interface
 */
export interface AuthToken {
  token: string;
  userId: string;
  expiresAt: number;
  permissions: string[];
}

/**
 * User preferences
 */
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  animations: boolean;
  soundEnabled: boolean;
  language: string;
}

/**
 * Wheel data structure
 */
export interface WheelData {
  name: string;
  items: WheelItem[];
}

/**
 * Individual wheel item
 */
export interface WheelItem {
  id: string;
  name: string;
  description: string;
  percentage: number;
  color: string;
  domain: string;
  base_challenge_rating: number;
  activity_tailored_id: string;
}

/**
 * Chat message interface
 */
export interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: number;
  type?: 'text' | 'system' | 'error';
}

/**
 * Activity details interface
 */
export interface ActivityDetails {
  id: string;
  name: string;
  detailed_description: string;
  preparation_steps: string[];
  tips_for_success: string[];
  reflection_questions: string[];
}

/**
 * Event types for custom events
 */
export interface CustomEventMap {
  'wheel-spin-start': CustomEvent<void>;
  'wheel-spin-complete': CustomEvent<{ selectedActivityId: string; selectedActivity: WheelItem }>;
  'wheel-data-updated': CustomEvent<{ itemCount: number }>;
  'wheel-error': CustomEvent<{ message: string }>;
  'chat-message-sent': CustomEvent<{ message: string }>;
  'connection-status-changed': CustomEvent<{ isConnected: boolean }>;
  'state-updated': CustomEvent<{ state: Partial<AppState> }>;
}

/**
 * Physics configuration for the wheel
 */
export interface PhysicsConfig {
  ball: {
    restitution: number;
    friction: number;
    frictionAir: number;
    radius: number;
  };
  nails: {
    restitution: number;
    isStatic: boolean;
    radius: number;
  };
  world: {
    gravity: { x: number; y: number };
  };
}

/**
 * Rendering configuration for PixiJS
 */
export interface RenderConfig {
  width: number;
  height: number;
  backgroundColor: number;
  antialias: boolean;
  resolution: number;
  autoDensity: boolean;
}

/**
 * Animation state for wheel spinning
 */
export interface SpinState {
  isSpinning: boolean;
  startTime: number;
  duration: number;
  startAngle: number;
  endAngle: number;
  selectedItem: WheelItem | null;
}

/**
 * Error types
 */
export type AppError = 
  | 'CONNECTION_ERROR'
  | 'WHEEL_ERROR'
  | 'PHYSICS_ERROR'
  | 'RENDER_ERROR'
  | 'VALIDATION_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Loading states
 */
export type LoadingState = 
  | 'idle'
  | 'loading'
  | 'success'
  | 'error';

/**
 * Connection states
 */
export type ConnectionState = 
  | 'disconnected'
  | 'connecting'
  | 'connected'
  | 'reconnecting'
  | 'error';

/**
 * Utility type for making properties optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Utility type for event handlers
 */
export type EventHandler<T = Event> = (event: T) => void;

/**
 * Utility type for async event handlers
 */
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>;

/**
 * Component lifecycle states
 */
export type ComponentState = 
  | 'initializing'
  | 'ready'
  | 'active'
  | 'inactive'
  | 'destroyed';

/**
 * Validation result
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  renderTime: number;
  updateTime: number;
}

/**
 * Debug information
 */
export interface DebugInfo {
  version: string;
  buildTime: string;
  userAgent: string;
  webglSupported: boolean;
  performanceMetrics: PerformanceMetrics;
}
