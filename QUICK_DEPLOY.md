# Quick Deploy to DigitalOcean App Platform

## 🚀 One-Click Deploy

1. **Fork/Clone** this repository to your GitHub account

2. **Create App** on DigitalOcean App Platform:
   - Connect GitHub repository
   - Select branch: `main`
   - App Platform will auto-detect the `.do/app.yaml` configuration

3. **Set Environment Variables** in App Platform dashboard:
   ```
   DJANGO_SECRET_KEY = [Generate with: python -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())']
   APP_DOMAIN = your-app-name.ondigitalocean.app
   MISTRAL_API_KEY = your-mistral-api-key (if using AI features)
   ```

4. **Deploy** - Click the deploy button!

## 📋 What Gets Deployed

- **Web Service**: Django app with Gunicorn
- **Database**: Managed PostgreSQL 15
- **Static Files**: Served via WhiteNoise
- **Frontend**: React/Vite app (if applicable)

## 🔧 Configuration Files

- `.do/app.yaml` - App Platform configuration
- `backend/build.sh` - Build script for static files and migrations
- `backend/config/settings/prod.py` - Production Django settings

## 🐛 Troubleshooting

**Build Issues:**
```bash
# Check logs in App Platform dashboard
# Common fixes:
- Ensure all dependencies in requirements.txt
- Check that build.sh is executable
- Verify environment variables are set
```

**Runtime Issues:**
```bash
# Check runtime logs
# Common fixes:
- Verify DATABASE_URL is connected
- Check ALLOWED_HOSTS includes your domain
- Ensure static files are collected
```

## 📚 Full Documentation

See `DEPLOYMENT.md` for complete deployment guide with detailed explanations.
