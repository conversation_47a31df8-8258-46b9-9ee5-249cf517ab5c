# DigitalOcean App Platform Secrets Configuration

## Overview

This document provides instructions for configuring secrets in DigitalOcean App Platform for the Goali application. All sensitive values have been removed from the `do.yaml` file and must be configured through the DigitalOcean control panel.

## Required Secrets

The following secrets must be configured in the DigitalOcean App Platform web UI:

### 1. DJANGO_SECRET_KEY
- **Type**: SECRET
- **Scope**: RUN_AND_BUILD_TIME
- **Value**: `tDTGVJKlzGke7RQydZd6OJvVzVibKzp5bLf3ZPqjsk7s6LyF2PxeYwDk084XPvDW`
- **Description**: <PERSON><PERSON><PERSON>'s secret key for cryptographic signing

### 2. MISTRAL_API_KEY
- **Type**: SECRET
- **Scope**: RUN_AND_BUILD_TIME
- **Value**: `lmIzupERcWLLiRSevDrxvMRX6s7OXv70`
- **Description**: API key for Mistral AI service

### 3. DJANGO_ADMIN_PASSWORD
- **Type**: SECRET
- **Scope**: BUILD_TIME
- **Value**: `GoaliAdmin2025!`
- **Description**: Password for the Django admin superuser account

### 4. DATABASE_URL
- **Type**: SECRET
- **Scope**: RUN_AND_BUILD_TIME
- **Value**: `postgresql://doadmin:<EMAIL>:25060/defaultdb?sslmode=require`
- **Description**: Complete PostgreSQL database connection string

### 5. REDIS_URL
- **Type**: SECRET
- **Scope**: RUN_TIME
- **Value**: `redis://default:<EMAIL>:25061/0`
- **Description**: DigitalOcean Managed Valkey (Redis-compatible) connection URL

## Configuration Steps

### Method 1: Using DigitalOcean Control Panel (Recommended)

1. **Navigate to your app**:
   - Go to [DigitalOcean Control Panel](https://cloud.digitalocean.com/apps)
   - Click on your `monkfish-app` application

2. **Access Settings**:
   - Click the **Settings** tab
   - Click on the `goali-backend` component

3. **Add Environment Variables**:
   - For each secret listed above:
     - Click **Add Variable**
     - Enter the **Key** name (e.g., `DJANGO_SECRET_KEY`)
     - Enter the **Value** (copy from the values above)
     - Check the **Encrypt** checkbox
     - Select the appropriate **Scope** (RUN_AND_BUILD_TIME, BUILD_TIME, or RUN_TIME)
     - Click **Save**

4. **Deploy**:
   - After adding all secrets, click **Save** to trigger a new deployment

### Method 2: Using doctl CLI (Alternative)

```bash
# Update app with secrets (requires doctl to be installed and configured)
doctl apps update <app-id> --spec do.yaml

# Then set secrets via control panel as CLI doesn't support setting secret values directly
```

## Security Notes

⚠️ **IMPORTANT SECURITY CONSIDERATIONS**:

1. **Never commit secret values to version control**
2. **The values above are provided for migration purposes only**
3. **Consider rotating these secrets after configuration**:
   - Generate a new Django secret key
   - Rotate the Mistral API key if possible
   - Update the admin password
   - Database credentials should be managed through DigitalOcean's database service

## Verification

After configuring all secrets:

1. **Check deployment logs** for any missing environment variable errors
2. **Test application functionality**:
   - Django admin login should work
   - Database connections should be successful
   - Mistral AI integration should function
3. **Verify secrets are encrypted** in the DigitalOcean control panel (values should show as `[ENCRYPTED]`)

## Troubleshooting

### Common Issues:

1. **Missing environment variable errors**:
   - Ensure all secrets are configured with correct names
   - Check that scopes match the requirements (BUILD_TIME vs RUN_TIME)

2. **Database connection failures**:
   - Verify DATABASE_URL is correctly formatted
   - Check database firewall rules allow App Platform access

3. **Django admin login issues**:
   - Verify DJANGO_ADMIN_PASSWORD is set correctly
   - Check that the superuser creation command in build_command executed successfully

### Getting Help:

- Check DigitalOcean App Platform logs in the control panel
- Review deployment logs for specific error messages
- Ensure all secrets have the **Encrypt** checkbox checked

## Next Steps

After successful configuration:

1. **Test the deployment thoroughly**
2. **Consider implementing secret rotation policies**
3. **Monitor application logs for any security-related issues**
4. **Update this documentation if secret requirements change**

---

**Last Updated**: 2025-06-28
**Configuration Target**: gguine/prod-slow branch deployment
