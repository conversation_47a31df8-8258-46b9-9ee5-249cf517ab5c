# Environment Template for Goali Production Deployment
# Copy this file to .env.prod.local and fill in actual values (NEVER commit .env.prod.local)

# ==============================================
# DIGITAL OCEAN DEPLOYMENT CONFIGURATION
# ==============================================

# Environment identification
ENVIRONMENT=production
DJANGO_SETTINGS_MODULE=config.settings.prod
DEBUG=False

# Execution mode
GOALI_DEFAULT_EXECUTION_MODE=production

# Digital Ocean App Configuration
# These will be automatically set by Digital Ocean App Platform:
# APP_URL=https://goali-secure-aec2e.ondigitalocean.app
# PORT=8080

# ==============================================
# SECRETS (Set via Digital Ocean Environment Variables)
# ==============================================
# These should NEVER be set here - they are managed by Digital Ocean

# Django secret key (auto-generated by Digital Ocean)
SECRET_KEY=WILL_BE_SET_BY_DIGITAL_OCEAN

# API Keys (set as encrypted environment variables in Digital Ocean)
MISTRAL_API_KEY=WILL_BE_SET_BY_DIGITAL_OCEAN

# Database configuration (local PostgreSQL in container)
DATABASE_URL=postgresql://goali:secure_generated_password@localhost:5432/goali_prod

# Cache configuration (local Redis in container)
REDIS_URL=redis://127.0.0.1:6379/0
CELERY_BROKER_URL=redis://127.0.0.1:6379/0

# ==============================================
# NON-SECRET CONFIGURATION
# ==============================================

# LLM Configuration (non-secret settings)
DEFAULT_LLM_MODEL_NAME=mistral-small-latest
DEFAULT_LLM_TEMPERATURE=0.7
DEFAULT_LLM_INPUT_TOKEN_PRICE=0.000002
DEFAULT_LLM_OUTPUT_TOKEN_PRICE=0.000006

# Host configuration (handled by Digital Ocean)
ALLOWED_HOSTS=*
CORS_ALLOWED_ORIGINS=https://goali-secure-aec2e.ondigitalocean.app,https://*.ondigitalocean.app

# Security settings
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
SESSION_COOKIE_HTTPONLY=True
CSRF_COOKIE_HTTPONLY=True

# Monitoring and logging
LOGGING_LEVEL=INFO
ENABLE_PERFORMANCE_MONITORING=True
ENABLE_ERROR_TRACKING=True

# Performance settings
CACHE_TIMEOUT=300
CONN_MAX_AGE=60
CONN_HEALTH_CHECKS=True

# ==============================================
# DEPLOYMENT INSTRUCTIONS
# ==============================================

# 1. This template shows what environment variables are used
# 2. In Digital Ocean App Platform:
#    - SECRET_KEY and MISTRAL_API_KEY are set as encrypted environment variables
#    - Other settings are automatically configured by the container
# 3. Local development:
#    - Copy this to .env.dev.local
#    - Set your development MISTRAL_API_KEY
#    - Use development-friendly values for other settings

# ==============================================
# SECURITY NOTES
# ==============================================

# ✅ DO:
# - Set secrets via Digital Ocean dashboard environment variables
# - Use encrypted SECRET type for sensitive values
# - Keep this template file updated with new variables
# - Document the purpose of each variable

# ❌ DON'T:
# - Put real API keys or passwords in this file
# - Commit files ending in .local or .secret
# - Share production credentials via chat/email
# - Use development credentials in production
