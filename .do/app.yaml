name: goali-app
services:
- name: web
  source_dir: /backend
  github:
    repo: elgui/goali
    branch: main
    deploy_on_push: true
  build_command: ./build.sh
  run_command: gunicorn --worker-tmp-dir /dev/shm config.wsgi
  environment_slug: python
  instance_count: 1
  instance_size_slug: basic-xxs
  http_port: 8080
  envs:
  - key: DEBUG
    value: "False"
  - key: DJANGO_SETTINGS_MODULE
    value: config.settings.prod
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DJAN<PERSON>O_ALLOWED_HOSTS
    value: ${APP_DOMAIN}
  - key: APP_DOMAIN
    scope: RUN_AND_BUILD_TIME
    type: SECRET

databases:
- engine: PG
  name: goali-db
  num_nodes: 1
  size: db-s-dev-database
  version: "15"

static_sites:
- name: frontend
  source_dir: /frontend
  github:
    repo: elgui/goali
    branch: main
    deploy_on_push: true
  build_command: npm ci && npm run build
  output_dir: /dist
  index_document: index.html
  error_document: index.html
  routes:
  - path: /static
    preserve_path_prefix: false
