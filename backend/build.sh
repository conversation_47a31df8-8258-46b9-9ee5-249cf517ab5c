#!/bin/bash

# Build script for DigitalOcean App Platform
set -e

echo "Starting build process..."

# Install Python dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Set Django settings for production
export DJANGO_SETTINGS_MODULE=config.settings.prod
export DEBUG=False

# Create necessary directories
mkdir -p staticfiles
mkdir -p media

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput --clear

# Run database migrations
echo "Running database migrations..."
python manage.py migrate --noinput

echo "Build process completed successfully!"
