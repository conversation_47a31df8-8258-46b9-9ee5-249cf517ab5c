#!/usr/bin/env python3
"""
Comprehensive test script for the Advanced Observability System

This script validates:
1. ObservabilityService functionality
2. Progress tracking integration
3. Real-time WebSocket streaming
4. Performance metrics collection
5. LLM cost tracking
6. Error handling and alerting

Run with: python test_observability_system.py
"""

import os
import sys
import django
import asyncio
import time
import json
from typing import Dict, Any

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
sys.path.append('/usr/src/app')
django.setup()

from apps.main.services.observability_service import (
    observability, EventType, Severity, TraceContext
)
from apps.main.services.progress_tracking_service import ProgressTrackingService


class ObservabilitySystemTest:
    """Comprehensive test suite for the observability system"""
    
    def __init__(self):
        self.test_results = []
        self.start_time = time.time()
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details,
            'timestamp': time.time()
        })
        print(f"{status} {test_name}: {details}")
    
    async def test_basic_event_emission(self):
        """Test basic event emission functionality"""
        try:
            # Enable sync mode for reliable testing
            observability.enable_sync_mode()

            # Emit a test event
            event_id = observability.emit_event(
                EventType.WORKFLOW_START,
                'test',
                'basic_test',
                metadata={'test_data': 'hello world'},
                tags={'test': 'true'}
            )

            # Check if event was added to buffer
            recent_events = list(observability.event_buffer)
            test_events = [e for e in recent_events if e.operation == 'basic_test']

            success = len(event_id) > 0 and len(test_events) > 0
            self.log_test(
                "Basic Event Emission",
                success,
                f"Event ID: {event_id[:8]}..., Events in buffer: {len(test_events)}" if success else "Failed to emit event"
            )

        except Exception as e:
            self.log_test("Basic Event Emission", False, f"Exception: {str(e)}")
    
    async def test_trace_context_propagation(self):
        """Test trace context propagation"""
        try:
            # Set initial context
            trace_id = "test-trace-123"
            span_id = "test-span-456"
            TraceContext.set_context(trace_id, span_id)
            
            # Emit event and check context
            event_id = observability.emit_event(
                EventType.NODE_START,
                'test',
                'context_test'
            )
            
            # Verify context is maintained
            current_trace = TraceContext.get_trace_id()
            current_span = TraceContext.get_span_id()
            
            success = current_trace == trace_id and current_span == span_id
            self.log_test(
                "Trace Context Propagation",
                success,
                f"Trace: {current_trace}, Span: {current_span}"
            )
            
            # Clean up
            TraceContext.clear_context()
            
        except Exception as e:
            self.log_test("Trace Context Propagation", False, f"Exception: {str(e)}")
    
    async def test_performance_tracking(self):
        """Test performance tracking functionality"""
        try:
            # Record some operations
            operations = [
                ('fast_operation', 50),
                ('normal_operation', 200),
                ('slow_operation', 1000),
                ('fast_operation', 45),
                ('normal_operation', 180)
            ]
            
            for op_name, duration in operations:
                observability.performance_tracker.record_operation(op_name, duration)
            
            # Get performance summary
            summary = observability.performance_tracker.get_performance_summary()
            
            # Verify tracking
            fast_stats = summary['operation_stats'].get('fast_operation', {})
            success = (
                fast_stats.get('count', 0) == 2 and
                abs(fast_stats.get('avg_time', 0) - 47.5) < 1
            )
            
            self.log_test(
                "Performance Tracking",
                success,
                f"Fast op avg: {fast_stats.get('avg_time', 0):.1f}ms"
            )
            
        except Exception as e:
            self.log_test("Performance Tracking", False, f"Exception: {str(e)}")
    
    async def test_llm_cost_tracking(self):
        """Test LLM cost tracking"""
        try:
            # Track some LLM calls
            observability.track_llm_call(
                model='gpt-4o-mini',
                tokens_used=1500,
                cost=0.0045,
                duration_ms=2300,
                metadata={'test': 'cost_tracking'}
            )
            
            observability.track_llm_call(
                model='gpt-4o-mini',
                tokens_used=800,
                cost=0.0024,
                duration_ms=1200
            )
            
            # Check cost accumulation
            total_cost = observability.cost_tracker.get('gpt-4o-mini', 0)
            expected_cost = 0.0045 + 0.0024
            
            success = abs(total_cost - expected_cost) < 0.0001
            self.log_test(
                "LLM Cost Tracking",
                success,
                f"Total cost: ${total_cost:.4f}"
            )
            
        except Exception as e:
            self.log_test("LLM Cost Tracking", False, f"Exception: {str(e)}")
    
    async def test_error_handling(self):
        """Test error event handling"""
        try:
            # Clear buffer first
            observability.event_buffer.clear()

            # Emit error event
            observability.emit_event(
                EventType.ERROR,
                'test',
                'error_test',
                severity=Severity.ERROR,
                metadata={
                    'error_type': 'TestError',
                    'error_message': 'This is a test error'
                }
            )

            # Check if error was recorded (sync mode)
            recent_events = list(observability.event_buffer)
            error_events = [e for e in recent_events if e.event_type == EventType.ERROR and e.operation == 'error_test']

            success = len(error_events) > 0
            self.log_test(
                "Error Handling",
                success,
                f"Error events recorded: {len(error_events)}"
            )

        except Exception as e:
            self.log_test("Error Handling", False, f"Exception: {str(e)}")
    
    async def test_trace_operation_context_manager(self):
        """Test trace operation context manager"""
        try:
            # Clear buffer first
            observability.event_buffer.clear()

            # Use context manager
            with observability.trace_operation('test', 'context_manager_test'):
                # Simulate some work
                await asyncio.sleep(0.01)

                # Emit event within context
                observability.emit_event(
                    EventType.NODE_START,
                    'test',
                    'nested_operation'
                )

            # Check for start and end events (sync mode)
            recent_events = list(observability.event_buffer)
            start_events = [e for e in recent_events if e.event_type == EventType.TASK_START and e.operation == 'context_manager_test']
            end_events = [e for e in recent_events if e.event_type == EventType.TASK_END and e.operation == 'context_manager_test']

            success = len(start_events) > 0 and len(end_events) > 0
            self.log_test(
                "Trace Operation Context Manager",
                success,
                f"Start: {len(start_events)}, End: {len(end_events)}, Total events: {len(recent_events)}"
            )

        except Exception as e:
            self.log_test("Trace Operation Context Manager", False, f"Exception: {str(e)}")
    
    async def test_progress_tracking_integration(self):
        """Test integration with progress tracking service"""
        try:
            # Get progress service
            progress_service = await ProgressTrackingService.get_instance()
            
            # Create a tracker
            tracker = progress_service.create_tracker(
                name="Test Workflow",
                user_id="test-user",
                workflow_type="test_workflow"
            )
            
            # Start and complete a stage
            stage_id = tracker.start_stage("test_stage", "Test Stage", "Testing...")
            await asyncio.sleep(0.02)
            tracker.update_stage(stage_id, 50, "Halfway done")
            await asyncio.sleep(0.02)
            tracker.complete_stage(stage_id, "Stage complete")
            
            # Complete tracker
            tracker.complete_tracker("Test complete")
            
            # Wait for processing
            await asyncio.sleep(0.1)
            
            success = tracker.is_completed and tracker.total_progress == 100.0
            self.log_test(
                "Progress Tracking Integration",
                success,
                f"Progress: {tracker.total_progress}%, Completed: {tracker.is_completed}"
            )
            
        except Exception as e:
            self.log_test("Progress Tracking Integration", False, f"Exception: {str(e)}")
    
    async def test_dashboard_data_generation(self):
        """Test dashboard data generation"""
        try:
            # Get dashboard data
            dashboard_data = observability.get_performance_dashboard_data()
            
            # Verify structure
            required_keys = [
                'performance_summary',
                'active_traces',
                'total_events',
                'cost_summary',
                'sampling_rate',
                'recent_events'
            ]
            
            missing_keys = [key for key in required_keys if key not in dashboard_data]
            success = len(missing_keys) == 0
            
            self.log_test(
                "Dashboard Data Generation",
                success,
                f"Missing keys: {missing_keys}" if missing_keys else "All keys present"
            )
            
        except Exception as e:
            self.log_test("Dashboard Data Generation", False, f"Exception: {str(e)}")
    
    async def test_high_throughput_scenario(self):
        """Test high throughput event processing"""
        try:
            # Clear buffer first
            observability.event_buffer.clear()

            # Emit many events quickly
            event_count = 100
            start_time = time.time()

            for i in range(event_count):
                observability.emit_event(
                    EventType.NODE_START,
                    'test',
                    f'high_throughput_{i}',
                    metadata={'iteration': i}
                )

            processing_time = time.time() - start_time
            events_per_second = event_count / processing_time

            # Check that events were actually recorded
            recorded_events = len([e for e in observability.event_buffer if 'high_throughput_' in e.operation])

            success = events_per_second > 50 and recorded_events == event_count
            self.log_test(
                "High Throughput Scenario",
                success,
                f"{events_per_second:.1f} events/sec, {recorded_events}/{event_count} recorded"
            )

        except Exception as e:
            self.log_test("High Throughput Scenario", False, f"Exception: {str(e)}")
    
    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        total_time = time.time() - self.start_time
        
        print("\n" + "="*60)
        print("OBSERVABILITY SYSTEM TEST SUMMARY")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"Total Time: {total_time:.2f}s")
        
        if failed_tests > 0:
            print("\nFAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  ❌ {result['test']}: {result['details']}")
        
        print("\n" + "="*60)
        
        # Return success status
        return failed_tests == 0


async def main():
    """Run all observability system tests"""
    print("🔍 Starting Advanced Observability System Tests...")
    print("="*60)
    
    test_suite = ObservabilitySystemTest()
    
    # Run all tests
    await test_suite.test_basic_event_emission()
    await test_suite.test_trace_context_propagation()
    await test_suite.test_performance_tracking()
    await test_suite.test_llm_cost_tracking()
    await test_suite.test_error_handling()
    await test_suite.test_trace_operation_context_manager()
    await test_suite.test_progress_tracking_integration()
    await test_suite.test_dashboard_data_generation()
    await test_suite.test_high_throughput_scenario()
    
    # Print summary
    success = test_suite.print_summary()
    
    # Cleanup
    await observability.shutdown()
    
    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite failed with exception: {e}")
        sys.exit(1)
