#!/usr/bin/env python3
"""
Test script for wheel generation observability integration

This script validates:
1. Wheel generation workflow observability
2. Real-time progress tracking
3. Performance metrics collection
4. LLM cost tracking during wheel generation
5. Error handling in workflow context

Run with: python test_wheel_generation_observability.py
"""

import os
import sys
import django
import asyncio
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
sys.path.append('/usr/src/app')
django.setup()

from apps.main.services.observability_service import (
    observability, EventType, Severity, TraceContext
)
from apps.main.services.progress_tracking_service import ProgressTrackingService


class WheelGenerationObservabilityTest:
    """Test observability integration with wheel generation workflow"""
    
    def __init__(self):
        self.test_results = []
        self.start_time = time.time()
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details,
            'timestamp': time.time()
        })
        print(f"{status} {test_name}: {details}")
    
    async def test_workflow_lifecycle_tracking(self):
        """Test complete workflow lifecycle tracking"""
        try:
            # Enable sync mode for testing
            observability.enable_sync_mode()
            observability.event_buffer.clear()
            
            # Simulate workflow start
            observability.emit_event(
                EventType.WORKFLOW_START,
                'celery',
                'wheel_generation_workflow',
                metadata={
                    'user_profile_id': 'test-user-123',
                    'task_id': 'test-task-456',
                    'workflow_id': 'test-workflow-789'
                },
                tags={'workflow_type': 'wheel_generation', 'priority': 'high'}
            )
            
            # Simulate node executions
            with observability.trace_operation('langgraph', 'orchestrator_node'):
                await asyncio.sleep(0.01)
                
                # Simulate LLM call
                observability.track_llm_call(
                    model='gpt-4o-mini',
                    tokens_used=1200,
                    cost=0.0036,
                    duration_ms=1800,
                    metadata={'agent': 'orchestrator', 'purpose': 'context_analysis'}
                )
            
            with observability.trace_operation('langgraph', 'activity_agent_node'):
                await asyncio.sleep(0.01)
                
                # Simulate another LLM call
                observability.track_llm_call(
                    model='gpt-4o-mini',
                    tokens_used=800,
                    cost=0.0024,
                    duration_ms=1200,
                    metadata={'agent': 'activity_agent', 'purpose': 'activity_generation'}
                )
            
            # Simulate workflow completion
            observability.emit_event(
                EventType.WORKFLOW_END,
                'celery',
                'wheel_generation_workflow',
                duration_ms=5000,
                metadata={
                    'user_profile_id': 'test-user-123',
                    'success': True,
                    'wheel_items_count': 4
                },
                metrics={
                    'execution_time_ms': 5000.0,
                    'wheel_items_generated': 4.0
                }
            )
            
            # Analyze events
            events = list(observability.event_buffer)
            workflow_start_events = [e for e in events if e.event_type == EventType.WORKFLOW_START]
            workflow_end_events = [e for e in events if e.event_type == EventType.WORKFLOW_END]
            # For langgraph component, trace_operation creates NODE_START/NODE_END events
            node_events = [e for e in events if e.event_type in [EventType.NODE_START, EventType.NODE_END]]
            llm_events = [e for e in events if e.event_type == EventType.LLM_CALL]

            success = (
                len(workflow_start_events) == 1 and
                len(workflow_end_events) == 1 and
                len(node_events) >= 4 and  # Start/end for each node
                len(llm_events) == 2
            )
            
            self.log_test(
                "Workflow Lifecycle Tracking",
                success,
                f"Start: {len(workflow_start_events)}, End: {len(workflow_end_events)}, Nodes: {len(node_events)}, LLM: {len(llm_events)}"
            )
            
        except Exception as e:
            self.log_test("Workflow Lifecycle Tracking", False, f"Exception: {str(e)}")
    
    async def test_progress_tracking_integration(self):
        """Test integration between observability and progress tracking"""
        try:
            # Get progress service
            progress_service = await ProgressTrackingService.get_instance()
            
            # Create a tracker for wheel generation
            tracker = progress_service.create_tracker(
                name="Wheel Generation Test",
                user_id="test-user-123",
                workflow_type="wheel_generation"
            )
            
            # Simulate workflow stages with observability
            observability.event_buffer.clear()
            
            # Stage 1: Initialization
            stage_id = tracker.start_stage("initialization", "Initializing Workflow", "Setting up...")
            observability.emit_event(
                EventType.NODE_START,
                'langgraph',
                'initialization_stage',
                metadata={'stage_id': stage_id, 'tracker_id': tracker.tracker_id}
            )
            await asyncio.sleep(0.01)
            tracker.update_stage(stage_id, 25, "Initialization complete")
            tracker.complete_stage(stage_id, "Ready for processing")
            
            # Stage 2: Agent Processing
            stage_id = tracker.start_stage("agent_processing", "Agent Processing", "Running agents...")
            observability.emit_event(
                EventType.NODE_START,
                'langgraph',
                'agent_processing_stage',
                metadata={'stage_id': stage_id, 'tracker_id': tracker.tracker_id}
            )
            await asyncio.sleep(0.01)
            tracker.update_stage(stage_id, 75, "Agents processing")
            tracker.complete_stage(stage_id, "Agent processing complete")
            
            # Stage 3: Finalization
            stage_id = tracker.start_stage("finalization", "Finalizing", "Creating wheel...")
            observability.emit_event(
                EventType.NODE_START,
                'langgraph',
                'finalization_stage',
                metadata={'stage_id': stage_id, 'tracker_id': tracker.tracker_id}
            )
            await asyncio.sleep(0.01)
            tracker.update_stage(stage_id, 100, "Wheel created")
            tracker.complete_stage(stage_id, "Finalization complete")
            
            # Complete tracker
            tracker.complete_tracker("Wheel generation completed successfully")
            
            # Check correlation between progress and observability events
            events = list(observability.event_buffer)
            stage_events = [e for e in events if 'stage' in e.operation]
            
            success = (
                tracker.is_completed and
                tracker.total_progress == 100.0 and
                len(stage_events) == 3
            )
            
            self.log_test(
                "Progress Tracking Integration",
                success,
                f"Tracker complete: {tracker.is_completed}, Progress: {tracker.total_progress}%, Stage events: {len(stage_events)}"
            )
            
        except Exception as e:
            self.log_test("Progress Tracking Integration", False, f"Exception: {str(e)}")
    
    async def test_performance_metrics_collection(self):
        """Test performance metrics collection during workflow"""
        try:
            observability.event_buffer.clear()
            # Clear performance tracker to start fresh
            observability.performance_tracker.operation_stats.clear()
            observability.performance_tracker.samples.clear()

            # Simulate various operations with different performance characteristics
            operations = [
                ('fast_node', 50),
                ('normal_node', 200),
                ('slow_node', 1000),
                ('llm_call', 2000),
                ('database_query', 150)
            ]
            
            for op_name, duration in operations:
                observability.emit_event(
                    EventType.NODE_START,
                    'langgraph',
                    op_name,
                    duration_ms=duration,
                    metrics={'execution_time_ms': float(duration)}
                )
            
            # Get performance summary
            dashboard_data = observability.get_performance_dashboard_data()
            performance_summary = dashboard_data['performance_summary']
            operation_stats = performance_summary['operation_stats']
            
            # Check that performance data was collected
            success = (
                len(operation_stats) == len(operations) and
                'slow_node' in operation_stats and
                operation_stats['slow_node']['avg_time'] == 1000
            )
            
            self.log_test(
                "Performance Metrics Collection",
                success,
                f"Operations tracked: {len(operation_stats)}, Slow node avg: {operation_stats.get('slow_node', {}).get('avg_time', 0)}ms"
            )
            
        except Exception as e:
            self.log_test("Performance Metrics Collection", False, f"Exception: {str(e)}")
    
    async def test_cost_tracking_accuracy(self):
        """Test LLM cost tracking accuracy"""
        try:
            # Reset cost tracker
            observability.cost_tracker.clear()
            
            # Simulate multiple LLM calls with different costs
            llm_calls = [
                ('gpt-4o-mini', 1500, 0.0045),
                ('gpt-4o-mini', 800, 0.0024),
                ('gpt-4o', 1200, 0.024),
                ('gpt-4o-mini', 600, 0.0018)
            ]
            
            total_expected_cost_mini = 0.0045 + 0.0024 + 0.0018
            total_expected_cost_4o = 0.024
            
            for model, tokens, cost in llm_calls:
                observability.track_llm_call(
                    model=model,
                    tokens_used=tokens,
                    cost=cost,
                    duration_ms=1500,
                    metadata={'test': 'cost_tracking'}
                )
            
            # Check cost accumulation
            mini_cost = observability.cost_tracker.get('gpt-4o-mini', 0)
            gpt4_cost = observability.cost_tracker.get('gpt-4o', 0)
            
            success = (
                abs(mini_cost - total_expected_cost_mini) < 0.0001 and
                abs(gpt4_cost - total_expected_cost_4o) < 0.0001
            )
            
            self.log_test(
                "Cost Tracking Accuracy",
                success,
                f"GPT-4o-mini: ${mini_cost:.4f} (expected: ${total_expected_cost_mini:.4f}), GPT-4o: ${gpt4_cost:.4f}"
            )
            
        except Exception as e:
            self.log_test("Cost Tracking Accuracy", False, f"Exception: {str(e)}")
    
    async def test_error_context_capture(self):
        """Test error context capture in workflow scenarios"""
        try:
            observability.event_buffer.clear()
            
            # Simulate workflow with error
            try:
                with observability.trace_operation('langgraph', 'failing_node'):
                    # Simulate some processing
                    await asyncio.sleep(0.01)
                    
                    # Simulate an error
                    raise ValueError("Simulated workflow error for testing")
                    
            except ValueError:
                pass  # Expected error
            
            # Check error capture
            events = list(observability.event_buffer)
            error_events = [e for e in events if e.event_type == EventType.ERROR]
            
            success = (
                len(error_events) == 1 and
                error_events[0].metadata.get('error_type') == 'ValueError' and
                'Simulated workflow error' in error_events[0].metadata.get('error_message', '')
            )
            
            self.log_test(
                "Error Context Capture",
                success,
                f"Error events: {len(error_events)}, Error type: {error_events[0].metadata.get('error_type', 'None') if error_events else 'None'}"
            )
            
        except Exception as e:
            self.log_test("Error Context Capture", False, f"Exception: {str(e)}")
    
    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        total_time = time.time() - self.start_time
        
        print("\n" + "="*70)
        print("WHEEL GENERATION OBSERVABILITY TEST SUMMARY")
        print("="*70)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"Total Time: {total_time:.2f}s")
        
        if failed_tests > 0:
            print("\nFAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  ❌ {result['test']}: {result['details']}")
        
        print("\n" + "="*70)
        
        return failed_tests == 0


async def main():
    """Run all wheel generation observability tests"""
    print("🔍 Starting Wheel Generation Observability Tests...")
    print("="*70)
    
    test_suite = WheelGenerationObservabilityTest()
    
    # Run all tests
    await test_suite.test_workflow_lifecycle_tracking()
    await test_suite.test_progress_tracking_integration()
    await test_suite.test_performance_metrics_collection()
    await test_suite.test_cost_tracking_accuracy()
    await test_suite.test_error_context_capture()
    
    # Print summary
    success = test_suite.print_summary()
    
    # Cleanup
    await observability.shutdown()
    
    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite failed with exception: {e}")
        sys.exit(1)
