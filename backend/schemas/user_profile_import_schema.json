{"openapi": "3.0.3", "info": {"title": "Game of Life - User Profile Import Schema", "description": "Comprehensive OpenAPI schema for importing complete user profiles including demographics, environment, psychological traits, goals, beliefs, skills, resources, and preferences. This schema supports AI agent integration for automated profile creation from questionnaire data.", "version": "1.0.0", "contact": {"name": "Game of Life API Support", "email": "<EMAIL>"}}, "servers": [{"url": "https://api.gameoflife.dev/v1", "description": "Production server"}, {"url": "http://localhost:8000/api/v1", "description": "Development server"}], "paths": {"/user-profiles/import": {"post": {"summary": "Import Complete User Profile", "description": "Import a comprehensive user profile from structured data. Supports full profile creation including demographics, environment, psychological traits, goals, beliefs, skills, resources, and preferences.", "operationId": "importUserProfile", "tags": ["User Profiles"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileImportRequest"}}}}, "responses": {"200": {"description": "Profile imported successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImportResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Profile already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/user-profiles/ai-generate": {"post": {"summary": "AI Generate Profile from Questionnaire", "description": "Use AI to generate a comprehensive user profile from questionnaire responses or interview transcripts.", "operationId": "aiGenerateProfile", "tags": ["AI Generation"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AIGenerateRequest"}}}}, "responses": {"200": {"description": "Profile generated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AIGenerateResponse"}}}}, "400": {"description": "Invalid questionnaire data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/user-profiles/schema": {"get": {"summary": "Get Profile Schema", "description": "Retrieve the complete OpenAPI schema for user profile import structure.", "operationId": "getProfileSchema", "tags": ["<PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "description": "The complete OpenAPI schema for user profile structure"}}}}}}}}, "components": {"schemas": {"UserProfileImportRequest": {"type": "object", "required": ["profile_data"], "properties": {"profile_data": {"$ref": "#/components/schemas/CompleteUserProfile"}, "options": {"$ref": "#/components/schemas/ImportOptions"}}}, "ImportOptions": {"type": "object", "properties": {"overwriteExisting": {"type": "boolean", "default": false, "description": "Whether to overwrite existing profile if username/email exists"}, "validateBeforeImport": {"type": "boolean", "default": true, "description": "Whether to validate data before importing"}, "createBackup": {"type": "boolean", "default": true, "description": "Whether to create backup of existing data"}}}, "AIGenerateRequest": {"type": "object", "required": ["questionnaire_data"], "properties": {"questionnaire_data": {"type": "string", "description": "Questionnaire responses or interview transcript", "minLength": 100, "maxLength": 50000}, "options": {"$ref": "#/components/schemas/AIGenerationOptions"}}}, "AIGenerationOptions": {"type": "object", "properties": {"includeArchetypeAnalysis": {"type": "boolean", "default": true, "description": "Include HEXACO personality archetype analysis"}, "includeEnvironmentInference": {"type": "boolean", "default": true, "description": "Infer environment details from responses"}, "includeGoalsExtraction": {"type": "boolean", "default": true, "description": "Extract goals and aspirations from responses"}}}, "CompleteUserProfile": {"type": "object", "required": ["user_account", "profile_name"], "properties": {"user_account": {"$ref": "#/components/schemas/UserAccount"}, "profile_name": {"type": "string", "description": "Display name for the user profile", "minLength": 1, "maxLength": 255}, "is_real": {"type": "boolean", "default": true, "description": "True for real users, False for test/archetypal profiles"}, "demographics": {"$ref": "#/components/schemas/Demographics"}, "environments": {"type": "array", "items": {"$ref": "#/components/schemas/UserEnvironment"}, "description": "User environments with current environment marked"}, "traits": {"type": "array", "items": {"$ref": "#/components/schemas/TraitInclination"}, "description": "HEXACO personality trait inclinations"}, "beliefs": {"type": "array", "items": {"$ref": "#/components/schemas/Belief"}, "description": "Core beliefs with evidence and influences"}, "aspirations": {"type": "array", "items": {"$ref": "#/components/schemas/Aspiration"}, "description": "Long-term aspirations and vision elements"}, "intentions": {"type": "array", "items": {"$ref": "#/components/schemas/Intention"}, "description": "Short to medium-term intentions and goals"}, "inspirations": {"type": "array", "items": {"$ref": "#/components/schemas/Inspiration"}, "description": "Sources of inspiration and motivation"}, "skills": {"type": "array", "items": {"$ref": "#/components/schemas/Skill"}, "description": "User skills and capabilities"}, "resources": {"type": "array", "items": {"$ref": "#/components/schemas/Resource"}, "description": "Available resources and tools"}, "limitations": {"type": "array", "items": {"$ref": "#/components/schemas/Limitation"}, "description": "Personal limitations and constraints"}, "preferences": {"type": "array", "items": {"$ref": "#/components/schemas/Preference"}, "description": "Personal preferences and patterns"}, "current_mood": {"$ref": "#/components/schemas/CurrentMood"}, "trust_level": {"$ref": "#/components/schemas/TrustLevel"}}}, "UserAccount": {"type": "object", "required": ["username", "email"], "properties": {"username": {"type": "string", "pattern": "^[a-zA-Z0-9_]+$", "minLength": 3, "maxLength": 150, "description": "Unique username for the account"}, "email": {"type": "string", "format": "email", "description": "User's email address"}, "first_name": {"type": "string", "maxLength": 150}, "last_name": {"type": "string", "maxLength": 150}, "password": {"type": "string", "minLength": 8, "description": "User password (will be hashed on server)"}, "is_staff": {"type": "boolean", "default": false}, "is_superuser": {"type": "boolean", "default": false}}}, "Demographics": {"type": "object", "required": ["full_name", "age", "gender", "location", "language", "occupation"], "properties": {"full_name": {"type": "string", "maxLength": 255, "description": "User's full name"}, "age": {"type": "integer", "minimum": 13, "maximum": 120, "description": "User's age"}, "gender": {"type": "string", "maxLength": 50, "description": "User's gender identity"}, "location": {"type": "string", "maxLength": 255, "description": "User's current location or residence"}, "language": {"type": "string", "maxLength": 50, "description": "Preferred or native language"}, "occupation": {"type": "string", "maxLength": 255, "description": "User's occupation or professional role"}}}, "UserEnvironment": {"type": "object", "required": ["environment_name", "environment_description", "is_current"], "properties": {"environment_name": {"type": "string", "maxLength": 255, "description": "User's personal name for this environment"}, "environment_description": {"type": "string", "description": "Detailed description of the environment"}, "is_current": {"type": "boolean", "description": "Whether this is the user's current environment"}, "effective_start": {"type": "string", "format": "date", "description": "When this environment became available"}, "effective_end": {"type": "string", "format": "date", "description": "When this environment will no longer be available"}, "generic_environment_code": {"type": "string", "description": "Code of the generic environment archetype"}, "environment_details": {"type": "object", "description": "Additional environment details specific to this user", "additionalProperties": true}, "physical_properties": {"$ref": "#/components/schemas/PhysicalProperties"}, "social_context": {"$ref": "#/components/schemas/SocialContext"}, "activity_support": {"$ref": "#/components/schemas/ActivitySupport"}, "psychological_qualities": {"$ref": "#/components/schemas/PsychologicalQualities"}}}, "GenericEnvironment": {"type": "object", "required": ["code", "name", "description", "is_active", "is_indoor", "primary_category", "typical_space_size", "typical_privacy_level"], "properties": {"code": {"type": "string", "maxLength": 50, "description": "Unique code for the generic environment"}, "name": {"type": "string", "maxLength": 255, "description": "Name of the generic environment"}, "description": {"type": "string", "description": "Description of the generic environment"}, "is_active": {"type": "boolean", "description": "Whether this environment type is currently active/available"}, "created_at": {"type": "string", "format": "date-time", "description": "When this environment type was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "When this environment type was last updated"}, "is_indoor": {"type": "boolean", "description": "Whether this is primarily an indoor environment"}, "primary_category": {"type": "string", "maxLength": 100, "description": "Primary category of the environment"}, "typical_space_size": {"type": "string", "maxLength": 50, "description": "Typical space size category"}, "typical_privacy_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Typical privacy level for this environment type"}, "archetype_attributes": {"type": "object", "description": "JSON object containing archetype-specific attributes", "additionalProperties": true}, "physical_properties": {"$ref": "#/components/schemas/PhysicalProperties"}, "social_context": {"$ref": "#/components/schemas/SocialContext"}, "activity_support": {"$ref": "#/components/schemas/ActivitySupport"}, "psychological_qualities": {"$ref": "#/components/schemas/PsychologicalQualities"}}}, "PhysicalProperties": {"type": "object", "properties": {"rurality": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Rurality level (0=urban, 100=rural)"}, "noise_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Ambient noise level"}, "light_quality": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Natural light quality"}, "temperature_range": {"type": "string", "description": "Temperature variation characteristics"}, "accessibility": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Physical accessibility level"}, "air_quality": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Air quality rating"}, "has_natural_elements": {"type": "boolean", "description": "Presence of natural elements"}, "surface_type": {"type": "string", "description": "Primary surface type"}, "water_proximity": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Proximity to water sources"}, "space_size": {"type": "string", "description": "Overall space size category"}}}, "SocialContext": {"type": "object", "properties": {"privacy_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Level of privacy available"}, "typical_occupancy": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Typical occupancy percentage"}, "social_interaction_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Level of social interaction"}, "formality_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Formality level of interactions"}, "safety_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Perceived safety level"}, "supervision_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Level of supervision or oversight"}, "cultural_diversity": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Cultural diversity level"}}}, "ActivitySupport": {"type": "object", "properties": {"digital_connectivity": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Quality of internet/digital connectivity"}, "resource_availability": {"type": "integer", "minimum": 0, "maximum": 100, "description": "General resource availability"}, "domain_specific_support": {"type": "object", "description": "Support levels for specific activity domains", "properties": {"phys_active": {"type": "integer", "minimum": 0, "maximum": 100}, "creative_making": {"type": "integer", "minimum": 0, "maximum": 100}, "creative_music": {"type": "integer", "minimum": 0, "maximum": 100}, "soc_connecting": {"type": "integer", "minimum": 0, "maximum": 100}, "intel_learning": {"type": "integer", "minimum": 0, "maximum": 100}, "refl_mindful": {"type": "integer", "minimum": 0, "maximum": 100}, "prod_skill": {"type": "integer", "minimum": 0, "maximum": 100}}}, "time_availability": {"type": "object", "description": "Time availability patterns"}}}, "PsychologicalQualities": {"type": "object", "properties": {"restorative_quality": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Restorative and rejuvenating quality"}, "stimulation_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Level of stimulation provided"}, "aesthetic_appeal": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Aesthetic and beauty appeal"}, "novelty_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Level of novelty and variety"}, "comfort_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Physical and emotional comfort"}, "personal_significance": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Personal meaning and significance"}, "emotional_associations": {"type": "object", "description": "Emotional associations with the environment"}}}, "TraitInclination": {"type": "object", "required": ["trait_code", "strength", "awareness"], "properties": {"trait_code": {"type": "string", "description": "Code of the generic trait (e.g., 'openness_creativity')", "enum": ["honesty_sincerity", "honesty_fairness", "honesty_greed_avoidance", "honesty_modesty", "emotion_fearfulness", "emotion_anxiety", "emotion_dependence", "emotion_sentimentality", "extra_self_esteem", "extra_social_boldness", "extra_sociability", "extra_liveliness", "agree_forgiveness", "agree_gentleness", "agree_flexibility", "agree_patience", "consc_organization", "consc_diligence", "consc_perfectionism", "consc_prudence", "open_aesthetic", "open_inquisitive", "open_creativity", "open_unconventional"]}, "strength": {"type": "number", "minimum": 0, "maximum": 100, "description": "Strength of the trait inclination"}, "awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of this trait"}}}, "UserTraitInclination": {"type": "object", "required": ["trait_code", "strength", "awareness"], "properties": {"trait_code": {"type": "string", "description": "Code of the generic trait"}, "strength": {"type": "number", "minimum": 0, "maximum": 100, "description": "Strength of the trait inclination"}, "awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of this trait"}}}, "GenericTrait": {"type": "object", "required": ["code", "name", "description", "category", "trait_type"], "properties": {"code": {"type": "string", "maxLength": 50, "description": "Unique code for the trait"}, "name": {"type": "string", "maxLength": 255, "description": "Name of the trait"}, "description": {"type": "string", "description": "Description of the trait"}, "category": {"type": "string", "maxLength": 50, "description": "HEXACO category (honesty, emotion, extra, agree, consc, open)"}, "trait_type": {"type": "string", "maxLength": 50, "description": "Type classification of the trait"}, "subcategory": {"type": "string", "maxLength": 50, "description": "Subcategory within the HEXACO dimension"}, "created_at": {"type": "string", "format": "date-time", "description": "When this trait was created"}, "updated_at": {"type": "string", "format": "date-time", "description": "When this trait was last updated"}, "low_description": {"type": "string", "description": "Description of low trait expression"}, "high_description": {"type": "string", "description": "Description of high trait expression"}}}, "UserBelief": {"type": "object", "required": ["content"], "properties": {"content": {"type": "string", "description": "The belief statement or content"}, "user_confidence": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's confidence in this belief"}, "system_confidence": {"type": "integer", "minimum": 0, "maximum": 100, "description": "System's assessment of belief accuracy"}, "emotionality": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Emotional charge of the belief"}, "stability": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Stability/consistency of the belief"}, "user_awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of holding this belief"}, "evidence": {"type": "array", "items": {"$ref": "#/components/schemas/BeliefEvidence"}, "description": "Evidence supporting or challenging the belief"}}}, "Belief": {"type": "object", "required": ["content"], "properties": {"content": {"type": "string", "description": "The belief statement or content"}, "user_confidence": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's confidence in this belief"}, "system_confidence": {"type": "integer", "minimum": 0, "maximum": 100, "description": "System's assessment of belief accuracy"}, "emotionality": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Emotional charge of the belief"}, "stability": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Stability/consistency of the belief"}, "user_awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of holding this belief"}, "evidence": {"type": "array", "items": {"$ref": "#/components/schemas/BeliefEvidence"}, "description": "Evidence supporting or challenging the belief"}}}, "BeliefEvidence": {"type": "object", "required": ["evidence_type", "description"], "properties": {"evidence_type": {"type": "string", "enum": ["EXPERIENCE", "EXTERNAL_FEEDBACK", "PATTERN", "OBSERVATION", "RESEARCH"], "description": "Type of evidence"}, "description": {"type": "string", "description": "Description of the evidence"}, "credibility_score": {"type": "number", "minimum": 0, "maximum": 1, "description": "Credibility score of the evidence"}, "source": {"type": "string", "description": "Source of the evidence"}}}, "Aspiration": {"type": "object", "required": ["title", "description"], "properties": {"title": {"type": "string", "maxLength": 255, "description": "Title of the aspiration"}, "description": {"type": "string", "description": "Detailed description of the aspiration"}, "importance_according_user": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's rating of importance"}, "importance_according_system": {"type": "integer", "minimum": 0, "maximum": 100, "description": "System's assessment of importance"}, "strength": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Strength of the aspiration"}, "domain": {"type": "string", "description": "Life domain of the aspiration"}, "horizon": {"type": "string", "description": "Time horizon (Short-term, Medium-term, Long-term, Lifelong)"}, "level_of_ambition": {"type": "string", "description": "Ambition level (Low, Medium, High, Very High)"}, "effective_start": {"type": "string", "format": "date"}, "duration_estimate": {"type": "string", "description": "Estimated duration"}, "effective_end": {"type": "string", "format": "date"}}}, "UserAspiration": {"type": "object", "required": ["title", "description"], "properties": {"title": {"type": "string", "maxLength": 255, "description": "Title of the aspiration"}, "description": {"type": "string", "description": "Detailed description of the aspiration"}, "importance_according_user": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's rating of importance"}, "importance_according_system": {"type": "integer", "minimum": 0, "maximum": 100, "description": "System's assessment of importance"}, "strength": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Strength of the aspiration"}, "domain": {"type": "string", "description": "Life domain of the aspiration"}, "horizon": {"type": "string", "description": "Time horizon (Short-term, Medium-term, Long-term, Lifelong)"}, "level_of_ambition": {"type": "string", "description": "Ambition level (Low, Medium, High, Very High)"}, "effective_start": {"type": "string", "format": "date"}, "duration_estimate": {"type": "string", "description": "Estimated duration"}, "effective_end": {"type": "string", "format": "date"}}}, "Intention": {"type": "object", "required": ["title", "description"], "properties": {"title": {"type": "string", "maxLength": 255, "description": "Title of the intention"}, "description": {"type": "string", "description": "Detailed description of the intention"}, "importance_according_user": {"type": "integer", "minimum": 0, "maximum": 100}, "importance_according_system": {"type": "integer", "minimum": 0, "maximum": 100}, "strength": {"type": "integer", "minimum": 0, "maximum": 100}, "start_date": {"type": "string", "format": "date"}, "due_date": {"type": "string", "format": "date"}, "is_completed": {"type": "boolean", "default": false}, "progress_notes": {"type": "string", "description": "Notes on progress"}, "effective_start": {"type": "string", "format": "date"}, "duration_estimate": {"type": "string"}, "effective_end": {"type": "string", "format": "date"}}}, "UserIntention": {"type": "object", "required": ["title", "description"], "properties": {"title": {"type": "string", "maxLength": 255, "description": "Title of the intention"}, "description": {"type": "string", "description": "Detailed description of the intention"}, "importance_according_user": {"type": "integer", "minimum": 0, "maximum": 100}, "importance_according_system": {"type": "integer", "minimum": 0, "maximum": 100}, "strength": {"type": "integer", "minimum": 0, "maximum": 100}, "start_date": {"type": "string", "format": "date"}, "due_date": {"type": "string", "format": "date"}, "is_completed": {"type": "boolean", "default": false}, "progress_notes": {"type": "string", "description": "Notes on progress"}, "effective_start": {"type": "string", "format": "date"}, "duration_estimate": {"type": "string"}, "effective_end": {"type": "string", "format": "date"}}}, "Inspiration": {"type": "object", "required": ["source", "description"], "properties": {"source": {"type": "string", "maxLength": 255, "description": "Source of inspiration"}, "description": {"type": "string", "description": "Description of how this source inspires"}, "strength": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Strength of inspiration"}, "reference_url": {"type": "string", "format": "uri", "description": "Reference URL if applicable"}}}, "UserInspiration": {"type": "object", "required": ["source", "description"], "properties": {"source": {"type": "string", "maxLength": 255, "description": "Source of inspiration"}, "description": {"type": "string", "description": "Description of how this source inspires"}, "strength": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Strength of inspiration"}, "reference_url": {"type": "string", "format": "uri", "description": "Reference URL if applicable"}}}, "Skill": {"type": "object", "required": ["skill_code", "description", "level", "practice_frequency", "formal_training", "contexts"], "properties": {"skill_code": {"type": "string", "description": "Code of the generic skill"}, "description": {"type": "string", "description": "Personal description of the skill"}, "level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Skill level (0-100)"}, "practice_frequency": {"type": "string", "maxLength": 50, "description": "How frequently this skill is practiced"}, "formal_training": {"type": "boolean", "description": "Whether formal training was received for this skill"}, "training_details": {"type": "string", "description": "Details about formal training received"}, "contexts": {"type": "object", "description": "JSON object describing contexts where skill is used", "additionalProperties": true}, "growth_goal": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Target level for skill growth"}, "stagnation_point": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Level where skill development stagnated"}, "user_awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of this skill level"}, "user_enjoyment": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's enjoyment of using this skill"}, "note": {"type": "string", "description": "Additional notes about the skill"}, "last_practiced": {"type": "string", "format": "date", "description": "When the skill was last practiced"}, "acquisition_date": {"type": "string", "format": "date", "description": "When the skill was acquired"}}}, "GenericSkill": {"type": "object", "required": ["code", "name", "description", "category", "base_difficulty", "development_timeframe", "decay_rate"], "properties": {"code": {"type": "string", "maxLength": 50, "description": "Unique code for the skill"}, "name": {"type": "string", "maxLength": 255, "description": "Name of the skill"}, "description": {"type": "string", "description": "Description of the skill"}, "category": {"type": "string", "maxLength": 100, "description": "Category of the skill"}, "subcategory": {"type": "string", "maxLength": 100, "description": "Subcategory of the skill"}, "base_difficulty": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Base difficulty level for learning this skill"}, "development_timeframe": {"type": "string", "maxLength": 100, "description": "Typical timeframe for skill development"}, "decay_rate": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Rate at which skill decays without practice"}, "level_0_description": {"type": "string", "description": "Description of beginner level"}, "level_25_description": {"type": "string", "description": "Description of novice level"}, "level_50_description": {"type": "string", "description": "Description of intermediate level"}, "level_75_description": {"type": "string", "description": "Description of advanced level"}, "level_100_description": {"type": "string", "description": "Description of expert level"}}}, "Resource": {"type": "object", "required": ["specific_name", "resource_code"], "properties": {"specific_name": {"type": "string", "maxLength": 255, "description": "Specific name for this resource instance"}, "resource_code": {"type": "string", "description": "Code of the generic resource type"}, "location_details": {"type": "string", "description": "Where the resource is located"}, "ownership_details": {"type": "string", "description": "Ownership or access details"}, "contact_info": {"type": "string", "description": "Contact information for access"}, "notes": {"type": "string", "description": "Additional notes about the resource"}}}, "UserResource": {"type": "object", "required": ["specific_name", "resource_code"], "properties": {"specific_name": {"type": "string", "maxLength": 255, "description": "Specific name for this resource instance"}, "resource_code": {"type": "string", "description": "Code of the generic resource type"}, "location_details": {"type": "string", "description": "Where the resource is located"}, "ownership_details": {"type": "string", "description": "Ownership or access details"}, "contact_info": {"type": "string", "description": "Contact information for access"}, "notes": {"type": "string", "description": "Additional notes about the resource"}}}, "GenericResource": {"type": "object", "required": ["code", "name", "description", "category", "resource_type", "op_cost_currency", "op_cost", "acq_cost_currency", "acq_cost"], "properties": {"code": {"type": "string", "maxLength": 50, "description": "Unique code for the resource type"}, "name": {"type": "string", "maxLength": 255, "description": "Name of the resource type"}, "description": {"type": "string", "description": "Description of the resource type"}, "category": {"type": "string", "maxLength": 100, "description": "Category of the resource"}, "resource_type": {"type": "string", "maxLength": 50, "description": "Type classification of the resource"}, "subcategory": {"type": "string", "maxLength": 100, "description": "Subcategory of the resource"}, "op_cost_currency": {"type": "string", "maxLength": 3, "description": "Currency code for operational cost (e.g., USD, EUR)"}, "op_cost": {"type": "number", "minimum": 0, "description": "Operational cost amount"}, "acq_cost_currency": {"type": "string", "maxLength": 3, "description": "Currency code for acquisition cost"}, "acq_cost": {"type": "number", "minimum": 0, "description": "Acquisition cost amount"}}}, "Limitation": {"type": "object", "required": ["limitation_code", "severity", "effective_start", "duration_estimate", "effective_end", "valid_until", "is_unlimited", "user_awareness"], "properties": {"limitation_code": {"type": "string", "description": "Code of the generic limitation type"}, "severity": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Severity of the limitation"}, "effective_start": {"type": "string", "format": "date", "description": "When this limitation becomes effective"}, "duration_estimate": {"type": "string", "description": "Estimated duration of the limitation"}, "effective_end": {"type": "string", "format": "date", "description": "When this limitation ends"}, "valid_until": {"type": "string", "format": "date", "description": "When this limitation assessment is valid until"}, "is_unlimited": {"type": "boolean", "description": "Whether the limitation has no time boundary"}, "user_awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of this limitation"}}}, "UserLimitation": {"type": "object", "required": ["limitation_code", "severity", "effective_start", "duration_estimate", "effective_end", "valid_until", "is_unlimited", "user_awareness"], "properties": {"limitation_code": {"type": "string", "description": "Code of the generic limitation type"}, "severity": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Severity of the limitation"}, "effective_start": {"type": "string", "format": "date", "description": "When this limitation becomes effective"}, "duration_estimate": {"type": "string", "description": "Estimated duration of the limitation"}, "effective_end": {"type": "string", "format": "date", "description": "When this limitation ends"}, "valid_until": {"type": "string", "format": "date", "description": "When this limitation assessment is valid until"}, "is_unlimited": {"type": "boolean", "description": "Whether the limitation has no time boundary"}, "user_awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of this limitation"}}}, "GenericUserLimitation": {"type": "object", "required": ["code", "name", "description", "category", "limitation_type"], "properties": {"code": {"type": "string", "maxLength": 50, "description": "Unique code for the limitation type"}, "name": {"type": "string", "maxLength": 255, "description": "Name of the limitation type"}, "description": {"type": "string", "description": "Description of the limitation type"}, "category": {"type": "string", "maxLength": 100, "description": "Category of the limitation"}, "limitation_type": {"type": "string", "maxLength": 50, "description": "Type classification of the limitation"}, "subcategory": {"type": "string", "maxLength": 100, "description": "Subcategory of the limitation"}}}, "Preference": {"type": "object", "required": ["pref_name", "pref_description", "pref_strength"], "properties": {"pref_name": {"type": "string", "maxLength": 255, "description": "Name of the preference"}, "pref_description": {"type": "string", "description": "Detailed description of the preference"}, "pref_strength": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Strength of the preference"}, "user_awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of this preference"}, "environment_specific": {"type": "boolean", "description": "Whether this preference is specific to an environment"}, "effective_start": {"type": "string", "format": "date"}, "duration_estimate": {"type": "string"}, "effective_end": {"type": "string", "format": "date"}}}, "UserPreference": {"type": "object", "required": ["pref_name", "pref_description", "pref_strength"], "properties": {"pref_name": {"type": "string", "maxLength": 255, "description": "Name of the preference"}, "pref_description": {"type": "string", "description": "Detailed description of the preference"}, "pref_strength": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Strength of the preference"}, "user_awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of this preference"}, "environment_specific": {"type": "boolean", "description": "Whether this preference is specific to an environment"}, "effective_start": {"type": "string", "format": "date"}, "duration_estimate": {"type": "string"}, "effective_end": {"type": "string", "format": "date"}}}, "UserMood": {"type": "object", "required": ["description", "height"], "properties": {"description": {"type": "string", "description": "Description of the current mood"}, "height": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Intensity of the mood"}, "user_awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of their mood"}, "effective_start": {"type": "string", "format": "date"}, "duration_estimate": {"type": "string"}, "effective_end": {"type": "string", "format": "date"}}}, "CurrentMood": {"type": "object", "required": ["description", "height"], "properties": {"description": {"type": "string", "description": "Description of the current mood"}, "height": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Intensity of the mood"}, "user_awareness": {"type": "integer", "minimum": 0, "maximum": 100, "description": "User's awareness of their mood"}, "effective_start": {"type": "string", "format": "date"}, "duration_estimate": {"type": "string"}, "effective_end": {"type": "string", "format": "date"}}}, "TrustLevel": {"type": "object", "required": ["value"], "properties": {"value": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Trust level value"}, "aggregate_type": {"type": "string", "description": "Type of trust aggregation", "enum": ["Foundation", "Expansion", "Integration", "Mastery"]}, "aggregate_id": {"type": "string", "description": "Unique identifier for trust aggregate"}, "notes": {"type": "string", "description": "Notes about the trust level"}}}, "ActivityTailored": {"type": "object", "required": ["activity_code", "name", "description", "created_on", "duration_range", "social_requirements", "base_challenge_rating", "challengingness", "version", "tailorization_level"], "properties": {"activity_code": {"type": "string", "maxLength": 50, "description": "Code of the generic activity"}, "name": {"type": "string", "maxLength": 255, "description": "Name of the tailored activity"}, "description": {"type": "string", "description": "Description of the tailored activity"}, "created_on": {"type": "string", "format": "date", "description": "Date when the activity was created"}, "duration_range": {"type": "string", "maxLength": 50, "description": "Expected duration range for the activity"}, "instructions": {"type": "string", "description": "Detailed instructions for the activity"}, "social_requirements": {"type": "object", "description": "JSON object describing social requirements", "additionalProperties": true}, "base_challenge_rating": {"type": "integer", "minimum": 1, "maximum": 10, "description": "Base challenge rating of the activity"}, "challengingness": {"type": "object", "description": "JSON object describing challenge aspects", "additionalProperties": true}, "version": {"type": "integer", "minimum": 1, "description": "Version number of the tailored activity"}, "tailorization_level": {"type": "integer", "minimum": 1, "maximum": 10, "description": "Level of tailorization applied (1-10)"}, "tailored_name": {"type": "string", "maxLength": 255, "description": "User-specific name for the activity (deprecated, use 'name')"}, "tailored_description": {"type": "string", "description": "User-specific description of the activity (deprecated, use 'description')"}, "tailored_instructions": {"type": "string", "description": "User-specific instructions for the activity (deprecated, use 'instructions')"}, "estimated_duration_minutes": {"type": "integer", "minimum": 1, "description": "Estimated duration in minutes (deprecated, use 'duration_range')"}, "difficulty_level": {"type": "integer", "minimum": 1, "maximum": 10, "description": "Difficulty level (deprecated, use 'base_challenge_rating')"}, "required_resources": {"type": "array", "items": {"type": "string"}, "description": "List of required resources"}, "created_by": {"type": "integer", "description": "ID of the user who created this tailored activity"}}}, "GenericActivity": {"type": "object", "required": ["code", "name", "description", "category", "created_on", "duration_range", "social_requirements"], "properties": {"code": {"type": "string", "maxLength": 50, "description": "Unique code for the activity"}, "name": {"type": "string", "maxLength": 255, "description": "Name of the activity"}, "description": {"type": "string", "description": "Description of the activity"}, "category": {"type": "string", "maxLength": 100, "description": "Category of the activity"}, "subcategory": {"type": "string", "maxLength": 100, "description": "Subcategory of the activity"}, "created_on": {"type": "string", "format": "date", "description": "Date when the activity was created"}, "duration_range": {"type": "string", "maxLength": 50, "description": "Expected duration range for the activity"}, "instructions": {"type": "string", "description": "General instructions for the activity"}, "social_requirements": {"type": "object", "description": "JSON object describing social requirements", "additionalProperties": true}, "base_duration_minutes": {"type": "integer", "minimum": 1, "description": "Base duration in minutes (deprecated, use 'duration_range')"}, "base_difficulty": {"type": "integer", "minimum": 1, "maximum": 10, "description": "Base difficulty level"}, "required_skills": {"type": "array", "items": {"type": "string"}, "description": "List of required skill codes"}, "required_resources": {"type": "array", "items": {"type": "string"}, "description": "List of required resource codes"}}}, "ImportResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "profile_id": {"type": "integer", "description": "ID of the created/updated profile"}, "profile_name": {"type": "string", "description": "Name of the imported profile"}, "created_records": {"type": "integer", "description": "Number of database records created"}, "updated_records": {"type": "integer", "description": "Number of database records updated"}, "warnings": {"type": "array", "items": {"type": "string"}, "description": "Any warnings during import"}}}, "AIGenerateResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "profile_data": {"$ref": "#/components/schemas/CompleteUserProfile"}, "generation_metadata": {"type": "object", "properties": {"confidence_score": {"type": "number", "minimum": 0, "maximum": 1, "description": "AI confidence in generated profile"}, "processing_time": {"type": "number", "description": "Time taken to generate profile"}, "tokens_used": {"type": "integer", "description": "Number of tokens used for generation"}, "archetype_analysis": {"type": "object", "description": "Detailed archetype analysis results"}}}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "default": false}, "error": {"type": "string", "description": "Error message"}, "details": {"type": "object", "description": "Additional error details"}, "validation_errors": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string"}, "message": {"type": "string"}}}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}, "csrfToken": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-CSRFToken"}}}, "security": [{"bearerAuth": []}, {"csrfToken": []}], "tags": [{"name": "User Profiles", "description": "User profile management operations"}, {"name": "AI Generation", "description": "AI-powered profile generation"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Schema and documentation endpoints"}]}