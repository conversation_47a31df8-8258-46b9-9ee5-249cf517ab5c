#!/usr/bin/env python3
"""
Test Profile Gap Analysis - Debug Script

This script tests the profile gap analysis logic to ensure it's correctly
identifying missing profile information and generating specific questions.
"""

import os
import sys
import django
import asyncio
import logging

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_profile_gap_analysis():
    """Test the profile gap analysis for a new user with minimal profile data."""
    
    print("🔍 Testing Profile Gap Analysis")
    print("=" * 50)
    
    # Test with a user that should have gaps (user 191 from the logs)
    user_profile_id = "191"
    user_ws_session_name = "test_session"
    
    # Create conversation dispatcher
    dispatcher = ConversationDispatcher(
        user_profile_id=user_profile_id,
        user_ws_session_name=user_ws_session_name
    )
    
    try:
        # Test profile completion check
        print("📊 Step 1: Checking profile completion...")
        profile_completion = await dispatcher._check_profile_completion()
        print(f"   Profile completion: {profile_completion:.1%}")
        
        # Test profile gap analysis
        print("\n🔍 Step 2: Analyzing profile gaps...")
        profile_gaps = await dispatcher._analyze_profile_gaps()
        
        print(f"   Critical gaps: {len(profile_gaps.get('critical', []))}")
        print(f"   Important gaps: {len(profile_gaps.get('important', []))}")
        print(f"   Optional gaps: {len(profile_gaps.get('optional', []))}")
        print(f"   Profile readiness: {profile_gaps.get('profile_readiness', 'unknown')}")
        
        # Show specific gap details
        print("\n📋 Step 3: Critical gap details...")
        critical_gaps = profile_gaps.get('critical', [])
        for i, gap in enumerate(critical_gaps, 1):
            print(f"   {i}. Field: {gap.get('field', 'unknown')}")
            print(f"      Question: {gap.get('question', 'No question defined')}")
            print(f"      Priority: {gap.get('priority_weight', 0)}")
            print()
        
        # Test next priority field
        next_priority = profile_gaps.get('next_priority_field', {})
        if next_priority:
            print("🎯 Step 4: Next priority field...")
            print(f"   Field: {next_priority.get('field', 'unknown')}")
            print(f"   Question: {next_priority.get('question', 'No question')}")
            print(f"   Context hint: {next_priority.get('context_hint', 'No hint')}")
        else:
            print("❌ Step 4: No next priority field identified!")
        
        # Test wheel request handling
        print("\n🎯 Step 5: Testing wheel request handling...")
        test_message = {"text": "make me a wheel"}
        wheel_response = await dispatcher._handle_wheel_request_with_direct_response(
            test_message, profile_gaps
        )
        
        if wheel_response:
            print(f"   Workflow type: {wheel_response.get('workflow_type', 'unknown')}")
            print(f"   Direct response: {wheel_response.get('direct_response', 'No response')[:100]}...")
            print(f"   Reason: {wheel_response.get('reason', 'No reason')}")
        else:
            print("   No wheel response generated")
        
        print("\n✅ Profile gap analysis test completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error during profile gap analysis test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_mentor_coordination():
    """Test the mentor tool coordination logic."""
    
    print("\n🤖 Testing Mentor Tool Coordination")
    print("=" * 50)
    
    user_profile_id = "191"
    user_ws_session_name = "test_session"
    
    dispatcher = ConversationDispatcher(
        user_profile_id=user_profile_id,
        user_ws_session_name=user_ws_session_name
    )
    
    try:
        # Get profile gaps
        profile_gaps = await dispatcher._analyze_profile_gaps()
        
        # Test if mentor service is available
        if dispatcher.mentor_service:
            print("✅ MentorService is available")
            
            # Test getting mentor agent
            mentor_agent = await dispatcher.mentor_service.get_mentor_agent()
            if mentor_agent:
                print("✅ Mentor agent is available")
                
                # Test instruction injection
                if hasattr(mentor_agent, 'inject_instructions'):
                    print("✅ Mentor agent supports instruction injection")
                    
                    # Test the coordination method
                    await dispatcher._coordinate_onboarding_tools(
                        mentor_agent, profile_gaps, {}
                    )
                    print("✅ Onboarding tools coordination completed")
                    
                    # Check if instructions were injected
                    if hasattr(mentor_agent, 'runtime_instructions'):
                        instructions = mentor_agent.runtime_instructions
                        if instructions:
                            print(f"✅ Instructions injected: {len(instructions)} characters")
                            print(f"   Preview: {instructions[:200]}...")
                        else:
                            print("❌ No instructions were injected")
                    else:
                        print("❌ Mentor agent doesn't have runtime_instructions attribute")
                else:
                    print("❌ Mentor agent doesn't support instruction injection")
            else:
                print("❌ Could not get mentor agent")
        else:
            print("❌ MentorService is not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during mentor coordination test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting Profile Gap Analysis Debug Tests")
    print("=" * 60)
    
    # Test 1: Profile gap analysis
    test1_success = await test_profile_gap_analysis()
    
    # Test 2: Mentor coordination
    test2_success = await test_mentor_coordination()
    
    print("\n📊 Test Summary")
    print("=" * 30)
    print(f"Profile Gap Analysis: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"Mentor Coordination: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! The issue might be elsewhere.")
    else:
        print("\n⚠️  Some tests failed. This indicates the root cause of the issue.")

if __name__ == "__main__":
    asyncio.run(main())
