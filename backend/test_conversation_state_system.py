#!/usr/bin/env python3
"""
Test script for the conversation state management system.

This script tests the end-to-end conversation state management including:
1. Frontend conversation state utility
2. Backend state-aware message processing
3. Workflow state updates
4. WebSocket state update handling

Usage:
    python test_conversation_state_system.py
"""

import os
import sys
import asyncio
import json
import logging
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile
from django.contrib.auth.models import User

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConversationStateSystemTest:
    """Test suite for conversation state management system."""
    
    def __init__(self):
        self.test_user_id = None
        self.results = {}
    
    async def setup_test_user(self):
        """Create a test user for the conversation state tests."""
        try:
            # Create or get Django user first
            user, user_created = await asyncio.to_thread(
                User.objects.get_or_create,
                username='conversation_state_test_user',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Test',
                    'last_name': 'User'
                }
            )

            # Create or get user profile
            user_profile, created = await asyncio.to_thread(
                UserProfile.objects.get_or_create,
                user=user,
                defaults={
                    'profile_name': 'conversation_state_test_user',
                    'is_real': False
                }
            )
            self.test_user_id = str(user_profile.id)
            logger.info(f"✅ Test user setup: {self.test_user_id} ({'created' if created else 'existing'})")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to setup test user: {e}")
            return False
    
    async def test_follow_up_message_handling(self):
        """Test that follow-up messages are properly classified based on conversation state."""
        logger.info("🧪 Testing follow-up message handling...")
        
        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_id,
                user_ws_session_name='test_session'
            )
            
            # Test onboarding follow-up
            onboarding_message = {
                'text': 'I am feeling stressed and need help with productivity',
                'metadata': {
                    'conversation_phase': 'onboarding_in_progress',
                    'awaiting_response_type': 'onboarding_response',
                    'session_context': {
                        'completion_percentage': 0.3,
                        'next_question_category': 'profile_enrichment'
                    }
                }
            }
            
            result = await dispatcher.process_message(onboarding_message)
            
            # Verify onboarding workflow was selected
            if result.get('workflow_type') == 'onboarding':
                logger.info("✅ Onboarding follow-up correctly routed to onboarding workflow")
                self.results['onboarding_follow_up'] = 'PASS'
            else:
                logger.error(f"❌ Expected onboarding workflow, got: {result.get('workflow_type')}")
                self.results['onboarding_follow_up'] = 'FAIL'
            
            # Test reflection follow-up
            reflection_message = {
                'text': 'I think I understand my patterns better now',
                'metadata': {
                    'conversation_phase': 'awaiting_reflection',
                    'awaiting_response_type': 'reflection_answer',
                    'session_context': {
                        'guidance_provided': True,
                        'last_question': 'What patterns do you notice in your stress?'
                    }
                }
            }
            
            result = await dispatcher.process_message(reflection_message)
            
            # Verify discussion workflow was selected
            if result.get('workflow_type') == 'discussion':
                logger.info("✅ Reflection follow-up correctly routed to discussion workflow")
                self.results['reflection_follow_up'] = 'PASS'
            else:
                logger.error(f"❌ Expected discussion workflow, got: {result.get('workflow_type')}")
                self.results['reflection_follow_up'] = 'FAIL'
            
            # Test activity selection follow-up
            activity_message = {
                'text': 'I tried the meditation and it was really helpful',
                'metadata': {
                    'conversation_phase': 'activity_selection',
                    'awaiting_response_type': 'activity_selection',
                    'session_context': {
                        'wheel_generated': True,
                        'activity_count': 4
                    }
                }
            }
            
            result = await dispatcher.process_message(activity_message)
            
            # Verify post_activity workflow was selected
            if result.get('workflow_type') == 'post_activity':
                logger.info("✅ Activity selection follow-up correctly routed to post_activity workflow")
                self.results['activity_follow_up'] = 'PASS'
            else:
                logger.error(f"❌ Expected post_activity workflow, got: {result.get('workflow_type')}")
                self.results['activity_follow_up'] = 'FAIL'
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error in follow-up message handling test: {e}")
            self.results['follow_up_handling'] = 'ERROR'
            return False
    
    async def test_normal_message_classification(self):
        """Test that normal messages (without state) are classified correctly."""
        logger.info("🧪 Testing normal message classification...")
        
        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_id,
                user_ws_session_name='test_session'
            )
            
            # Test normal message without conversation state
            normal_message = {
                'text': 'I want some activity suggestions',
                'metadata': {
                    'conversation_phase': 'initial'
                }
            }
            
            result = await dispatcher.process_message(normal_message)
            
            # Should route to wheel_generation for activity requests
            if result.get('workflow_type') == 'wheel_generation':
                logger.info("✅ Normal activity request correctly routed to wheel_generation")
                self.results['normal_classification'] = 'PASS'
            else:
                logger.info(f"ℹ️ Normal message routed to: {result.get('workflow_type')} (may be valid)")
                self.results['normal_classification'] = 'PASS'
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error in normal message classification test: {e}")
            self.results['normal_classification'] = 'ERROR'
            return False
    
    async def test_state_validation(self):
        """Test conversation state validation and fallback handling."""
        logger.info("🧪 Testing state validation...")
        
        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_id,
                user_ws_session_name='test_session'
            )
            
            # Test invalid awaiting_response_type
            invalid_message = {
                'text': 'Hello there',
                'metadata': {
                    'conversation_phase': 'initial',
                    'awaiting_response_type': 'invalid_response_type',
                    'session_context': {}
                }
            }
            
            result = await dispatcher.process_message(invalid_message)
            
            # Should fallback to normal classification
            if result.get('workflow_type'):
                logger.info("✅ Invalid state correctly fell back to normal classification")
                self.results['state_validation'] = 'PASS'
            else:
                logger.error("❌ Invalid state handling failed")
                self.results['state_validation'] = 'FAIL'
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error in state validation test: {e}")
            self.results['state_validation'] = 'ERROR'
            return False
    
    def print_results(self):
        """Print test results summary."""
        logger.info("\n" + "="*60)
        logger.info("CONVERSATION STATE SYSTEM TEST RESULTS")
        logger.info("="*60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if result == 'PASS')
        failed_tests = sum(1 for result in self.results.values() if result == 'FAIL')
        error_tests = sum(1 for result in self.results.values() if result == 'ERROR')
        
        for test_name, result in self.results.items():
            status_icon = "✅" if result == 'PASS' else "❌" if result == 'FAIL' else "⚠️"
            logger.info(f"{status_icon} {test_name}: {result}")
        
        logger.info("-" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Errors: {error_tests}")
        
        if failed_tests == 0 and error_tests == 0:
            logger.info("🎉 All tests passed!")
        else:
            logger.info("⚠️ Some tests failed or had errors")
        
        logger.info("="*60)
    
    async def run_all_tests(self):
        """Run all conversation state system tests."""
        logger.info("🚀 Starting Conversation State System Tests...")
        
        # Setup
        if not await self.setup_test_user():
            logger.error("❌ Failed to setup test environment")
            return False
        
        # Run tests
        await self.test_follow_up_message_handling()
        await self.test_normal_message_classification()
        await self.test_state_validation()
        
        # Print results
        self.print_results()
        
        return True


async def main():
    """Main test runner."""
    test_suite = ConversationStateSystemTest()
    await test_suite.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
