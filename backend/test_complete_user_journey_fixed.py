#!/usr/bin/env python3
"""
Comprehensive end-to-end test for the complete user journey after fixes.

This test validates:
1. Onboarding workflow for incomplete profiles
2. Wheel generation workflow for complete profiles  
3. Post-activity workflow for activity feedback
4. Database integrity and proper data storage

Run with: python test_complete_user_journey_fixed.py
"""

import asyncio
import os
import sys
import django
import time
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.models import Wheel, WheelItem, HistoryEvent
from apps.user.models import UserProfile
from apps.activity.models import ActivityTailored
from django.db.models import Count
from asgiref.sync import sync_to_async


class ComprehensiveUserJourneyTest:
    """Test the complete user journey with all fixed workflows."""
    
    def __init__(self):
        self.test_user_id = '77'  # PhiPhi - our test user
        self.session_name = 'comprehensive_test'
        self.results = {
            'onboarding': {'status': 'pending', 'details': {}},
            'wheel_generation': {'status': 'pending', 'details': {}},
            'post_activity': {'status': 'pending', 'details': {}},
            'database_integrity': {'status': 'pending', 'details': {}}
        }
    
    async def run_comprehensive_test(self):
        """Run the complete test suite."""
        print("🚀 Starting Comprehensive User Journey Test")
        print("=" * 60)
        
        try:
            # Test 1: Onboarding workflow (for incomplete profiles)
            await self._test_onboarding_workflow()
            
            # Test 2: Wheel generation workflow
            await self._test_wheel_generation_workflow()
            
            # Test 3: Post-activity workflow
            await self._test_post_activity_workflow()
            
            # Test 4: Database integrity checks
            await self._test_database_integrity()
            
            # Generate final report
            self._generate_final_report()
            
        except Exception as e:
            print(f"❌ Test suite failed with error: {str(e)}")
            import traceback
            traceback.print_exc()
    
    async def _test_onboarding_workflow(self):
        """Test onboarding workflow for incomplete profiles."""
        print("\n📋 Testing Onboarding Workflow")
        print("-" * 40)
        
        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_id,
                user_ws_session_name=f"{self.session_name}_onboarding",
                fail_fast_on_errors=True
            )
            
            # Test message that should trigger onboarding for incomplete profile
            message = {
                'text': 'Hi, I need some help getting started',
                'metadata': {'test_context': 'onboarding_test'}
            }
            
            start_time = time.time()
            result = await dispatcher.process_message(message)
            duration = time.time() - start_time
            
            # Validate onboarding detection
            workflow_type = result.get('workflow_type')
            confidence = result.get('confidence', 0.0)
            
            if workflow_type == 'onboarding':
                self.results['onboarding']['status'] = 'success'
                print(f"✅ Onboarding workflow detected correctly")
                print(f"   Confidence: {confidence:.1%}")
                print(f"   Duration: {duration:.2f}s")
            else:
                self.results['onboarding']['status'] = 'failed'
                print(f"❌ Expected onboarding, got: {workflow_type}")
            
            self.results['onboarding']['details'] = {
                'workflow_type': workflow_type,
                'confidence': confidence,
                'duration': duration,
                'workflow_id': result.get('workflow_id')
            }
            
            # Wait for workflow completion
            await asyncio.sleep(8)
            
        except Exception as e:
            self.results['onboarding']['status'] = 'error'
            self.results['onboarding']['details'] = {'error': str(e)}
            print(f"❌ Onboarding test failed: {str(e)}")
    
    async def _test_wheel_generation_workflow(self):
        """Test wheel generation workflow."""
        print("\n🎯 Testing Wheel Generation Workflow")
        print("-" * 40)
        
        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_id,
                user_ws_session_name=f"{self.session_name}_wheel",
                fail_fast_on_errors=True
            )
            
            # Force wheel generation with explicit request
            message = {
                'text': 'generate wheel',
                'metadata': {'test_context': 'wheel_generation_test'}
            }
            
            # Count wheels before (using async)
            wheels_before = await sync_to_async(Wheel.objects.count)()

            start_time = time.time()
            result = await dispatcher.process_message(message)
            duration = time.time() - start_time

            workflow_type = result.get('workflow_type')
            confidence = result.get('confidence', 0.0)

            print(f"   Workflow detected: {workflow_type}")
            print(f"   Confidence: {confidence:.1%}")
            print(f"   Duration: {duration:.2f}s")

            # Wait for wheel generation to complete (it takes time to run through all agents)
            print("   Waiting for wheel generation...")
            await asyncio.sleep(30)  # Increased wait time for comprehensive workflow

            # Check if wheels were created (using async)
            wheels_after = await sync_to_async(Wheel.objects.count)()
            new_wheels = wheels_after - wheels_before

            wheel_items = 0
            if new_wheels > 0:
                # Get the latest wheel (using async)
                latest_wheel = await sync_to_async(Wheel.objects.order_by('-created_at').first)()
                if latest_wheel:
                    wheel_items = await sync_to_async(latest_wheel.items.count)()

                self.results['wheel_generation']['status'] = 'success'
                print(f"✅ Wheel generation successful")
                print(f"   New wheels created: {new_wheels}")
                print(f"   Latest wheel items: {wheel_items}")
            else:
                self.results['wheel_generation']['status'] = 'failed'
                print(f"❌ No wheels were created")
            
            self.results['wheel_generation']['details'] = {
                'workflow_type': workflow_type,
                'confidence': confidence,
                'duration': duration,
                'new_wheels': new_wheels,
                'wheel_items': wheel_items if new_wheels > 0 else 0,
                'workflow_id': result.get('workflow_id')
            }
            
        except Exception as e:
            self.results['wheel_generation']['status'] = 'error'
            self.results['wheel_generation']['details'] = {'error': str(e)}
            print(f"❌ Wheel generation test failed: {str(e)}")
    
    async def _test_post_activity_workflow(self):
        """Test post-activity workflow."""
        print("\n💬 Testing Post-Activity Workflow")
        print("-" * 40)
        
        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_id,
                user_ws_session_name=f"{self.session_name}_post_activity",
                fail_fast_on_errors=True
            )
            
            # Test post-activity feedback with explicit metadata
            message = {
                'text': 'I just completed the meditation activity and it was really helpful!',
                'metadata': {
                    'requested_workflow': 'post_activity',
                    'activity_id': '57',
                    'activity_name': 'Meditation Practice'
                }
            }
            
            start_time = time.time()
            result = await dispatcher.process_message(message)
            duration = time.time() - start_time
            
            workflow_type = result.get('workflow_type')
            confidence = result.get('confidence', 0.0)
            action_required = result.get('action_required')
            
            print(f"   Workflow detected: {workflow_type}")
            print(f"   Confidence: {confidence:.1%}")
            print(f"   Action required: {action_required}")
            print(f"   Duration: {duration:.2f}s")
            
            # Wait for workflow completion
            print("   Waiting for post-activity workflow...")
            await asyncio.sleep(10)
            
            if workflow_type == 'post_activity' and not action_required:
                self.results['post_activity']['status'] = 'success'
                print(f"✅ Post-activity workflow successful")
            else:
                self.results['post_activity']['status'] = 'failed'
                print(f"❌ Post-activity workflow failed")
            
            self.results['post_activity']['details'] = {
                'workflow_type': workflow_type,
                'confidence': confidence,
                'duration': duration,
                'action_required': action_required,
                'workflow_id': result.get('workflow_id')
            }
            
        except Exception as e:
            self.results['post_activity']['status'] = 'error'
            self.results['post_activity']['details'] = {'error': str(e)}
            print(f"❌ Post-activity test failed: {str(e)}")
    
    async def _test_database_integrity(self):
        """Test database integrity and data consistency."""
        print("\n🗄️ Testing Database Integrity")
        print("-" * 40)

        try:
            # Check user profile (using async)
            user_profile = await sync_to_async(UserProfile.objects.get)(id=self.test_user_id)
            print(f"   User profile: {user_profile.first_name} {user_profile.last_name}")

            # Check wheels (using async)
            user_wheels_count = await sync_to_async(
                lambda: Wheel.objects.filter(name__contains=f'User {self.test_user_id}').count()
            )()
            print(f"   User wheels: {user_wheels_count}")

            # Check tailored activities (using async)
            tailored_activities_count = await sync_to_async(
                lambda: ActivityTailored.objects.filter(user_profile=user_profile).count()
            )()
            print(f"   Tailored activities: {tailored_activities_count}")

            # Check for duplicate activities (should be resolved) (using async)
            duplicates_count = await sync_to_async(
                lambda: ActivityTailored.objects.filter(
                    user_profile=user_profile
                ).values('generic_activity_id', 'version').annotate(
                    count=Count('id')
                ).filter(count__gt=1).count()
            )()

            print(f"   Duplicate activities: {duplicates_count}")

            # Check history events (using async)
            recent_events_count = await sync_to_async(
                lambda: HistoryEvent.objects.filter(
                    user_profile_id=self.test_user_id
                ).order_by('-timestamp')[:10].count()
            )()
            print(f"   Recent history events: {recent_events_count}")

            if duplicates_count == 0:
                self.results['database_integrity']['status'] = 'success'
                print(f"✅ Database integrity check passed")
            else:
                self.results['database_integrity']['status'] = 'warning'
                print(f"⚠️ Found {duplicates_count} duplicate activities")

            self.results['database_integrity']['details'] = {
                'user_wheels': user_wheels_count,
                'tailored_activities': tailored_activities_count,
                'duplicate_activities': duplicates_count,
                'recent_events': recent_events_count
            }
            
        except Exception as e:
            self.results['database_integrity']['status'] = 'error'
            self.results['database_integrity']['details'] = {'error': str(e)}
            print(f"❌ Database integrity test failed: {str(e)}")
    
    def _generate_final_report(self):
        """Generate final test report."""
        print("\n📊 Final Test Report")
        print("=" * 60)
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results.values() if r['status'] == 'success')
        failed_tests = sum(1 for r in self.results.values() if r['status'] == 'failed')
        error_tests = sum(1 for r in self.results.values() if r['status'] == 'error')
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"🔥 Errors: {error_tests}")
        print(f"Success Rate: {successful_tests/total_tests:.1%}")
        
        print("\nDetailed Results:")
        for test_name, result in self.results.items():
            status_icon = {
                'success': '✅',
                'failed': '❌', 
                'error': '🔥',
                'pending': '⏳',
                'warning': '⚠️'
            }.get(result['status'], '❓')
            
            print(f"  {status_icon} {test_name.replace('_', ' ').title()}: {result['status']}")
            
            if 'workflow_id' in result['details']:
                print(f"     Workflow ID: {result['details']['workflow_id']}")
            if 'duration' in result['details']:
                print(f"     Duration: {result['details']['duration']:.2f}s")
        
        if successful_tests == total_tests:
            print(f"\n🎉 ALL TESTS PASSED! The user journey is working correctly.")
        else:
            print(f"\n⚠️ Some tests failed. Please review the issues above.")


async def main():
    """Main test runner."""
    test_runner = ComprehensiveUserJourneyTest()
    await test_runner.run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())
