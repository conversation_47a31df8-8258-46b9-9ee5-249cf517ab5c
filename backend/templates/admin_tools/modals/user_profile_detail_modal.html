<!-- User Profile Detail Modal -->
<div class="modal fade" id="user-profile-detail-modal" tabindex="-1" aria-labelledby="profile-modal-title" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="profile-modal-title">👤 User Profile Details</h5>
                <div class="modal-actions">
                    <button id="edit-profile-mode-btn" class="btn btn-light btn-sm me-2" title="Switch to edit mode">
                        ✏️ Edit Mode
                    </button>
                    <button id="save-profile-btn" class="btn btn-success btn-sm me-2" style="display: none;" title="Save changes">
                        💾 Save Changes
                    </button>
                    <button id="cancel-edit-btn" class="btn btn-secondary btn-sm me-2" style="display: none;" title="Cancel editing">
                        ❌ Cancel
                    </button>
                    <button id="refresh-profile-btn" class="btn btn-light btn-sm me-2" title="Refresh profile data">
                        🔄 Refresh
                    </button>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body p-0">
                <div id="profile-modal-body" class="profile-modal-body">
                    <!-- Profile content will be loaded here -->
                    <div class="modal-loading text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Loading user profile...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* User Profile Modal Custom Styles */

.profile-modal-body {
    max-height: 75vh;
    overflow-y: auto;
    padding: 0;
}

/* Ensure modal is above backdrop */
#user-profile-detail-modal {
    z-index: 9999 !important; /* Significantly higher to ensure it's above the backdrop */
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.modal-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* Profile content sections */
.profile-section {
    border-bottom: 1px solid #e9ecef;
    padding: 25px;
}

.profile-section:last-child {
    border-bottom: none;
}

.profile-section h3 {
    margin: 0 0 20px 0;
    color: #495057;
    font-size: 1.4em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.profile-section-icon {
    font-size: 1.2em;
}

.profile-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.profile-field {
    margin-bottom: 15px;
}

.profile-field label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 14px;
}

.profile-field-value {
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    min-height: 20px;
    font-size: 14px;
}

.profile-field-value.empty {
    color: #6c757d;
    font-style: italic;
}

/* Edit mode styles */
.profile-field input,
.profile-field select,
.profile-field textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background: white;
}

.profile-field textarea {
    min-height: 80px;
    resize: vertical;
}

.profile-field input:focus,
.profile-field select:focus,
.profile-field textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

/* Data tables for lists */
.profile-data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.profile-data-table th,
.profile-data-table td {
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    text-align: left;
    font-size: 13px;
}

.profile-data-table th {
    background: #f8f9fa;
    font-weight: 600;
}

.profile-data-table tr:nth-child(even) {
    background: #f8f9fa;
}

/* Statistics cards */
.profile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.profile-stat-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
}

.profile-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    display: block;
}

.profile-stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

/* Completeness indicator */
.profile-completeness {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
}

.completeness-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: conic-gradient(#28a745 0deg, #28a745 var(--percentage), #e9ecef var(--percentage), #e9ecef 360deg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #495057;
    position: relative;
}

.completeness-circle::before {
    content: '';
    position: absolute;
    width: 40px;
    height: 40px;
    background: #e7f3ff;
    border-radius: 50%;
}

.completeness-circle span {
    position: relative;
    z-index: 1;
    font-size: 12px;
}

.completeness-info h4 {
    margin: 0 0 5px 0;
    color: #495057;
}

.completeness-info p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

/* Loading states */
.modal-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px;
    color: #6c757d;
}

.loader {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error states */
.profile-error {
    padding: 20px;
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    margin: 20px;
}

/* Responsive design */
@media (max-width: 1200px) {
    .user-profile-modal .modal-content {
        width: 98%;
        max-width: none;
    }
}

@media (max-width: 768px) {
    .profile-modal-body {
        max-height: 70vh;
    }
    
    .modal-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .modal-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .profile-grid {
        grid-template-columns: 1fr;
    }
    
    .profile-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Enhanced scrollbar */
.profile-modal-body::-webkit-scrollbar {
    width: 8px;
}

.profile-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.profile-modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.profile-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<script>
/**
 * User Profile Detail Modal Management
 */
let userProfileModalInstance = null; // Declare a variable to hold the Bootstrap modal instance

window.openProfileModal = function(profileId, mode = 'view') {
    const modalElement = document.getElementById('user-profile-detail-modal');
    const modalBody = document.getElementById('profile-modal-body');
    const title = document.getElementById('profile-modal-title');

    // Initialize Bootstrap modal instance if it doesn't exist
    if (!userProfileModalInstance) {
        userProfileModalInstance = new bootstrap.Modal(modalElement);
    }

    // Show modal
    userProfileModalInstance.show();

    // Set title based on mode
    title.textContent = mode === 'edit' ? '✏️ Edit User Profile' : '👤 User Profile Details';

    // Show loading state
    modalBody.innerHTML = `
        <div class="modal-loading text-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading user profile...</p>
        </div>
    `;

    // Load profile data
    loadProfileData(profileId, mode);
};

async function loadProfileData(profileId, mode) {
    try {
        const response = await fetch(`/admin/user-profiles/api/${profileId}/`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const profileData = await response.json();
        renderProfileData(profileData, mode);
        
    } catch (error) {
        console.error('Error loading profile data:', error);
        document.getElementById('profile-modal-body').innerHTML = `
            <div class="profile-error">
                <h4>⚠️ Error Loading Profile</h4>
                <p>${error.message}</p>
                <button onclick="loadProfileData(${profileId}, '${mode}')" class="btn btn-primary">
                    🔄 Retry
                </button>
            </div>
        `;
    }
}

function renderProfileData(data, mode) {
    const modalBody = document.getElementById('profile-modal-body');
    const isEditMode = mode === 'edit';

    // Calculate completeness percentage
    const completeness = data.statistics?.profile_completeness || 0;

    modalBody.innerHTML = `
        <!-- Profile Completeness -->
        <div class="profile-section">
            <div class="profile-completeness">
                <div class="completeness-circle" style="--percentage: ${completeness * 3.6}deg;">
                    <span>${completeness}%</span>
                </div>
                <div class="completeness-info">
                    <h4>Profile Completeness</h4>
                    <p>This profile is ${completeness}% complete. ${completeness < 50 ? 'Consider adding more information.' : completeness < 80 ? 'Good progress! Add more details for better personalization.' : 'Excellent! This profile has comprehensive information.'}</p>
                </div>
            </div>
        </div>

        <!-- Basic Profile Information -->
        <div class="profile-section">
            <h3><span class="profile-section-icon">👤</span>Basic Information</h3>
            <div class="profile-grid">
                <div class="profile-field">
                    <label>Profile Name</label>
                    ${isEditMode ?
                        `<input type="text" name="profile_name" value="${data.profile_name || ''}" />` :
                        `<div class="profile-field-value">${data.profile_name || '<em>Not set</em>'}</div>`
                    }
                </div>
                <div class="profile-field">
                    <label>Profile Type</label>
                    <div class="profile-field-value">
                        <span class="profile-type-badge ${data.is_real ? 'profile-type-real' : 'profile-type-test'}">
                            ${data.profile_type}
                        </span>
                    </div>
                </div>
                <div class="profile-field">
                    <label>User Account</label>
                    <div class="profile-field-value">
                        <strong>${data.user.username}</strong><br>
                        <small>${data.user.email || 'No email'}</small><br>
                        <small>Joined: ${new Date(data.user.date_joined).toLocaleDateString()}</small>
                        ${data.user.last_login ? `<br><small>Last login: ${new Date(data.user.last_login).toLocaleDateString()}</small>` : ''}
                    </div>
                </div>
            </div>
        </div>

        <!-- Demographics -->
        <div class="profile-section">
            <h3><span class="profile-section-icon">📊</span>Demographics</h3>
            ${data.demographics ? `
                <div class="profile-grid">
                    <div class="profile-field">
                        <label>Full Name</label>
                        ${isEditMode ?
                            `<input type="text" name="demographics.full_name" value="${data.demographics.full_name || ''}" />` :
                            `<div class="profile-field-value">${data.demographics.full_name || '<em>Not set</em>'}</div>`
                        }
                    </div>
                    <div class="profile-field">
                        <label>Age</label>
                        ${isEditMode ?
                            `<input type="number" name="demographics.age" value="${data.demographics.age || ''}" min="1" max="120" />` :
                            `<div class="profile-field-value">${data.demographics.age || '<em>Not set</em>'}</div>`
                        }
                    </div>
                    <div class="profile-field">
                        <label>Gender</label>
                        ${isEditMode ?
                            `<input type="text" name="demographics.gender" value="${data.demographics.gender || ''}" />` :
                            `<div class="profile-field-value">${data.demographics.gender || '<em>Not set</em>'}</div>`
                        }
                    </div>
                    <div class="profile-field">
                        <label>Location</label>
                        ${isEditMode ?
                            `<input type="text" name="demographics.location" value="${data.demographics.location || ''}" />` :
                            `<div class="profile-field-value">${data.demographics.location || '<em>Not set</em>'}</div>`
                        }
                    </div>
                    <div class="profile-field">
                        <label>Language</label>
                        ${isEditMode ?
                            `<input type="text" name="demographics.language" value="${data.demographics.language || ''}" />` :
                            `<div class="profile-field-value">${data.demographics.language || '<em>Not set</em>'}</div>`
                        }
                    </div>
                    <div class="profile-field">
                        <label>Occupation</label>
                        ${isEditMode ?
                            `<input type="text" name="demographics.occupation" value="${data.demographics.occupation || ''}" />` :
                            `<div class="profile-field-value">${data.demographics.occupation || '<em>Not set</em>'}</div>`
                        }
                    </div>
                </div>
            ` : `
                <div class="profile-field-value empty">No demographics information available</div>
            `}
        </div>

        <!-- Current Environment -->
        <div class="profile-section">
            <h3><span class="profile-section-icon">🏠</span>Current Environment</h3>
            ${data.current_environment ? `
                <div class="profile-grid">
                    <div class="profile-field">
                        <label>Environment Name</label>
                        <div class="profile-field-value">${data.current_environment.environment_name}</div>
                    </div>
                    <div class="profile-field">
                        <label>Description</label>
                        <div class="profile-field-value">${data.current_environment.environment_description || '<em>No description</em>'}</div>
                    </div>
                    ${data.current_environment.generic_environment ? `
                        <div class="profile-field">
                            <label>Generic Environment</label>
                            <div class="profile-field-value">
                                <strong>${data.current_environment.generic_environment.name}</strong><br>
                                <small>${data.current_environment.generic_environment.description}</small>
                            </div>
                        </div>
                    ` : ''}
                </div>
            ` : `
                <div class="profile-field-value empty">No current environment set</div>
            `}
        </div>

        <!-- Statistics -->
        <div class="profile-section">
            <h3><span class="profile-section-icon">📈</span>Profile Statistics</h3>
            <div class="profile-stats">
                <div class="profile-stat-card">
                    <span class="profile-stat-number">${data.statistics?.total_environments || 0}</span>
                    <div class="profile-stat-label">Environments</div>
                </div>
                <div class="profile-stat-card">
                    <span class="profile-stat-number">${data.statistics?.total_skills || 0}</span>
                    <div class="profile-stat-label">Skills</div>
                </div>
                <div class="profile-stat-card">
                    <span class="profile-stat-number">${data.statistics?.total_resources || 0}</span>
                    <div class="profile-stat-label">Resources</div>
                </div>
                <div class="profile-stat-card">
                    <span class="profile-stat-number">${data.statistics?.total_preferences || 0}</span>
                    <div class="profile-stat-label">Preferences</div>
                </div>
                <div class="profile-stat-card">
                    <span class="profile-stat-number">${data.statistics?.total_history_events || 0}</span>
                    <div class="profile-stat-label">History Events</div>
                </div>
            </div>
        </div>

        ${renderEnvironmentsSection(data.environments, isEditMode)}
        ${renderSkillsSection(data.skills, isEditMode)}
        ${renderResourcesSection(data.resources, isEditMode)}
        ${renderPreferencesSection(data.preferences, isEditMode)}
        ${renderHistorySection(data.history_events)}
    `;

    // Setup edit mode functionality
    if (isEditMode) {
        setupEditMode();
    }
}

function renderEnvironmentsSection(environments, isEditMode) {
    if (!environments || environments.length === 0) {
        return `
            <div class="profile-section">
                <h3><span class="profile-section-icon">🏠</span>Environments</h3>
                <div class="profile-field-value empty">No environments defined</div>
            </div>
        `;
    }

    return `
        <div class="profile-section">
            <h3><span class="profile-section-icon">🏠</span>Environments (${environments.length})</h3>
            <table class="profile-data-table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Generic Type</th>
                        <th>Current</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    ${environments.map(env => `
                        <tr>
                            <td><strong>${env.environment_name}</strong></td>
                            <td>${env.environment_description || '<em>No description</em>'}</td>
                            <td>${env.generic_environment ? env.generic_environment.name : '<em>None</em>'}</td>
                            <td>${env.is_current ? '✅ Current' : ''}</td>
                            <td>
                                ${env.physical_properties ? '🏗️ Physical' : ''}
                                ${env.social_context ? '👥 Social' : ''}
                                ${env.activity_support ? '⚡ Activity' : ''}
                                ${env.psychological_qualities ? '🧠 Psychological' : ''}
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function renderSkillsSection(skills, isEditMode) {
    if (!skills || skills.length === 0) {
        return `
            <div class="profile-section">
                <h3><span class="profile-section-icon">🎯</span>Skills</h3>
                <div class="profile-field-value empty">No skills defined</div>
            </div>
        `;
    }

    return `
        <div class="profile-section">
            <h3><span class="profile-section-icon">🎯</span>Skills (${skills.length})</h3>
            <table class="profile-data-table">
                <thead>
                    <tr>
                        <th>Skill</th>
                        <th>Category</th>
                        <th>Level</th>
                        <th>Confidence</th>
                        <th>Interest</th>
                        <th>Growth Goal</th>
                    </tr>
                </thead>
                <tbody>
                    ${skills.map(skill => `
                        <tr>
                            <td>
                                <strong>${skill.generic_skill ? skill.generic_skill.name : 'Unknown Skill'}</strong>
                                ${skill.generic_skill?.description ? `<br><small>${skill.generic_skill.description}</small>` : ''}
                            </td>
                            <td>${skill.generic_skill?.category || '<em>No category</em>'}</td>
                            <td>${skill.level || '<em>Not set</em>'}</td>
                            <td>${skill.confidence || '<em>Not set</em>'}</td>
                            <td>${skill.interest || '<em>Not set</em>'}</td>
                            <td>${skill.growth_goal || '<em>No goal</em>'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function renderResourcesSection(resources, isEditMode) {
    if (!resources || resources.length === 0) {
        return `
            <div class="profile-section">
                <h3><span class="profile-section-icon">🛠️</span>Resources</h3>
                <div class="profile-field-value empty">No resources defined</div>
            </div>
        `;
    }

    return `
        <div class="profile-section">
            <h3><span class="profile-section-icon">🛠️</span>Resources (${resources.length})</h3>
            <table class="profile-data-table">
                <thead>
                    <tr>
                        <th>Resource</th>
                        <th>Type</th>
                        <th>Location</th>
                        <th>Ownership</th>
                        <th>Contact</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    ${resources.map(resource => `
                        <tr>
                            <td>
                                <strong>${resource.specific_name}</strong>
                                ${resource.generic_resource ? `<br><small>${resource.generic_resource.name}</small>` : ''}
                            </td>
                            <td>${resource.generic_resource?.resource_type || '<em>Unknown</em>'}</td>
                            <td>${resource.location_details || '<em>Not specified</em>'}</td>
                            <td>${resource.ownership_details || '<em>Not specified</em>'}</td>
                            <td>${resource.contact_info || '<em>No contact</em>'}</td>
                            <td>${resource.notes || '<em>No notes</em>'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function renderPreferencesSection(preferences, isEditMode) {
    if (!preferences || preferences.length === 0) {
        return `
            <div class="profile-section">
                <h3><span class="profile-section-icon">⚙️</span>Preferences</h3>
                <div class="profile-field-value empty">No preferences defined</div>
            </div>
        `;
    }

    return `
        <div class="profile-section">
            <h3><span class="profile-section-icon">⚙️</span>Preferences (${preferences.length})</h3>
            <table class="profile-data-table">
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Value</th>
                        <th>Strength</th>
                        <th>Context</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    ${preferences.map(pref => `
                        <tr>
                            <td><strong>${pref.preference_type}</strong></td>
                            <td>${pref.preference_value || '<em>Not set</em>'}</td>
                            <td>${pref.strength || '<em>Not set</em>'}</td>
                            <td>${pref.context || '<em>General</em>'}</td>
                            <td>${pref.notes || '<em>No notes</em>'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function renderHistorySection(historyEvents) {
    if (!historyEvents || historyEvents.length === 0) {
        return `
            <div class="profile-section">
                <h3><span class="profile-section-icon">📜</span>Recent History</h3>
                <div class="profile-field-value empty">No history events</div>
            </div>
        `;
    }

    return `
        <div class="profile-section">
            <h3><span class="profile-section-icon">📜</span>Recent History (${historyEvents.length})</h3>
            <table class="profile-data-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Event Type</th>
                        <th>Description</th>
                        <th>Metadata</th>
                    </tr>
                </thead>
                <tbody>
                    ${historyEvents.map(event => `
                        <tr>
                            <td>${new Date(event.timestamp).toLocaleString()}</td>
                            <td><strong>${event.event_type}</strong></td>
                            <td>${event.description}</td>
                            <td>${event.metadata ? `<pre>${JSON.stringify(event.metadata, null, 2)}</pre>` : '<em>No metadata</em>'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function setupEditMode() {
    // Setup edit mode functionality
    const editBtn = document.getElementById('edit-profile-mode-btn');
    const saveBtn = document.getElementById('save-profile-btn');
    const cancelBtn = document.getElementById('cancel-edit-btn');

    editBtn.style.display = 'none';
    saveBtn.style.display = 'inline-block';
    cancelBtn.style.display = 'inline-block';

    // Add save functionality
    saveBtn.onclick = function() {
        saveProfileChanges();
    };

    // Add cancel functionality
    cancelBtn.onclick = function() {
        // Reload the modal in view mode
        const modal = document.getElementById('user-profile-detail-modal');
        const profileId = modal.getAttribute('data-profile-id');
        if (profileId) {
            openProfileModal(profileId, 'view');
        }
    };
}

function saveProfileChanges() {
    // Collect form data and save
    console.log('Saving profile changes...');
    // TODO: Implement save functionality
}

// Modal initialization
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('user-profile-detail-modal');

    // Bootstrap handles modal close functionality automatically
    // Just ensure any custom cleanup happens on modal hide
    modal.addEventListener('hidden.bs.modal', function () {
        // Clear modal content when closed
        const modalBody = document.getElementById('profile-modal-body');
        modalBody.innerHTML = `
            <div class="modal-loading text-center p-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Loading user profile...</p>
            </div>
        `;
        // Explicitly remove any lingering modal backdrops
        const backdrops = document.getElementsByClassName('modal-backdrop');
        while(backdrops.length > 0) {
            backdrops[0].parentNode.removeChild(backdrops[0]);
        }
    });
});
</script>
