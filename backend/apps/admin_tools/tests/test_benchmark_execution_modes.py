"""
Tests for benchmark execution mode parameter handling and error display.

This module tests the fixes for:
1. Execution mode parameters (use_real_llm, use_real_tools, use_real_db) not being passed through
2. Error display improvements in the UI for WebSocket debug_info messages
3. Database constraint issues with tool_call_details field
"""

import json
import pytest
from unittest.mock import patch, MagicMock
from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse

from apps.main.models import (
    BenchmarkScenario, GenericAgent, LLMConfig, EvaluationCriteriaTemplate
)


@pytest.fixture
def admin_user(db):
    """Get or create an admin user for testing."""
    user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    return user


@pytest.fixture
def test_scenario(db):
    """Create a test benchmark scenario."""
    scenario = BenchmarkScenario.objects.create(
        name='Test Workflow Scenario',
        description='Test scenario for execution mode testing',
        agent_role='mentor',
        input_data={'test': 'input'},  # Add required field
        metadata={
            'workflow_type': 'wheel_generation',
            'expected_quality_criteria': ['clarity', 'completeness'],
            'user_profile_context': {'trust_level': 50}
        }
    )
    return scenario


@pytest.fixture
def test_agent(db):
    """Create a test generic agent."""
    agent, created = GenericAgent.objects.get_or_create(
        role='mentor',
        defaults={
            'description': 'Test agent for execution mode testing',
            'system_instructions': 'Test instruction',
            'input_schema': {},
            'output_schema': {},
            'langgraph_node_class': 'apps.main.agents.mentor_agent.MentorAgent',
            'version': '1.0.0',
            'is_active': True
        }
    )
    return agent


@pytest.fixture
def test_llm_config(db):
    """Create a test LLM configuration."""
    config = LLMConfig.objects.create(
        name='test-gpt-4',
        model_name='gpt-4',
        temperature=0.7,
        input_token_price=0.00003,
        output_token_price=0.00006,
        is_default=True
    )
    return config


@pytest.fixture
def test_evaluation_template(db):
    """Create a test evaluation criteria template."""
    template = EvaluationCriteriaTemplate.objects.create(
        name='Test Evaluation Template',
        description='Test template for execution mode testing',
        workflow_type='wheel_generation',
        category='contextual',
        criteria={
            'clarity': ['Response is clear and understandable'],
            'completeness': ['Response addresses all aspects of the request']
        },
        contextual_criteria={
            'trust_level': {
                '0-39': {
                    'safety': ['Extra safety checks required']
                },
                '40-69': {
                    'balance': ['Balanced approach needed']
                },
                '70-100': {
                    'efficiency': ['Focus on efficiency']
                }
            }
        },
        variable_ranges={
            'trust_level': {'min': 0, 'max': 100, 'type': 'integer'}
        }
    )
    return template


@pytest.mark.django_db
class TestBenchmarkExecutionModes:
    """Tests for benchmark execution mode parameter handling."""

    def test_execution_mode_parameters_passed_to_task(self, admin_user, test_scenario, test_evaluation_template):
        """Test that execution mode parameters are properly passed to the Celery task."""
        client = Client()
        client.force_login(admin_user)

        # Mock the Celery app send_task method
        with patch('celery.current_app.send_task') as mock_task:
            mock_task.return_value.id = 'test-task-id'

            # Prepare request data with execution mode parameters
            request_data = {
                'template_id': test_evaluation_template.id,
                'scenario_id': test_scenario.id,
                'params': {
                    'runs': 1,
                    'warmup_runs': 0,
                    'semantic_evaluation': True,
                    'use_real_llm': True,
                    'use_real_tools': True,
                    'use_real_db': True,
                    'context_variables': {
                        'trust_level': 75,
                        'mood': {'valence': 0.5, 'arousal': 0.3},
                        'environment': {'stress_level': 20, 'time_pressure': 30}
                    }
                }
            }

            # Make the API request
            url = reverse('game_of_life_admin:benchmark_runs_api')
            response = client.post(
                url,
                data=json.dumps(request_data),
                content_type='application/json'
            )

            # Check response
            assert response.status_code == 200
            response_data = json.loads(response.content)
            assert 'task_id' in response_data

            # Verify that the task was called with execution mode parameters
            mock_task.assert_called_once()
            call_args = mock_task.call_args

            # Check that the correct task was called
            assert call_args[0][0] == 'apps.main.tasks.benchmark_tasks.run_workflow_benchmark'

            # Check the kwargs for execution mode parameters
            kwargs = call_args[1].get('kwargs', {})
            params = kwargs.get('params', {})

            # Check that execution mode parameters are included in params
            assert params.get('use_real_llm') is True
            assert params.get('use_real_tools') is True
            assert params.get('use_real_db') is True

    def test_execution_mode_parameters_default_values(self, admin_user, test_scenario, test_evaluation_template):
        """Test that execution mode parameters default to False when not provided."""
        client = Client()
        client.force_login(admin_user)

        # Mock the Celery app send_task method
        with patch('celery.current_app.send_task') as mock_task:
            mock_task.return_value.id = 'test-task-id'

            # Prepare request data without execution mode parameters
            request_data = {
                'template_id': test_evaluation_template.id,
                'scenario_id': test_scenario.id,
                'params': {
                    'runs': 1,
                    'warmup_runs': 0,
                    'semantic_evaluation': True,
                    'context_variables': {
                        'trust_level': 50
                    }
                }
            }

            # Make the API request
            url = reverse('game_of_life_admin:benchmark_runs_api')
            response = client.post(
                url,
                data=json.dumps(request_data),
                content_type='application/json'
            )

            # Check response
            assert response.status_code == 200

            # Verify that the task was called with default execution mode parameters
            mock_task.assert_called_once()
            call_args = mock_task.call_args

            # Check that the correct task was called
            assert call_args[0][0] == 'apps.main.tasks.benchmark_tasks.run_workflow_benchmark'

            # Check the kwargs for execution mode parameters
            kwargs = call_args[1].get('kwargs', {})
            params = kwargs.get('params', {})

            # Check that execution mode parameters default to False
            assert params.get('use_real_llm', False) is False
            assert params.get('use_real_tools', False) is False
            assert params.get('use_real_db', False) is False

    def test_template_test_sync_execution_mode_parameters(self, admin_user, test_scenario, test_evaluation_template):
        """Test that execution mode parameters are handled in the sync template test endpoint."""
        client = Client()
        client.force_login(admin_user)

        # Mock the Celery app send_task method
        with patch('celery.current_app.send_task') as mock_task:
            mock_task.return_value.id = 'sync-test-id'

            # Prepare request data with execution mode parameters
            request_data = {
                'template_id': test_evaluation_template.id,
                'scenario_id': test_scenario.id,
                'params': {
                    'runs': 1,
                    'warmup_runs': 0,
                    'semantic_evaluation': True,
                    'use_real_llm': False,  # Test with False values
                    'use_real_tools': False,
                    'use_real_db': False,
                    'context_variables': {
                        'trust_level': 25
                    }
                }
            }

            # Make the API request to the benchmark runs API (async endpoint)
            url = reverse('game_of_life_admin:benchmark_runs_api')
            response = client.post(
                url,
                data=json.dumps(request_data),
                content_type='application/json'
            )

            # Check response
            assert response.status_code == 200
            response_data = json.loads(response.content)
            # The async endpoint returns a task_id
            assert 'task_id' in response_data

            # Verify that the task was called with execution mode parameters
            mock_task.assert_called_once()
            call_args = mock_task.call_args

            # Check that the correct task was called
            assert call_args[0][0] == 'apps.main.tasks.benchmark_tasks.run_workflow_benchmark'

            # Check the kwargs for execution mode parameters
            kwargs = call_args[1].get('kwargs', {})
            params = kwargs.get('params', {})

            # Check that execution mode parameters are properly passed
            assert params.get('use_real_llm') is False
            assert params.get('use_real_tools') is False
            assert params.get('use_real_db') is False


@pytest.mark.django_db
class TestBenchmarkRunCreation:
    """Tests for BenchmarkRun creation with tool_call_details field."""

    def test_benchmark_run_creation_with_tool_call_details(self, test_scenario, test_llm_config, test_agent):
        """Test that BenchmarkRun objects can be created with tool_call_details field."""
        from apps.main.models import BenchmarkRun

        # Create a benchmark run with tool_call_details
        run = BenchmarkRun.objects.create(
            scenario=test_scenario,
            agent_definition=test_agent,
            agent_version=test_agent.version,
            llm_config=test_llm_config,
            parameters={'test': 'params'},
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=100.0,
            llm_calls=5,
            tool_calls=3,
            tool_breakdown={'test_tool': 3},
            tool_call_details={
                'real_tools_used': True,
                'mocked_tools_used': False,
                'tool_calls': [
                    {
                        'tool': 'test_tool',
                        'count': 3,
                        'avg_duration_ms': 150.0
                    }
                ]
            },
            memory_operations=0,
            total_input_tokens=100,
            total_output_tokens=50,
            raw_results={'test': 'results'},
            agent_communications={}
        )

        # Verify the run was created successfully
        assert run.id is not None
        assert run.tool_call_details is not None
        assert run.tool_call_details['real_tools_used'] is True
        assert len(run.tool_call_details['tool_calls']) == 1

    def test_benchmark_run_creation_without_tool_call_details(self, test_scenario, test_llm_config, test_agent):
        """Test that BenchmarkRun objects can be created without explicitly setting tool_call_details."""
        from apps.main.models import BenchmarkRun

        # Create a benchmark run without explicitly setting tool_call_details
        run = BenchmarkRun.objects.create(
            scenario=test_scenario,
            agent_definition=test_agent,
            agent_version=test_agent.version,
            llm_config=test_llm_config,
            parameters={'test': 'params'},
            runs_count=1,
            mean_duration=1000.0,
            median_duration=1000.0,
            min_duration=1000.0,
            max_duration=1000.0,
            std_dev=0.0,
            success_rate=100.0,
            llm_calls=5,
            tool_calls=3,
            tool_breakdown={'test_tool': 3},
            memory_operations=0,
            total_input_tokens=100,
            total_output_tokens=50,
            raw_results={'test': 'results'},
            agent_communications={}
        )

        # Verify the run was created successfully with default tool_call_details
        assert run.id is not None
        assert run.tool_call_details == {}  # Should default to empty dict
