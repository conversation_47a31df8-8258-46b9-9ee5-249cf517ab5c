"""
Tests for benchmark management views.

This module contains tests for the benchmark management views in the admin_tools app.
"""

import json
import pytest
from django.urls import reverse
from django.contrib.auth.models import User
from apps.main.models import (
    BenchmarkScenario, BenchmarkRun, GenericAgent, BenchmarkTag,
    EvaluationCriteriaTemplate, LLMConfig
)


@pytest.fixture
def admin_user(db):
    """Create an admin user for testing."""
    import uuid
    username = f'admin_{uuid.uuid4().hex[:8]}'
    user = User.objects.create_user(
        username=username,
        email=f'{username}@example.com',
        password='password'
    )
    user.is_staff = True
    user.is_superuser = True
    user.save()
    return user


@pytest.fixture
def benchmark_scenario(db):
    """Create a benchmark scenario for testing."""
    scenario = BenchmarkScenario.objects.create(
        name='Test Scenario',
        description='Test Description',
        agent_role='mentor',
        input_data={'test': 'data'},
        workflow_type='test_workflow',  # Use structured field instead of metadata
        metadata={},  # Keep metadata empty or add other metadata
        is_active=True,
        version=1,
        is_latest=True,
    )
    return scenario


@pytest.fixture
def llm_config(db):
    """Create an LLM config for testing."""
    config = LLMConfig.objects.create(
        name='test-gpt-4',
        model_name='gpt-4-1106-preview',
        temperature=0.7,
        input_token_price=0.01,
        output_token_price=0.03,
        is_default=True,
        is_evaluation=False
    )
    return config


@pytest.fixture
def benchmark_tag(db):
    """Create a benchmark tag for testing."""
    tag = BenchmarkTag.objects.create(name='test_tag')
    return tag


@pytest.fixture
def generic_agent(db):
    """Create a generic agent for testing."""
    agent, created = GenericAgent.objects.get_or_create(
        role='mentor',
        defaults={
            'description': 'Test Description',
            'system_instructions': 'Test system instructions',
            'input_schema': {},
            'output_schema': {},
            'langgraph_node_class': 'apps.main.agents.mentor_agent.MentorAgent',
            'version': '1.0.0',
            'is_active': True
        }
    )
    return agent


@pytest.fixture
def benchmark_run(db, benchmark_scenario, generic_agent, llm_config):
    """Create a benchmark run for testing."""
    run = BenchmarkRun.objects.create(
        scenario=benchmark_scenario,
        agent_definition=generic_agent,
        agent_version='1.0.0',
        llm_config=llm_config,  # Add LLM config
        success_rate=0.8,
        semantic_score=0.9,
        mean_duration=1000.0,
        median_duration=1000.0,
        min_duration=1000.0,
        max_duration=1000.0,
        std_dev=0.0,
        total_input_tokens=100,
        total_output_tokens=50,
        estimated_cost=0.01,
        raw_results={'test': 'output'},
        runs_count=1,
        tool_calls=0,
        tool_breakdown={}
    )
    return run


@pytest.fixture
def evaluation_criteria_template(db):
    """Create an evaluation criteria template for testing."""
    template, _ = EvaluationCriteriaTemplate.objects.get_or_create(
        name='Test Template',
        defaults={
            'description': 'Test Description',
            'criteria': {'test': 'criteria'},
        }
    )
    return template


@pytest.mark.django_db
class TestBenchmarkDashboard:
    """Tests for the benchmark dashboard view."""

    def test_benchmark_dashboard_view(self, client, admin_user, benchmark_run):
        """Test the benchmark dashboard view."""
        client.force_login(admin_user)
        url = reverse('admin:benchmark_dashboard')
        response = client.get(url)
        assert response.status_code == 200
        assert 'recent_runs' in response.context
        assert 'active_scenarios' in response.context
        assert 'total_runs' in response.context
        assert 'success_rate' in response.context


@pytest.mark.django_db
class TestBenchmarkHistory:
    """Tests for the benchmark history view."""

    def test_benchmark_history_view(self, client, admin_user, benchmark_run):
        """Test the benchmark history view."""
        client.force_login(admin_user)
        url = reverse('admin:benchmark_history')
        response = client.get(url)
        assert response.status_code == 200
        assert 'runs' in response.context
        assert 'success_rate' in response.context
        assert 'avg_semantic_score' in response.context
        assert 'token_usage' in response.context
        assert 'cost' in response.context

    def test_benchmark_history_view_with_agent_role(self, client, admin_user, benchmark_run):
        """Test the benchmark history view with agent role filter."""
        client.force_login(admin_user)
        url = reverse('admin:benchmark_history_by_agent', args=['mentor'])
        response = client.get(url)
        assert response.status_code == 200
        assert 'runs' in response.context
        assert 'current_agent_role' in response.context
        assert response.context['current_agent_role'] == 'mentor'


@pytest.mark.django_db
class TestBenchmarkManagement:
    """Tests for the benchmark management view."""

    def test_benchmark_management_view(self, client, admin_user, benchmark_scenario, benchmark_tag):
        """Test the benchmark management view."""
        client.force_login(admin_user)
        url = reverse('admin:benchmark_management')
        response = client.get(url)
        assert response.status_code == 200
        assert 'scenarios' in response.context
        assert 'tags' in response.context
        assert 'workflow_types' in response.context
        assert 'workflow_stats' in response.context
        assert 'agent_roles' in response.context


@pytest.mark.django_db
class TestBenchmarkScenarioAPI:
    """Tests for the benchmark scenario API views."""

    def test_get_scenarios(self, client, admin_user, benchmark_scenario):
        """Test getting all scenarios."""
        client.force_login(admin_user)
        url = reverse('admin:benchmark_scenarios_api')
        response = client.get(url)
        assert response.status_code == 200
        data = json.loads(response.content)
        assert 'scenarios' in data
        assert len(data['scenarios']) == 1
        assert data['scenarios'][0]['name'] == 'Test Scenario'

    def test_get_scenario_detail(self, client, admin_user, benchmark_scenario):
        """Test getting a specific scenario."""
        client.force_login(admin_user)
        url = reverse('admin:benchmark_scenario_detail_api', args=[benchmark_scenario.id])
        response = client.get(url)
        assert response.status_code == 200
        data = json.loads(response.content)
        assert 'scenario' in data
        assert data['scenario']['name'] == 'Test Scenario'
        assert data['scenario']['description'] == 'Test Description'
        assert data['scenario']['agent_role'] == 'mentor'

    def test_create_scenario(self, client, admin_user):
        """Test creating a new scenario."""
        client.force_login(admin_user)
        url = reverse('admin:benchmark_scenarios_api')
        data = {
            'name': 'New Scenario',
            'description': 'New Description',
            'agent_role': 'mentor',
            'input_data': {'test': 'new_data'},
            'metadata': {'workflow_type': 'new_workflow'},
            'is_active': True,
            'tags': ['new_tag']
        }
        response = client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True
        assert 'id' in data
        assert 'message' in data

        # Verify the scenario was created
        scenario = BenchmarkScenario.objects.get(name='New Scenario')
        assert scenario.description == 'New Description'
        assert scenario.agent_role == 'mentor'
        assert scenario.metadata['workflow_type'] == 'new_workflow'

        # Verify the tag was created and associated
        tag = BenchmarkTag.objects.get(name='new_tag')
        assert tag in scenario.tags.all()

    def test_update_scenario(self, client, admin_user, benchmark_scenario):
        """Test updating an existing scenario."""
        client.force_login(admin_user)
        url = reverse('admin:benchmark_scenario_detail_api', args=[benchmark_scenario.id])
        data = {
            'name': 'Updated Scenario',
            'description': 'Updated Description',
            'is_active': False
        }
        response = client.put(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True

        # Verify the scenario was updated
        scenario = BenchmarkScenario.objects.get(id=benchmark_scenario.id)
        assert scenario.name == 'Updated Scenario'
        assert scenario.description == 'Updated Description'
        assert scenario.is_active is False

    def test_delete_scenario(self, client, admin_user, benchmark_scenario):
        """Test deleting a scenario."""
        client.force_login(admin_user)
        url = reverse('admin:benchmark_scenario_detail_api', args=[benchmark_scenario.id])
        response = client.delete(url)
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True

        # Verify the scenario was deleted
        with pytest.raises(BenchmarkScenario.DoesNotExist):
            BenchmarkScenario.objects.get(id=benchmark_scenario.id)

    def test_batch_update_scenarios(self, client, admin_user, benchmark_scenario):
        """Test batch updating scenarios."""
        client.force_login(admin_user)
        url = reverse('admin:benchmark_scenarios_api')
        data = {
            'scenario_ids': [benchmark_scenario.id],
            'add_tag': 'batch_tag'
        }
        response = client.patch(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True

        # Verify the tag was added
        scenario = BenchmarkScenario.objects.get(id=benchmark_scenario.id)
        tag = BenchmarkTag.objects.get(name='batch_tag')
        assert tag in scenario.tags.all()


@pytest.mark.django_db
class TestWorkflowTypeAPI:
    """Tests for the workflow type API views."""

    def test_get_workflow_types(self, client, admin_user, benchmark_scenario):
        """Test getting workflow types."""
        client.force_login(admin_user)
        url = reverse('admin:workflow_types_api')
        response = client.get(url)
        assert response.status_code == 200
        data = json.loads(response.content)
        assert 'workflow_types' in data
        assert 'stats' in data
        assert 'test_workflow' in data['workflow_types']

        # Verify stats
        workflow_stat = next((s for s in data['stats'] if s['name'] == 'test_workflow'), None)
        assert workflow_stat is not None
        assert workflow_stat['scenario_count'] == 1
        assert workflow_stat['active_count'] == 1


@pytest.mark.django_db
class TestEvaluationCriteriaTemplateAPI:
    """Tests for the evaluation criteria template API views."""

    def test_get_templates(self, client, admin_user, evaluation_criteria_template):
        """Test getting all templates."""
        client.force_login(admin_user)
        url = reverse('admin:evaluation_templates_api')
        response = client.get(url)
        assert response.status_code == 200
        data = json.loads(response.content)
        assert 'templates' in data
        assert len(data['templates']) == 1
        assert data['templates'][0]['name'] == 'Test Template'

    def test_get_template_detail(self, client, admin_user, evaluation_criteria_template):
        """Test getting a specific template."""
        client.force_login(admin_user)
        url = reverse('admin:evaluation_template_detail_api', args=[evaluation_criteria_template.id])
        response = client.get(url)
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['id'] == evaluation_criteria_template.id
        assert data['name'] == 'Test Template'
        assert data['description'] == 'Test Description'
        assert data['criteria'] == {'test': 'criteria'}

    def test_create_template(self, client, admin_user):
        """Test creating a new template."""
        client.force_login(admin_user)
        url = reverse('admin:evaluation_templates_api')
        data = {
            'name': 'New Template',
            'description': 'New Description',
            'criteria': {'test': 'new_criteria'}
        }
        response = client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True
        assert 'id' in data

        # Verify the template was created
        template = EvaluationCriteriaTemplate.objects.get(name='New Template')
        assert template.description == 'New Description'
        assert template.criteria == {'test': 'new_criteria'}

    def test_update_template(self, client, admin_user, evaluation_criteria_template):
        """Test updating an existing template."""
        client.force_login(admin_user)
        url = reverse('admin:evaluation_template_detail_api', args=[evaluation_criteria_template.id])
        data = {
            'name': 'Updated Template',
            'description': 'Updated Description',
            'criteria': {'test': 'updated_criteria'}
        }
        response = client.put(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True

        # Verify the template was updated
        template = EvaluationCriteriaTemplate.objects.get(id=evaluation_criteria_template.id)
        assert template.name == 'Updated Template'
        assert template.description == 'Updated Description'
        assert template.criteria == {'test': 'updated_criteria'}

    def test_delete_template(self, client, admin_user, evaluation_criteria_template):
        """Test deleting a template."""
        client.force_login(admin_user)
        url = reverse('admin:evaluation_template_detail_api', args=[evaluation_criteria_template.id])
        response = client.delete(url)
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True

        # Verify the template was deleted
        with pytest.raises(EvaluationCriteriaTemplate.DoesNotExist):
            EvaluationCriteriaTemplate.objects.get(id=evaluation_criteria_template.id)


@pytest.mark.django_db(transaction=True)
class TestBenchmarkValidationAPI:
    """Tests for the benchmark validation API views."""

    def test_validate_scenario(self, client, admin_user, benchmark_scenario, monkeypatch):
        """Test validating a specific scenario."""
        # Mock the call_command function to avoid actually running the validation command
        def mock_call_command(*args, **kwargs):
            return None

        monkeypatch.setattr('apps.admin_tools.benchmark.views.call_command', mock_call_command)

        client.force_login(admin_user)
        url = reverse('admin:benchmark_scenario_validation_api', args=[benchmark_scenario.id])
        response = client.post(url, content_type='application/json')
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True
        assert 'result' in data
        assert data['result']['scenario_id'] == benchmark_scenario.id
        assert data['result']['scenario_name'] == benchmark_scenario.name

    def test_validate_multiple_scenarios(self, client, admin_user, benchmark_scenario, monkeypatch):
        """Test validating multiple scenarios."""
        # Mock the call_command function to avoid actually running the validation command
        def mock_call_command(*args, **kwargs):
            return None

        monkeypatch.setattr('apps.admin_tools.benchmark.views.call_command', mock_call_command)

        client.force_login(admin_user)
        url = reverse('admin:benchmark_validation_api')
        data = {
            'scenario_ids': [benchmark_scenario.id]
        }
        response = client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True
        assert 'results' in data
        assert len(data['results']) == 1
        assert data['results'][0]['scenario_id'] == benchmark_scenario.id
        assert data['results'][0]['scenario_name'] == benchmark_scenario.name


@pytest.mark.django_db
class TestBenchmarkImportExport:
    """Tests for the benchmark import/export views."""

    def test_export_scenarios(self, client, admin_user, benchmark_scenario):
        """Test exporting scenarios."""
        client.force_login(admin_user)
        url = reverse('admin:export_scenarios')
        response = client.get(url)
        assert response.status_code == 200
        assert response['Content-Type'] == 'application/json'
        assert 'Content-Disposition' in response

        # Parse the exported data
        data = json.loads(response.content)
        assert len(data) == 1
        assert data[0]['name'] == benchmark_scenario.name
        assert data[0]['description'] == benchmark_scenario.description
        assert data[0]['agent_role'] == benchmark_scenario.agent_role
        assert data[0]['metadata'] == benchmark_scenario.metadata

    def test_import_scenarios(self, client, admin_user):
        """Test importing scenarios."""
        client.force_login(admin_user)
        url = reverse('admin:import_scenarios')

        # Create a JSON file with scenario data
        scenario_data = [
            {
                'name': 'Imported Scenario',
                'description': 'Imported Description',
                'agent_role': 'mentor',
                'input_data': {'test': 'imported_data'},
                'metadata': {'workflow_type': 'imported_workflow'},
                'is_active': True,
                'tags': ['imported_tag']
            }
        ]

        # Create a file-like object with the JSON data
        import io
        file_content = io.BytesIO(json.dumps(scenario_data).encode('utf-8'))
        file_content.name = 'scenarios.json'

        # Submit the form with the file
        response = client.post(url, {'scenario_file': file_content})
        assert response.status_code == 302  # Redirect after successful import

        # Verify the scenario was imported
        scenario = BenchmarkScenario.objects.get(name='Imported Scenario')
        assert scenario.description == 'Imported Description'
        assert scenario.agent_role == 'mentor'
        assert scenario.metadata['workflow_type'] == 'imported_workflow'

        # Verify the tag was created and associated
        tag = BenchmarkTag.objects.get(name='imported_tag')
        assert tag in scenario.tags.all()


@pytest.mark.django_db
class TestRunAllBenchmarksAPI:
    """Tests for the run all benchmarks API view."""

    def test_run_all_benchmarks(self, client, admin_user, benchmark_scenario, monkeypatch):
        """Test running all benchmarks."""
        # Mock the celery task to avoid actually running the benchmarks
        def mock_send_task(*args, **kwargs):
            class MockTask:
                id = 'mock-task-id'
            return MockTask()

        monkeypatch.setattr('apps.admin_tools.benchmark.views.celery_app.send_task', mock_send_task)

        client.force_login(admin_user)
        url = reverse('admin:run_all_benchmarks')
        data = {
            'runs': 3,
            'semantic_evaluation': True
        }
        response = client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.content)
        assert data['success'] is True
        assert data['task_id'] == 'mock-task-id'
        assert 'message' in data


# OBSOLETE TEST REMOVED: TestMultiRangeContextualEvaluation
# This test was testing internal implementation details that have changed
# with the new Celery-based architecture. The multi-range contextual evaluation
# functionality is now properly tested in the contextual evaluation integration tests.
