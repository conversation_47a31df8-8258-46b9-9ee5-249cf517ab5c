# Admin Tools AI Workspace - AI Agent Entrypoint

> **🤖 AI Agent Entry Point Rules**
>
> This file serves as the mandatory entry point for AI agents working in this workspace.
>
> **RULES TO RESPECT:**
> - **Maintain the list of available tools and their description up-to-date**
> - **Maintain the list of available AI-intended documentation and their description up-to-date**
> - **Do not write anything here that is not specifically about the usage of that workspace**
> - **Clean if you see that one of those rules is not respected**
>
> This file must contain ONLY:
> 1. Clear workspace purpose and scope
> 2. Complete catalog of available tools with descriptions and usage
> 3. Complete catalog of available documentation with descriptions and purpose
> 4. Quick-start commands for common scenarios
> 5. Decision matrix for tool selection based on symptoms/needs

---

## 🎯 **Workspace Purpose**

This workspace provides a comprehensive suite of AI-intuitive tools designed for systematic debugging, monitoring, and improvement of Goali's multi-agent system. It enables autonomous capabilities for agent management, benchmark analysis, user story simulation, real-time system observability, and systematic agent improvement through normalized testing scenarios.

**Core Functions:**
- **Agent Management**: Manage agent characteristics, instructions, and LLM configurations
- **Benchmark Analysis**: Access and analyze benchmark results with intelligent insights
- **Admin Validation**: Validate admin page quality using Playwright automation
- **Knowledge Management**: Document findings and maintain comprehensive reports
- **System Debugging**: Comprehensive debugging tools for agent development and improvement

---

## 🚀 **Available Tools**

### **Primary Tools** (Most Important)

#### **AI Workspace Main Interface** (`workspace.py`)
**Purpose**: Central interface providing unified access to all workspace tools and capabilities
**Usage**: `from apps.admin_tools.ai_workspace import AIWorkspace; workspace = AIWorkspace()`
**Output**: Comprehensive system health analysis, agent performance reports, admin validation results
**Success Criteria**: All tools initialized, system health analyzed, improvements identified

#### **Agent Manager** (`tools/agent_manager.py`)
**Purpose**: Manage Goali agent characteristics, instructions, and LLM configurations
**Usage**: `workspace.agent_manager.get_all_agents()` or direct import
**Output**: Agent configuration data, update confirmations, validation results
**Success Criteria**: Agent configs updated successfully, backups created, validation passed

#### **Benchmark Analyzer** (`tools/benchmark_analyzer.py`)
**Purpose**: Access and analyze benchmark results with intelligent insights and trend analysis
**Usage**: `workspace.benchmark_analyzer.analyze_agent_performance_trends(agent_role='mentor', days=30)`
**Output**: Performance trend analysis, quality indicators, improvement recommendations
**Success Criteria**: Trends identified, quality patterns analyzed, actionable recommendations generated

### **Specialized Tools**

#### **Admin Validator** (`tools/admin_validator.py`)
**Purpose**: Validate admin page quality using Playwright automation for UI testing
**Usage**: `workspace.admin_validator.test_admin_dashboard()`
**Output**: UI component status, accessibility compliance, performance metrics
**Success Criteria**: All UI elements functional, accessibility standards met, performance acceptable

#### **Knowledge Manager** (`tools/knowledge_manager.py`)
**Purpose**: Document findings and maintain comprehensive reports from AI agent work
**Usage**: `workspace.knowledge_manager.document_finding(finding_data)`
**Output**: Documentation paths, comprehensive reports, knowledge base updates
**Success Criteria**: Findings documented, reports generated, knowledge base maintained

#### **Benchmark Tools** (`tools/benchmark_tools.py`)
**Purpose**: Intelligent selection of existing benchmark-oriented tools from real_condition_tests
**Usage**: `workspace.benchmark_tools.run_quick_benchmark_test(test_config)`
**Output**: Benchmark execution results, system validation status, quality metrics
**Success Criteria**: Benchmarks execute successfully, system health validated, quality thresholds met

### **Debugging & Analysis Tools**

#### **Agent Debugging System** (`tools/agent_debugging_system.py`)
**Purpose**: Comprehensive debugging and improvement system for early-stage agent development
**Usage**: Direct import and initialization for specialized debugging scenarios
**Output**: Agent behavior analysis, user story simulation results, improvement metrics
**Success Criteria**: Agent issues identified, debugging scenarios executed, improvements measured

#### **ADHD User Debugging** (`tools/adhd_user_debugging.py`)
**Purpose**: Specialized debugging tool for addressing ADHD user experience issues
**Usage**: Direct import for ADHD-specific user experience testing and debugging
**Output**: ADHD user experience analysis, workflow optimization recommendations
**Success Criteria**: ADHD user issues identified, experience optimized, workflows improved

### **Utility Tools**

#### **Observability Tools** (`observability_tools.py`)
**Purpose**: Enhanced monitoring and debugging tools for system maintenance
**Usage**: `python observability_tools.py --monitor-websockets` or `--check-system-health`
**Output**: WebSocket monitoring data, system health reports, workflow tracking information
**Success Criteria**: System monitored effectively, health issues identified, workflows tracked

---

## 📚 **Available Documentation**

### **Core Documentation**

#### **Workspace Guide** (`docs/WORKSPACE_GUIDE.md`)
**Purpose**: Comprehensive guide to workspace architecture and component usage
**Use When**: Need to understand workspace structure, component relationships, or usage patterns
**Key Sections**: Architecture overview, component capabilities, AI-friendly features, integration patterns

#### **Authoritative Schemas** (`docs/AUTHORITATIVE_SCHEMAS.md`)
**Purpose**: Definitive schemas and data structures for workspace components
**Use When**: Need to understand data formats, API contracts, or integration specifications
**Key Sections**: Schema definitions, data validation rules, API specifications

#### **Agent Debugging Strategy** (`docs/AGENT_DEBUGGING_STRATEGY.md`)
**Purpose**: Comprehensive strategy for debugging and improving agent performance
**Use When**: Working on agent quality issues or systematic agent improvements
**Key Sections**: Debugging methodologies, performance analysis, improvement strategies

### **Reference Documentation**

#### **ADHD UX Analysis Report** (`docs/ADHD_UX_ANALYSIS_REPORT.md`)
**Purpose**: Specialized analysis of ADHD user experience patterns and improvements
**Use When**: Working on ADHD-specific user experience issues or accessibility improvements
**Key Sections**: ADHD user patterns, UX optimization strategies, accessibility guidelines

#### **Configuration File** (`config.json`)
**Purpose**: Workspace configuration settings and tool enablement
**Use When**: Need to configure workspace behavior, enable/disable tools, or adjust settings
**Key Sections**: Tool configurations, integration settings, performance parameters

#### **Task Management** (`TASKS.md`)
**Purpose**: Current mission objectives and task tracking for workspace development
**Use When**: Need to understand current priorities, mission status, or development roadmap
**Key Sections**: Mission phases, task completion status, success criteria

---

## 🧠 **AI Agent Decision Matrix**

| **Symptom/Need** | **Primary Tool** | **Expected Result** | **Next Action** |
|-------------------|------------------|---------------------|-----------------|
| "Agent not performing well" | `agent_manager.get_all_agents()` | ✅ Agent configs analyzed | Update instructions/LLM config |
| "Need benchmark analysis" | `benchmark_analyzer.analyze_agent_performance_trends()` | ✅ Performance trends identified | Apply recommendations |
| "Admin UI issues" | `admin_validator.test_admin_dashboard()` | ✅ UI issues identified | Fix UI components |
| "System health check" | `workspace.analyze_system_health()` | ✅ Health status report | Address identified issues |
| "Need documentation" | `knowledge_manager.document_finding()` | ✅ Findings documented | Share knowledge |
| "ADHD user issues" | `adhd_user_debugging.py` | ✅ ADHD issues identified | Optimize user experience |
| "Benchmark system broken" | `benchmark_tools.validate_benchmark_system()` | ✅ System validated | Fix identified issues |
| "Agent debugging needed" | `agent_debugging_system.py` | ✅ Debug analysis complete | Apply improvements |
| "WebSocket monitoring" | `observability_tools.py --monitor-websockets` | ✅ WebSocket data captured | Analyze communication patterns |
| "Performance optimization" | `workspace.improve_agent_performance()` | ✅ Improvements identified | Implement optimizations |

---

## 🎮 **Quick Start Commands**

### **Emergency/Most Common Issues**
```python
# Complete system health analysis
from apps.admin_tools.ai_workspace import AIWorkspace
workspace = AIWorkspace()
await workspace.initialize()
health_report = await workspace.analyze_system_health()

# Agent performance improvement
improvement_report = await workspace.improve_agent_performance('mentor')

# Admin interface validation
validation_report = await workspace.validate_admin_interfaces()

# Quick benchmark system validation
validation = await workspace.benchmark_tools.validate_benchmark_system()
```

### **Diagnostic Commands**
```python
# Analyze agent performance trends
analysis = await workspace.benchmark_analyzer.analyze_agent_performance_trends(
    agent_role='mentor', days=30
)

# Get recent benchmark runs
runs = workspace.benchmark_analyzer.get_recent_benchmark_runs(days=7, limit=50)

# Document findings
workspace.knowledge_manager.document_finding({
    'title': 'Performance Analysis',
    'description': 'Found performance bottleneck...',
    'category': 'performance'
})
```

### **System Monitoring Commands**
```bash
# WebSocket monitoring
python observability_tools.py --monitor-websockets

# System health check
python observability_tools.py --check-system-health

# Workflow tracking
python observability_tools.py --track-workflow <workflow_id>

# Celery log analysis
python observability_tools.py --analyze-celery-logs
```

---

**🤖 AI Agent Status**: Ready for comprehensive system analysis and agent improvement
**Last Updated**: June 17, 2025 | **Tool Count**: 8 active tools
**Mission**: Systematic debugging, monitoring, and improvement of Goali's multi-agent system
