"""
Admin tools views for the Game of Life project.

This module contains views for the admin tools, including:
- Dashboard views
- Benchmark run views
- History views
"""

import json
import logging
from datetime import datetime
from django.http import JsonResponse, HttpResponse, Http404, HttpResponseForbidden
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.db import transaction
from django.views import View
from django.db.models import Count, Q
from django.views.decorators.http import require_http_methods
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync, sync_to_async
from channels.db import database_sync_to_async
import traceback

# Import models and services needed for benchmarking
from apps.main.models import BenchmarkScenario, BenchmarkRun, GenericAgent, BenchmarkTag
from apps.main.services.benchmark_manager import AgentBenchmarker
from django.utils.dateparse import parse_date
from celery import current_app as celery_app # Import Celery app instance
import asyncio # For potential async operations if needed directly in view
import uuid # For handling UUIDs in API responses

# Import benchmark management views - these are now in the benchmark package
from apps.admin_tools.benchmark import views as benchmark_views

logger = logging.getLogger(__name__)

# Enhanced WebSocket Connection Dashboard
@staff_member_required
def connection_dashboard_view(request):
    """
    Enhanced admin view for monitoring and debugging WebSocket connections.
    Replaces the old websocket_tester_view with comprehensive monitoring capabilities.
    """
    context = {
        'title': 'WebSocket Connection Dashboard',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': '',
        'app_label': 'admin_tools',
    }

    # Get basic connection statistics
    try:
        import redis
        from django.conf import settings

        # Get Redis connection info
        channel_layers = getattr(settings, 'CHANNEL_LAYERS', {})
        config = channel_layers.get('default', {}).get('CONFIG', {})
        hosts = config.get('hosts', [])

        if hosts:
            host_info = hosts[0]
            if isinstance(host_info, tuple):
                host, port = host_info
            else:
                host, port = host_info, 6379

            r = redis.Redis(host=host, port=port, decode_responses=True)
            r.ping()

            # Get basic stats
            info = r.info()
            context['redis_connected'] = True
            context['redis_clients'] = info.get('connected_clients', 0)
            context['redis_memory'] = info.get('used_memory_human', 'Unknown')

            # Count active sessions
            session_keys = r.keys("ws_session:*")
            context['active_sessions'] = len(session_keys)

        else:
            context['redis_connected'] = False
            context['active_sessions'] = 0

    except Exception as e:
        context['redis_connected'] = False
        context['redis_error'] = str(e)
        context['active_sessions'] = 0

    return render(request, 'admin_tools/connection_dashboard.html', context)

# Keep the old websocket tester for backward compatibility (deprecated)
@staff_member_required
def websocket_tester_view(request):
    """
    Admin view to send messages to the WebSocket consumer and view history.
    """
    context = {
        'title': 'WebSocket Tester',
        'has_permission': True, # Required for admin templates
        'site_header': 'Game of Life Admin', # Optional: Customize header
        'opts': '', # Placeholder for model options if extending ModelAdmin
        'app_label': 'admin_tools', # App label for breadcrumbs/context
    }

    if request.method == 'POST':
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest' or \
                  request.content_type == 'application/json'

        send_status = None
        send_error = None
        user_id = None
        message_json = None

        try:
            if is_ajax:
                # Handle Fetch request (JSON body)
                data = json.loads(request.body)
                user_id = data.get('user_id')
                message_json = data.get('message_json')
            else:
                # Handle standard form submission (fallback)
                user_id = request.POST.get('user_id')
                message_json = request.POST.get('message_json')
                context['last_user_id'] = user_id
                context['last_sent_message'] = message_json

            if not user_id or not message_json:
                raise ValueError("Missing user_id or message_json")

            # Validate JSON and send message (common logic)
            message_data = json.loads(message_json) # Validate JSON structure
            channel_layer = get_channel_layer()
            # Target the predictable group name that the UserSessionConsumer joins
            target_group_name = f"user_session_group_{user_id}"

            async_to_sync(channel_layer.group_send)(
                target_group_name, # Send to the user-specific group
                {
                    # Use the custom type that maps to the 'admin_message' handler
                    "type": "admin.message",
                    # Pass the raw JSON string as 'text' which the handler expects
                    "text": message_json
                }
            )
            send_status = f"Message successfully sent to group '{target_group_name}' via admin.message handler." # Update status message

        except json.JSONDecodeError:
            send_error = "Invalid JSON format. Please check your input."
        except ValueError as e:
             send_error = str(e)
        except Exception as e:
            send_error = f"An error occurred while sending: {e}"

        if is_ajax:
            # Return JSON response for Fetch request
            return JsonResponse({'send_status': send_status, 'send_error': send_error})
        else:
            # Update context for standard form submission render
            context['send_status'] = send_status
            context['send_error'] = send_error

    # Render the full page for GET requests or standard POST fallback
    return render(request, 'admin_tools/websocket_tester.html', context)


###############################################################################
# Dashboard Views
###############################################################################

@staff_member_required
def dashboard(request):
    """Admin dashboard view."""
    context = {
        'title': 'Admin Dashboard',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    # Get recent benchmark runs with related data
    recent_runs = BenchmarkRun.objects.select_related('scenario', 'agent_definition').order_by('-execution_date')[:10]
    context['recent_runs'] = recent_runs

    # Get active benchmark scenarios
    active_scenarios = BenchmarkScenario.objects.filter(is_active=True).count()
    context['active_scenarios'] = active_scenarios

    # Get total benchmark runs
    total_runs = BenchmarkRun.objects.count()
    context['total_runs'] = total_runs

    # Get success rate (using success_rate field, considering >= 0.5 as success)
    if total_runs > 0:
        success_runs = BenchmarkRun.objects.filter(success_rate__gte=0.5).count()
        context['success_rate'] = (success_runs / total_runs * 100)
    else:
        context['success_rate'] = 0

    # Prepare recent runs data for JavaScript (for error status updates)
    recent_runs_data = []
    for run in recent_runs:
        # Check if run has errors in raw_results
        has_errors = False
        if run.raw_results and isinstance(run.raw_results, dict):
            errors = run.raw_results.get('errors', [])
            has_errors = isinstance(errors, list) and len(errors) > 0

        recent_runs_data.append({
            'id': run.id,
            'has_errors': has_errors
        })

    import json
    context['recent_runs_json'] = json.dumps(recent_runs_data)

    return render(request, 'admin_tools/benchmark_dashboard.html', context)

###############################################################################
# Benchmark Run Views
###############################################################################

@staff_member_required
def benchmark_run(request, run_id=None):
    """Admin view for benchmark run details."""
    context = {
        'title': 'Benchmark Run',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    if run_id:
        # Get the benchmark run
        run = get_object_or_404(BenchmarkRun, id=run_id)
        context['run'] = run

        # Get the scenario
        scenario = run.scenario
        context['scenario'] = scenario

        # Get the agent
        agent = run.agent
        context['agent'] = agent

        # Get the run details
        context['run_details'] = {
            'id': run.id,
            'execution_date': run.execution_date,
            'success': run.success,
            'success_rate': run.success_rate,
            'semantic_score': run.semantic_score,
            'execution_time': run.execution_time,
            'token_usage': run.token_usage,
            'cost': run.cost,
            'evaluator_llm_model': run.evaluator_llm_model,
        }

        # Get the run output
        context['run_output'] = run.output

        # Get the run evaluation
        context['run_evaluation'] = run.evaluation

        return render(request, 'admin_tools/benchmark_run.html', context)
    else:
        # Redirect to the dashboard
        return redirect('game_of_life_admin:dashboard')

###############################################################################
# History Views
###############################################################################

@staff_member_required
def history(request):
    """Admin view for benchmark run history."""
    context = {
        'title': 'Benchmark History',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    # Get all benchmark runs
    runs = BenchmarkRun.objects.all().order_by('-execution_date')
    context['runs'] = runs

    # Get success rate
    success_runs = runs.filter(success=True).count()
    context['success_rate'] = (success_runs / runs.count() * 100) if runs.count() > 0 else 0

    # Get average semantic score
    semantic_scores = [run.semantic_score for run in runs if run.semantic_score is not None]
    context['avg_semantic_score'] = sum(semantic_scores) / len(semantic_scores) if semantic_scores else 0

    # Get total token usage
    token_usage = sum([run.token_usage for run in runs if run.token_usage is not None])
    context['token_usage'] = token_usage

    # Get total cost
    cost = sum([run.cost for run in runs if run.cost is not None])
    context['cost'] = cost

    return render(request, 'admin_tools/history.html', context)

###############################################################################
# API Views
###############################################################################

class BenchmarkRunView(View):
    """API View for benchmark run operations."""

    async def _check_permissions(self, request):
        """Async-safe permission check."""
        is_authenticated = await sync_to_async(lambda: request.user.is_authenticated)()
        if not is_authenticated:
            return False
        is_active = await sync_to_async(lambda: request.user.is_active)()
        is_staff = await sync_to_async(lambda: request.user.is_staff)()
        return is_active and is_staff

    def _check_permissions_sync(self, request):
        """Sync version of permission check."""
        return (request.user.is_authenticated and
                request.user.is_active and
                request.user.is_staff)

    def dispatch(self, request, *args, **kwargs):
        # Perform permission check before calling get or post
        if not self._check_permissions_sync(request):
            return HttpResponseForbidden("You do not have permission to access this resource.")
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, run_id=None):
        """Get benchmark run details."""
        try:
            if run_id:
                # Get a specific run
                # Get a specific run (now sync)
                run = get_object_or_404(BenchmarkRun.objects.select_related('scenario', 'agent_definition'), id=run_id)
                # Extract context package information
                context_package = self._extract_context_package(run)

                # Import the _determine_execution_type function from benchmark views
                from apps.admin_tools.benchmark.views import _determine_execution_type

                # Determine execution type (Agent vs Workflow evaluation)
                execution_type = _determine_execution_type(run)

                # Extract workflow-specific data if this is a workflow evaluation
                workflow_type = None
                agent_communications = None

                if 'Workflow' in execution_type:
                    # Extract workflow type from scenario metadata or parameters
                    if run.scenario and run.scenario.metadata:
                        workflow_type = run.scenario.metadata.get('workflow_type')

                    if not workflow_type and run.parameters:
                        workflow_type = run.parameters.get('workflow_type')

                    # Extract agent communications from the database field first (preferred)
                    agent_communications = run.agent_communications or {}

                    # If not found in database field, fallback to raw results
                    if not agent_communications and run.raw_results:
                        agent_communications = run.raw_results.get('agent_communications', {})
                        # If not found in top level, check in last_output
                        if not agent_communications:
                            last_output = run.raw_results.get('last_output', {})
                            if isinstance(last_output, dict):
                                agent_communications = last_output.get('agent_communications', {})

                run_data = {
                    'id': run.id,
                    'scenario': run.scenario.name if run.scenario else 'N/A',
                    'agent_role': run.agent_definition.role if run.agent_definition else 'N/A',
                    'execution_date': run.execution_date.isoformat(),
                    'execution_type': execution_type,  # Add execution type
                    'workflow_type': workflow_type,    # Add workflow type for workflow evaluations
                    'success_rate': run.success_rate,
                    'semantic_score': run.semantic_score,
                    'semantic_evaluation_details': run.semantic_evaluation_details,
                    'semantic_evaluations': run.semantic_evaluations,
                    'mean_duration': run.mean_duration,
                    'median_duration': run.median_duration,
                    'min_duration': run.min_duration,
                    'max_duration': run.max_duration,
                    'std_dev': run.std_dev,
                    'llm_calls': run.llm_calls,
                    'tool_calls': run.tool_calls,
                    'tool_breakdown': run.tool_breakdown,
                    'tool_call_details': self._extract_tool_call_details(run),
                    'token_usage': run.total_tokens,
                    'total_input_tokens': run.total_input_tokens,
                    'total_output_tokens': run.total_output_tokens,
                    'estimated_cost': float(run.estimated_cost) if run.estimated_cost else 0.0,
                    'llm_model': run.agent_llm_model_name,
                    'llm_temperature': run.llm_temperature,
                    'agent_version': run.agent_version,
                    'evaluator_llm_model': run.evaluator_llm_model,
                    'parameters': run.parameters,
                    'raw_results': run.raw_results,
                    'context_package': context_package,
                    'agent_communications': agent_communications,  # Add agent communications for workflow evaluations
                    'comparison_results': self._extract_comparison_results(run),
                }

                return JsonResponse(run_data)
            else:
                # List runs with filtering
                scenario_id = request.GET.get('scenario_id')
                agent_role = request.GET.get('agent_role')  # Fixed: use agent_role instead of agent_id
                tag = request.GET.get('tag')  # Added tag filtering
                success = request.GET.get('success')
                start_date = request.GET.get('start_date')
                end_date = request.GET.get('end_date')
                include_chart_data = request.GET.get('include_chart_data', 'false').lower() == 'true' # Check for parameter

                # Debug logging for filter parameters
                logger.debug(f"Filter parameters: scenario_id={scenario_id}, agent_role={agent_role}, tag={tag}, success={success}, start_date={start_date}, end_date={end_date}, include_chart_data={include_chart_data}")

                queryset = BenchmarkRun.objects.select_related('scenario', 'agent_definition')

                if scenario_id:
                    queryset = queryset.filter(scenario_id=scenario_id)

                if agent_role:
                    # Filter by agent role through agent_definition
                    queryset = queryset.filter(agent_definition__role=agent_role)

                if tag:
                    # Filter by scenario tags
                    try:
                        tag_id = int(tag)
                        queryset = queryset.filter(scenario__tags__id=tag_id)
                    except ValueError:
                        # If not an integer, treat as tag name
                        queryset = queryset.filter(scenario__tags__name=tag)

                if success is not None:
                    success_bool = success.lower() == 'true'
                    # Use success_rate >= 0.5 to determine success
                    if success_bool:
                        queryset = queryset.filter(success_rate__gte=0.5)
                    else:
                        queryset = queryset.filter(success_rate__lt=0.5)

                if start_date:
                    start_date_obj = parse_date(start_date)
                    if start_date_obj:
                        queryset = queryset.filter(execution_date__gte=start_date_obj)

                if end_date:
                    end_date_obj = parse_date(end_date)
                    if end_date_obj:
                        # Include runs on the end date itself
                        queryset = queryset.filter(execution_date__lte=end_date_obj)

                # Order the queryset for consistent chart data
                ordered_queryset = queryset.order_by('execution_date')

                runs_data = []
                chart_data = {}

                if include_chart_data:
                    # Initialize chart data structures
                    chart_labels = []
                    mean_durations = []
                    median_durations = []
                    success_rates = []
                    semantic_scores = []
                    llm_models = []
                    agent_versions = []
                    dimension_data = {} # For scenario view dimensions

                    # Determine if it's a scenario-specific view for dimension data
                    is_scenario_view = scenario_id is not None

                    for run in ordered_queryset:
                        chart_labels.append(run.execution_date.isoformat())
                        mean_durations.append(float(run.mean_duration) if run.mean_duration is not None else None)
                        median_durations.append(float(run.median_duration) if run.median_duration is not None else None)
                        success_rates.append(float(run.success_rate) if run.success_rate is not None else None)
                        semantic_scores.append(float(run.semantic_score) if run.semantic_score is not None else None)
                        llm_models.append(run.agent_llm_model_name or 'N/A')
                        agent_versions.append(run.agent_version or 'N/A')

                        if is_scenario_view and run.semantic_evaluations:
                             # Assuming semantic_evaluations is a dict of evaluators
                             # and we take the first one for dimension data for the chart
                             first_evaluator_key = next(iter(run.semantic_evaluations), None)
                             if first_evaluator_key:
                                 evaluation = run.semantic_evaluations[first_evaluator_key]
                                 if evaluation and evaluation.get('dimensions'):
                                     for dim_name, dim_data in evaluation['dimensions'].items():
                                         if dim_name not in dimension_data:
                                             # Initialize list with None for previous runs
                                             dimension_data[dim_name] = [None] * (len(chart_labels) - 1)
                                         dimension_data[dim_name].append(float(dim_data.get('score')) if dim_data.get('score') is not None else None)

                    # Ensure all dimension lists have the same length as labels
                    for dim_name in dimension_data:
                         while len(dimension_data[dim_name]) < len(chart_labels):
                             dimension_data[dim_name].append(None)


                    chart_data = {
                        'labels': chart_labels,
                        'mean_durations': mean_durations,
                        'median_durations': median_durations,
                        'success_rates': success_rates,
                        'semantic_scores': semantic_scores,
                        'llm_models': llm_models,
                        'agent_versions': agent_versions,
                        'dimension_data': dimension_data if is_scenario_view else {}, # Include dimensions only for scenario view
                    }

                # Prepare runs data for the table (using the original queryset order)
                # Re-fetch or re-process the queryset to get the table data
                # Using the original queryset (not ordered by date for table display)
                runs_for_table = queryset.order_by('-execution_date') # Keep original table order
                for run in runs_for_table:
                     try:
                         # ... (existing code to format run data for table) ...
                        # Extract evaluation variables from parameters
                        params = run.parameters or {}
                        context_vars = params.get('context_variables', {})

                        # Also check in evaluation_template_data if not found in context_variables
                        if not context_vars:
                            template_data = params.get('evaluation_template_data', {})
                            context_vars = template_data.get('context_variables', {})

                        # Extract individual evaluation variables with proper handling of different formats
                        try:
                            trust_level_raw = context_vars.get('trust_level', 'N/A')
                            if isinstance(trust_level_raw, dict) and 'value' in trust_level_raw:
                                trust_level = trust_level_raw['value']
                            elif isinstance(trust_level_raw, (int, float)):
                                trust_level = trust_level_raw
                            else:
                                trust_level = 'N/A'
                        except Exception:
                            trust_level = 'N/A'

                        # Extract valence - check both nested and direct formats
                        try:
                            valence_raw = context_vars.get('valence', 'N/A')
                            if valence_raw == 'N/A':
                                # Try nested format
                                mood = context_vars.get('mood', {})
                                valence_raw = mood.get('valence', 'N/A') if isinstance(mood, dict) else 'N/A'

                            if isinstance(valence_raw, dict) and 'value' in valence_raw:
                                valence = valence_raw['value']
                            elif isinstance(valence_raw, (int, float)):
                                valence = valence_raw
                            else:
                                valence = 'N/A'
                        except Exception:
                            valence = 'N/A'

                        # Extract arousal - check both nested and direct formats
                        try:
                            arousal_raw = context_vars.get('arousal', 'N/A')
                            if arousal_raw == 'N/A':
                                # Try nested format
                                mood = context_vars.get('mood', {})
                                arousal_raw = mood.get('arousal', 'N/A') if isinstance(mood, dict) else 'N/A'

                            if isinstance(arousal_raw, dict) and 'value' in arousal_raw:
                                arousal = arousal_raw['value']
                            elif isinstance(arousal_raw, (int, float)):
                                arousal = arousal_raw
                            else:
                                arousal = 'N/A'
                        except Exception:
                            arousal = 'N/A'

                        # Extract stress level - check both nested and direct formats
                        try:
                            stress_level_raw = context_vars.get('stress_level', 'N/A')
                            if stress_level_raw == 'N/A':
                                # Try nested format
                                environment = context_vars.get('environment', {})
                                stress_level_raw = environment.get('stress_level', 'N/A') if isinstance(environment, dict) else 'N/A'

                            if isinstance(stress_level_raw, dict) and 'value' in stress_level_raw:
                                stress_level = stress_level_raw['value']
                            elif isinstance(stress_level_raw, (int, float)):
                                stress_level = stress_level_raw
                            else:
                                stress_level = 'N/A'
                        except Exception:
                            stress_level = 'N/A'

                        # Extract time pressure - check both nested and direct formats
                        try:
                            time_pressure_raw = context_vars.get('time_pressure', 'N/A')
                            if time_pressure_raw == 'N/A':
                                # Try nested format
                                environment = context_vars.get('environment', {})
                                time_pressure_raw = environment.get('time_pressure', 'N/A') if isinstance(environment, dict) else 'N/A'

                            if isinstance(time_pressure_raw, dict) and 'value' in time_pressure_raw:
                                time_pressure = time_pressure_raw['value']
                            elif isinstance(time_pressure_raw, (int, float)):
                                time_pressure = time_pressure_raw
                            else:
                                time_pressure = 'N/A'
                        except Exception:
                            time_pressure = 'N/A'

                        # Format token usage for display with safe property access
                        try:
                            # Use raw numbers for frontend display (not the k-formatted version)
                            if run.total_input_tokens is not None and run.total_output_tokens is not None:
                                total_tokens = (run.total_input_tokens or 0) + (run.total_output_tokens or 0)
                                token_usage_display = f"{run.total_input_tokens}+{run.total_output_tokens}={total_tokens}"
                            elif run.total_input_tokens is not None or run.total_output_tokens is not None:
                                # Handle case where only one is set
                                input_tokens = run.total_input_tokens or 0
                                output_tokens = run.total_output_tokens or 0
                                total_tokens = input_tokens + output_tokens
                                token_usage_display = f"{input_tokens}+{output_tokens}={total_tokens}"
                            else:
                                # Both total_input_tokens and total_output_tokens are None
                                token_usage_display = "N/A"
                        except Exception as e:
                            logger.warning(f"Error formatting token usage for run {run.id}: {e}")
                            token_usage_display = "N/A"

                        # Format cost for display with safe conversion
                        try:
                            if run.estimated_cost is not None:
                                cost_display = f"${float(run.estimated_cost):.6f}"
                            else:
                                cost_display = '$0.000000'
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error formatting cost for run {run.id}: {e}")
                            cost_display = '$0.000000'

                        # Import the _determine_execution_type function from benchmark views
                        from apps.admin_tools.benchmark.views import _determine_execution_type

                        # Determine execution type (Agent vs Workflow evaluation)
                        execution_type = _determine_execution_type(run)

                        runs_data.append({
                            'id': run.id,
                            'scenario_id': run.scenario_id,
                            'scenario_name': run.scenario.name if run.scenario else 'N/A',
                            'agent_role': run.agent_definition.role if run.agent_definition else 'N/A',
                            'execution_date': run.execution_date.isoformat(),
                            'execution_type': execution_type,  # Add execution type for proper display
                            'success_rate': run.success_rate,
                            'semantic_score': run.semantic_score,
                            'mean_duration': run.mean_duration,
                            'trust_level': trust_level,
                            'valence': valence,
                            'arousal': arousal,
                            'stress_level': stress_level,
                            'time_pressure': time_pressure,
                            'token_usage': token_usage_display,
                            'cost': cost_display,
                            'total_input_tokens': run.total_input_tokens,
                            'total_output_tokens': run.total_output_tokens,
                            'estimated_cost': float(run.estimated_cost) if run.estimated_cost else 0.0,
                            'context_variables': context_vars,  # Include full context variables for frontend processing
                            'parameters': run.parameters,  # Add parameters for error checking
                        })
                     except Exception as e:
                         logger.error(f"Error processing run {run.id} for table data: {e}", exc_info=True)

                response_data = {'runs': runs_data}
                if include_chart_data:
                    response_data['chart_data'] = chart_data

                return JsonResponse(response_data)

        except Exception as e:
            logger.error("Error in BenchmarkRunView.get", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request):
        """Run a benchmark scenario."""
        try:
            data = json.loads(request.body)

            # Check if this is an evaluation template test request
            if 'evaluation_template_id' in data or 'evaluation_template_data' in data:
                return self._handle_template_test_sync(data)

            # Original benchmark run logic
            # Validate required fields
            required_fields = ['scenario_id']
            for field in required_fields:
                if field not in data:
                    return JsonResponse({'error': f"Missing required field: {field}"}, status=400)

            # Get parameters
            params = data.get('params', {})
            runs = params.get('runs', 1)
            semantic_evaluation = params.get('semantic_evaluation', True)

            # Get the scenario (now sync)
            scenario = get_object_or_404(BenchmarkScenario, id=data['scenario_id'])

            # Launch benchmark task (now sync)
            from celery import current_app as celery_app

            task_params = {
                'runs': runs,
                'semantic_evaluation': semantic_evaluation,
            }

            # Add optional parameters
            if 'llm_config_id' in params:
                task_params['llm_config_id'] = params['llm_config_id']
            if 'evaluation_llm_config_id' in params:
                task_params['evaluation_llm_config_id'] = params['evaluation_llm_config_id']
            if 'context_variables' in params:
                task_params['context_variables'] = params['context_variables']

            # Extract user_profile_id for separate parameter
            user_profile_id = params.get('user_profile_id') if params else None

            # Add user_profile_id to task_params for backwards compatibility
            if 'user_profile_id' in params:
                task_params['user_profile_id'] = params['user_profile_id']

            # Add execution mode parameters (use_real_llm, use_real_tools, use_real_db)
            if 'use_real_llm' in params:
                task_params['use_real_llm'] = params['use_real_llm']
            if 'use_real_tools' in params:
                task_params['use_real_tools'] = params['use_real_tools']
            if 'use_real_db' in params:
                task_params['use_real_db'] = params['use_real_db']

            # Launch the task with user_profile_id as separate parameter
            task = celery_app.send_task(
                'apps.main.tasks.benchmark_tasks.run_workflow_benchmark',
                args=[str(scenario.id)],
                kwargs={
                    'params': task_params,
                    'user_profile_id': user_profile_id
                }
            )

            task_id = task.id

            return JsonResponse({
                'success': True,
                'task_id': task_id,
                'message': f"Benchmark task launched successfully."
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': "Invalid JSON body"}, status=400)
        except Exception as e:
            logger.error("Error in BenchmarkRunView.post", exc_info=True)

            # Emit error event via WebSocket for real-time feedback
            try:
                from apps.main.services.event_service import EventService
                import traceback
                import asyncio

                # Create task to emit error event (since this is a sync context)
                asyncio.create_task(
                    EventService.emit_debug_info(
                        level='error',
                        message=f"Benchmark API error: {str(e)}",
                        source='BenchmarkRunView',
                        details={
                            'error_type': type(e).__name__,
                            'error_message': str(e),
                            'traceback': traceback.format_exc(),
                            'endpoint': 'POST /admin/benchmarks/api/run/'
                        }
                    )
                )
            except Exception as emit_error:
                logger.warning(f"Failed to emit error event: {emit_error}")

            return JsonResponse({'error': str(e)}, status=500)

    def delete(self, request):
        """Delete benchmark runs."""
        try:
            data = json.loads(request.body)
            run_ids = data.get('run_ids', [])

            if not run_ids:
                return JsonResponse({'success': False, 'message': 'No run IDs provided for deletion.'}, status=400)

            # Ensure all IDs are valid UUIDs if applicable, or integers
            valid_run_ids = []
            for run_id in run_ids:
                try:
                    # Assuming IDs can be UUIDs or integers
                    uuid.UUID(str(run_id)) # Validate as UUID
                    valid_run_ids.append(run_id)
                except ValueError:
                    try:
                        int(run_id) # Validate as int
                        valid_run_ids.append(run_id)
                    except ValueError:
                        logger.warning(f"Invalid run ID format received for deletion: {run_id}")
                        continue

            if not valid_run_ids:
                return JsonResponse({'success': False, 'message': 'No valid run IDs found for deletion.'}, status=400)

            # Perform deletion
            deleted_count, _ = BenchmarkRun.objects.filter(id__in=valid_run_ids).delete()

            if deleted_count > 0:
                logger.info(f"Successfully deleted {deleted_count} benchmark run(s). IDs: {valid_run_ids}")
                return JsonResponse({'success': True, 'message': f'Successfully deleted {deleted_count} benchmark run(s).'})
            else:
                logger.warning(f"No benchmark runs found for deletion with provided IDs: {valid_run_ids}")
                return JsonResponse({'success': False, 'message': 'No benchmark runs found matching the provided IDs.'}, status=404)

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': 'Invalid JSON body.'}, status=400)
        except Exception as e:
            logger.error(f"Error deleting benchmark runs: {e}", exc_info=True)
            return JsonResponse({'success': False, 'message': f'An error occurred during deletion: {str(e)}'}, status=500)

    def _extract_tool_call_details(self, run):
        """Extract tool call details for the modal display."""
        try:
            tool_call_details = {
                'total_calls': run.tool_calls or 0,
                'mocked_calls': 0,
                'real_calls': 0,
            }

            # Try to extract mocked vs real calls from raw results
            if run.raw_results:
                # Look for tool call information in various places
                errors = run.raw_results.get('errors', [])
                for error in errors:
                    if 'mock' in str(error).lower():
                        tool_call_details['mocked_calls'] += 1

                # Calculate real calls
                tool_call_details['real_calls'] = tool_call_details['total_calls'] - tool_call_details['mocked_calls']

            return tool_call_details
        except Exception as e:
            logger.warning(f"Error extracting tool call details for run {run.id}: {e}")
            return {'total_calls': 0, 'mocked_calls': 0, 'real_calls': 0}

    def _extract_comparison_results(self, run):
        """Extract comparison results if available."""
        try:
            if run.compared_to_run and run.performance_p_value is not None:
                return {
                    'compared_to_run_id': str(run.compared_to_run.id),
                    'performance_p_value': run.performance_p_value,
                    'is_performance_significant': run.is_performance_significant,
                }
            return None
        except Exception as e:
            logger.warning(f"Error extracting comparison results for run {run.id}: {e}")
            return None

    def _extract_context_package(self, run):
        """Extract and format context package information from a benchmark run."""
        try:
            context_package = {
                'summary': {},  # High-level summary for quick analysis
                'input_context': {},
                'agent_output': {},
                'context_flow': [],
                'evaluation_context': {},
                'analysis': {}  # Meaningful analysis data
            }

            # Extract input context from parameters
            params = run.parameters or {}

            # Get context variables used for evaluation
            context_vars = params.get('context_variables', {})
            if context_vars:
                context_package['input_context']['context_variables'] = context_vars

            # Get evaluation template data if present
            template_data = params.get('evaluation_template_data', {})
            if template_data:
                context_package['evaluation_context']['template_data'] = template_data

            # Extract scenario input data
            scenario_input = {}
            if run.scenario and run.scenario.input_data:
                scenario_input = run.scenario.input_data
                context_package['input_context']['scenario_input'] = scenario_input

            # Extract agent output from raw results
            raw_results = run.raw_results or {}

            # Get the last output data which contains agent responses
            # Handle both agent benchmarks (last_output_data) and workflow benchmarks (last_output)
            last_output = raw_results.get('last_output_data', {}) or raw_results.get('last_output', {})
            if last_output:
                context_package['agent_output']['final_output'] = last_output

                # For workflow benchmarks, extract wheel data if available
                if 'output_data' in last_output and 'wheel' in last_output['output_data']:
                    wheel_data = last_output['output_data']['wheel']
                    context_package['agent_output']['wheel_data'] = wheel_data
                    context_package['agent_output']['workflow_type'] = last_output.get('workflow_type', 'unknown')

            # Extract individual run outputs if available
            individual_runs = raw_results.get('individual_runs', [])
            if individual_runs:
                context_package['agent_output']['individual_runs'] = individual_runs

            # Extract context flow information
            context_flow = []

            # Add initial context
            if context_vars or scenario_input:
                context_flow.append({
                    'stage': 'input',
                    'description': 'Initial context provided to agent',
                    'data': {
                        'context_variables': context_vars,
                        'scenario_input': scenario_input
                    }
                })

            # Add agent processing stage
            if last_output:
                context_flow.append({
                    'stage': 'processing',
                    'description': 'Agent processing and output generation',
                    'data': last_output
                })

            # Add evaluation stage if semantic evaluation was performed
            if run.semantic_score is not None:
                context_flow.append({
                    'stage': 'evaluation',
                    'description': 'Semantic evaluation of agent output',
                    'data': {
                        'semantic_score': run.semantic_score,
                        'evaluation_details': run.semantic_evaluation_details
                    }
                })

            context_package['context_flow'] = context_flow

            # Generate high-level summary
            context_package['summary'] = self._generate_context_summary(run, context_vars, last_output, scenario_input)

            # Generate analysis data
            context_package['analysis'] = self._generate_context_analysis(run, context_vars, last_output, individual_runs)

            return context_package

        except Exception as e:
            logger.error(f"Error extracting context package for run {run.id}: {e}", exc_info=True)
            return {
                'error': f"Failed to extract context package: {str(e)}",
                'summary': {},
                'input_context': {},
                'agent_output': {},
                'context_flow': [],
                'evaluation_context': {},
                'analysis': {}
            }

    def _generate_context_summary(self, run, context_vars, last_output, scenario_input):
        """Generate high-level summary for quick analysis."""
        try:
            summary = {
                'context_profile': 'Unknown',
                'agent_confidence': 'N/A',
                'response_quality': 'N/A',
                'key_factors': [],
                'performance_indicators': {}
            }

            # Determine context profile based on context variables
            if context_vars:
                trust_level = self._extract_numeric_value(context_vars.get('trust_level', 0))
                stress_level = self._extract_numeric_value(context_vars.get('stress_level', 0))

                if trust_level >= 70:
                    if stress_level <= 30:
                        summary['context_profile'] = 'High Trust, Low Stress'
                    else:
                        summary['context_profile'] = 'High Trust, High Stress'
                elif trust_level >= 40:
                    if stress_level <= 30:
                        summary['context_profile'] = 'Moderate Trust, Low Stress'
                    else:
                        summary['context_profile'] = 'Moderate Trust, High Stress'
                else:
                    if stress_level <= 30:
                        summary['context_profile'] = 'Low Trust, Low Stress'
                    else:
                        summary['context_profile'] = 'Low Trust, High Stress'

                # Extract key factors
                key_factors = []
                if trust_level < 40:
                    key_factors.append('Low Trust Environment')
                if stress_level > 70:
                    key_factors.append('High Stress Situation')

                valence = self._extract_numeric_value(context_vars.get('valence', 0))
                if valence < -0.5:
                    key_factors.append('Negative Mood')
                elif valence > 0.5:
                    key_factors.append('Positive Mood')

                summary['key_factors'] = key_factors

            # Extract agent confidence if available
            if last_output and isinstance(last_output, dict):
                confidence = last_output.get('confidence_score') or last_output.get('confidence')
                if confidence is not None:
                    try:
                        conf_val = float(confidence)
                        if conf_val >= 0.8:
                            summary['agent_confidence'] = 'High'
                        elif conf_val >= 0.6:
                            summary['agent_confidence'] = 'Moderate'
                        else:
                            summary['agent_confidence'] = 'Low'
                    except (ValueError, TypeError):
                        pass

            # Determine response quality based on semantic score
            if run.semantic_score is not None:
                try:
                    score = float(run.semantic_score)
                    if score >= 0.8:
                        summary['response_quality'] = 'Excellent'
                    elif score >= 0.6:
                        summary['response_quality'] = 'Good'
                    elif score >= 0.4:
                        summary['response_quality'] = 'Fair'
                    else:
                        summary['response_quality'] = 'Poor'
                except (ValueError, TypeError):
                    pass

            # Performance indicators
            summary['performance_indicators'] = {
                'semantic_score': run.semantic_score,
                'success_rate': run.success_rate,
                'mean_duration': run.mean_duration,
                'token_efficiency': self._calculate_token_efficiency(run)
            }

            return summary

        except Exception as e:
            logger.warning(f"Error generating context summary for run {run.id}: {e}")
            return {'error': str(e)}

    def _generate_context_analysis(self, run, context_vars, last_output, individual_runs):
        """Generate meaningful analysis data for human analysis."""
        try:
            analysis = {
                'context_impact': {},
                'agent_behavior': {},
                'performance_patterns': {},
                'recommendations': []
            }

            # Context impact analysis
            if context_vars:
                trust_level = self._extract_numeric_value(context_vars.get('trust_level', 0))
                stress_level = self._extract_numeric_value(context_vars.get('stress_level', 0))

                analysis['context_impact'] = {
                    'trust_influence': self._analyze_trust_impact(trust_level, run.semantic_score),
                    'stress_influence': self._analyze_stress_impact(stress_level, run.mean_duration),
                    'mood_influence': self._analyze_mood_impact(context_vars, last_output)
                }

            # Agent behavior analysis
            if last_output:
                analysis['agent_behavior'] = {
                    'response_length': len(str(last_output.get('agent_response', ''))) if last_output.get('agent_response') else 0,
                    'reasoning_depth': len(str(last_output.get('reasoning', ''))) if last_output.get('reasoning') else 0,
                    'confidence_level': last_output.get('confidence_score', 'N/A'),
                    'response_type': self._classify_response_type(last_output)
                }

            # Performance patterns
            if individual_runs and len(individual_runs) > 1:
                durations = [run.get('duration', 0) for run in individual_runs if run.get('duration')]
                if durations:
                    analysis['performance_patterns'] = {
                        'consistency': self._calculate_consistency(durations),
                        'improvement_trend': self._analyze_improvement_trend(individual_runs),
                        'variability': max(durations) - min(durations) if durations else 0
                    }

            # Generate recommendations
            analysis['recommendations'] = self._generate_recommendations(run, context_vars, analysis)

            return analysis

        except Exception as e:
            logger.warning(f"Error generating context analysis for run {run.id}: {e}")
            return {'error': str(e)}

    def _extract_numeric_value(self, value):
        """Extract numeric value from various formats."""
        if isinstance(value, dict) and 'value' in value:
            return float(value['value'])
        elif isinstance(value, (int, float)):
            return float(value)
        return 0.0

    def _analyze_trust_impact(self, trust_level, semantic_score):
        """Analyze how trust level impacts performance."""
        if semantic_score is None:
            return "Insufficient data"

        if trust_level >= 70 and semantic_score >= 0.7:
            return "High trust correlates with good performance"
        elif trust_level < 40 and semantic_score < 0.5:
            return "Low trust may be limiting performance"
        else:
            return "Trust level shows mixed correlation with performance"

    def _analyze_stress_impact(self, stress_level, mean_duration):
        """Analyze how stress level impacts response time."""
        if mean_duration is None:
            return "Insufficient data"

        if stress_level > 70 and mean_duration > 2000:
            return "High stress may be increasing response time"
        elif stress_level < 30 and mean_duration < 1500:
            return "Low stress environment enables faster responses"
        else:
            return "Stress level shows mixed correlation with response time"

    def _analyze_mood_impact(self, context_vars, last_output):
        """Analyze how mood impacts agent output."""
        if not last_output:
            return "Insufficient data"

        valence = self._extract_numeric_value(context_vars.get('valence', 0))
        response = str(last_output.get('agent_response', ''))

        if valence > 0.5 and ('positive' in response.lower() or 'good' in response.lower()):
            return "Positive mood reflected in optimistic response"
        elif valence < -0.5 and ('concern' in response.lower() or 'careful' in response.lower()):
            return "Negative mood reflected in cautious response"
        else:
            return "Mood impact on response tone unclear"

    def _classify_response_type(self, last_output):
        """Classify the type of agent response."""
        if not last_output or not last_output.get('agent_response'):
            return "No response"

        response = str(last_output.get('agent_response', '')).lower()

        if 'recommend' in response or 'suggest' in response:
            return "Recommendation"
        elif 'question' in response or '?' in response:
            return "Inquiry"
        elif 'explain' in response or 'because' in response:
            return "Explanation"
        else:
            return "General response"

    def _calculate_consistency(self, durations):
        """Calculate consistency score based on duration variance."""
        if len(durations) < 2:
            return "N/A"

        avg = sum(durations) / len(durations)
        variance = sum((d - avg) ** 2 for d in durations) / len(durations)
        std_dev = variance ** 0.5

        if std_dev / avg < 0.1:
            return "Very consistent"
        elif std_dev / avg < 0.2:
            return "Consistent"
        else:
            return "Variable"

    def _analyze_improvement_trend(self, individual_runs):
        """Analyze if performance improves across runs."""
        if len(individual_runs) < 2:
            return "Insufficient data"

        # Simple trend analysis based on duration
        durations = [run.get('duration', 0) for run in individual_runs if run.get('duration')]
        if len(durations) < 2:
            return "Insufficient data"

        if durations[-1] < durations[0]:
            return "Improving (faster responses)"
        elif durations[-1] > durations[0]:
            return "Declining (slower responses)"
        else:
            return "Stable performance"

    def _calculate_token_efficiency(self, run):
        """Calculate token efficiency score."""
        try:
            if run.total_input_tokens and run.total_output_tokens and run.semantic_score:
                total_tokens = run.total_input_tokens + run.total_output_tokens
                efficiency = float(run.semantic_score) / (total_tokens / 1000)  # Score per 1k tokens
                return round(efficiency, 3)
        except (TypeError, ZeroDivisionError):
            pass
        return "N/A"

    def _generate_recommendations(self, run, context_vars, analysis):
        """Generate actionable recommendations based on analysis."""
        recommendations = []

        # Performance-based recommendations
        if run.semantic_score is not None and run.semantic_score < 0.5:
            recommendations.append("Consider adjusting evaluation criteria or agent prompts")

        if run.mean_duration is not None and run.mean_duration > 3000:
            recommendations.append("Investigate potential performance bottlenecks")

        # Context-based recommendations
        if context_vars:
            trust_level = self._extract_numeric_value(context_vars.get('trust_level', 0))
            stress_level = self._extract_numeric_value(context_vars.get('stress_level', 0))

            if trust_level < 40:
                recommendations.append("Test with higher trust scenarios to validate agent capabilities")

            if stress_level > 70:
                recommendations.append("Consider stress-reduction strategies in agent design")

        # Behavior-based recommendations
        behavior = analysis.get('agent_behavior', {})
        if behavior.get('confidence_level') and isinstance(behavior['confidence_level'], (int, float)):
            if behavior['confidence_level'] < 0.5:
                recommendations.append("Review agent confidence calibration")

        return recommendations[:5]  # Limit to top 5 recommendations

    def _handle_template_test_sync(self, data):
        """Handle evaluation template testing requests (sync version)."""
        try:
            # Validate required fields for template testing
            if 'scenario_id' not in data:
                return JsonResponse({'error': "Missing required field: scenario_id"}, status=400)

            # Get parameters
            params = data.get('params', {})
            context_variables = params.get('context_variables', {})

            # Extract execution mode parameters
            use_real_llm = params.get('use_real_llm', False)
            use_real_tools = params.get('use_real_tools', False)
            use_real_db = params.get('use_real_db', False)

            # Get the scenario and template (now sync)
            from apps.main.models import EvaluationCriteriaTemplate
            scenario = get_object_or_404(BenchmarkScenario, id=data['scenario_id'])

            # Handle both saved templates and template data
            if 'evaluation_template_id' in data:
                template = get_object_or_404(EvaluationCriteriaTemplate, id=data['evaluation_template_id'])
                template_data = {
                    'id': template.id,
                    'name': template.name,
                    'description': template.description,
                    'criteria': template.criteria,
                    'contextual_criteria': template.contextual_criteria,
                    'context_variables': context_variables
                }
            elif 'evaluation_template_data' in data:
                template_data = data['evaluation_template_data']
                template_data['context_variables'] = context_variables
            else:
                raise ValueError("Either evaluation_template_id or evaluation_template_data must be provided")

            # Start the Celery task for template testing
            from apps.main.tasks.benchmark_tasks import run_template_test

            task = run_template_test.delay(
                scenario_id=str(scenario.id),
                template_data=template_data,
                params=params
            )

            logger.info(f"Started template test task {task.id} for scenario {scenario.id}")

            return JsonResponse({
                'success': True,
                'task_id': task.id,
                'message': f'Template test started for scenario "{scenario.name}". Task ID: {task.id}'
            })

        except Exception as e:
            logger.error("Error in template test", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    async def _handle_template_test(self, data):
        """Handle evaluation template testing requests."""
        try:
            # Validate required fields for template testing
            if 'scenario_id' not in data:
                return JsonResponse({'error': "Missing required field: scenario_id"}, status=400)

            # Get parameters
            params = data.get('params', {})
            runs = min(params.get('runs', 1), 5)  # Limit to 5 runs for template testing
            semantic_evaluation = params.get('semantic_evaluation', True)
            context_variables = params.get('context_variables', {})
            multi_range_evaluation = params.get('multi_range_contextual_evaluation', False)
            selected_combinations = params.get('selected_combinations', [])
            selected_combination_indices = params.get('selected_combination_indices', [])

            # Get the scenario and template
            @database_sync_to_async
            def _get_scenario_and_template():
                from apps.main.models import EvaluationCriteriaTemplate
                scenario = get_object_or_404(BenchmarkScenario, id=data['scenario_id'])

                # Handle both saved templates and template data
                if 'evaluation_template_id' in data:
                    template = get_object_or_404(EvaluationCriteriaTemplate, id=data['evaluation_template_id'])
                    template_data = {
                        'id': template.id,
                        'name': template.name,
                        'description': template.description,
                        'criteria': template.criteria,
                        'contextual_criteria': template.contextual_criteria,
                        'context_variables': context_variables
                    }
                elif 'evaluation_template_data' in data:
                    template_data = data['evaluation_template_data']
                    template_data['context_variables'] = context_variables
                    template = None  # No saved template
                else:
                    raise ValueError("Either evaluation_template_id or evaluation_template_data must be provided")

                return scenario, template, template_data

            scenario, template, template_data = await _get_scenario_and_template()

            # Start the Celery task for template testing instead of running synchronously
            from apps.main.tasks.benchmark_tasks import run_template_test

            task = run_template_test.delay(
                scenario_id=str(scenario.id),
                template_data=template_data,
                params=params
            )

            logger.info(f"Started template test task {task.id} for scenario {scenario.id}")

            return JsonResponse({
                'success': True,
                'task_id': task.id,
                'message': f'Template test started for scenario "{scenario.name}". Task ID: {task.id}'
            })

        except Exception as e:
            logger.error("Error in template test", exc_info=True)

            # Emit error event via WebSocket for real-time feedback
            try:
                from apps.main.services.event_service import EventService
                import traceback
                import asyncio

                # Create task to emit error event
                asyncio.create_task(
                    EventService.emit_debug_info(
                        level='error',
                        message=f"Template test error: {str(e)}",
                        source='BenchmarkRunView',
                        details={
                            'error_type': type(e).__name__,
                            'error_message': str(e),
                            'traceback': traceback.format_exc(),
                            'endpoint': 'POST /admin/benchmarks/api/run/ (template test)'
                        }
                    )
                )
            except Exception as emit_error:
                logger.warning(f"Failed to emit error event: {emit_error}")

            return JsonResponse({'error': str(e)}, status=500)


class BenchmarkRunStopView(View):
    """Stop a running benchmark task."""

    async def post(self, request, task_id):
        """Stop a benchmark task by task ID."""
        try:
            # Import Celery app
            from celery import current_app as celery_app

            # Revoke the task
            celery_app.control.revoke(task_id, terminate=True)

            logger.info(f"Stopped benchmark task: {task_id}")

            return JsonResponse({
                'success': True,
                'message': f'Task {task_id} has been stopped.'
            })

        except Exception as e:
            logger.error(f"Error stopping task {task_id}: {e}", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)


class BenchmarkTaskStatusView(View):
    """
    Check the status of a benchmark task with enhanced error reporting.

    This view provides detailed information about task execution including:
    - Task completion status
    - Detailed error information with classification
    - Progress metadata
    - Error summaries for failed tasks
    """

    async def get(self, request, task_id):
        """
        Get the status of a benchmark task by task ID with enhanced error reporting.

        Args:
            request: HTTP request object
            task_id: Celery task ID to check status for

        Returns:
            JsonResponse with detailed task status including error information
        """
        try:
            from celery.result import AsyncResult

            # Get the task result
            result = AsyncResult(task_id)

            # Check task status
            if result.ready():
                if result.failed():
                    # Enhanced error handling for failed tasks
                    error_info = self._extract_error_information(result)

                    return JsonResponse({
                        'status': 'failed',
                        'error': error_info.get('primary_error', str(result.result)),
                        'task_id': task_id,
                        'error_details': error_info,
                        'has_errors': True,
                        'has_critical_errors': error_info.get('has_critical_errors', True)
                    })
                else:
                    # Task completed successfully - check for non-critical errors
                    task_result = result.result
                    error_info = self._extract_success_errors(task_result)

                    # Also check the BenchmarkRun object for errors
                    benchmark_run_error_info = await self._extract_benchmark_run_errors(task_result)

                    # Merge error information from both sources
                    merged_error_info = self._merge_error_info(error_info, benchmark_run_error_info)

                    response_data = {
                        'status': 'completed',
                        'result': task_result,
                        'task_id': task_id,
                        'runs': task_result.get('runs', []) if isinstance(task_result, dict) else [],
                        'has_errors': merged_error_info.get('has_errors', False),
                        'has_critical_errors': merged_error_info.get('has_critical_errors', False)
                    }

                    # Add error information if present
                    if merged_error_info.get('has_errors'):
                        response_data['error_details'] = merged_error_info
                        response_data['error_summary'] = merged_error_info.get('error_summary', {})

                    return JsonResponse(response_data)
            else:
                # Task is still running or pending
                status = result.status.lower() if result.status else 'pending'

                # Get task metadata if available
                meta = result.info if hasattr(result, 'info') and result.info else {}

                # Extract error information from metadata if present
                meta_errors = self._extract_meta_errors(meta)

                response_data = {
                    'status': status,
                    'task_id': task_id,
                    'meta': meta,
                    'has_errors': meta_errors.get('has_errors', False)
                }

                if meta_errors.get('has_errors'):
                    response_data['error_details'] = meta_errors

                return JsonResponse(response_data)

        except Exception as e:
            logger.error(f"Error checking task status {task_id}: {e}", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def _extract_error_information(self, result):
        """
        Extract detailed error information from a failed Celery task result.

        Args:
            result: AsyncResult object for a failed task

        Returns:
            dict: Detailed error information
        """
        error_info = {
            'primary_error': str(result.result),
            'has_errors': True,
            'has_critical_errors': True,
            'error_summary': {
                'total_errors': 1,
                'critical_errors': 1,
                'warnings': 0,
                'info_messages': 0
            },
            'errors': []
        }

        try:
            # Check if the result has detailed error information
            if hasattr(result, 'info') and isinstance(result.info, dict):
                meta = result.info

                # Extract errors from meta if available
                if 'errors' in meta and isinstance(meta['errors'], list):
                    error_info['errors'] = meta['errors']
                    error_info['error_summary'] = self._calculate_error_summary(meta['errors'])
                    error_info['has_critical_errors'] = error_info['error_summary']['critical_errors'] > 0

                # Add primary error information
                if 'exc_type' in meta and 'exc_message' in meta:
                    primary_error = {
                        'type': 'critical',
                        'level': 'error',
                        'message': meta['exc_message'],
                        'source': 'celery_task',
                        'details': {
                            'error_type': meta['exc_type'],
                            'task_status': meta.get('status', 'Task failed')
                        }
                    }

                    # Add to errors list if not already present
                    if not any(e.get('message') == primary_error['message'] for e in error_info['errors']):
                        error_info['errors'].append(primary_error)
                        error_info['error_summary'] = self._calculate_error_summary(error_info['errors'])

        except Exception as e:
            logger.warning(f"Failed to extract detailed error information: {e}")

        return error_info

    def _extract_success_errors(self, task_result):
        """
        Extract error information from a successful task that may have non-critical errors.

        Args:
            task_result: The result dictionary from a successful task

        Returns:
            dict: Error information if present
        """
        if not isinstance(task_result, dict):
            return {'has_errors': False, 'has_critical_errors': False}

        # Check for errors in the task result
        errors = task_result.get('errors', [])
        if not errors:
            return {'has_errors': False, 'has_critical_errors': False}

        error_summary = self._calculate_error_summary(errors)

        return {
            'has_errors': len(errors) > 0,
            'has_critical_errors': error_summary['critical_errors'] > 0,
            'errors': errors,
            'error_summary': error_summary
        }

    def _extract_meta_errors(self, meta):
        """
        Extract error information from task metadata (for running tasks).

        Args:
            meta: Task metadata dictionary

        Returns:
            dict: Error information if present
        """
        if not isinstance(meta, dict):
            return {'has_errors': False}

        errors = meta.get('errors', [])
        if not errors:
            return {'has_errors': False}

        error_summary = self._calculate_error_summary(errors)

        return {
            'has_errors': len(errors) > 0,
            'has_critical_errors': error_summary['critical_errors'] > 0,
            'errors': errors,
            'error_summary': error_summary
        }

    def _calculate_error_summary(self, errors):
        """
        Calculate error summary statistics from a list of errors.

        Args:
            errors: List of error dictionaries

        Returns:
            dict: Error summary with counts by type
        """
        if not isinstance(errors, list):
            return {
                'total_errors': 0,
                'critical_errors': 0,
                'warnings': 0,
                'info_messages': 0
            }

        critical_count = 0
        warning_count = 0
        info_count = 0

        for error in errors:
            if not isinstance(error, dict):
                continue

            error_type = error.get('type', '').lower()
            error_level = error.get('level', '').lower()

            if error_type == 'critical' or error_level == 'error':
                critical_count += 1
            elif error_type == 'warning' or error_level == 'warning':
                warning_count += 1
            else:
                info_count += 1

        return {
            'total_errors': len(errors),
            'critical_errors': critical_count,
            'warnings': warning_count,
            'info_messages': info_count
        }

    async def _extract_benchmark_run_errors(self, task_result):
        """
        Extract error information from the BenchmarkRun object.

        Args:
            task_result: The result dictionary from a successful task

        Returns:
            dict: Error information from BenchmarkRun if present
        """
        if not isinstance(task_result, dict):
            return {'has_errors': False, 'has_critical_errors': False}

        # Get the benchmark run ID from the task result
        benchmark_run_id = task_result.get('benchmark_run_id')
        if not benchmark_run_id:
            return {'has_errors': False, 'has_critical_errors': False}

        try:
            # Get the BenchmarkRun object (use sync_to_async for async context)
            from asgiref.sync import sync_to_async
            benchmark_run = await sync_to_async(BenchmarkRun.objects.get)(id=benchmark_run_id)

            # Check if it has errors using the model methods
            if not benchmark_run.has_errors():
                return {'has_errors': False, 'has_critical_errors': False}

            # Extract errors from raw_results
            raw_results = benchmark_run.raw_results or {}
            errors = raw_results.get('errors', [])
            error_summary = raw_results.get('error_summary', {})

            # If no error_summary, calculate it
            if not error_summary and errors:
                error_summary = self._calculate_error_summary(errors)

            return {
                'has_errors': len(errors) > 0,
                'has_critical_errors': benchmark_run.has_critical_errors(),
                'errors': errors,
                'error_summary': error_summary,
                'source': 'benchmark_run'
            }

        except BenchmarkRun.DoesNotExist:
            logger.warning(f"BenchmarkRun with ID {benchmark_run_id} not found")
            return {'has_errors': False, 'has_critical_errors': False}
        except Exception as e:
            logger.error(f"Error extracting BenchmarkRun errors: {e}")
            return {'has_errors': False, 'has_critical_errors': False}

    def _merge_error_info(self, task_error_info, benchmark_run_error_info):
        """
        Merge error information from task result and BenchmarkRun object.

        Args:
            task_error_info: Error info from task result
            benchmark_run_error_info: Error info from BenchmarkRun object

        Returns:
            dict: Merged error information
        """
        # If neither has errors, return no errors
        if not task_error_info.get('has_errors') and not benchmark_run_error_info.get('has_errors'):
            return {'has_errors': False, 'has_critical_errors': False}

        # Start with the more comprehensive source (usually BenchmarkRun)
        if benchmark_run_error_info.get('has_errors'):
            merged = benchmark_run_error_info.copy()

            # Add task errors if they exist and are different
            task_errors = task_error_info.get('errors', [])
            benchmark_errors = merged.get('errors', [])

            # Merge errors, avoiding duplicates
            all_errors = benchmark_errors.copy()
            for task_error in task_errors:
                # Check if this error is already in benchmark errors
                if not any(be.get('message') == task_error.get('message') for be in benchmark_errors):
                    all_errors.append(task_error)

            # Update merged info
            merged['errors'] = all_errors
            merged['error_summary'] = self._calculate_error_summary(all_errors)
            merged['has_errors'] = len(all_errors) > 0
            merged['has_critical_errors'] = merged['error_summary']['critical_errors'] > 0

        else:
            # Use task error info as fallback
            merged = task_error_info.copy()

        return merged


###############################################################################
# User Profile Management Views
###############################################################################

@staff_member_required
def user_profile_management(request):
    """
    Admin view for comprehensive user profile management.

    Displays all user profiles with complete information including demographics,
    environments, skills, resources, and preferences with search/filter capabilities.
    """
    context = {
        'title': 'User Profile Management',
        'has_permission': True,
        'site_header': 'Game of Life Admin',
        'opts': None,
        'app_label': 'admin_tools',
    }

    # Import user models
    from apps.user.models import (
        UserProfile, Demographics, UserEnvironment, Skill,
        UserResource, Preference
    )
    from apps.main.models import HistoryEvent

    # Get search and filter parameters
    search_query = request.GET.get('search', '').strip()
    profile_type = request.GET.get('profile_type', '')  # 'real' or 'test'
    has_demographics = request.GET.get('has_demographics', '')
    has_environment = request.GET.get('has_environment', '')
    completeness = request.GET.get('completeness', '')

    # Base queryset with related data
    profiles = UserProfile.objects.select_related(
        'user', 'current_environment', 'demographics'
    ).prefetch_related(
        'environments', 'skills', 'preferences', 'historyevent_set'
    )

    # Apply filters
    if search_query:
        profiles = profiles.filter(
            Q(profile_name__icontains=search_query) |
            Q(user__username__icontains=search_query) |
            Q(user__email__icontains=search_query) |
            Q(demographics__full_name__icontains=search_query)
        )

    if profile_type == 'real':
        profiles = profiles.filter(is_real=True)
    elif profile_type == 'test':
        profiles = profiles.filter(is_real=False)

    if has_demographics == 'yes':
        profiles = profiles.filter(demographics__isnull=False)
    elif has_demographics == 'no':
        profiles = profiles.filter(demographics__isnull=True)

    if has_environment == 'yes':
        profiles = profiles.filter(current_environment__isnull=False)
    elif has_environment == 'no':
        profiles = profiles.filter(current_environment__isnull=True)

    # Order profiles
    profiles = profiles.order_by('-is_real', 'profile_name')

    # Get statistics
    total_profiles = UserProfile.objects.count()
    real_profiles = UserProfile.objects.filter(is_real=True).count()
    test_profiles = UserProfile.objects.filter(is_real=False).count()
    profiles_with_demographics = UserProfile.objects.filter(demographics__isnull=False).count()
    profiles_with_environment = UserProfile.objects.filter(current_environment__isnull=False).count()

    context.update({
        'profiles': profiles,
        'search_query': search_query,
        'profile_type': profile_type,
        'has_demographics': has_demographics,
        'has_environment': has_environment,
        'completeness': completeness,
        'total_profiles': total_profiles,
        'real_profiles': real_profiles,
        'test_profiles': test_profiles,
        'profiles_with_demographics': profiles_with_demographics,
        'profiles_with_environment': profiles_with_environment,
    })

    return render(request, 'admin_tools/user_profile_management.html', context)


class UserProfileAPIView(View):
    """API View for user profile operations."""

    def dispatch(self, request, *args, **kwargs):
        # Check permissions
        if not (request.user.is_authenticated and request.user.is_active and request.user.is_staff):
            return HttpResponseForbidden("You do not have permission to access this resource.")
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, profile_id=None):
        """Get user profile details."""
        try:
            from apps.user.models import (
                UserProfile, Demographics, UserEnvironment, Skill,
                UserResource, Preference
            )
            from apps.main.models import HistoryEvent

            if profile_id:
                # Get specific profile with all related data
                profile = get_object_or_404(
                    UserProfile.objects.select_related(
                        'user', 'current_environment', 'demographics'
                    ).prefetch_related(
                        'environments__generic_environment',
                        'environments__physical_properties',
                        'environments__social_context',
                        'environments__activity_support',
                        'environments__psychological_qualities',
                        'skills__generic_skill',
                        'environments__user_resources__generic_resource',
                        'preferences',
                        'historyevent_set'
                    ),
                    id=profile_id
                )

                # Build comprehensive profile data
                profile_data = {
                    'id': profile.id,
                    'profile_name': profile.profile_name,
                    'is_real': profile.is_real,
                    'profile_type': profile.profile_type,
                    'user': {
                        'id': profile.user.id,
                        'username': profile.user.username,
                        'email': profile.user.email,
                        'first_name': profile.user.first_name,
                        'last_name': profile.user.last_name,
                        'is_active': profile.user.is_active,
                        'date_joined': profile.user.date_joined.isoformat(),
                        'last_login': profile.user.last_login.isoformat() if profile.user.last_login else None,
                    },
                    'demographics': None,
                    'current_environment': None,
                    'environments': [],
                    'skills': [],
                    'resources': [],
                    'preferences': [],
                    'history_events': [],
                    'statistics': {}
                }

                # Add demographics if available
                if hasattr(profile, 'demographics') and profile.demographics:
                    demo = profile.demographics
                    profile_data['demographics'] = {
                        'full_name': demo.full_name,
                        'age': demo.age,
                        'gender': demo.gender,
                        'location': demo.location,
                        'language': demo.language,
                        'occupation': demo.occupation,
                    }

                # Add current environment if available
                if profile.current_environment:
                    env = profile.current_environment
                    profile_data['current_environment'] = {
                        'id': env.id,
                        'environment_name': env.environment_name,
                        'environment_description': env.environment_description,
                        'is_current': env.is_current,
                        'generic_environment': {
                            'name': env.generic_environment.name,
                            'description': env.generic_environment.description,
                        } if env.generic_environment else None,
                        'environment_details': env.environment_details,
                    }

                # Add all environments
                for env in profile.environments.all():
                    env_data = {
                        'id': env.id,
                        'environment_name': env.environment_name,
                        'environment_description': env.environment_description,
                        'is_current': env.is_current,
                        'generic_environment': {
                            'name': env.generic_environment.name,
                            'description': env.generic_environment.description,
                        } if env.generic_environment else None,
                        'environment_details': env.environment_details,
                        'physical_properties': None,
                        'social_context': None,
                        'activity_support': None,
                        'psychological_qualities': None,
                    }

                    # Add related environment data if available
                    if hasattr(env, 'physical_properties') and env.physical_properties:
                        props = env.physical_properties
                        env_data['physical_properties'] = {
                            'rurality': props.rurality,
                            'noise_level': props.noise_level,
                            'light_quality': props.light_quality,
                            'temperature_range': props.temperature_range,
                            'accessibility': props.accessibility,
                            'air_quality': props.air_quality,
                            'has_natural_elements': props.has_natural_elements,
                            'surface_type': props.surface_type,
                            'water_proximity': props.water_proximity,
                            'space_size': props.space_size,
                        }

                    if hasattr(env, 'social_context') and env.social_context:
                        social = env.social_context
                        env_data['social_context'] = {
                            'privacy_level': social.privacy_level,
                            'typical_occupancy': social.typical_occupancy,
                            'social_interaction_level': social.social_interaction_level,
                            'formality_level': social.formality_level,
                            'safety_level': social.safety_level,
                            'supervision_level': social.supervision_level,
                            'cultural_diversity': social.cultural_diversity,
                        }

                    if hasattr(env, 'activity_support') and env.activity_support:
                        support = env.activity_support
                        env_data['activity_support'] = {
                            'digital_connectivity': support.digital_connectivity,
                            'resource_availability': support.resource_availability,
                            'time_availability': support.time_availability,
                            'domain_specific_support': support.domain_specific_support,
                        }

                    if hasattr(env, 'psychological_qualities') and env.psychological_qualities:
                        psych = env.psychological_qualities
                        env_data['psychological_qualities'] = {
                            'restorative_quality': psych.restorative_quality,
                            'stimulation_level': psych.stimulation_level,
                            'aesthetic_appeal': psych.aesthetic_appeal,
                            'novelty_level': psych.novelty_level,
                            'comfort_level': psych.comfort_level,
                            'personal_significance': psych.personal_significance,
                            'emotional_associations': psych.emotional_associations,
                        }

                    profile_data['environments'].append(env_data)

                # Add skills
                for skill in profile.skills.all():
                    skill_data = {
                        'id': skill.id,
                        'level': skill.level,
                        'confidence': skill.confidence,
                        'interest': skill.interest,
                        'growth_goal': skill.growth_goal,
                        'stagnation_point': skill.stagnation_point,
                        'generic_skill': {
                            'name': skill.generic_skill.name,
                            'description': skill.generic_skill.description,
                            'category': skill.generic_skill.category,
                        } if skill.generic_skill else None,
                    }
                    profile_data['skills'].append(skill_data)

                # Add resources (accessed through environments)
                all_resources = []
                for env in profile.environments.all():
                    all_resources.extend(env.user_resources.all())

                for resource in all_resources:
                    resource_data = {
                        'id': resource.id,
                        'specific_name': resource.specific_name,
                        'location_details': resource.location_details,
                        'ownership_details': resource.ownership_details,
                        'contact_info': resource.contact_info,
                        'notes': resource.notes,
                        'environment_name': resource.user_environment.environment_name,
                        'generic_resource': {
                            'name': resource.generic_resource.name,
                            'description': resource.generic_resource.description,
                            'resource_type': resource.generic_resource.resource_type,
                        } if resource.generic_resource else None,
                    }
                    profile_data['resources'].append(resource_data)

                # Add preferences
                for pref in profile.preferences.all():
                    pref_data = {
                        'id': pref.id,
                        'preference_type': pref.pref_name,
                        'preference_value': pref.pref_description,
                        'strength': pref.pref_strength,
                        'context': getattr(pref, 'environment', None),
                        'notes': getattr(pref, 'user_awareness', None),
                    }
                    profile_data['preferences'].append(pref_data)

                # Add recent history events (limit to last 20)
                for event in profile.historyevent_set.all().order_by('-timestamp')[:20]:
                    event_data = {
                        'id': event.id,
                        'event_type': event.event_type,
                        'details': event.details,
                        'timestamp': event.timestamp.isoformat(),
                    }
                    profile_data['history_events'].append(event_data)

                # Add statistics
                total_resources = sum(env.user_resources.count() for env in profile.environments.all())
                profile_data['statistics'] = {
                    'total_environments': profile.environments.count(),
                    'total_skills': profile.skills.count(),
                    'total_resources': total_resources,
                    'total_preferences': profile.preferences.count(),
                    'total_history_events': profile.historyevent_set.count(),
                    'profile_completeness': self._calculate_profile_completeness(profile),
                }

                return JsonResponse(profile_data)

            else:
                # List profiles with basic info
                profiles = UserProfile.objects.select_related(
                    'user', 'demographics'
                ).annotate(
                    environments_count=Count('environments'),
                    skills_count=Count('skills'),
                    preferences_count=Count('preferences'),
                ).order_by('-is_real', 'profile_name')

                profiles_data = []
                for profile in profiles:
                    # Calculate resources count manually since it's through environments
                    resources_count = sum(env.user_resources.count() for env in profile.environments.all())

                    profile_data = {
                        'id': profile.id,
                        'profile_name': profile.profile_name,
                        'is_real': profile.is_real,
                        'profile_type': profile.profile_type,
                        'user_username': profile.user.username,
                        'user_email': profile.user.email,
                        'has_demographics': hasattr(profile, 'demographics') and profile.demographics is not None,
                        'demographics_name': profile.demographics.full_name if hasattr(profile, 'demographics') and profile.demographics else None,
                        'current_environment_name': profile.current_environment.environment_name if profile.current_environment else None,
                        'environments_count': profile.environments_count,
                        'skills_count': profile.skills_count,
                        'resources_count': resources_count,
                        'preferences_count': profile.preferences_count,
                        'profile_completeness': self._calculate_profile_completeness(profile),
                    }
                    profiles_data.append(profile_data)

                return JsonResponse({'profiles': profiles_data})

        except Exception as e:
            logger.error(f"Error in UserProfileAPIView.get: {e}", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)

    def _calculate_profile_completeness(self, profile):
        """Calculate profile completeness percentage."""
        try:
            total_fields = 10  # Adjust based on what we consider complete
            completed_fields = 0

            # Check basic profile info
            if profile.profile_name:
                completed_fields += 1

            # Check demographics
            if hasattr(profile, 'demographics') and profile.demographics:
                demo = profile.demographics
                if demo.full_name:
                    completed_fields += 1
                if demo.age:
                    completed_fields += 1
                if demo.gender:
                    completed_fields += 1
                if demo.location:
                    completed_fields += 1
                if demo.occupation:
                    completed_fields += 1

            # Check environment
            if profile.current_environment:
                completed_fields += 1

            # Check skills
            if profile.skills.exists():
                completed_fields += 1

            # Check resources (through environments)
            has_resources = any(env.user_resources.exists() for env in profile.environments.all())
            if has_resources:
                completed_fields += 1

            # Check preferences
            if profile.preferences.exists():
                completed_fields += 1

            return round((completed_fields / total_fields) * 100, 1)

        except Exception as e:
            logger.warning(f"Error calculating profile completeness for profile {profile.id}: {e}")
            return 0.0

    def post(self, request, profile_id=None):
        """Update user profile data."""
        try:
            if not profile_id:
                return JsonResponse({'error': 'Profile ID is required for updates'}, status=400)

            from apps.user.models import UserProfile, Demographics

            # Get the profile
            profile = get_object_or_404(UserProfile, id=profile_id)

            # Parse request data
            data = json.loads(request.body)

            # Update basic profile information
            if 'profile_name' in data:
                profile.profile_name = data['profile_name']
                profile.save()

            # Update demographics
            if 'demographics' in data:
                demo_data = data['demographics']

                # Get or create demographics
                demographics, created = Demographics.objects.get_or_create(
                    user_profile=profile,
                    defaults={
                        'full_name': demo_data.get('full_name', ''),
                        'age': demo_data.get('age', 0),
                        'gender': demo_data.get('gender', ''),
                        'location': demo_data.get('location', ''),
                        'language': demo_data.get('language', ''),
                        'occupation': demo_data.get('occupation', ''),
                    }
                )

                if not created:
                    # Update existing demographics
                    for field in ['full_name', 'age', 'gender', 'location', 'language', 'occupation']:
                        if field in demo_data:
                            setattr(demographics, field, demo_data[field])
                    demographics.save()

            return JsonResponse({
                'success': True,
                'message': 'Profile updated successfully',
                'profile_id': profile.id
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            logger.error(f"Error updating profile {profile_id}: {e}", exc_info=True)
            return JsonResponse({'error': str(e)}, status=500)


@require_http_methods(["POST"])
def batch_delete_profiles(request):
    """Batch delete user profiles"""
    try:
        import json
        data = json.loads(request.body)
        profile_ids = data.get('profile_ids', [])

        if not profile_ids:
            return JsonResponse({'error': 'No profile IDs provided'}, status=400)

        # Import user models
        from apps.user.models import UserProfile

        # Get profiles to delete
        profiles_to_delete = UserProfile.objects.filter(id__in=profile_ids)
        deleted_count = profiles_to_delete.count()

        # Delete the profiles
        profiles_to_delete.delete()

        return JsonResponse({
            'success': True,
            'deleted_count': deleted_count,
            'message': f'Successfully deleted {deleted_count} profiles'
        })

    except Exception as e:
        logger.error(f"Error in batch delete: {e}", exc_info=True)
        return JsonResponse({'error': str(e)}, status=500)


@require_http_methods(["POST"])
def batch_export_profiles(request):
    """Batch export user profiles to CSV"""
    try:
        import json
        import csv
        from django.http import HttpResponse
        from io import StringIO

        data = json.loads(request.body)
        profile_ids = data.get('profile_ids', [])

        if not profile_ids:
            return JsonResponse({'error': 'No profile IDs provided'}, status=400)

        # Import user models
        from apps.user.models import UserProfile

        # Get profiles to export
        profiles = UserProfile.objects.filter(id__in=profile_ids).select_related(
            'user', 'demographics', 'current_environment'
        ).prefetch_related(
            'environments', 'skills', 'preferences'
        )

        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="user_profiles_export.csv"'

        writer = csv.writer(response)

        # Write header
        writer.writerow([
            'Profile ID', 'Profile Name', 'Profile Type', 'Is Real',
            'Username', 'Email', 'Date Joined',
            'Demographics Name', 'Age', 'Gender', 'Location',
            'Current Environment', 'Environment Description',
            'Total Environments', 'Total Skills', 'Total Preferences',
            'Profile Completeness'
        ])

        # Helper function to calculate completeness
        def calculate_completeness(profile):
            try:
                total_fields = 10
                completed_fields = 0

                if profile.profile_name:
                    completed_fields += 1
                if hasattr(profile, 'demographics') and profile.demographics:
                    demo = profile.demographics
                    if demo.full_name:
                        completed_fields += 1
                    if demo.age:
                        completed_fields += 1
                    if demo.gender:
                        completed_fields += 1
                    if demo.location:
                        completed_fields += 1
                    if demo.occupation:
                        completed_fields += 1
                if profile.current_environment:
                    completed_fields += 1
                if profile.skills.exists():
                    completed_fields += 1
                has_resources = any(env.user_resources.exists() for env in profile.environments.all())
                if has_resources:
                    completed_fields += 1
                if profile.preferences.exists():
                    completed_fields += 1

                return round((completed_fields / total_fields) * 100, 1)
            except:
                return 0.0

        # Write data
        for profile in profiles:
            writer.writerow([
                profile.id,
                profile.profile_name or '',
                profile.profile_type or '',
                'Yes' if profile.is_real else 'No',
                profile.user.username,
                profile.user.email or '',
                profile.user.date_joined.strftime('%Y-%m-%d %H:%M:%S'),
                profile.demographics.full_name if profile.demographics else '',
                profile.demographics.age if profile.demographics else '',
                profile.demographics.gender if profile.demographics else '',
                profile.demographics.location if profile.demographics else '',
                profile.current_environment.environment_name if profile.current_environment else '',
                profile.current_environment.environment_description if profile.current_environment else '',
                profile.environments.count(),
                profile.skills.count(),
                profile.preferences.count(),
                calculate_completeness(profile)
            ])

        return response

    except Exception as e:
        logger.error(f"Error in batch export: {e}", exc_info=True)
        return JsonResponse({'error': str(e)}, status=500)
