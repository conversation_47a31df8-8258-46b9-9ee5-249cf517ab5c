"""
WebSocket Connection Tracking Service

This service provides utilities for tracking and monitoring WebSocket connections
using Redis as the backend storage. It integrates with the ConnectionMonitorConsumer
to provide real-time connection monitoring capabilities.
"""

import json
import logging
import redis
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from django.conf import settings

logger = logging.getLogger(__name__)


class ConnectionTracker:
    """
    Service for tracking WebSocket connections and their metadata.
    
    Uses Redis to store connection information with TTL for automatic cleanup.
    Provides methods for connection lifecycle management and monitoring.
    """
    
    def __init__(self):
        self.redis_client = None
        self._init_redis()
    
    def _init_redis(self):
        """Initialize Redis connection using Django Channels configuration."""
        try:
            channel_layers = getattr(settings, 'CHANNEL_LAYERS', {})
            config = channel_layers.get('default', {}).get('CONFIG', {})
            hosts = config.get('hosts', [])
            
            if hosts:
                host_info = hosts[0]
                if isinstance(host_info, tuple):
                    host, port = host_info
                else:
                    host, port = host_info, 6379
                
                self.redis_client = redis.Redis(
                    host=host, 
                    port=port, 
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )
                self.redis_client.ping()
                logger.info(f"ConnectionTracker: Redis connected at {host}:{port}")
            else:
                logger.warning("ConnectionTracker: No Redis hosts configured")
                
        except Exception as e:
            logger.error(f"ConnectionTracker: Failed to connect to Redis: {e}")
            self.redis_client = None
    
    def track_connection(self, session_id: str, user_id: str, metadata: Optional[Dict] = None) -> bool:
        """
        Track a new WebSocket connection.
        
        Args:
            session_id: Unique session identifier
            user_id: User profile ID
            metadata: Additional connection metadata
            
        Returns:
            bool: True if successfully tracked, False otherwise
        """
        if not self.redis_client:
            return False
            
        try:
            connection_data = {
                'user_id': str(user_id),
                'connected_at': datetime.now(timezone.utc).isoformat(),
                'last_activity': datetime.now(timezone.utc).isoformat(),
                'message_count': 0,
                'current_workflow': None,
                'status': 'connected'
            }
            
            if metadata:
                connection_data.update(metadata)
            
            key = f"ws_session:{session_id}"
            self.redis_client.hset(key, mapping=connection_data)
            self.redis_client.expire(key, 86400)  # 24 hour TTL
            
            # Update connection count
            self._update_connection_stats('connect')
            
            logger.info(f"Tracked connection: {session_id} for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error tracking connection {session_id}: {e}")
            return False
    
    def update_connection_activity(self, session_id: str, activity_data: Optional[Dict] = None) -> bool:
        """
        Update connection activity timestamp and optional data.
        
        Args:
            session_id: Session identifier
            activity_data: Additional activity data to update
            
        Returns:
            bool: True if successfully updated, False otherwise
        """
        if not self.redis_client:
            return False
            
        try:
            key = f"ws_session:{session_id}"
            
            # Check if session exists
            if not self.redis_client.exists(key):
                logger.warning(f"Session {session_id} not found for activity update")
                return False
            
            update_data = {
                'last_activity': datetime.now(timezone.utc).isoformat()
            }
            
            if activity_data:
                update_data.update(activity_data)
            
            self.redis_client.hset(key, mapping=update_data)
            
            # Increment message count if this is a message activity
            if activity_data and activity_data.get('type') == 'message':
                self.redis_client.hincrby(key, 'message_count', 1)
                self._update_message_stats()
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating connection activity {session_id}: {e}")
            return False
    
    def untrack_connection(self, session_id: str) -> bool:
        """
        Remove connection tracking when WebSocket disconnects.
        
        Args:
            session_id: Session identifier
            
        Returns:
            bool: True if successfully removed, False otherwise
        """
        if not self.redis_client:
            return False
            
        try:
            key = f"ws_session:{session_id}"
            
            # Get connection data before deletion for logging
            connection_data = self.redis_client.hgetall(key)
            
            if connection_data:
                user_id = connection_data.get('user_id', 'unknown')
                connected_at = connection_data.get('connected_at')
                
                # Calculate session duration
                if connected_at:
                    try:
                        start_time = datetime.fromisoformat(connected_at)
                        duration = datetime.now(timezone.utc) - start_time
                        logger.info(f"Session {session_id} for user {user_id} lasted {duration}")
                    except:
                        pass
                
                # Remove the session
                self.redis_client.delete(key)
                
                # Update connection count
                self._update_connection_stats('disconnect')
                
                logger.info(f"Untracked connection: {session_id}")
                return True
            else:
                logger.warning(f"Session {session_id} not found for removal")
                return False
                
        except Exception as e:
            logger.error(f"Error untracking connection {session_id}: {e}")
            return False
    
    def get_active_connections(self) -> List[Dict[str, Any]]:
        """
        Get list of all active connections.
        
        Returns:
            List of connection dictionaries
        """
        connections = []
        
        if not self.redis_client:
            return connections
            
        try:
            session_keys = self.redis_client.keys("ws_session:*")
            
            for key in session_keys:
                session_data = self.redis_client.hgetall(key)
                
                if session_data:
                    session_id = key.split(':')[1]
                    
                    # Calculate connection duration
                    connected_at = session_data.get('connected_at')
                    if connected_at:
                        try:
                            connected_time = datetime.fromisoformat(connected_at)
                            duration = datetime.now(timezone.utc) - connected_time
                            duration_str = str(duration).split('.')[0]  # Remove microseconds
                        except:
                            duration_str = "Unknown"
                    else:
                        duration_str = "Unknown"
                    
                    connections.append({
                        'session_id': session_id,
                        'user_id': session_data.get('user_id', 'Unknown'),
                        'connected_at': connected_at,
                        'duration': duration_str,
                        'last_activity': session_data.get('last_activity'),
                        'message_count': int(session_data.get('message_count', 0)),
                        'current_workflow': session_data.get('current_workflow'),
                        'status': session_data.get('status', 'connected')
                    })
                    
        except Exception as e:
            logger.error(f"Error getting active connections: {e}")
            
        return connections
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """
        Get connection statistics.
        
        Returns:
            Dictionary with connection statistics
        """
        stats = {
            'total_active': 0,
            'connections_today': 0,
            'disconnections_today': 0,
            'messages_today': 0,
            'average_duration': 0
        }
        
        if not self.redis_client:
            return stats
            
        try:
            # Count active connections
            active_keys = self.redis_client.keys("ws_session:*")
            stats['total_active'] = len(active_keys)
            
            # Get daily stats
            today_key = f"connection_stats:{datetime.now().strftime('%Y-%m-%d')}"
            daily_stats = self.redis_client.hgetall(today_key)
            
            if daily_stats:
                stats['connections_today'] = int(daily_stats.get('connections', 0))
                stats['disconnections_today'] = int(daily_stats.get('disconnections', 0))
                stats['messages_today'] = int(daily_stats.get('messages', 0))
                
        except Exception as e:
            logger.error(f"Error getting connection stats: {e}")
            
        return stats
    
    def _update_connection_stats(self, action: str):
        """Update daily connection statistics."""
        if not self.redis_client:
            return
            
        try:
            today_key = f"connection_stats:{datetime.now().strftime('%Y-%m-%d')}"
            
            if action == 'connect':
                self.redis_client.hincrby(today_key, 'connections', 1)
            elif action == 'disconnect':
                self.redis_client.hincrby(today_key, 'disconnections', 1)
            
            # Set expiry for the daily stats (keep for 30 days)
            self.redis_client.expire(today_key, 30 * 24 * 3600)
            
        except Exception as e:
            logger.error(f"Error updating connection stats: {e}")
    
    def _update_message_stats(self):
        """Update message statistics."""
        if not self.redis_client:
            return
            
        try:
            # Update daily message count
            today_key = f"connection_stats:{datetime.now().strftime('%Y-%m-%d')}"
            self.redis_client.hincrby(today_key, 'messages', 1)
            
            # Update per-minute message count for rate calculation
            minute_key = f"message_rate:{datetime.now().strftime('%Y-%m-%d:%H:%M')}"
            self.redis_client.incr(minute_key)
            self.redis_client.expire(minute_key, 3600)  # Keep for 1 hour
            
        except Exception as e:
            logger.error(f"Error updating message stats: {e}")


# Global instance
connection_tracker = ConnectionTracker()
