{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrastyle %}
{{ block.super }}
<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    .dashboard {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    .header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 8px;
    }
    
    .header p {
        color: #666;
        font-size: 1.1rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin-bottom: 24px;
    }
    
    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }
    
    .stat-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
    }
    
    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 1.5rem;
    }
    
    .stat-icon.connections {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
    }
    
    .stat-icon.messages {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
    }
    
    .stat-icon.workflows {
        background: linear-gradient(135deg, #fa709a, #fee140);
    }
    
    .stat-icon.errors {
        background: linear-gradient(135deg, #ff9a9e, #fecfef);
    }
    
    .stat-title {
        font-size: 0.9rem;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 600;
    }
    
    .stat-value {
        font-size: 2.2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 8px;
    }
    
    .stat-change {
        font-size: 0.85rem;
        padding: 4px 8px;
        border-radius: 8px;
        font-weight: 500;
    }
    
    .stat-change.positive {
        background: rgba(67, 233, 123, 0.1);
        color: #1a9852;
    }
    
    .stat-change.negative {
        background: rgba(255, 154, 158, 0.1);
        color: #e63946;
    }
    
    .main-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 24px;
    }
    
    .connections-panel {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 2px solid #f0f0f0;
    }
    
    .panel-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: #333;
    }
    
    .filter-controls {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;
    }
    
    .filter-btn {
        padding: 8px 16px;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        background: white;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .filter-btn.active {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: transparent;
    }
    
    .connection-list {
        max-height: 500px;
        overflow-y: auto;
    }
    
    .connection-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border-radius: 12px;
        margin-bottom: 12px;
        background: #f8f9fa;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .connection-item:hover {
        background: #e9ecef;
        transform: translateX(4px);
    }
    
    .connection-status {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 16px;
    }
    
    .connection-status.connected {
        background: #28a745;
        box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
    }
    
    .connection-status.disconnected {
        background: #dc3545;
    }
    
    .connection-info {
        flex: 1;
    }
    
    .connection-user {
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
    }
    
    .connection-details {
        font-size: 0.85rem;
        color: #666;
    }
    
    .connection-metrics {
        text-align: right;
        font-size: 0.8rem;
        color: #666;
    }
    
    .sidebar {
        display: flex;
        flex-direction: column;
        gap: 24px;
    }
    
    .activity-panel, .system-panel {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    .activity-chart {
        height: 200px;
        background: linear-gradient(135deg, #f6f9fc, #eef4f7);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        font-style: italic;
    }
    
    .system-status {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }
    
    .system-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .system-label {
        font-weight: 500;
        color: #333;
    }
    
    .system-value {
        font-size: 0.9rem;
        padding: 4px 8px;
        border-radius: 6px;
        font-weight: 500;
    }
    
    .system-value.good {
        background: rgba(67, 233, 123, 0.1);
        color: #1a9852;
    }
    
    .system-value.warning {
        background: rgba(255, 193, 7, 0.1);
        color: #d39e00;
    }
    
    .system-value.error {
        background: rgba(255, 154, 158, 0.1);
        color: #e63946;
    }
    
    .action-buttons {
        display: flex;
        gap: 12px;
        margin-top: 20px;
    }
    
    .action-btn {
        flex: 1;
        padding: 12px 16px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .action-btn.primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }
    
    .action-btn.secondary {
        background: #e9ecef;
        color: #495057;
    }

    .action-btn.warning {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        font-weight: 600;
    }

    .action-btn.warning:hover {
        background: linear-gradient(135deg, #e0a800, #d39e00);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
    }

    .action-btn.danger {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        font-weight: 600;
    }

    .action-btn.danger:hover {
        background: linear-gradient(135deg, #c82333, #bd2130);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Enhanced Deep Debugging Styles */
    .debug-panel {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        margin-top: 24px;
    }

    .message-inspector {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        margin-top: 24px;
    }

    .message-flow {
        display: flex;
        flex-direction: column;
        gap: 8px;
        max-height: 500px;
        overflow-y: auto;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        background: #f8f9fa;
    }

    .message-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background: white;
        border-radius: 8px;
        border-left: 4px solid #667eea;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
    }

    .message-item:hover {
        background: #f0f4ff;
        transform: translateX(4px);
    }

    .message-item.debug_info {
        border-left-color: #ffc107;
    }

    .message-item.workflow_status {
        border-left-color: #28a745;
    }

    .message-item.chat_message {
        border-left-color: #007bff;
    }

    .message-item.wheel_data {
        border-left-color: #dc3545;
    }

    .message-item.error {
        border-left-color: #e74c3c;
        background: #fdf2f2;
    }

    .message-timestamp {
        font-size: 0.8rem;
        color: #666;
        margin-right: 12px;
        min-width: 80px;
        font-family: monospace;
    }

    .message-type {
        font-weight: 600;
        margin-right: 12px;
        min-width: 120px;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .message-content {
        flex: 1;
        font-size: 0.9rem;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .message-size {
        font-size: 0.8rem;
        color: #666;
        margin-left: 12px;
        min-width: 60px;
        text-align: right;
    }

    .expandable-content {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        margin-top: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        z-index: 10;
        display: none;
    }

    .expandable-content.expanded {
        display: block;
    }

    .json-viewer {
        background: #2d3748;
        color: #e2e8f0;
        padding: 16px;
        border-radius: 8px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.85rem;
        line-height: 1.5;
        overflow-x: auto;
        white-space: pre-wrap;
        word-break: break-all;
    }

    .filter-tabs {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 8px;
    }

    .filter-tab {
        padding: 8px 16px;
        border: none;
        background: transparent;
        color: #666;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.2s ease;
        font-weight: 500;
    }

    .filter-tab.active {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .filter-tab:hover:not(.active) {
        background: #f0f0f0;
    }

    /* Ultra-Powerful Session-Focused Dashboard Styles */
    .session-focus-panel {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 32px;
        box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
        margin-top: 24px;
        border: 2px solid transparent;
        background-clip: padding-box;
        position: relative;
    }

    .session-focus-panel::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 20px;
        padding: 2px;
        background: linear-gradient(135deg, #667eea, #764ba2, #f093fb, #f5576c);
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        z-index: -1;
    }

    .session-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 20px;
        border-bottom: 3px solid #f0f0f0;
    }

    .session-info {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .session-avatar {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
    }

    .session-details h2 {
        font-size: 1.8rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 8px;
    }

    .session-meta {
        display: flex;
        gap: 16px;
        font-size: 0.9rem;
        color: #666;
    }

    .session-controls {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .session-status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .session-status-badge.active {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
        color: white;
        box-shadow: 0 4px 12px rgba(67, 233, 123, 0.3);
    }

    .session-status-badge.inactive {
        background: linear-gradient(135deg, #ff9a9e, #fecfef);
        color: #8b5a5a;
    }

    .chronological-timeline {
        background: #f8f9fa;
        border-radius: 16px;
        padding: 24px;
        margin-top: 24px;
        position: relative;
        min-height: 600px;
        max-height: 800px;
        overflow-y: auto;
    }

    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 2px solid #e0e0e0;
        position: sticky;
        top: 0;
        background: #f8f9fa;
        z-index: 10;
    }

    .timeline-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #333;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .timeline-controls {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .timeline-filter {
        padding: 6px 12px;
        border: 1px solid #ddd;
        border-radius: 8px;
        background: white;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .timeline-filter.active {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-color: transparent;
    }

    .message-timeline {
        position: relative;
        padding-left: 40px;
    }

    .timeline-line {
        position: absolute;
        left: 20px;
        top: 0;
        bottom: 0;
        width: 3px;
        background: linear-gradient(180deg, #667eea, #764ba2, #f093fb);
        border-radius: 2px;
    }

    .timeline-message {
        position: relative;
        margin-bottom: 24px;
        animation: fadeInUp 0.3s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .timeline-dot {
        position: absolute;
        left: -28px;
        top: 12px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 5;
    }

    .timeline-dot.frontend {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
    }

    .timeline-dot.backend {
        background: linear-gradient(135deg, #fa709a, #fee140);
    }

    .timeline-dot.system {
        background: linear-gradient(135deg, #a8edea, #fed6e3);
    }

    .message-bubble {
        background: white;
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        border: 1px solid #e0e0e0;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
    }

    .message-bubble:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border-color: #667eea;
    }

    .message-bubble.frontend {
        border-left: 4px solid #4facfe;
        margin-right: 60px;
    }

    .message-bubble.backend {
        border-left: 4px solid #fa709a;
        margin-left: 60px;
    }

    .message-bubble.system {
        border-left: 4px solid #a8edea;
        margin: 0 30px;
        background: #f0f8ff;
    }

    .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
    }

    .message-direction {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .message-direction.frontend {
        color: #4facfe;
    }

    .message-direction.backend {
        color: #fa709a;
    }

    .message-direction.system {
        color: #666;
    }

    .direction-arrow {
        font-size: 1.2rem;
    }

    .message-timestamp-detailed {
        font-size: 0.8rem;
        color: #666;
        font-family: monospace;
        background: #f8f9fa;
        padding: 4px 8px;
        border-radius: 6px;
    }

    .message-type-badge {
        display: inline-block;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 8px;
    }

    .message-type-badge.chat_message {
        background: rgba(0, 123, 255, 0.1);
        color: #007bff;
    }

    .message-type-badge.debug_info {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .message-type-badge.workflow_status {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .message-type-badge.wheel_data {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .message-type-badge.system_message {
        background: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }

    .message-content-preview {
        font-size: 0.95rem;
        line-height: 1.5;
        color: #333;
        margin-bottom: 12px;
        max-height: 100px;
        overflow: hidden;
        position: relative;
    }

    .message-content-preview.expanded {
        max-height: none;
        overflow: visible;
    }

    .message-expand-fade {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 30px;
        background: linear-gradient(transparent, white);
        pointer-events: none;
    }

    .message-metadata {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.8rem;
        color: #666;
        border-top: 1px solid #f0f0f0;
        padding-top: 12px;
        margin-top: 12px;
    }

    .message-size-info {
        display: flex;
        gap: 16px;
    }

    .message-actions {
        display: flex;
        gap: 8px;
    }

    .message-action-btn {
        padding: 4px 8px;
        border: 1px solid #ddd;
        border-radius: 6px;
        background: white;
        color: #666;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .message-action-btn:hover {
        background: #f8f9fa;
        border-color: #667eea;
        color: #667eea;
    }

    .session-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .session-stat-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid #e0e0e0;
    }

    .session-stat-value {
        font-size: 1.8rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 4px;
    }

    .session-stat-label {
        font-size: 0.8rem;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .real-time-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: rgba(67, 233, 123, 0.1);
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        color: #1a9852;
    }

    .real-time-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #1a9852;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .session-search {
        position: relative;
        margin-bottom: 16px;
    }

    .session-search input {
        width: 100%;
        padding: 12px 16px 12px 40px;
        border: 2px solid #e0e0e0;
        border-radius: 12px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .session-search input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .session-search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        font-size: 1.1rem;
    }

    .performance-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-top: 16px;
    }

    .metric-card {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 8px;
        text-align: center;
    }

    .metric-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 4px;
    }

    .metric-label {
        font-size: 0.85rem;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .connection-details-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .connection-details-modal.show {
        display: flex;
    }

    .modal-content {
        background: white;
        border-radius: 16px;
        padding: 24px;
        max-width: 800px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 2px solid #f0f0f0;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .modal-close:hover {
        background: #f0f0f0;
        color: #333;
    }

    @media (max-width: 768px) {
        .main-content {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .modal-content {
            margin: 20px;
            max-width: calc(100vw - 40px);
        }
    }
</style>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="dashboard">
        <header class="header">
            <h1>WebSocket Connection Dashboard</h1>
            <p>Real-time monitoring and management of WebSocket connections</p>
        </header>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon connections">🔗</div>
                    <div>
                        <div class="stat-title">Active Connections</div>
                        <div class="stat-value" id="activeConnections">{{ active_sessions|default:"0" }}</div>
                        <div class="stat-change positive" id="connectionChange">Loading...</div>
                    </div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon messages">💬</div>
                    <div>
                        <div class="stat-title">Messages/min</div>
                        <div class="stat-value" id="messagesPerMin">0</div>
                        <div class="stat-change positive" id="messageChange">Loading...</div>
                    </div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon workflows">⚡</div>
                    <div>
                        <div class="stat-title">Active Workflows</div>
                        <div class="stat-value" id="activeWorkflows">0</div>
                        <div class="stat-change positive" id="workflowChange">Loading...</div>
                    </div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon errors">⚠️</div>
                    <div>
                        <div class="stat-title">Error Rate</div>
                        <div class="stat-value" id="errorRate">0%</div>
                        <div class="stat-change negative" id="errorChange">Loading...</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="connections-panel">
                <div class="panel-header">
                    <h2 class="panel-title">Live Connections</h2>
                </div>

                <div class="filter-controls">
                    <button class="filter-btn active" onclick="filterConnections('all')">All</button>
                    <button class="filter-btn" onclick="filterConnections('connected')">Connected</button>
                    <button class="filter-btn" onclick="filterConnections('workflows')">In Workflow</button>
                    <button class="filter-btn" onclick="filterConnections('errors')">Errors</button>
                </div>

                <div class="connection-list" id="connectionList">
                    <!-- Connections will be populated by JavaScript -->
                    <div class="connection-item" style="text-align: center; color: #666;">
                        <span>Loading connections...</span>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="action-btn primary" onclick="refreshConnections()">Refresh Data</button>
                    <button class="action-btn secondary" onclick="exportData()">Export Logs</button>
                    <button class="action-btn secondary" onclick="toggleMessageInspector()">Message Inspector</button>
                    <button class="action-btn primary" onclick="toggleSessionFocus()">🎯 Session Focus</button>
                    <button class="action-btn warning" onclick="generateTestMessages()">🧪 Generate Test Messages</button>
                </div>

                <div class="action-buttons" style="margin-top: 12px;">
                    <button class="action-btn warning" onclick="cleanStaleConnections()">🧹 Clean Stale Connections</button>
                    <button class="action-btn secondary" onclick="detectConnectionLeaks()">🔍 Detect Leaks</button>
                    <button class="action-btn danger" onclick="forceCleanupConnections()">⚠️ Force Cleanup</button>
                </div>
            </div>

            <div class="sidebar">
                <div class="activity-panel">
                    <div class="panel-header">
                        <h3 class="panel-title">Connection Activity</h3>
                    </div>
                    <div class="activity-chart" id="activityChart">
                        📊 Real-time activity chart
                    </div>
                </div>

                <div class="system-panel">
                    <div class="panel-header">
                        <h3 class="panel-title">System Health</h3>
                    </div>
                    <div class="system-status" id="systemStatus">
                        <div class="system-item">
                            <span class="system-label">Redis</span>
                            <span class="system-value {% if redis_connected %}good{% else %}error{% endif %}">
                                {% if redis_connected %}Healthy{% else %}Disconnected{% endif %}
                            </span>
                        </div>
                        <div class="system-item">
                            <span class="system-label">Celery Workers</span>
                            <span class="system-value good" id="celeryStatus">Loading...</span>
                        </div>
                        <div class="system-item">
                            <span class="system-label">Database</span>
                            <span class="system-value good" id="databaseStatus">Loading...</span>
                        </div>
                        <div class="system-item">
                            <span class="system-label">Memory Usage</span>
                            <span class="system-value good" id="memoryUsage">Loading...</span>
                        </div>
                        <div class="system-item">
                            <span class="system-label">CPU Usage</span>
                            <span class="system-value good" id="cpuUsage">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Message Inspector Panel -->
        <div class="message-inspector" id="messageInspector" style="display: none;">
            <div class="panel-header">
                <h2 class="panel-title">🔍 Real-time Message Inspector</h2>
                <button class="action-btn secondary" onclick="clearMessageFlow()">Clear Messages</button>
            </div>

            <div class="filter-tabs">
                <button class="filter-tab active" onclick="filterMessages('all')">All Messages</button>
                <button class="filter-tab" onclick="filterMessages('chat_message')">Chat</button>
                <button class="filter-tab" onclick="filterMessages('debug_info')">Debug</button>
                <button class="filter-tab" onclick="filterMessages('workflow_status')">Workflows</button>
                <button class="filter-tab" onclick="filterMessages('wheel_data')">Wheel Data</button>
                <button class="filter-tab" onclick="filterMessages('error')">Errors</button>
            </div>

            <div class="performance-metrics">
                <div class="metric-card">
                    <div class="metric-value" id="totalMessages">0</div>
                    <div class="metric-label">Total Messages</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="avgMessageSize">0 KB</div>
                    <div class="metric-label">Avg Message Size</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="messagesPerSecond">0</div>
                    <div class="metric-label">Messages/sec</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="errorCount">0</div>
                    <div class="metric-label">Errors</div>
                </div>
            </div>

            <div class="message-flow" id="messageFlow">
                <div id="noMessagesPlaceholder" class="no-messages-placeholder" style="text-align: center; color: #666; padding: 40px; background: #f8f9fa; border-radius: 12px; margin: 20px;">
                    <div style="font-size: 3rem; margin-bottom: 16px;">📡</div>
                    <h3 style="color: #495057; margin-bottom: 16px;">Waiting for Message Flow...</h3>
                    <p style="margin-bottom: 20px;">No messages detected yet. To see real-time message monitoring:</p>
                    <ul style="text-align: left; display: inline-block; margin-bottom: 20px;">
                        <li style="margin-bottom: 8px;">🧪 Click "Generate Test Messages" to see sample data</li>
                        <li style="margin-bottom: 8px;">💬 Open the main app and send chat messages</li>
                        <li style="margin-bottom: 8px;">⚙️ Trigger workflows or agent interactions</li>
                    </ul>
                    <p><small>Make sure Message Inspector is enabled above ☝️</small></p>
                </div>
            </div>
        </div>

        <!-- Performance Analysis Panel -->
        <div class="debug-panel" id="performancePanel" style="display: none;">
            <div class="panel-header">
                <h2 class="panel-title">⚡ Performance Analysis</h2>
            </div>

            <div id="performanceCharts">
                <!-- Performance charts will be rendered here -->
                <div style="text-align: center; color: #666; padding: 40px;">
                    <span>📊 Performance metrics will appear here</span>
                </div>
            </div>
        </div>

        <!-- Ultra-Powerful Session-Focused Dashboard -->
        <div class="session-focus-panel" id="sessionFocusPanel" style="display: none;">
            <div class="session-header">
                <div class="session-info">
                    <div class="session-avatar" id="sessionAvatar">👤</div>
                    <div class="session-details">
                        <h2 id="sessionTitle">No Session Selected</h2>
                        <div class="session-meta">
                            <span id="sessionId">Select a connection to focus</span>
                            <span id="sessionDuration">Duration: --</span>
                            <span id="sessionMessageCount">Messages: --</span>
                        </div>
                    </div>
                </div>
                <div class="session-controls">
                    <div class="real-time-indicator" id="realTimeIndicator">
                        <div class="real-time-dot"></div>
                        <span>Live Monitoring</span>
                    </div>
                    <div class="session-status-badge" id="sessionStatusBadge">Inactive</div>
                    <button class="action-btn secondary" onclick="clearSessionFocus()">Clear Focus</button>
                </div>
            </div>

            <!-- Session Statistics -->
            <div class="session-stats-grid">
                <div class="session-stat-card">
                    <div class="session-stat-value" id="frontendMessageCount">0</div>
                    <div class="session-stat-label">Frontend → Backend</div>
                </div>
                <div class="session-stat-card">
                    <div class="session-stat-value" id="backendMessageCount">0</div>
                    <div class="session-stat-label">Backend → Frontend</div>
                </div>
                <div class="session-stat-card">
                    <div class="session-stat-value" id="systemMessageCount">0</div>
                    <div class="session-stat-label">System Messages</div>
                </div>
                <div class="session-stat-card">
                    <div class="session-stat-value" id="averageResponseTime">0ms</div>
                    <div class="session-stat-label">Avg Response Time</div>
                </div>
                <div class="session-stat-card">
                    <div class="session-stat-value" id="totalDataTransferred">0 KB</div>
                    <div class="session-stat-label">Data Transferred</div>
                </div>
                <div class="session-stat-card">
                    <div class="session-stat-value" id="errorCount">0</div>
                    <div class="session-stat-label">Errors</div>
                </div>
            </div>

            <!-- Chronological Timeline -->
            <div class="chronological-timeline">
                <div class="timeline-header">
                    <div class="timeline-title">
                        🕒 Chronological Message Timeline
                        <span id="timelineMessageCount">(0 messages)</span>
                    </div>
                    <div class="timeline-controls">
                        <div class="session-search">
                            <div class="session-search-icon">🔍</div>
                            <input type="text" id="timelineSearch" placeholder="Search messages..." onkeyup="filterTimelineMessages()">
                        </div>
                        <button class="timeline-filter active" onclick="filterTimeline('all')">All</button>
                        <button class="timeline-filter" onclick="filterTimeline('frontend')">Frontend</button>
                        <button class="timeline-filter" onclick="filterTimeline('backend')">Backend</button>
                        <button class="timeline-filter" onclick="filterTimeline('system')">System</button>
                        <button class="timeline-filter" onclick="filterTimeline('errors')">Errors</button>
                        <button class="action-btn secondary" onclick="exportSessionData()">Export Session</button>
                        <button class="action-btn secondary" onclick="autoScrollTimeline = !autoScrollTimeline">
                            <span id="autoScrollText">📍 Auto-scroll: ON</span>
                        </button>
                    </div>
                </div>

                <div class="message-timeline" id="messageTimeline">
                    <div class="timeline-line"></div>
                    <div style="text-align: center; color: #666; padding: 60px 20px;">
                        <div style="font-size: 3rem; margin-bottom: 16px;">🎯</div>
                        <div style="font-size: 1.2rem; font-weight: 600; margin-bottom: 8px;">Session Focus Mode</div>
                        <div>Click on a connection above to start monitoring its message flow</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Connection Details Modal -->
    <div class="connection-details-modal" id="connectionModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Connection Details</h3>
                <button class="modal-close" onclick="closeConnectionModal()">&times;</button>
            </div>
            <div id="connectionModalContent">
                <!-- Connection details will be populated here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extrahead %}
{{ block.super }}
<script>
// Enhanced WebSocket connection for deep debugging
let monitorSocket = null;
let isConnected = false;
let messageBuffer = [];
let messageStats = {
    total: 0,
    totalSize: 0,
    errorCount: 0,
    lastSecondCount: 0,
    lastSecondTime: Date.now()
};
let currentFilter = 'all';
let inspectorVisible = false;

// Ultra-Powerful Session Focus Variables
let sessionFocusVisible = false;
let focusedSessionId = null;
let sessionMessages = [];
let sessionStats = {
    frontendMessages: 0,
    backendMessages: 0,
    systemMessages: 0,
    totalDataTransferred: 0,
    errorCount: 0,
    responseTimes: [],
    startTime: null
};
let autoScrollTimeline = true;
let currentTimelineFilter = 'all';

// Initialize enhanced dashboard
document.addEventListener('DOMContentLoaded', function() {
    connectToMonitor();
    setupMessageInspector();
    setupPerformanceMonitoring();

    // Set up periodic refresh as fallback
    setInterval(function() {
        if (!isConnected) {
            connectToMonitor();
        }
        updatePerformanceMetrics();
    }, 10000); // Try to reconnect every 10 seconds

    // Update messages per second counter
    setInterval(updateMessagesPerSecond, 1000);
});

function connectToMonitor() {
    const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
    const wsPath = wsScheme + '://' + window.location.host + "/ws/connection-monitor/";

    console.log('Connecting to:', wsPath);

    monitorSocket = new WebSocket(wsPath);

    monitorSocket.onopen = function(e) {
        console.log('✅ Monitor WebSocket connected');
        isConnected = true;

        // Request initial data
        requestConnectionData();
        requestSystemHealth();
        requestMessageStats();
    };

    monitorSocket.onmessage = function(e) {
        try {
            const data = JSON.parse(e.data);
            handleMonitorMessage(data);
        } catch (error) {
            console.error('Error parsing monitor message:', error);
        }
    };

    monitorSocket.onerror = function(e) {
        console.error('Monitor WebSocket error:', e);
        isConnected = false;
    };

    monitorSocket.onclose = function(e) {
        console.log('Monitor WebSocket closed:', e.code);
        isConnected = false;

        // Try to reconnect after 5 seconds
        setTimeout(connectToMonitor, 5000);
    };
}

function handleMonitorMessage(data) {
    // Track all messages for deep analysis
    trackMessage(data);

    switch(data.type) {
        case 'connection_data':
            updateConnectionList(data.data);
            break;
        case 'system_health':
            updateSystemHealth(data.data);
            break;
        case 'message_stats':
            updateMessageStats(data.data);
            break;
        case 'connection_event':
            handleConnectionEvent(data.data);
            break;
        case 'message_flow':
            handleMessageFlow(data.data);
            break;
        case 'connection_details':
            handleConnectionDetails(data);
            break;
        case 'debug_info':
        case 'chat_message':
        case 'workflow_status':
        case 'wheel_data':
        case 'error':
            // Add to real-time message inspector
            addMessageToInspector(data);
            break;
        case 'message_monitoring_started':
            handleMessageMonitoringStarted(data);
            break;
        case 'message_monitoring_stopped':
            handleMessageMonitoringStopped(data);
            break;
        case 'session_monitoring_started':
            handleSessionMonitoringStarted(data);
            break;
        case 'session_monitoring_stopped':
            handleSessionMonitoringStopped(data);
            break;
        case 'connection_cleanup_result':
            handleConnectionCleanupResult(data);
            break;
        case 'leak_detection_result':
            handleLeakDetectionResult(data);
            break;
        case 'force_cleanup_result':
            handleForceCleanupResult(data);
            break;
        case 'connection_cleanup_error':
        case 'leak_detection_error':
        case 'force_cleanup_error':
            handleConnectionManagementError(data);
            break;
        default:
            console.log('Unknown message type:', data.type);
            addMessageToInspector(data);
    }
}

function updateConnectionList(connections) {
    const connectionList = document.getElementById('connectionList');

    if (!connections || connections.length === 0) {
        connectionList.innerHTML = '<div class="connection-item" style="text-align: center; color: #666;"><span>No active connections</span></div>';
        return;
    }

    connectionList.innerHTML = connections.map(conn => `
        <div class="connection-item" onclick="showConnectionDetails('${conn.session_id}')">
            <div class="connection-status ${conn.status}"></div>
            <div class="connection-info">
                <div class="connection-user">User ${conn.user_id}</div>
                <div class="connection-details">
                    Session: ${conn.session_id.substring(0, 8)}... •
                    ${conn.current_workflow || 'No workflow'} •
                    ${conn.ip_address || 'Unknown IP'}
                </div>
            </div>
            <div class="connection-metrics">
                <div>${conn.message_count} messages</div>
                <div>Connected ${conn.duration}</div>
                <div style="font-size: 0.7rem; color: #999; margin-top: 4px;">
                    <button onclick="event.stopPropagation(); focusOnSession('${conn.session_id}', ${JSON.stringify(conn).replace(/"/g, '&quot;')})"
                            style="padding: 2px 6px; border: 1px solid #667eea; border-radius: 4px; background: white; color: #667eea; cursor: pointer; font-size: 0.7rem; margin-right: 4px;">
                        🎯 Focus
                    </button>
                    <span>Click for details</span>
                </div>
            </div>
        </div>
    `).join('');

    // Update active connections count
    document.getElementById('activeConnections').textContent = connections.length;
}

function updateSystemHealth(health) {
    // Update individual system components
    if (health.redis) {
        const redisStatus = document.querySelector('#systemStatus .system-item:nth-child(1) .system-value');
        redisStatus.textContent = health.redis === 'healthy' ? 'Healthy' : health.redis;
        redisStatus.className = 'system-value ' + (health.redis === 'healthy' ? 'good' : 'error');
    }

    if (health.celery_workers) {
        const celeryStatus = document.getElementById('celeryStatus');
        celeryStatus.textContent = health.celery_workers;
        celeryStatus.className = 'system-value ' + (health.celery_workers.includes('active') ? 'good' : 'warning');
    }

    if (health.database) {
        const dbStatus = document.getElementById('databaseStatus');
        dbStatus.textContent = health.database === 'healthy' ? 'Healthy' : health.database;
        dbStatus.className = 'system-value ' + (health.database === 'healthy' ? 'good' : 'error');
    }
}

function updateMessageStats(stats) {
    document.getElementById('messagesPerMin').textContent = stats.messages_per_minute || 0;
    document.getElementById('activeWorkflows').textContent = stats.active_workflows || 0;
    document.getElementById('errorRate').textContent = (stats.error_rate || 0).toFixed(1) + '%';
}

function requestConnectionData() {
    if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
        monitorSocket.send(JSON.stringify({type: 'get_connections'}));
    }
}

function requestSystemHealth() {
    if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
        monitorSocket.send(JSON.stringify({type: 'get_system_health'}));
    }
}

function requestMessageStats() {
    if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
        monitorSocket.send(JSON.stringify({type: 'get_message_stats'}));
    }
}

function filterConnections(filter) {
    // Remove active class from all buttons
    document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));

    // Add active class to clicked button
    event.target.classList.add('active');

    // TODO: Implement filtering logic
    console.log('Filtering connections by:', filter);
}

function refreshConnections() {
    console.log('Refreshing connection data...');
    requestConnectionData();
    requestSystemHealth();
    requestMessageStats();
}

function exportData() {
    console.log('Exporting connection data...');

    const exportData = {
        timestamp: new Date().toISOString(),
        messageBuffer: messageBuffer,
        messageStats: messageStats,
        connections: [], // Would be populated from current connection data
        systemHealth: {} // Would be populated from current system health
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `websocket-debug-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function showConnectionDetails(sessionId) {
    const modal = document.getElementById('connectionModal');
    const content = document.getElementById('connectionModalContent');

    // Request detailed connection info
    if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
        monitorSocket.send(JSON.stringify({
            type: 'get_connection_details',
            session_id: sessionId
        }));
    }

    content.innerHTML = `
        <div style="text-align: center; padding: 20px;">
            <div style="font-size: 1.2rem; margin-bottom: 10px;">Loading connection details...</div>
            <div style="color: #666;">Session ID: ${sessionId}</div>
        </div>
    `;

    modal.classList.add('show');
}

function closeConnectionModal() {
    const modal = document.getElementById('connectionModal');
    modal.classList.remove('show');
}

function updatePerformanceMetrics() {
    document.getElementById('totalMessages').textContent = messageStats.total;
    document.getElementById('avgMessageSize').textContent =
        messageStats.total > 0 ? formatBytes(messageStats.totalSize / messageStats.total) : '0 B';
    document.getElementById('errorCount').textContent = messageStats.errorCount;
}

function updateMessagesPerSecond() {
    document.getElementById('messagesPerSecond').textContent = messageStats.lastSecondCount;
    messageStats.lastSecondCount = 0;
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('connectionModal');
    if (event.target === modal) {
        closeConnectionModal();
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeConnectionModal();
    }
    if (event.ctrlKey && event.key === 'i') {
        event.preventDefault();
        toggleMessageInspector();
    }
    if (event.ctrlKey && event.key === 'r') {
        event.preventDefault();
        refreshConnections();
    }
});

function handleConnectionEvent(event) {
    console.log('Connection event:', event);
    // Add to message inspector if visible
    if (inspectorVisible) {
        addMessageToInspector({
            type: 'connection_event',
            data: event,
            timestamp: new Date().toISOString()
        });
    }
}

function handleMessageFlow(event) {
    console.log('Message flow event:', event);

    // Extract the actual message from the flow event
    const actualMessage = event.message || event;

    // Add to message inspector if visible
    if (inspectorVisible && actualMessage) {
        // Use the original message type, not 'message_flow'
        const messageToAdd = {
            type: actualMessage.type || 'unknown',
            content: actualMessage.content,
            data: actualMessage.data || actualMessage,
            direction: event.direction || 'unknown',
            session_id: event.session_id,
            user_id: event.user_id,
            timestamp: event.timestamp || new Date().toISOString()
        };

        addMessageToInspector(messageToAdd);
    }

    // Also add to session timeline if session focus is active
    if (sessionFocusVisible && focusedSessionId && event.session_id === focusedSessionId) {
        addMessageToTimeline(event);
    }
}

function handleMessageMonitoringStarted(data) {
    console.log('✅ Message monitoring started:', data);
    showNotification('Message monitoring started', 'success');

    // Update UI to show monitoring is active
    const inspectorPanel = document.getElementById('messageInspector');
    if (inspectorPanel) {
        inspectorPanel.style.display = 'block';
        inspectorVisible = true;
    }
}

function handleMessageMonitoringStopped(data) {
    console.log('⏹️ Message monitoring stopped:', data);
    showNotification('Message monitoring stopped', 'info');
}

function handleSessionMonitoringStarted(data) {
    console.log('✅ Session monitoring started:', data);
    showNotification('Session monitoring started', 'success');

    // Update UI to show session focus is active
    const sessionPanel = document.getElementById('sessionFocusPanel');
    if (sessionPanel) {
        sessionPanel.style.display = 'block';
        sessionFocusVisible = true;
    }
}

function handleSessionMonitoringStopped(data) {
    console.log('⏹️ Session monitoring stopped:', data);
    showNotification('Session monitoring stopped', 'info');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    `;

    // Set background color based on type
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    notification.style.backgroundColor = colors[type] || colors.info;

    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function handleConnectionDetails(response) {
    console.log('Connection details received:', response);
    const content = document.getElementById('connectionModalContent');

    if (response.error) {
        content.innerHTML = `
            <div style="text-align: center; padding: 20px;">
                <div style="color: #e74c3c; font-size: 1.2rem; margin-bottom: 10px;">❌ Error</div>
                <div style="color: #666;">${response.error}</div>
            </div>
        `;
        return;
    }

    const details = response.data;
    if (!details) {
        content.innerHTML = `
            <div style="text-align: center; padding: 20px;">
                <div style="color: #e74c3c; font-size: 1.2rem; margin-bottom: 10px;">❌ No Data</div>
                <div style="color: #666;">No connection details available</div>
            </div>
        `;
        return;
    }

    // Create comprehensive connection details display
    content.innerHTML = `
        <div class="connection-details-content">
            <div class="detail-section">
                <h3>🔗 Connection Information</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">Session ID:</span>
                        <span class="detail-value">${details.session_id}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">User ID:</span>
                        <span class="detail-value">${details.user_id}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value status-${details.status}">${details.status}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Connection Type:</span>
                        <span class="detail-value">${details.connection_type || 'WebSocket'}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h3>⏱️ Timing Information</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">Connected At:</span>
                        <span class="detail-value">${details.connected_at ? new Date(details.connected_at).toLocaleString() : 'Unknown'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Duration:</span>
                        <span class="detail-value">${details.duration_human || 'Unknown'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Last Activity:</span>
                        <span class="detail-value">${details.last_activity ? new Date(details.last_activity).toLocaleString() : 'Unknown'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Duration (seconds):</span>
                        <span class="detail-value">${details.duration_seconds || 0}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h3>📊 Activity Metrics</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">Message Count:</span>
                        <span class="detail-value">${details.message_count || 0}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Current Workflow:</span>
                        <span class="detail-value">${details.current_workflow || 'None'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Bytes Sent:</span>
                        <span class="detail-value">${formatBytes(details.total_bytes_sent || 0)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Bytes Received:</span>
                        <span class="detail-value">${formatBytes(details.total_bytes_received || 0)}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h3>🌐 Network Information</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">IP Address:</span>
                        <span class="detail-value">${details.ip_address || 'Unknown'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">User Agent:</span>
                        <span class="detail-value" style="font-size: 0.8rem; word-break: break-all;">${details.user_agent || 'Unknown'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Protocol Version:</span>
                        <span class="detail-value">${details.protocol_version || 'ws'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Last Ping:</span>
                        <span class="detail-value">${details.last_ping ? new Date(details.last_ping).toLocaleString() : 'Unknown'}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h3>🔧 Debug Actions</h3>
                <div class="action-buttons">
                    <button class="action-btn secondary" onclick="disconnectUser('${details.user_id}')">Disconnect User</button>
                    <button class="action-btn secondary" onclick="exportConnectionData('${details.session_id}')">Export Data</button>
                    <button class="action-btn secondary" onclick="refreshConnectionDetails('${details.session_id}')">Refresh</button>
                </div>
            </div>
        </div>

        <style>
        .connection-details-content {
            max-height: 70vh;
            overflow-y: auto;
        }

        .detail-section {
            margin-bottom: 24px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .detail-section h3 {
            margin-bottom: 16px;
            color: #333;
            font-size: 1.1rem;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: white;
            border-radius: 6px;
            border-left: 3px solid #667eea;
        }

        .detail-label {
            font-weight: 600;
            color: #555;
            margin-right: 12px;
        }

        .detail-value {
            color: #333;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .detail-value.status-connected {
            color: #28a745;
            font-weight: 600;
        }

        .detail-value.status-disconnected {
            color: #dc3545;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
        }
        </style>
    `;
}

// Enhanced Deep Debugging Functions
function setupMessageInspector() {
    console.log('🔍 Setting up message inspector...');
}

function setupPerformanceMonitoring() {
    console.log('⚡ Setting up performance monitoring...');
}

function generateTestMessages() {
    console.log('🧪 Generating test messages...');

    if (!monitorSocket || monitorSocket.readyState !== WebSocket.OPEN) {
        showNotification('WebSocket not connected. Cannot generate test messages.', 'error');
        return;
    }

    // Enable message inspector if not already enabled
    if (!inspectorVisible) {
        toggleMessageInspector();
    }

    // Generate a series of test messages
    const testMessages = [
        {
            type: 'chat_message',
            content: 'Hello! I need help setting up my goals.',
            direction: 'incoming'
        },
        {
            type: 'chat_response',
            content: 'I\'d be happy to help you set up your goals! Let\'s start by understanding what you want to achieve.',
            agent: 'mentor',
            direction: 'outgoing'
        },
        {
            type: 'workflow_status',
            status: 'analyzing_user_input',
            progress: 25,
            direction: 'outgoing'
        },
        {
            type: 'chat_message',
            content: 'I want to improve my fitness and learn a new skill.',
            direction: 'incoming'
        },
        {
            type: 'workflow_status',
            status: 'generating_wheel',
            progress: 75,
            direction: 'outgoing'
        },
        {
            type: 'wheel_data',
            wheel_items: [
                { activity: 'Morning jog', category: 'fitness' },
                { activity: 'Learn Python basics', category: 'skill' }
            ],
            direction: 'outgoing'
        }
    ];

    let messageIndex = 0;
    const sessionId = 'test-session-' + Date.now();
    const userId = 'test-user-' + Math.floor(Math.random() * 1000);

    function sendNextTestMessage() {
        if (messageIndex >= testMessages.length) {
            showNotification('Test message generation completed!', 'success');
            return;
        }

        const testMessage = testMessages[messageIndex];

        // Simulate the message flow event
        const messageFlowEvent = {
            type: 'message_flow',
            data: {
                direction: testMessage.direction,
                session_id: sessionId,
                user_id: userId,
                message: {
                    type: testMessage.type,
                    content: testMessage.content,
                    agent: testMessage.agent,
                    status: testMessage.status,
                    progress: testMessage.progress,
                    wheel_items: testMessage.wheel_items
                },
                timestamp: new Date().toISOString()
            }
        };

        // Add directly to message inspector
        handleMessageFlow(messageFlowEvent.data);

        messageIndex++;

        // Schedule next message
        setTimeout(sendNextTestMessage, 1500); // 1.5 second delay between messages
    }

    showNotification('Generating test messages...', 'info');
    sendNextTestMessage();
}

function trackMessage(data) {
    messageStats.total++;
    messageStats.totalSize += JSON.stringify(data).length;

    if (data.type === 'error') {
        messageStats.errorCount++;
    }

    // Track messages per second
    const now = Date.now();
    if (now - messageStats.lastSecondTime >= 1000) {
        messageStats.lastSecondCount = 0;
        messageStats.lastSecondTime = now;
    }
    messageStats.lastSecondCount++;

    // Keep message buffer for analysis (limit to last 1000 messages)
    messageBuffer.push({
        ...data,
        timestamp: new Date().toISOString(),
        size: JSON.stringify(data).length
    });

    if (messageBuffer.length > 1000) {
        messageBuffer.shift();
    }
}

function addMessageToInspector(data) {
    if (!inspectorVisible) return;

    const messageFlow = document.getElementById('messageFlow');
    if (!messageFlow) return;

    // Update message statistics
    updateMessageStats(data);

    // Create message item
    const messageItem = document.createElement('div');
    messageItem.className = `message-item ${data.type}`;
    messageItem.onclick = () => toggleMessageDetails(messageItem);

    const timestamp = new Date(data.timestamp || Date.now()).toLocaleTimeString();
    const content = getMessagePreview(data);
    const size = formatBytes(JSON.stringify(data).length);

    messageItem.innerHTML = `
        <span class="message-timestamp">${timestamp}</span>
        <span class="message-type">${data.type}</span>
        <span class="message-content">${content}</span>
        <span class="message-size">${size}</span>
        <div class="expandable-content">
            <div class="json-viewer">${JSON.stringify(data, null, 2)}</div>
            <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #e0e0e0;">
                <button onclick="event.stopPropagation(); copyMessageToClipboard(this)"
                        style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer;">
                    📋 Copy JSON
                </button>
                <button onclick="event.stopPropagation(); validateMessageStructure(this)"
                        style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer; margin-left: 8px;">
                    ✅ Validate
                </button>
                <button onclick="event.stopPropagation(); showMessageDetails(this)"
                        style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer; margin-left: 8px;">
                    🔍 Details
                </button>
            </div>
        </div>
    `;

    // Store the original data for later use
    messageItem.setAttribute('data-message', JSON.stringify(data));

    // Apply current filter
    if (currentFilter !== 'all' && data.type !== currentFilter) {
        messageItem.style.display = 'none';
    }

    // Insert at the top for newest-first display
    messageFlow.insertBefore(messageItem, messageFlow.firstChild);

    // Remove old messages to prevent memory issues
    const items = messageFlow.querySelectorAll('.message-item');
    if (items.length > 500) {
        items[items.length - 1].remove();
    }

    // Clear the "waiting" message if it exists
    const waitingMsg = messageFlow.querySelector('#noMessagesPlaceholder');
    if (waitingMsg) {
        waitingMsg.remove();
    }

    // Update performance metrics display
    updatePerformanceMetrics();
}

function getMessagePreview(data) {
    switch (data.type) {
        case 'chat_message':
            return data.content || data.data?.content || 'Chat message';
        case 'debug_info':
            return data.data?.message || data.content?.message || 'Debug info';
        case 'workflow_status':
            return `Workflow: ${data.data?.workflow_id || 'unknown'} - ${data.data?.status || 'unknown'}`;
        case 'wheel_data':
            const itemCount = data.data?.wheel?.items?.length || data.wheel?.items?.length || 0;
            return `Wheel generated with ${itemCount} items`;
        case 'error':
            return data.data?.message || data.message || 'Error occurred';
        default:
            return JSON.stringify(data).substring(0, 100) + '...';
    }
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function updateMessageStats(data) {
    // Update global message statistics
    messageStats.total++;
    messageStats.lastSecondCount++;

    if (data.type === 'error') {
        messageStats.errorCount++;
    }

    const messageSize = JSON.stringify(data).length;
    messageStats.totalSize += messageSize;
}

function copyMessageToClipboard(button) {
    const messageItem = button.closest('.message-item');
    const messageData = messageItem.getAttribute('data-message');

    if (messageData) {
        navigator.clipboard.writeText(messageData).then(() => {
            showNotification('Message copied to clipboard', 'success');
        }).catch(() => {
            showNotification('Failed to copy to clipboard', 'error');
        });
    }
}

function validateMessageStructure(button) {
    const messageItem = button.closest('.message-item');
    const messageData = JSON.parse(messageItem.getAttribute('data-message'));

    // Basic validation
    const hasType = messageData.type && typeof messageData.type === 'string';
    const hasTimestamp = messageData.timestamp || messageData.content?.timestamp;
    const hasContent = messageData.content || messageData.data;

    let validationResult = '✅ Message Structure Validation:\n\n';
    validationResult += `Type: ${hasType ? '✅' : '❌'} ${messageData.type || 'Missing'}\n`;
    validationResult += `Timestamp: ${hasTimestamp ? '✅' : '❌'} ${hasTimestamp ? 'Present' : 'Missing'}\n`;
    validationResult += `Content: ${hasContent ? '✅' : '❌'} ${hasContent ? 'Present' : 'Missing'}\n`;

    // Type-specific validation
    if (messageData.type === 'chat_message' && messageData.content) {
        validationResult += `\n📝 Chat Message Validation:\n`;
        validationResult += `Message: ${messageData.content.message ? '✅' : '❌'}\n`;
        validationResult += `User Profile: ${messageData.content.user_profile_id ? '✅' : '❌'}\n`;
    }

    if (messageData.type === 'wheel_data' && messageData.content) {
        validationResult += `\n🎡 Wheel Data Validation:\n`;
        validationResult += `Segments: ${messageData.content.segments ? '✅' : '❌'}\n`;
        validationResult += `Center Text: ${messageData.content.center_text ? '✅' : '❌'}\n`;
    }

    alert(validationResult);
}

function showMessageDetails(button) {
    const messageItem = button.closest('.message-item');
    const messageData = JSON.parse(messageItem.getAttribute('data-message'));

    // Create a detailed modal for the message
    const modal = document.createElement('div');
    modal.className = 'connection-details-modal show';
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3>📨 Message Details: ${messageData.type}</h3>
                <button class="modal-close" onclick="this.closest('.connection-details-modal').remove()">&times;</button>
            </div>
            <div style="padding: 20px;">
                <div style="margin-bottom: 20px;">
                    <strong>Type:</strong> ${messageData.type}<br>
                    <strong>Timestamp:</strong> ${messageData.timestamp || 'Not provided'}<br>
                    <strong>Size:</strong> ${formatBytes(JSON.stringify(messageData).length)}
                </div>
                <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin-bottom: 16px;">
                    <h4>Preview:</h4>
                    <div>${getMessagePreview(messageData)}</div>
                </div>
                <div style="background: #2d3748; color: #e2e8f0; padding: 16px; border-radius: 8px; font-family: monospace; font-size: 0.85rem; line-height: 1.5; overflow-x: auto; white-space: pre-wrap;">
${JSON.stringify(messageData, null, 2)}
                </div>
                <div style="margin-top: 16px; text-align: right;">
                    <button onclick="copyToClipboard('${escapeForAttribute(JSON.stringify(messageData))}')"
                            style="padding: 8px 16px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer; margin-right: 8px;">
                        📋 Copy JSON
                    </button>
                    <button onclick="this.closest('.connection-details-modal').remove()"
                            style="padding: 8px 16px; border: 1px solid #ddd; border-radius: 4px; background: #f8f9fa; cursor: pointer;">
                        Close
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function escapeForAttribute(str) {
    return str.replace(/'/g, "\\'").replace(/"/g, '\\"');
}

function copyToClipboard(jsonStr) {
    try {
        const data = JSON.parse(jsonStr);
        navigator.clipboard.writeText(JSON.stringify(data, null, 2)).then(() => {
            showNotification('JSON copied to clipboard', 'success');
        }).catch(() => {
            showNotification('Failed to copy to clipboard', 'error');
        });
    } catch (e) {
        showNotification('Invalid JSON data', 'error');
    }
}

function toggleMessageDetails(messageElement) {
    const expandableContent = messageElement.querySelector('.expandable-content');
    if (expandableContent) {
        expandableContent.classList.toggle('expanded');
    }
}

function toggleMessageInspector() {
    const inspector = document.getElementById('messageInspector');
    inspectorVisible = !inspectorVisible;

    if (inspectorVisible) {
        inspector.style.display = 'block';
        // Request real-time message flow
        if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
            monitorSocket.send(JSON.stringify({type: 'start_message_monitoring'}));
        }
    } else {
        inspector.style.display = 'none';
        // Stop real-time message flow
        if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
            monitorSocket.send(JSON.stringify({type: 'stop_message_monitoring'}));
        }
    }
}

function filterMessages(filter) {
    currentFilter = filter;

    // Update active tab
    document.querySelectorAll('.filter-tab').forEach(tab => tab.classList.remove('active'));
    event.target.classList.add('active');

    // Show/hide messages based on filter
    const messageItems = document.querySelectorAll('.message-item');
    messageItems.forEach(item => {
        if (filter === 'all' || item.classList.contains(filter)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

function clearMessageFlow() {
    const messageFlow = document.getElementById('messageFlow');
    messageFlow.innerHTML = `
        <div id="noMessagesPlaceholder" class="no-messages-placeholder" style="text-align: center; color: #666; padding: 40px; background: #f8f9fa; border-radius: 12px; margin: 20px;">
            <div style="font-size: 3rem; margin-bottom: 16px;">📡</div>
            <h3 style="color: #495057; margin-bottom: 16px;">Waiting for Message Flow...</h3>
            <p style="margin-bottom: 20px;">No messages detected yet. To see real-time message monitoring:</p>
            <ul style="text-align: left; display: inline-block; margin-bottom: 20px;">
                <li style="margin-bottom: 8px;">🧪 Click "Generate Test Messages" to see sample data</li>
                <li style="margin-bottom: 8px;">💬 Open the main app and send chat messages</li>
                <li style="margin-bottom: 8px;">⚙️ Trigger workflows or agent interactions</li>
            </ul>
            <p><small>Make sure Message Inspector is enabled above ☝️</small></p>
        </div>
    `;
    messageBuffer = [];
    messageStats = {
        total: 0,
        totalSize: 0,
        errorCount: 0,
        lastSecondCount: 0,
        lastSecondTime: Date.now()
    };
    updatePerformanceMetrics();
}

// Debug action functions
function disconnectUser(userId) {
    if (confirm(`Are you sure you want to disconnect user ${userId}?`)) {
        if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
            monitorSocket.send(JSON.stringify({
                type: 'disconnect_user',
                user_id: userId
            }));
            console.log(`Disconnecting user ${userId}`);

            // Close modal and refresh connections
            setTimeout(() => {
                closeConnectionModal();
                refreshConnections();
            }, 1000);
        }
    }
}

function exportConnectionData(sessionId) {
    // Export specific connection data
    const exportData = {
        timestamp: new Date().toISOString(),
        session_id: sessionId,
        connection_details: 'Exported from connection details modal',
        message_buffer: messageBuffer.filter(msg => msg.session_id === sessionId),
        system_info: {
            user_agent: navigator.userAgent,
            timestamp: new Date().toISOString()
        }
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `connection-${sessionId}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log(`Exported data for connection ${sessionId}`);
}

function refreshConnectionDetails(sessionId) {
    console.log(`Refreshing details for connection ${sessionId}`);
    showConnectionDetails(sessionId);
}

// Ultra-Powerful Session Focus Functions
function toggleSessionFocus() {
    const panel = document.getElementById('sessionFocusPanel');
    sessionFocusVisible = !sessionFocusVisible;

    if (sessionFocusVisible) {
        panel.style.display = 'block';
        console.log('🎯 Session Focus Mode activated');

        // Enable session message monitoring
        if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
            monitorSocket.send(JSON.stringify({type: 'start_session_monitoring'}));
        }
    } else {
        panel.style.display = 'none';
        clearSessionFocus();
        console.log('🎯 Session Focus Mode deactivated');
    }
}

function focusOnSession(sessionId, connectionData) {
    console.log(`🎯 Focusing on session: ${sessionId}`);

    focusedSessionId = sessionId;
    sessionMessages = [];
    sessionStats = {
        frontendMessages: 0,
        backendMessages: 0,
        systemMessages: 0,
        totalDataTransferred: 0,
        errorCount: 0,
        responseTimes: [],
        startTime: Date.now()
    };

    // Update session header
    updateSessionHeader(connectionData);

    // Clear timeline and show focused state
    const timeline = document.getElementById('messageTimeline');
    timeline.innerHTML = `
        <div class="timeline-line"></div>
        <div style="text-align: center; color: #667eea; padding: 40px 20px;">
            <div style="font-size: 2.5rem; margin-bottom: 16px;">🎯</div>
            <div style="font-size: 1.2rem; font-weight: 600; margin-bottom: 8px;">Monitoring Session: ${sessionId.substring(0, 8)}...</div>
            <div style="color: #666;">Waiting for messages...</div>
            <div class="real-time-indicator" style="justify-content: center; margin-top: 16px;">
                <div class="real-time-dot"></div>
                <span>Live monitoring active</span>
            </div>
        </div>
    `;

    // Update stats display
    updateSessionStats();

    // Request session-specific monitoring
    if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
        monitorSocket.send(JSON.stringify({
            type: 'focus_session',
            session_id: sessionId
        }));
    }

    // Show session focus panel if not visible
    if (!sessionFocusVisible) {
        toggleSessionFocus();
    }
}

function updateSessionHeader(connectionData) {
    if (!connectionData) return;

    document.getElementById('sessionAvatar').textContent = `U${connectionData.user_id || '?'}`;
    document.getElementById('sessionTitle').textContent = `Session: ${connectionData.session_id?.substring(0, 12)}...`;
    document.getElementById('sessionId').textContent = `ID: ${connectionData.session_id}`;
    document.getElementById('sessionDuration').textContent = `Duration: ${connectionData.duration || 'Unknown'}`;
    document.getElementById('sessionMessageCount').textContent = `Messages: ${connectionData.message_count || 0}`;

    const statusBadge = document.getElementById('sessionStatusBadge');
    statusBadge.textContent = connectionData.status || 'Unknown';
    statusBadge.className = `session-status-badge ${connectionData.status === 'connected' ? 'active' : 'inactive'}`;
}

function clearSessionFocus() {
    console.log('🎯 Clearing session focus');

    focusedSessionId = null;
    sessionMessages = [];
    sessionStats = {
        frontendMessages: 0,
        backendMessages: 0,
        systemMessages: 0,
        totalDataTransferred: 0,
        errorCount: 0,
        responseTimes: [],
        startTime: null
    };

    // Reset header
    document.getElementById('sessionAvatar').textContent = '👤';
    document.getElementById('sessionTitle').textContent = 'No Session Selected';
    document.getElementById('sessionId').textContent = 'Select a connection to focus';
    document.getElementById('sessionDuration').textContent = 'Duration: --';
    document.getElementById('sessionMessageCount').textContent = 'Messages: --';

    const statusBadge = document.getElementById('sessionStatusBadge');
    statusBadge.textContent = 'Inactive';
    statusBadge.className = 'session-status-badge inactive';

    // Clear timeline
    const timeline = document.getElementById('messageTimeline');
    timeline.innerHTML = `
        <div class="timeline-line"></div>
        <div style="text-align: center; color: #666; padding: 60px 20px;">
            <div style="font-size: 3rem; margin-bottom: 16px;">🎯</div>
            <div style="font-size: 1.2rem; font-weight: 600; margin-bottom: 8px;">Session Focus Mode</div>
            <div>Click on a connection above to start monitoring its message flow</div>
        </div>
    `;

    // Reset stats
    updateSessionStats();

    // Stop session monitoring
    if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
        monitorSocket.send(JSON.stringify({type: 'stop_session_monitoring'}));
    }
}

function addMessageToTimeline(message) {
    if (!focusedSessionId || !sessionFocusVisible) return;

    // Check if message belongs to focused session
    if (message.session_id !== focusedSessionId) return;

    // Add to session messages
    sessionMessages.push(message);

    // Update stats
    updateSessionStatsFromMessage(message);

    // Determine message direction and type
    const direction = determineMessageDirection(message);
    const messageElement = createTimelineMessageElement(message, direction);

    // Add to timeline
    const timeline = document.getElementById('messageTimeline');
    const timelineLine = timeline.querySelector('.timeline-line');

    // Remove placeholder if it exists
    const placeholder = timeline.querySelector('[style*="text-align: center"]');
    if (placeholder) {
        placeholder.remove();
    }

    // Insert message (newest at top for real-time feel)
    if (timelineLine.nextSibling) {
        timeline.insertBefore(messageElement, timelineLine.nextSibling);
    } else {
        timeline.appendChild(messageElement);
    }

    // Apply current filter
    applyTimelineFilter(messageElement, direction, message.type);

    // Auto-scroll if enabled
    if (autoScrollTimeline) {
        messageElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    // Update timeline message count
    updateTimelineMessageCount();

    // Limit timeline messages to prevent memory issues
    const messages = timeline.querySelectorAll('.timeline-message');
    if (messages.length > 500) {
        messages[messages.length - 1].remove();
        sessionMessages.shift(); // Remove oldest from array too
    }
}

function determineMessageDirection(message) {
    // Determine if message is from frontend to backend or vice versa
    if (message.direction) {
        return message.direction; // If explicitly provided
    }

    // Heuristic based on message type and content
    const frontendTypes = ['chat_message', 'user_input', 'wheel_request'];
    const backendTypes = ['wheel_data', 'debug_info', 'workflow_status', 'system_message'];
    const systemTypes = ['connection_event', 'error', 'system_health'];

    if (frontendTypes.includes(message.type)) {
        return 'frontend';
    } else if (backendTypes.includes(message.type)) {
        return 'backend';
    } else if (systemTypes.includes(message.type)) {
        return 'system';
    }

    // Default based on content analysis
    if (message.content && typeof message.content === 'string') {
        return 'frontend'; // User messages are typically strings
    } else if (message.data || message.wheel) {
        return 'backend'; // Structured data typically from backend
    }

    return 'system'; // Default fallback
}

function createTimelineMessageElement(message, direction) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `timeline-message ${direction}`;
    messageDiv.setAttribute('data-direction', direction);
    messageDiv.setAttribute('data-type', message.type);
    messageDiv.setAttribute('data-timestamp', message.timestamp || Date.now());

    const timestamp = new Date(message.timestamp || Date.now());
    const timeString = timestamp.toLocaleTimeString() + '.' + timestamp.getMilliseconds().toString().padStart(3, '0');

    const directionIcon = {
        'frontend': '→',
        'backend': '←',
        'system': '↕'
    }[direction] || '•';

    const directionLabel = {
        'frontend': 'Frontend → Backend',
        'backend': 'Backend → Frontend',
        'system': 'System Message'
    }[direction] || 'Unknown';

    const messageSize = JSON.stringify(message).length;
    const preview = getMessagePreviewForTimeline(message);

    messageDiv.innerHTML = `
        <div class="timeline-dot ${direction}"></div>
        <div class="message-bubble ${direction}" onclick="toggleTimelineMessageDetails(this)">
            <div class="message-header">
                <div class="message-direction ${direction}">
                    <span class="direction-arrow">${directionIcon}</span>
                    <span>${directionLabel}</span>
                </div>
                <div class="message-timestamp-detailed">${timeString}</div>
            </div>

            <div class="message-type-badge ${message.type}">${message.type}</div>

            <div class="message-content-preview" id="preview-${Date.now()}">
                ${preview}
                ${preview.length > 200 ? '<div class="message-expand-fade"></div>' : ''}
            </div>

            <div class="message-metadata">
                <div class="message-size-info">
                    <span>Size: ${formatBytes(messageSize)}</span>
                    <span>Type: ${message.type}</span>
                </div>
                <div class="message-actions">
                    <button class="message-action-btn" onclick="event.stopPropagation(); copyMessageToClipboard(this)">Copy</button>
                    <button class="message-action-btn" onclick="event.stopPropagation(); exportSingleMessage(this)">Export</button>
                    <button class="message-action-btn" onclick="event.stopPropagation(); showMessageInModal(this)">Details</button>
                </div>
            </div>

            <div class="expandable-content" style="display: none;">
                <div class="json-viewer">${JSON.stringify(message, null, 2)}</div>
            </div>
        </div>
    `;

    return messageDiv;
}

function getMessagePreviewForTimeline(message) {
    let preview = '';

    switch (message.type) {
        case 'chat_message':
            preview = message.content || message.data?.content || 'Chat message';
            break;
        case 'wheel_data':
            const itemCount = message.wheel?.items?.length || message.data?.wheel?.items?.length || 0;
            preview = `Wheel generated with ${itemCount} activities`;
            if (message.wheel?.items?.length > 0) {
                const firstItem = message.wheel.items[0];
                preview += `\nFirst activity: "${firstItem.title || firstItem.name || 'Unnamed'}"`;
            }
            break;
        case 'debug_info':
            preview = message.data?.message || message.content?.message || message.message || 'Debug information';
            break;
        case 'workflow_status':
            const workflow = message.data?.workflow_id || 'unknown';
            const status = message.data?.status || 'unknown';
            preview = `Workflow "${workflow}" status: ${status}`;
            break;
        case 'system_message':
            preview = message.content || message.message || 'System message';
            break;
        case 'error':
            preview = `❌ Error: ${message.data?.message || message.message || 'Unknown error'}`;
            break;
        default:
            preview = JSON.stringify(message).substring(0, 200);
            if (JSON.stringify(message).length > 200) {
                preview += '...';
            }
    }

    // Escape HTML and limit length
    preview = preview.replace(/</g, '&lt;').replace(/>/g, '&gt;');
    if (preview.length > 300) {
        preview = preview.substring(0, 300) + '...';
    }

    return preview;
}

function updateSessionStatsFromMessage(message) {
    const direction = determineMessageDirection(message);
    const messageSize = JSON.stringify(message).length;

    // Update counters
    if (direction === 'frontend') {
        sessionStats.frontendMessages++;
    } else if (direction === 'backend') {
        sessionStats.backendMessages++;
    } else {
        sessionStats.systemMessages++;
    }

    // Update data transferred
    sessionStats.totalDataTransferred += messageSize;

    // Track errors
    if (message.type === 'error') {
        sessionStats.errorCount++;
    }

    // Track response times (simplified)
    if (direction === 'backend' && sessionStats.frontendMessages > 0) {
        const responseTime = Date.now() - (sessionStats.startTime || Date.now());
        sessionStats.responseTimes.push(responseTime);
    }

    // Update display
    updateSessionStats();
}

function updateSessionStats() {
    document.getElementById('frontendMessageCount').textContent = sessionStats.frontendMessages;
    document.getElementById('backendMessageCount').textContent = sessionStats.backendMessages;
    document.getElementById('systemMessageCount').textContent = sessionStats.systemMessages;
    document.getElementById('totalDataTransferred').textContent = formatBytes(sessionStats.totalDataTransferred);
    document.getElementById('errorCount').textContent = sessionStats.errorCount;

    // Calculate average response time
    const avgResponseTime = sessionStats.responseTimes.length > 0
        ? sessionStats.responseTimes.reduce((a, b) => a + b, 0) / sessionStats.responseTimes.length
        : 0;
    document.getElementById('averageResponseTime').textContent = Math.round(avgResponseTime) + 'ms';
}

function updateTimelineMessageCount() {
    const count = sessionMessages.length;
    document.getElementById('timelineMessageCount').textContent = `(${count} messages)`;
}

function filterTimeline(filter) {
    currentTimelineFilter = filter;

    // Update active filter button
    document.querySelectorAll('.timeline-filter').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // Apply filter to all messages
    const messages = document.querySelectorAll('.timeline-message');
    messages.forEach(messageElement => {
        const direction = messageElement.getAttribute('data-direction');
        const type = messageElement.getAttribute('data-type');
        applyTimelineFilter(messageElement, direction, type);
    });
}

function applyTimelineFilter(messageElement, direction, type) {
    let show = true;

    switch (currentTimelineFilter) {
        case 'frontend':
            show = direction === 'frontend';
            break;
        case 'backend':
            show = direction === 'backend';
            break;
        case 'system':
            show = direction === 'system';
            break;
        case 'errors':
            show = type === 'error';
            break;
        case 'all':
        default:
            show = true;
    }

    messageElement.style.display = show ? 'block' : 'none';
}

function filterTimelineMessages() {
    const searchTerm = document.getElementById('timelineSearch').value.toLowerCase();
    const messages = document.querySelectorAll('.timeline-message');

    messages.forEach(messageElement => {
        const content = messageElement.textContent.toLowerCase();
        const matchesSearch = searchTerm === '' || content.includes(searchTerm);
        const matchesFilter = messageElement.style.display !== 'none' || currentTimelineFilter === 'all';

        messageElement.style.display = matchesSearch && matchesFilter ? 'block' : 'none';
    });
}

function toggleTimelineMessageDetails(element) {
    const expandableContent = element.querySelector('.expandable-content');
    const preview = element.querySelector('.message-content-preview');

    if (expandableContent.style.display === 'none') {
        expandableContent.style.display = 'block';
        preview.classList.add('expanded');
    } else {
        expandableContent.style.display = 'none';
        preview.classList.remove('expanded');
    }
}

function copyMessageToClipboard(button) {
    const messageElement = button.closest('.timeline-message');
    const jsonContent = messageElement.querySelector('.json-viewer').textContent;

    navigator.clipboard.writeText(jsonContent).then(() => {
        button.textContent = 'Copied!';
        setTimeout(() => {
            button.textContent = 'Copy';
        }, 2000);
    });
}

function exportSingleMessage(button) {
    const messageElement = button.closest('.timeline-message');
    const jsonContent = messageElement.querySelector('.json-viewer').textContent;
    const timestamp = messageElement.getAttribute('data-timestamp');
    const type = messageElement.getAttribute('data-type');

    const dataBlob = new Blob([jsonContent], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `message-${type}-${timestamp}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function exportSessionData() {
    if (!focusedSessionId) {
        alert('No session is currently focused');
        return;
    }

    const exportData = {
        session_id: focusedSessionId,
        export_timestamp: new Date().toISOString(),
        session_stats: sessionStats,
        messages: sessionMessages,
        message_count: sessionMessages.length,
        timeline_filter: currentTimelineFilter
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `session-${focusedSessionId.substring(0, 8)}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log(`Exported session data for ${focusedSessionId}`);
}

function showMessageInModal(button) {
    const messageElement = button.closest('.timeline-message');
    const jsonContent = messageElement.querySelector('.json-viewer').textContent;
    const type = messageElement.getAttribute('data-type');
    const direction = messageElement.getAttribute('data-direction');

    // Create and show modal with message details
    const modal = document.getElementById('connectionModal');
    const content = document.getElementById('connectionModalContent');

    content.innerHTML = `
        <div style="margin-bottom: 20px;">
            <h3>Message Details</h3>
            <div style="display: flex; gap: 16px; margin: 12px 0; font-size: 0.9rem;">
                <span><strong>Type:</strong> ${type}</span>
                <span><strong>Direction:</strong> ${direction}</span>
                <span><strong>Session:</strong> ${focusedSessionId?.substring(0, 8)}...</span>
            </div>
        </div>
        <div class="json-viewer" style="max-height: 60vh; overflow-y: auto;">${jsonContent}</div>
    `;

    modal.classList.add('show');
}

// Update the connection list to support session focusing
function updateConnectionList(connections) {
    const connectionList = document.getElementById('connectionList');

    if (!connections || connections.length === 0) {
        connectionList.innerHTML = '<div class="connection-item" style="text-align: center; color: #666;"><span>No active connections</span></div>';
        return;
    }

    connectionList.innerHTML = connections.map(conn => `
        <div class="connection-item" onclick="handleConnectionClick('${conn.session_id}', ${JSON.stringify(conn).replace(/"/g, '&quot;')})">
            <div class="connection-status ${conn.status}"></div>
            <div class="connection-info">
                <div class="connection-user">User ${conn.user_id}</div>
                <div class="connection-details">
                    Session: ${conn.session_id.substring(0, 8)}... •
                    ${conn.current_workflow || 'No workflow'} •
                    ${conn.ip_address || 'Unknown IP'}
                </div>
            </div>
            <div class="connection-metrics">
                <div>${conn.message_count} messages</div>
                <div>Connected ${conn.duration}</div>
                <div style="font-size: 0.7rem; color: #999;">
                    ${focusedSessionId === conn.session_id ? '🎯 Focused' : 'Click to focus/details'}
                </div>
            </div>
        </div>
    `).join('');

    // Update active connections count
    document.getElementById('activeConnections').textContent = connections.length;
}

function handleConnectionClick(sessionId, connectionData) {
    // If session focus is visible, focus on this session
    if (sessionFocusVisible) {
        focusOnSession(sessionId, connectionData);
    } else {
        // Otherwise show connection details modal
        showConnectionDetails(sessionId);
    }
}

// Connection Management Functions
function cleanStaleConnections() {
    if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
        monitorSocket.send(JSON.stringify({type: 'clean_connections'}));
        console.log('🧹 Requesting stale connection cleanup...');
    } else {
        console.error('❌ WebSocket not connected');
        alert('WebSocket not connected. Please refresh the page.');
    }
}

function detectConnectionLeaks() {
    if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
        monitorSocket.send(JSON.stringify({type: 'detect_leaks'}));
        console.log('🔍 Requesting connection leak detection...');
    } else {
        console.error('❌ WebSocket not connected');
        alert('WebSocket not connected. Please refresh the page.');
    }
}

function forceCleanupConnections() {
    if (confirm('⚠️ WARNING: This will forcefully clean ALL connections. This may disrupt active users. Are you sure?')) {
        if (monitorSocket && monitorSocket.readyState === WebSocket.OPEN) {
            monitorSocket.send(JSON.stringify({type: 'force_cleanup'}));
            console.log('⚠️ Requesting force cleanup...');
        } else {
            console.error('❌ WebSocket not connected');
            alert('WebSocket not connected. Please refresh the page.');
        }
    }
}

function handleConnectionCleanupResult(data) {
    console.log('✅ Connection cleanup result:', data);

    // Show notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 16px 24px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        font-weight: 600;
    `;
    notification.innerHTML = `
        🧹 Cleanup Complete<br>
        <small>Cleaned ${data.cleaned_count} stale connections</small>
    `;

    document.body.appendChild(notification);
    setTimeout(() => {
        document.body.removeChild(notification);
    }, 5000);

    // Refresh connection data
    requestConnectionData();
}

function handleLeakDetectionResult(data) {
    console.log('🔍 Leak detection result:', data);

    // Create detailed modal for leak detection results
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        border-radius: 16px;
        padding: 24px;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    `;

    let indicatorsHtml = '';
    if (data.leak_indicators && data.leak_indicators.length > 0) {
        indicatorsHtml = data.leak_indicators.map(indicator => {
            const severityColor = indicator.severity === 'warning' ? '#ffc107' : '#17a2b8';
            return `
                <div style="margin: 12px 0; padding: 12px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid ${severityColor};">
                    <strong>${indicator.type.replace(/_/g, ' ').toUpperCase()}</strong><br>
                    <small>Count: ${indicator.count || 'N/A'} | Severity: ${indicator.severity}</small>
                </div>
            `;
        }).join('');
    } else {
        indicatorsHtml = '<div style="color: #28a745; text-align: center; padding: 20px;">✅ No connection leaks detected!</div>';
    }

    content.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3>🔍 Connection Leak Detection Results</h3>
            <button onclick="this.closest('.leak-modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">&times;</button>
        </div>
        <div style="margin-bottom: 16px;">
            <strong>Summary:</strong> ${data.total_indicators} potential issues found<br>
            <small>Admin connections: ${data.admin_connections_count} | Memory connections: ${data.memory_connections_count}</small>
        </div>
        ${indicatorsHtml}
        <div style="text-align: center; margin-top: 20px;">
            <button onclick="this.closest('.leak-modal').remove()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">Close</button>
        </div>
    `;

    modal.className = 'leak-modal';
    modal.appendChild(content);
    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function handleForceCleanupResult(data) {
    console.log('⚠️ Force cleanup result:', data);

    // Show warning notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 16px 24px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        font-weight: 600;
    `;
    notification.innerHTML = `
        ⚠️ Force Cleanup Complete<br>
        <small>Removed ${data.cleaned_count} connections</small>
    `;

    document.body.appendChild(notification);
    setTimeout(() => {
        document.body.removeChild(notification);
    }, 7000);

    // Refresh connection data
    requestConnectionData();
}

function handleConnectionManagementError(data) {
    console.error('❌ Connection management error:', data);

    // Show error notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 16px 24px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        font-weight: 600;
    `;
    notification.innerHTML = `
        ❌ Operation Failed<br>
        <small>${data.error || 'Unknown error occurred'}</small>
    `;

    document.body.appendChild(notification);
    setTimeout(() => {
        document.body.removeChild(notification);
    }, 7000);
}
</script>
{% endblock %}
