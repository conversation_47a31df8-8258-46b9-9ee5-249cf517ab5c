"""
Tests for the benchmark history view.

This module contains tests for the benchmark history view in the admin tools.
"""

import pytest
from datetime import datetime, timedelta
from django.urls import reverse
from django.contrib.auth.models import User
from apps.main.models import (
    BenchmarkScenario, BenchmarkRun, GenericAgent, BenchmarkTag, LLMConfig
)


@pytest.fixture
def admin_user(db):
    """Get or create an admin user for testing."""
    user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True,
        }
    )
    if created:
        user.set_password('password')
        user.save()
    return user


@pytest.fixture
def generic_agent(db):
    """Get or create a generic agent for testing."""
    agent, created = GenericAgent.objects.get_or_create(
        role='mentor',
        defaults={
            'description': 'Test agent for benchmarking',
            'system_instructions': 'Test system instructions',
            'input_schema': {},
            'output_schema': {},
            'langgraph_node_class': 'apps.main.agents.mentor_agent.MentorAgent',
            'version': '1.0.0',
            'is_active': True
        }
    )
    return agent


@pytest.fixture
def llm_config(db):
    """Create an LLM config for testing."""
    config = LLMConfig.objects.create(
        name='test-gpt-4',
        model_name='gpt-4-1106-preview',
        temperature=0.7,
        input_token_price=0.01,
        output_token_price=0.03,
        is_default=True,
        is_evaluation=False
    )
    return config


@pytest.fixture
def benchmark_tag(db):
    """Create a benchmark tag for testing."""
    tag = BenchmarkTag.objects.create(name='test-tag')
    return tag


@pytest.fixture
def benchmark_scenario(db, generic_agent, benchmark_tag):
    """Create a benchmark scenario for testing."""
    scenario = BenchmarkScenario.objects.create(
        name='Test Scenario',
        description='Test scenario for benchmarking',
        agent_role='mentor',
        input_data={'test': 'data'},
        metadata={'workflow_type': 'discussion'},
        is_active=True,
        version=1,
        is_latest=True
    )
    scenario.tags.add(benchmark_tag)
    return scenario


@pytest.fixture
def benchmark_run(db, benchmark_scenario, generic_agent, llm_config):
    """Create a benchmark run for testing."""
    run = BenchmarkRun.objects.create(
        scenario=benchmark_scenario,
        agent_definition=generic_agent,
        agent_version='1.0.0',
        llm_config=llm_config,  # Add LLM config
        parameters={'test': 'params'},
        runs_count=3,
        mean_duration=1000,
        median_duration=950,
        min_duration=900,
        max_duration=1100,
        std_dev=50,
        success_rate=75.0,
        llm_calls=10,
        tool_calls=5,
        tool_breakdown={'test_tool': 5},
        tool_call_details={},  # Add missing field for test
        memory_operations=3,
        semantic_score=0.85,
        semantic_evaluation_details={'test': 'details'},
        total_input_tokens=800,
        total_output_tokens=200,
        estimated_cost=0.02
    )
    return run


@pytest.mark.django_db
class TestBenchmarkHistory:
    """Tests for the benchmark history view."""

    def test_benchmark_history_view(self, client, admin_user, benchmark_run):
        """Test the benchmark history view."""
        # Login as admin
        client.force_login(admin_user)

        # Use the game_of_life_admin namespace for the URL
        url = reverse('game_of_life_admin:benchmark_history')
        response = client.get(url)

        # Check response status
        assert response.status_code == 200

        # Check context variables
        assert 'runs' in response.context
        assert 'agent_roles' in response.context
        assert 'tags' in response.context

        # Check that the run is in the context
        assert list(response.context['runs'])[0].id == benchmark_run.id

    def test_benchmark_history_with_agent_role_filter(self, client, admin_user, benchmark_run):
        """Test the benchmark history view with agent role filter."""
        # Login as admin
        client.force_login(admin_user)

        # Use the game_of_life_admin namespace for the URL
        url = reverse('game_of_life_admin:benchmark_history_by_agent', kwargs={'agent_role': 'mentor'})
        response = client.get(url)

        # Check response status
        assert response.status_code == 200

        # Check context variables
        assert 'runs' in response.context
        assert 'selected_agent_role' in response.context
        assert response.context['selected_agent_role'] == 'mentor'

    def test_benchmark_history_with_tag_filter(self, client, admin_user, benchmark_run, benchmark_tag):
        """Test the benchmark history view with tag filter."""
        # Login as admin
        client.force_login(admin_user)

        # Use the game_of_life_admin namespace for the URL
        url = reverse('game_of_life_admin:benchmark_history')
        response = client.get(url, {'tag': benchmark_tag.name})

        # Check response status
        assert response.status_code == 200

        # Check context variables
        assert 'runs' in response.context
        assert 'selected_tag' in response.context
        assert response.context['selected_tag'] == benchmark_tag.name

    def test_benchmark_history_with_date_filters(self, client, admin_user, benchmark_run):
        """Test the benchmark history view with date filters."""
        # Login as admin
        client.force_login(admin_user)

        # Set dates for filtering
        today = datetime.now().date().isoformat()
        yesterday = (datetime.now() - timedelta(days=1)).date().isoformat()
        tomorrow = (datetime.now() + timedelta(days=1)).date().isoformat()

        # Use the game_of_life_admin namespace for the URL
        url = reverse('game_of_life_admin:benchmark_history')

        # Test with start_date only
        response = client.get(url, {'start_date': yesterday})
        assert response.status_code == 200

        # Test with end_date only
        response = client.get(url, {'end_date': tomorrow})
        assert response.status_code == 200

        # Test with both start_date and end_date
        response = client.get(url, {'start_date': yesterday, 'end_date': tomorrow})
        assert response.status_code == 200
