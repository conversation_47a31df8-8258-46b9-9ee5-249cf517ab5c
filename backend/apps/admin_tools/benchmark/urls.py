# ACTIVE_FILE - 29-05-2025
"""
URL patterns for benchmark management views.

These URLs are not used directly, but are included in the custom admin site's
get_urls() method in config/admin.py.
"""

from django.urls import path
from . import views

# These URL patterns are for reference only and are not used directly.
# The actual URLs are defined in config/admin.py in the custom admin site's get_urls() method.
urlpatterns = [
    # Benchmark Management
    path('benchmarks/manage/', views.benchmark_management, name='benchmark_management'),

    # Benchmark History
    path('benchmarks/history/', views.benchmark_history, name='benchmark_history'),
    path('benchmarks/history/<str:agent_role>/', views.benchmark_history, name='benchmark_history_by_agent'),

    # Benchmark API Endpoints
    path('benchmarks/api/scenarios/', views.BenchmarkScenarioView.as_view(), name='benchmark_scenarios_api'),
    path('benchmarks/api/scenarios/<int:scenario_id>/', views.BenchmarkScenarioView.as_view(), name='benchmark_scenario_detail_api'),

    # Workflow Type API Endpoint
    path('benchmarks/api/workflow-types/', views.WorkflowTypeView.as_view(), name='workflow_types_api'),

    # Evaluation Criteria Template API Endpoints
    path('benchmarks/api/templates/', views.EvaluationCriteriaTemplateView.as_view(), name='evaluation_templates_api'),
    path('benchmarks/api/templates/<int:template_id>/', views.EvaluationCriteriaTemplateView.as_view(), name='evaluation_template_detail_api'),

    # Benchmark Validation API Endpoint
    path('benchmarks/api/validate/', views.BenchmarkValidationView.as_view(), name='benchmark_validation_api'),
    path('benchmarks/api/validate/<int:scenario_id>/', views.BenchmarkValidationView.as_view(), name='benchmark_scenario_validation_api'),

    # Benchmark Import/Export
    path('benchmarks/export/', views.export_scenarios, name='export_scenarios'),
    path('benchmarks/import/', views.import_scenarios, name='import_scenarios'),

    # Run All Benchmarks API Endpoint
    path('benchmarks/api/run-all/', views.run_all_benchmarks_view, name='run_all_benchmarks'),

    # User Profile API Endpoint
    path('benchmarks/api/user-profiles/', views.UserProfileView.as_view(), name='user_profiles_api'),

    # Quick Benchmark API Endpoints
    path('benchmarks/api/quick-benchmark/', views.QuickBenchmarkView.as_view(), name='quick_benchmark_api'),

    # Workflow-Aware Benchmark API Endpoints
    path('benchmarks/api/workflow-benchmark/', views.WorkflowBenchmarkAPIView.as_view(), name='workflow_benchmark_api'),
    path('benchmarks/api/evaluation-contexts/', views.EvaluationContextAPIView.as_view(), name='evaluation_contexts_api'),
]
