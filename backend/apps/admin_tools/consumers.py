import json
import logging
import async<PERSON>
from datetime import datetime, timezone
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from channels.layers import get_channel_layer
from django.conf import settings
import redis

logger = logging.getLogger(__name__)

class BenchmarkDashboardConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for the benchmark admin dashboard.
    Sends error and debug events immediately to connected staff users.
    """

    async def connect(self):
        user = self.scope.get('user')
        if not user or not user.is_authenticated or not user.is_staff:
            logger.warning(f"BenchmarkDashboardConsumer: Unauthorized connection attempt.")
            await self.close()
            return

        self.group_name = "benchmark_dashboard"

        # Accept connection
        await self.accept()

        # Add this channel to the benchmark_dashboard group
        await self.channel_layer.group_add(self.group_name, self.channel_name)
        logger.info(f"BenchmarkDashboardConsumer: User '{user.username}' connected and added to group '{self.group_name}'.")

    async def disconnect(self, close_code):
        # Remove this channel from the group
        await self.channel_layer.group_discard(self.group_name, self.channel_name)
        logger.info(f"BenchmarkDashboardConsumer: Disconnected channel {self.channel_name} from group '{self.group_name}'.")

    async def receive(self, text_data=None, bytes_data=None):
        # This consumer is read-only from client side; ignore any received messages
        pass

    # Handler for events sent to the group
    async def debug_info(self, event):
        # Forward debug_info events to the WebSocket client
        await self.send(text_data=json.dumps(event))

    async def error(self, event):
        # Forward error events to the WebSocket client
        await self.send(text_data=json.dumps(event))

    async def tool_argument_error(self, event):
        # Forward tool_argument_error events to the WebSocket client
        await self.send(text_data=json.dumps(event))


class AdminTesterConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for admin testing purposes.
    Accepts connections from authenticated staff users and joins 'admin_tester' group.
    """

    async def connect(self):
        user = self.scope.get('user')
        if not user or not user.is_authenticated or not user.is_staff:
            logger.warning(f"AdminTesterConsumer: Unauthorized connection attempt.")
            await self.close()
            return

        self.group_name = "admin_tester"

        # Accept connection
        await self.accept()

        # Add this channel to the admin_tester group
        await self.channel_layer.group_add(self.group_name, self.channel_name)
        logger.info(f"AdminTesterConsumer: User '{user.username}' connected and added to group '{self.group_name}'.")

    async def disconnect(self, close_code):
        # Remove this channel from the group
        await self.channel_layer.group_discard(self.group_name, self.channel_name)
        logger.info(f"AdminTesterConsumer: Disconnected channel {self.channel_name} from group '{self.group_name}'.")

    async def receive(self, text_data=None, bytes_data=None):
        # Echo received messages back to the client as JSON
        if text_data:
            await self.send(text_data=json.dumps({"echo": text_data}))

    # Example handler for group messages
    async def admin_test_event(self, event):
        # Forward admin_test_event to the WebSocket client
        await self.send(text_data=json.dumps(event))


class ConnectionMonitorConsumer(AsyncWebsocketConsumer):
    """
    Enhanced WebSocket consumer for real-time connection monitoring and debugging.

    Provides comprehensive monitoring of:
    - Active WebSocket connections
    - Message flow between frontend and backend
    - System health metrics
    - Real-time debugging information
    """

    # Class-level connection registry shared across all instances
    _global_connections = {}
    _global_stats = {
        'messages_per_minute': 0,
        'total_messages_today': 0,
        'error_rate': 0.02,
        'active_workflows': 0
    }

    # Class-level list of active consumer instances for broadcasting
    _active_consumers = []

    # Class-level logging state tracking for conditional logging
    _last_logged_global_connections_count = -1
    _last_logged_global_connections_keys = set()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.redis_client = None
        self.monitoring_tasks = []
        self.update_interval = 5  # seconds
        self.message_monitoring_enabled = False  # For real-time message monitoring
        self.session_monitoring_enabled = False  # For session-focused monitoring
        self.focused_session_id = None  # Currently focused session

        # In-memory tracking for when Redis is not available
        # Always use the class-level global connections for consistency
        self.in_memory_connections = self.__class__._global_connections
        self.in_memory_stats = self.__class__._global_stats

        # Variables to store last logged values for conditional logging
        self._last_logged_connections_count = -1
        self._last_logged_validated_connections_count = -1
        self._last_logged_in_memory_connections_count = -1
        self._last_logged_in_memory_connections_keys = set()
        self._last_logged_total_connections_count = -1
        self._last_logged_global_connections_count = -1
        self._last_logged_global_connections_keys = set()

        # Connection state tracking
        self.is_connected = False

    async def connect(self):
        """Accept connection for authenticated staff users only."""
        user = self.scope.get('user')

        # EMERGENCY FIX: Clean up stale consumers before checking limits
        try:
            self.__class__._active_consumers = [
                consumer for consumer in self.__class__._active_consumers
                if hasattr(consumer, 'is_connected') and getattr(consumer, 'is_connected', False)
            ]
        except Exception as e:
            logger.error(f"Error cleaning stale consumers during connect: {e}")

        # Strict connection limits to prevent leaks
        current_connections = len(self.__class__._active_consumers)
        MAX_MONITOR_CONNECTIONS = 5  # Increased slightly to allow for normal usage

        if current_connections >= MAX_MONITOR_CONNECTIONS:
            logger.warning(f"ConnectionMonitorConsumer: Connection limit reached ({current_connections}/{MAX_MONITOR_CONNECTIONS}). Rejecting connection.")
            await self.close(code=1013)  # Try again later
            return

        # TODO: Remove this bypass for production - only for testing
        bypass_auth = True  # Set to False for production

        if not bypass_auth and (not user or not user.is_authenticated or not user.is_staff):
            logger.warning(f"ConnectionMonitorConsumer: Unauthorized connection attempt.")
            await self.close()
            return

        # Set group name early to avoid AttributeError in disconnect
        self.group_name = "connection_monitor"

        # Initialize Redis connection
        await self._init_redis()

        # Accept connection
        await self.accept()

        # Set connected flag
        self.is_connected = True

        # Add to monitoring group
        await self.channel_layer.group_add(self.group_name, self.channel_name)

        # Add this consumer to the active consumers list for broadcasting
        if self not in self.__class__._active_consumers:
            self.__class__._active_consumers.append(self)

        # Start monitoring tasks
        await self._start_monitoring()

        # Set this as the global monitor for connection tracking
        set_global_connection_monitor(self)

        username = user.username if user and hasattr(user, 'username') else 'anonymous'
        logger.info(f"ConnectionMonitorConsumer: User '{username}' connected to connection monitor. Active connections: {len(self.__class__._active_consumers)}")

    async def disconnect(self, close_code):
        """Clean up monitoring tasks and connections."""
        # Set disconnected flag to prevent further message sending
        self.is_connected = False

        # Stop monitoring tasks
        for task in self.monitoring_tasks:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logger.error(f"Error cancelling monitoring task: {e}")

        # Remove from group - only if group_name was set (connection was accepted)
        try:
            if hasattr(self, 'group_name') and self.group_name:
                await self.channel_layer.group_discard(self.group_name, self.channel_name)
        except Exception as e:
            logger.error(f"Error removing from group during disconnect: {e}")

        # Remove this consumer from the active consumers list
        try:
            if self in self.__class__._active_consumers:
                self.__class__._active_consumers.remove(self)
        except Exception as e:
            logger.error(f"Error removing from active consumers: {e}")

        # EMERGENCY CLEANUP: Force cleanup of stale consumers
        try:
            # Remove any stale consumers that might be lingering
            self.__class__._active_consumers = [
                consumer for consumer in self.__class__._active_consumers
                if hasattr(consumer, 'is_connected') and consumer.is_connected
            ]
            logger.info(f"ConnectionMonitorConsumer: Cleaned up stale consumers. Active: {len(self.__class__._active_consumers)}")
        except Exception as e:
            logger.error(f"Error in emergency cleanup: {e}")

        logger.info(f"ConnectionMonitorConsumer: Disconnected with code {close_code}. Remaining connections: {len(self.__class__._active_consumers)}")

    async def receive(self, text_data=None, bytes_data=None):
        """Handle incoming messages from the admin dashboard."""
        if not text_data:
            return

        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'get_connections':
                await self._send_connection_data()
            elif message_type == 'get_system_health':
                await self._send_system_health()
            elif message_type == 'get_message_stats':
                await self._send_message_stats()
            elif message_type == 'get_connection_details':
                session_id = data.get('session_id')
                await self._send_connection_details(session_id)
            elif message_type == 'start_message_monitoring':
                await self._start_message_monitoring()
            elif message_type == 'stop_message_monitoring':
                await self._stop_message_monitoring()
            elif message_type == 'start_session_monitoring':
                await self._start_session_monitoring()
            elif message_type == 'stop_session_monitoring':
                await self._stop_session_monitoring()
            elif message_type == 'focus_session':
                session_id = data.get('session_id')
                await self._focus_session(session_id)
            elif message_type == 'disconnect_user':
                user_id = data.get('user_id')
                await self._disconnect_user(user_id)
            elif message_type == 'clean_connections':
                await self._clean_stale_connections()
            elif message_type == 'detect_leaks':
                await self._detect_connection_leaks()
            elif message_type == 'force_cleanup':
                await self._force_cleanup_connections()
            else:
                logger.warning(f"Unknown message type: {message_type}")

        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received: {text_data}")

    async def _init_redis(self):
        """Initialize Redis connection for session tracking."""
        try:
            channel_layers = getattr(settings, 'CHANNEL_LAYERS', {})
            config = channel_layers.get('default', {}).get('CONFIG', {})
            hosts = config.get('hosts', [])

            if hosts:
                host_info = hosts[0]
                if isinstance(host_info, tuple):
                    # Tuple format: (host, port)
                    host, port = host_info
                    self.redis_client = redis.Redis(
                        host=host,
                        port=port,
                        decode_responses=True,
                        socket_connect_timeout=5,
                        socket_timeout=5
                    )
                    logger.info(f"ConnectionMonitorConsumer: Connecting to Redis at {host}:{port}")
                elif isinstance(host_info, dict) and 'address' in host_info:
                    # Dictionary format with 'address' key (for SSL configs)
                    redis_url = host_info['address']
                    self.redis_client = redis.from_url(
                        redis_url,
                        decode_responses=True,
                        socket_connect_timeout=5,
                        socket_timeout=5
                    )
                    logger.info(f"ConnectionMonitorConsumer: Connecting to Redis via URL: {redis_url}")
                else:
                    # String format: full Redis URL
                    redis_url = host_info
                    self.redis_client = redis.from_url(
                        redis_url,
                        decode_responses=True,
                        socket_connect_timeout=5,
                        socket_timeout=5
                    )
                    logger.info(f"ConnectionMonitorConsumer: Connecting to Redis via URL: {redis_url}")

                # Test the connection
                await asyncio.get_event_loop().run_in_executor(None, self.redis_client.ping)
                logger.info("ConnectionMonitorConsumer: Redis connection successful")
            else:
                logger.warning("ConnectionMonitorConsumer: No Redis hosts configured")

        except Exception as e:
            logger.error(f"ConnectionMonitorConsumer: Failed to connect to Redis: {e}")
            self.redis_client = None

    async def _start_monitoring(self):
        """Start background monitoring tasks."""
        # Task to periodically send connection updates
        self.monitoring_tasks.append(
            asyncio.create_task(self._periodic_updates())
        )

    async def _periodic_updates(self):
        """Send periodic updates to connected clients."""
        update_count = 0
        max_updates = 720  # 720 * 5 seconds = 1 hour max runtime

        while update_count < max_updates:
            try:
                # Check if connection is still active before each update
                if not getattr(self, 'is_connected', False):
                    logger.info("Periodic updates stopping - connection no longer active")
                    break

                await asyncio.sleep(self.update_interval)

                # Use timeout for each update operation to prevent hanging
                try:
                    await asyncio.wait_for(self._send_connection_data(), timeout=10.0)
                    await asyncio.wait_for(self._send_system_health(), timeout=10.0)
                    await asyncio.wait_for(self._send_message_stats(), timeout=10.0)
                except asyncio.TimeoutError:
                    logger.warning("Periodic update operation timed out")
                    continue

                update_count += 1

            except asyncio.CancelledError:
                logger.info("Periodic updates cancelled")
                break
            except Exception as e:
                logger.error(f"Error in periodic updates: {e}")
                # Add a small delay before retrying to prevent tight error loops
                await asyncio.sleep(1.0)

        logger.info(f"Periodic updates completed after {update_count} cycles")

    async def _send_connection_data(self):
        """Send current connection data to clients."""
        # Check if connection is still active
        if not self.is_connected:
            return

        try:
            # Reduced debug logging to prevent performance issues
            connections = await self._get_active_connections()
            # Track connection count changes without excessive logging
            self._last_logged_connections_count = len(connections)

            # Validate connection data before sending
            validated_connections = []
            for conn in connections:
                try:
                    # Ensure all required fields are present and valid
                    validated_conn = {
                        'session_id': str(conn.get('session_id', 'unknown')),
                        'user_id': str(conn.get('user_id', 'Unknown')),
                        'connected_at': conn.get('connected_at', datetime.now(timezone.utc).isoformat()),
                        'duration': str(conn.get('duration', 'Unknown')),
                        'last_activity': conn.get('last_activity', datetime.now(timezone.utc).isoformat()),
                        'message_count': int(conn.get('message_count', 0)),
                        'current_workflow': conn.get('current_workflow'),
                        'status': str(conn.get('status', 'connected'))
                    }
                    validated_connections.append(validated_conn)
                except (ValueError, TypeError) as ve:
                    logger.warning(f"🔧 DEBUG: Skipping invalid connection data: {conn}, error: {ve}")
                    continue

            message_data = {
                'type': 'connection_data',
                'data': validated_connections,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            if len(validated_connections) != self._last_logged_validated_connections_count:
                # Reduced debug logging
                self._last_logged_validated_connections_count = len(validated_connections)
            await self.send(text_data=json.dumps(message_data))
            #logger.debug(f"🔧 DEBUG: Connection data sent successfully")

        except (TypeError, ValueError) as je:
            logger.error(f"🔧 ERROR: JSON encoding error in _send_connection_data: {je}")
        except Exception as e:
            logger.error(f"🔧 ERROR: Error sending connection data: {e}")
            logger.error(f"🔧 ERROR: Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"🔧 ERROR: Traceback: {traceback.format_exc()}")

    async def _send_system_health(self):
        """Send system health metrics to clients."""
        # Check if connection is still active
        if not self.is_connected:
            return

        try:
            health = await self._get_system_health()
            await self.send(text_data=json.dumps({
                'type': 'system_health',
                'data': health,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))
        except Exception as e:
            logger.error(f"Error sending system health: {e}")

    async def _send_message_stats(self):
        """Send message statistics to clients."""
        # Check if connection is still active
        if not self.is_connected:
            return

        try:
            stats = await self._get_message_stats()
            await self.send(text_data=json.dumps({
                'type': 'message_stats',
                'data': stats,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))
        except Exception as e:
            logger.error(f"Error sending message stats: {e}")

    async def _send_connection_details(self, session_id):
        """Send detailed information about a specific connection."""
        try:
            if not session_id:
                await self.send(text_data=json.dumps({
                    'type': 'connection_details',
                    'error': 'No session ID provided',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }))
                return

            # Look for connection in in-memory connections first
            connection_details = None

            if session_id in self.in_memory_connections:
                connection_details = self.in_memory_connections[session_id].copy()
                logger.debug(f"Found connection details in memory for {session_id}")

            # If not found in memory, check Redis
            elif self.redis_client:
                try:
                    key = f"ws_session:{session_id}"
                    session_data = await asyncio.get_event_loop().run_in_executor(
                        None, self.redis_client.hgetall, key
                    )
                    if session_data:
                        connection_details = session_data
                        logger.debug(f"Found connection details in Redis for {session_id}")
                except Exception as e:
                    logger.error(f"Error fetching connection details from Redis: {e}")

            if connection_details:
                # Enhance details with additional information
                enhanced_details = {
                    'session_id': session_id,
                    'user_id': connection_details.get('user_id', 'Unknown'),
                    'connected_at': connection_details.get('connected_at'),
                    'last_activity': connection_details.get('last_activity'),
                    'message_count': int(connection_details.get('message_count', 0)),
                    'current_workflow': connection_details.get('current_workflow'),
                    'status': connection_details.get('status', 'connected'),
                    'ip_address': connection_details.get('ip_address', 'Unknown'),
                    'user_agent': connection_details.get('user_agent', 'Unknown'),
                    'connection_type': 'WebSocket',
                    'protocol_version': 'ws',
                    'last_ping': connection_details.get('last_ping'),
                    'total_bytes_sent': connection_details.get('total_bytes_sent', 0),
                    'total_bytes_received': connection_details.get('total_bytes_received', 0)
                }

                # Calculate connection duration
                connected_at = connection_details.get('connected_at')
                if connected_at:
                    try:
                        connected_time = datetime.fromisoformat(connected_at)
                        duration = datetime.now(timezone.utc) - connected_time
                        enhanced_details['duration_seconds'] = int(duration.total_seconds())
                        enhanced_details['duration_human'] = str(duration).split('.')[0]
                    except Exception:
                        enhanced_details['duration_seconds'] = 0
                        enhanced_details['duration_human'] = 'Unknown'

                await self.send(text_data=json.dumps({
                    'type': 'connection_details',
                    'data': enhanced_details,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }))
                logger.debug(f"Sent connection details for {session_id}")
            else:
                await self.send(text_data=json.dumps({
                    'type': 'connection_details',
                    'error': f'Connection {session_id} not found',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }))
                logger.warning(f"Connection details not found for {session_id}")

        except Exception as e:
            logger.error(f"Error sending connection details: {e}")
            await self.send(text_data=json.dumps({
                'type': 'connection_details',
                'error': f'Internal error: {str(e)}',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))

    async def _start_message_monitoring(self):
        """Start real-time message monitoring."""
        try:
            # Enable message flow monitoring
            self.message_monitoring_enabled = True
            await self.send(text_data=json.dumps({
                'type': 'message_monitoring_started',
                'message': 'Real-time message monitoring enabled',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))
            logger.info("Message monitoring started for admin dashboard")
        except Exception as e:
            logger.error(f"Error starting message monitoring: {e}")

    async def _stop_message_monitoring(self):
        """Stop real-time message monitoring."""
        try:
            # Disable message flow monitoring
            self.message_monitoring_enabled = False
            await self.send(text_data=json.dumps({
                'type': 'message_monitoring_stopped',
                'message': 'Real-time message monitoring disabled',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))
            logger.info("Message monitoring stopped for admin dashboard")
        except Exception as e:
            logger.error(f"Error stopping message monitoring: {e}")

    async def _start_session_monitoring(self):
        """Start session-focused monitoring."""
        try:
            self.session_monitoring_enabled = True
            await self.send(text_data=json.dumps({
                'type': 'session_monitoring_started',
                'message': 'Session-focused monitoring enabled',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))
            logger.info("Session monitoring started for admin dashboard")
        except Exception as e:
            logger.error(f"Error starting session monitoring: {e}")

    async def _stop_session_monitoring(self):
        """Stop session-focused monitoring."""
        try:
            self.session_monitoring_enabled = False
            self.focused_session_id = None
            await self.send(text_data=json.dumps({
                'type': 'session_monitoring_stopped',
                'message': 'Session-focused monitoring disabled',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))
            logger.info("Session monitoring stopped for admin dashboard")
        except Exception as e:
            logger.error(f"Error stopping session monitoring: {e}")

    async def _focus_session(self, session_id):
        """Focus monitoring on a specific session."""
        try:
            if not session_id:
                await self.send(text_data=json.dumps({
                    'type': 'session_focus_error',
                    'error': 'No session ID provided',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }))
                return

            self.focused_session_id = session_id
            self.session_monitoring_enabled = True

            await self.send(text_data=json.dumps({
                'type': 'session_focused',
                'session_id': session_id,
                'message': f'Now monitoring session: {session_id}',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))

            logger.info(f"Admin dashboard now focusing on session: {session_id}")

            # Send recent messages for this session if available
            await self._send_session_history(session_id)

        except Exception as e:
            logger.error(f"Error focusing on session {session_id}: {e}")
            await self.send(text_data=json.dumps({
                'type': 'session_focus_error',
                'error': f'Failed to focus on session: {str(e)}',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))

    async def _send_session_history(self, session_id):
        """Send recent message history for a focused session."""
        try:
            # This would typically fetch from a message log/database
            # For now, we'll send a placeholder indicating session focus is active
            await self.send(text_data=json.dumps({
                'type': 'session_history',
                'session_id': session_id,
                'messages': [],  # Would contain recent messages
                'message': f'Session {session_id} history loaded',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))
            logger.debug(f"Sent session history for {session_id}")
        except Exception as e:
            logger.error(f"Error sending session history: {e}")

    async def _clean_stale_connections(self):
        """Clean up stale connections that are no longer active."""
        try:
            cleaned_count = 0
            stale_sessions = []

            # Check in-memory connections for stale ones
            current_time = datetime.now(timezone.utc)
            for session_id, data in list(self.in_memory_connections.items()):
                last_activity = data.get('last_activity')
                if last_activity:
                    try:
                        last_activity_time = datetime.fromisoformat(last_activity)
                        time_since_activity = current_time - last_activity_time

                        # Consider connections stale if no activity for more than 30 minutes
                        if time_since_activity.total_seconds() > 1800:  # 30 minutes
                            stale_sessions.append(session_id)
                            del self.in_memory_connections[session_id]
                            cleaned_count += 1
                    except Exception as e:
                        logger.warning(f"Error parsing last_activity for {session_id}: {e}")
                        # Remove sessions with invalid timestamps
                        stale_sessions.append(session_id)
                        del self.in_memory_connections[session_id]
                        cleaned_count += 1

            # Clean up Redis connections as well
            if self.redis_client:
                try:
                    session_keys = await asyncio.get_event_loop().run_in_executor(
                        None, self.redis_client.keys, "ws_session:*"
                    )

                    for key in session_keys:
                        session_data = await asyncio.get_event_loop().run_in_executor(
                            None, self.redis_client.hgetall, key
                        )

                        if session_data:
                            last_activity = session_data.get('last_activity')
                            if last_activity:
                                try:
                                    last_activity_time = datetime.fromisoformat(last_activity)
                                    time_since_activity = current_time - last_activity_time

                                    if time_since_activity.total_seconds() > 1800:  # 30 minutes
                                        session_id = key.split(':')[1]
                                        await asyncio.get_event_loop().run_in_executor(
                                            None, self.redis_client.delete, key
                                        )
                                        if session_id not in stale_sessions:
                                            stale_sessions.append(session_id)
                                            cleaned_count += 1
                                except Exception as e:
                                    logger.warning(f"Error processing Redis session {key}: {e}")
                                    # Delete sessions with invalid data
                                    await asyncio.get_event_loop().run_in_executor(
                                        None, self.redis_client.delete, key
                                    )
                                    session_id = key.split(':')[1]
                                    if session_id not in stale_sessions:
                                        stale_sessions.append(session_id)
                                        cleaned_count += 1
                except Exception as e:
                    logger.error(f"Error cleaning Redis connections: {e}")

            # Send cleanup result
            await self.send(text_data=json.dumps({
                'type': 'connection_cleanup_result',
                'cleaned_count': cleaned_count,
                'stale_sessions': stale_sessions,
                'message': f'Cleaned {cleaned_count} stale connections',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))

            logger.info(f"Cleaned {cleaned_count} stale connections: {stale_sessions}")

        except Exception as e:
            logger.error(f"Error cleaning stale connections: {e}")
            await self.send(text_data=json.dumps({
                'type': 'connection_cleanup_error',
                'error': f'Failed to clean connections: {str(e)}',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))

    async def _detect_connection_leaks(self):
        """Detect potential connection leaks."""
        try:
            leak_indicators = []

            # Check for duplicate admin connections
            admin_connections = [conn for conn in self.__class__._active_consumers]
            if len(admin_connections) > 3:  # More than 3 admin connections might indicate leaks
                leak_indicators.append({
                    'type': 'excessive_admin_connections',
                    'count': len(admin_connections),
                    'threshold': 3,
                    'severity': 'warning'
                })

            # Check for connections without recent activity
            stale_count = 0
            current_time = datetime.now(timezone.utc)
            for session_id, data in self.in_memory_connections.items():
                last_activity = data.get('last_activity')
                if last_activity:
                    try:
                        last_activity_time = datetime.fromisoformat(last_activity)
                        time_since_activity = current_time - last_activity_time
                        if time_since_activity.total_seconds() > 900:  # 15 minutes
                            stale_count += 1
                    except Exception:
                        stale_count += 1

            if stale_count > 0:
                leak_indicators.append({
                    'type': 'stale_connections',
                    'count': stale_count,
                    'threshold': 0,
                    'severity': 'info' if stale_count < 5 else 'warning'
                })

            # Check Redis vs in-memory discrepancies
            if self.redis_client:
                try:
                    redis_session_count = await asyncio.get_event_loop().run_in_executor(
                        None, lambda: len(self.redis_client.keys("ws_session:*"))
                    )
                    memory_session_count = len(self.in_memory_connections)

                    if abs(redis_session_count - memory_session_count) > 2:
                        leak_indicators.append({
                            'type': 'redis_memory_mismatch',
                            'redis_count': redis_session_count,
                            'memory_count': memory_session_count,
                            'difference': abs(redis_session_count - memory_session_count),
                            'severity': 'warning'
                        })
                except Exception as e:
                    logger.warning(f"Error checking Redis session count: {e}")

            # Send leak detection result
            await self.send(text_data=json.dumps({
                'type': 'leak_detection_result',
                'leak_indicators': leak_indicators,
                'total_indicators': len(leak_indicators),
                'admin_connections_count': len(admin_connections),
                'memory_connections_count': len(self.in_memory_connections),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))

            logger.info(f"Leak detection completed: {len(leak_indicators)} indicators found")

        except Exception as e:
            logger.error(f"Error detecting connection leaks: {e}")
            await self.send(text_data=json.dumps({
                'type': 'leak_detection_error',
                'error': f'Failed to detect leaks: {str(e)}',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))

    async def _force_cleanup_connections(self):
        """Force cleanup of all connections (emergency cleanup)."""
        try:
            cleaned_count = 0

            # Clear in-memory connections
            memory_count = len(self.in_memory_connections)
            self.in_memory_connections.clear()
            cleaned_count += memory_count

            # Clear Redis connections
            if self.redis_client:
                try:
                    session_keys = await asyncio.get_event_loop().run_in_executor(
                        None, self.redis_client.keys, "ws_session:*"
                    )

                    if session_keys:
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.redis_client.delete, *session_keys
                        )
                        cleaned_count += len(session_keys)
                except Exception as e:
                    logger.error(f"Error force cleaning Redis: {e}")

            # Reset class-level connections
            self.__class__._global_connections.clear()

            await self.send(text_data=json.dumps({
                'type': 'force_cleanup_result',
                'cleaned_count': cleaned_count,
                'message': f'Force cleaned {cleaned_count} connections',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))

            logger.warning(f"Force cleanup completed: {cleaned_count} connections removed")

        except Exception as e:
            logger.error(f"Error in force cleanup: {e}")
            await self.send(text_data=json.dumps({
                'type': 'force_cleanup_error',
                'error': f'Failed to force cleanup: {str(e)}',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }))

    @classmethod
    async def broadcast_session_message(cls, session_id, message_data):
        """Broadcast a message to admin dashboards monitoring this session."""
        try:
            # Add session_id to message data
            enhanced_message = {
                **message_data,
                'session_id': session_id,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            # Send to all admin dashboards that are monitoring this session
            for consumer in cls._active_consumers:
                if (consumer.session_monitoring_enabled and
                    consumer.focused_session_id == session_id):
                    await consumer.send(text_data=json.dumps(enhanced_message))
                    logger.debug(f"Broadcasted session message to admin dashboard: {message_data.get('type', 'unknown')}")

        except Exception as e:
            logger.error(f"Error broadcasting session message: {e}")

    @classmethod
    async def broadcast_message_flow(cls, flow_data):
        """Broadcast a message flow event to admin dashboards with message monitoring enabled."""
        try:
            # Create message flow event
            flow_event = {
                'type': 'message_flow',
                'data': flow_data,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            # Send to all admin dashboards that have message monitoring enabled
            for consumer in cls._active_consumers:
                if consumer.message_monitoring_enabled:
                    await consumer.send(text_data=json.dumps(flow_event))
                    logger.debug(f"Broadcasted message flow to admin dashboard: {flow_data.get('direction', 'unknown')} - {flow_data.get('message', {}).get('type', 'unknown')}")

        except Exception as e:
            logger.error(f"Error broadcasting message flow: {e}")

    async def _get_active_connections(self):
        """Get list of active WebSocket connections."""
        connections = []

        # Always check in-memory connections first (for real-time tracking)
        if len(self.in_memory_connections) != self._last_logged_in_memory_connections_count:
            # Reduced debug logging
            self._last_logged_in_memory_connections_count = len(self.in_memory_connections)

        current_in_memory_keys = set(self.in_memory_connections.keys())
        if current_in_memory_keys != self._last_logged_in_memory_connections_keys:
            # Reduced debug logging
            self._last_logged_in_memory_connections_keys = current_in_memory_keys

        for session_id, data in self.in_memory_connections.items():
            try:
                # Calculate connection duration
                connected_at = data.get('connected_at')
                if connected_at:
                    try:
                        connected_time = datetime.fromisoformat(connected_at)
                        duration = datetime.now(timezone.utc) - connected_time
                        duration_str = str(duration).split('.')[0]  # Remove microseconds
                    except Exception as de:
                        logger.warning(f"🔧 DEBUG: Error parsing connected_at time for {session_id}: {de}")
                        duration_str = "Unknown"
                else:
                    duration_str = "Unknown"

                # Validate and sanitize user_id
                user_id = data.get('user_id', 'Unknown')
                if isinstance(user_id, str) and user_id.startswith('persistent-user-'):
                    # Convert string user IDs to a safe format
                    user_id = f"test-{user_id.split('-')[-1]}"

                connection_data = {
                    'session_id': session_id,
                    'user_id': str(user_id),
                    'connected_at': connected_at,
                    'duration': duration_str,
                    'last_activity': data.get('last_activity'),
                    'message_count': int(data.get('message_count', 0)),
                    'current_workflow': data.get('current_workflow'),
                    'status': data.get('status', 'connected')
                }

                connections.append(connection_data)
                # Reduced debug logging

            except Exception as ce:
                logger.error(f"🔧 ERROR: Error processing connection {session_id}: {ce}")
                continue

        # If we have Redis, also check Redis connections (but don't duplicate)
        if self.redis_client:
            # Reduced debug logging
            try:
                # Get all session keys
                session_keys = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.keys, "ws_session:*"
                )

                for key in session_keys:
                    session_data = await asyncio.get_event_loop().run_in_executor(
                        None, self.redis_client.hgetall, key
                    )

                    if session_data:
                        session_id = key.split(':')[1]

                        # Skip if already in in-memory connections
                        if session_id in self.in_memory_connections:
                            continue

                        # Calculate connection duration
                        connected_at = session_data.get('connected_at')
                        if connected_at:
                            try:
                                connected_time = datetime.fromisoformat(connected_at)
                                duration = datetime.now(timezone.utc) - connected_time
                                duration_str = str(duration).split('.')[0]  # Remove microseconds
                            except:
                                duration_str = "Unknown"
                        else:
                            duration_str = "Unknown"

                        connections.append({
                            'session_id': session_id,
                            'user_id': session_data.get('user_id', 'Unknown'),
                            'connected_at': connected_at,
                            'duration': duration_str,
                            'last_activity': session_data.get('last_activity'),
                            'message_count': int(session_data.get('message_count', 0)),
                            'current_workflow': session_data.get('current_workflow'),
                            'status': 'connected'
                        })

            except Exception as e:
                logger.error(f"🔧 ERROR: Error getting Redis connections: {e}")

        if len(connections) != self._last_logged_total_connections_count:
            # Reduced debug logging
            self._last_logged_total_connections_count = len(connections)
        return connections

    async def _get_system_health(self):
        """Get system health metrics."""
        health = {
            'redis': 'unknown',
            'celery_workers': 'unknown',
            'database': 'unknown',
            'memory_usage': 'unknown',
            'cpu_usage': 'unknown'
        }

        # Check Redis health
        if self.redis_client:
            try:
                await asyncio.get_event_loop().run_in_executor(None, self.redis_client.ping)
                info = await asyncio.get_event_loop().run_in_executor(None, self.redis_client.info)
                health['redis'] = 'healthy'
                health['redis_clients'] = info.get('connected_clients', 0)
                health['redis_memory'] = info.get('used_memory_human', 'Unknown')
            except Exception as e:
                health['redis'] = f'error: {str(e)}'

        # Check Celery workers (simplified check)
        try:
            from celery import current_app
            inspect = current_app.control.inspect()
            stats = inspect.stats()
            if stats:
                active_workers = len(stats)
                health['celery_workers'] = f'{active_workers} active'
            else:
                health['celery_workers'] = 'no workers'
        except Exception as e:
            health['celery_workers'] = f'error: {str(e)}'

        # Database health (simple connection test) - Fixed async context issue
        try:
            from channels.db import database_sync_to_async
            from django.db import connection

            @database_sync_to_async
            def check_database():
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    return True

            await check_database()
            health['database'] = 'healthy'
        except Exception as e:
            health['database'] = f'error: {str(e)}'

        return health

    async def _get_message_stats(self):
        """Get message flow statistics."""
        stats = {
            'messages_per_minute': 0,
            'total_messages_today': 0,
            'error_rate': 0,
            'active_workflows': 0
        }

        if not self.redis_client:
            # Return mock data for testing when Redis is not available
            logger.warning("Redis not available, returning mock message stats")
            return {
                'messages_per_minute': 12,
                'total_messages_today': 156,
                'error_rate': 0.02,
                'active_workflows': 3
            }

        try:
            # Get message counts from Redis
            today_key = f"message_stats:{datetime.now().strftime('%Y-%m-%d')}"
            daily_stats = await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.hgetall, today_key
            )

            if daily_stats:
                stats['total_messages_today'] = int(daily_stats.get('total', 0))
                stats['error_rate'] = float(daily_stats.get('error_rate', 0))

            # Get recent message rate
            minute_key = f"message_rate:{datetime.now().strftime('%Y-%m-%d:%H:%M')}"
            minute_count = await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.get, minute_key
            )
            stats['messages_per_minute'] = int(minute_count or 0)

            # Count active workflows
            workflow_keys = await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.keys, "active_workflow:*"
            )
            stats['active_workflows'] = len(workflow_keys)

        except Exception as e:
            logger.error(f"Error getting message stats: {e}")

        return stats

    async def _disconnect_user(self, user_id):
        """Disconnect a specific user's WebSocket connections."""
        try:
            # Find user's session groups
            if self.redis_client:
                session_keys = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.keys, f"ws_session:*"
                )

                for key in session_keys:
                    session_data = await asyncio.get_event_loop().run_in_executor(
                        None, self.redis_client.hgetall, key
                    )

                    if session_data.get('user_id') == str(user_id):
                        # Send disconnect message to user's group
                        session_id = key.split(':')[1]
                        group_name = f"client_session_{session_id}"

                        await self.channel_layer.group_send(group_name, {
                            'type': 'admin_disconnect',
                            'message': 'Connection terminated by administrator'
                        })

                        # Remove session from Redis
                        await asyncio.get_event_loop().run_in_executor(
                            None, self.redis_client.delete, key
                        )

                        logger.info(f"Disconnected user {user_id} session {session_id}")

        except Exception as e:
            logger.error(f"Error disconnecting user {user_id}: {e}")

    # Handler for connection monitoring events
    async def connection_event(self, event):
        """Handle connection events from other consumers."""
        await self.send(text_data=json.dumps({
            'type': 'connection_event',
            'data': event,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }))

    # Handler for message flow events
    async def message_flow_event(self, event):
        """Handle message flow events for real-time monitoring."""
        await self.send(text_data=json.dumps({
            'type': 'message_flow',
            'data': event,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }))

    def register_connection(self, session_id, user_id='Unknown'):
        """Register a connection in memory tracking."""
        self.in_memory_connections[session_id] = {
            'user_id': user_id,
            'connected_at': datetime.now(timezone.utc).isoformat(),
            'last_activity': datetime.now(timezone.utc).isoformat(),
            'message_count': 0,
            'current_workflow': None,
            'status': 'connected'
        }
        logger.info(f"Registered connection: {session_id} for user {user_id}")

    def update_connection_activity(self, session_id, activity_data=None):
        """Update connection activity in memory tracking."""
        if session_id in self.in_memory_connections:
            self.in_memory_connections[session_id]['last_activity'] = datetime.now(timezone.utc).isoformat()

            if activity_data:
                if activity_data.get('type') == 'message':
                    self.in_memory_connections[session_id]['message_count'] += 1
                    self.in_memory_stats['messages_per_minute'] += 1

                if 'user_id' in activity_data:
                    self.in_memory_connections[session_id]['user_id'] = activity_data['user_id']

                if 'current_workflow' in activity_data:
                    self.in_memory_connections[session_id]['current_workflow'] = activity_data['current_workflow']

    def unregister_connection(self, session_id):
        """Remove a connection from memory tracking."""
        if session_id in self.in_memory_connections:
            del self.in_memory_connections[session_id]
            logger.info(f"Unregistered connection: {session_id}")

    @classmethod
    def register_global_connection(cls, session_id, user_id='Unknown'):
        """Register a connection in the global registry."""
        # Reduced debug logging to prevent performance issues

        # Validate and sanitize inputs
        if not session_id:
            logger.error(f"🔧 ERROR: Invalid session_id provided: {session_id}")
            return False

        # Sanitize user_id to prevent database issues
        sanitized_user_id = str(user_id)
        if isinstance(user_id, str) and user_id.startswith('persistent-user-'):
            # Convert problematic string user IDs to a safe format
            sanitized_user_id = f"test-{user_id.split('-')[-1]}"
            logger.debug(f"🔧 DEBUG: Sanitized user_id from {user_id} to {sanitized_user_id}")

        try:
            cls._global_connections[session_id] = {
                'user_id': sanitized_user_id,
                'connected_at': datetime.now(timezone.utc).isoformat(),
                'last_activity': datetime.now(timezone.utc).isoformat(),
                'message_count': 0,
                'current_workflow': None,
                'status': 'connected'
            }
            if len(cls._global_connections) != cls._last_logged_global_connections_count:
                # Reduced debug logging
                cls._last_logged_global_connections_count = len(cls._global_connections)

            current_global_keys = set(cls._global_connections.keys())
            if current_global_keys != cls._last_logged_global_connections_keys:
                # Reduced debug logging
                cls._last_logged_global_connections_keys = current_global_keys
            logger.info(f"Globally registered connection: {session_id} for user {sanitized_user_id}")
            return True

        except Exception as e:
            logger.error(f"🔧 ERROR: Failed to register global connection {session_id}: {e}")
            return False

    @classmethod
    def update_global_connection_activity(cls, session_id, activity_data=None):
        """Update connection activity in the global registry."""
        # Reduced debug logging to prevent performance issues
        if session_id in cls._global_connections:
            cls._global_connections[session_id]['last_activity'] = datetime.now(timezone.utc).isoformat()

            if activity_data:
                if activity_data.get('type') == 'message':
                    cls._global_connections[session_id]['message_count'] += 1
                    cls._global_stats['messages_per_minute'] += 1
                    # Reduced debug logging

                if 'user_id' in activity_data:
                    cls._global_connections[session_id]['user_id'] = activity_data['user_id']
                    # Reduced debug logging

                if 'current_workflow' in activity_data:
                    cls._global_connections[session_id]['current_workflow'] = activity_data['current_workflow']
        else:
            # Reduced debug logging - session not found
            pass

    @classmethod
    def unregister_global_connection(cls, session_id):
        """Remove a connection from the global registry."""
        if session_id in cls._global_connections:
            del cls._global_connections[session_id]
            logger.info(f"Globally unregistered connection: {session_id}")


# Helper functions for backward compatibility
def get_global_connection_monitor():
    """Get the global connection monitor class."""
    return ConnectionMonitorConsumer

def set_global_connection_monitor(monitor):
    """Set the global connection monitor instance (no-op, using class methods)."""
    pass
