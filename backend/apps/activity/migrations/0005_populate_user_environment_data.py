# Generated manually on 2025-06-20 for populating user_environment data

from django.db import migrations


def populate_user_environment_data(apps, schema_editor):
    """
    Populate the user_environment field for existing ActivityTailored objects.
    
    For each ActivityTailored object, find the user's current environment
    or create a default one if none exists.
    """
    ActivityTailored = apps.get_model('activity', 'ActivityTailored')
    UserEnvironment = apps.get_model('user', 'UserEnvironment')
    UserProfile = apps.get_model('user', 'UserProfile')
    GenericEnvironment = apps.get_model('user', 'GenericEnvironment')
    
    # Get all ActivityTailored objects that need user_environment populated
    activities = ActivityTailored.objects.filter(user_environment_id=2)  # Default value from migration
    
    print(f"Processing {activities.count()} ActivityTailored objects...")
    
    for activity in activities:
        user_profile = activity.user_profile
        
        # Try to find the user's current environment
        user_env = UserEnvironment.objects.filter(
            user_profile=user_profile,
            is_current=True
        ).first()
        
        if not user_env:
            # Create a default environment for this user
            # First, try to get a generic environment to base it on
            generic_env = GenericEnvironment.objects.first()
            
            user_env = UserEnvironment.objects.create(
                user_profile=user_profile,
                environment_name=f"{user_profile.profile_name}'s Environment",
                environment_description="Default environment created during migration",
                generic_environment=generic_env,
                effective_start='2025-01-01',
                is_current=True
            )
            print(f"Created default environment for user {user_profile.profile_name}")
        
        # Update the activity to use the correct user environment
        activity.user_environment = user_env
        activity.save()
    
    print("User environment population completed.")


def reverse_populate_user_environment_data(apps, schema_editor):
    """
    Reverse operation - not needed as we're just fixing data integrity.
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('activity', '0004_fix_wheelitem_activitytailored_relationship'),
        ('user', '0005_remove_personal_prefs_json'),
    ]

    operations = [
        migrations.RunPython(
            populate_user_environment_data,
            reverse_populate_user_environment_data,
        ),
    ]
