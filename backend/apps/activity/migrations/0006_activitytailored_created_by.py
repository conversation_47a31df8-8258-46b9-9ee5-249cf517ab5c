# Generated by Django 5.2.1 on 2025-06-20 15:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('activity', '0005_populate_user_environment_data'),
        ('user', '0005_remove_personal_prefs_json'),
    ]

    operations = [
        migrations.AddField(
            model_name='activitytailored',
            name='created_by',
            field=models.ForeignKey(blank=True, help_text='User who created this custom activity. If null, activity was system-generated.', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_activities', to='user.userprofile'),
        ),
    ]
