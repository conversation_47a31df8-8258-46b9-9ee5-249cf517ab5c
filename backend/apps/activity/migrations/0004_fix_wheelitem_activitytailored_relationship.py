# Generated by Django 5.2.1 on 2025-06-20 11:59

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('activity', '0003_alter_entitydomainrelationship_object_id'),
        ('user', '0005_remove_personal_prefs_json'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='activitytailored',
            name='unique_activity_version',
        ),
        migrations.AddField(
            model_name='activitytailored',
            name='user_environment',
            field=models.ForeignKey(default=2, help_text='The specific user environment this activity is tailored for.', on_delete=django.db.models.deletion.CASCADE, related_name='tailored_activities', to='user.userenvironment'),
            preserve_default=False,
        ),
        migrations.AddConstraint(
            model_name='activitytailored',
            constraint=models.UniqueConstraint(fields=('user_profile', 'generic_activity', 'user_environment', 'version'), name='unique_activity_version_environment'),
        ),
    ]
