import json
import uuid
import logging
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Tuple
from django.db import transaction, IntegrityError
from django.contrib.auth.models import User
from django.contrib.auth.hashers import make_password
from django.core.exceptions import ValidationError

from apps.user.models import (
    UserProfile, Demographics, UserEnvironment, UserTraitInclination,
    Belief, BeliefEvidence, BeliefInfluence, Aspiration, Intention, 
    Inspiration, GoalInspiration, Skill, UserResource, UserLimitation, 
    Preference, CurrentMood, TrustLevel, GenericTrait, GenericSkill, 
    GenericResource, GenericUserLimitation, GenericEnvironment,
    UserEnvironmentPhysicalProperties, UserEnvironmentSocialContext,
    UserEnvironmentActivitySupport, UserEnvironmentPsychologicalQualities,
    Inventory
)

logger = logging.getLogger(__name__)


class ProfileImportError(Exception):
    """Custom exception for profile import errors"""
    pass


class ProfileImportService:
    """
    Service for importing complete user profiles from structured data.
    
    Handles the complex process of creating user accounts, profiles, and all
    related data in a transactional manner with proper error handling and 
    validation.
    """
    
    def __init__(self):
        self.warnings = []
        self.created_records = 0
        self.updated_records = 0
    
    def import_profile(self, profile_data: Dict[str, Any], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Import a complete user profile from structured data.
        
        Args:
            profile_data: Complete profile data matching the schema
            options: Import options (overwrite, validate, backup)
            
        Returns:
            Dict with import results including success status, profile info, and stats
            
        Raises:
            ProfileImportError: If import fails due to validation or data errors
        """
        if options is None:
            options = {}
            
        self.warnings = []
        self.created_records = 0
        self.updated_records = 0
        
        try:
            with transaction.atomic():
                # 1. Create or get user account
                user = self._create_or_get_user(profile_data['user_account'], options)
                
                # 2. Create or get user profile
                user_profile = self._create_or_get_user_profile(user, profile_data, options)
                
                # 3. Import demographics
                if 'demographics' in profile_data:
                    self._import_demographics(user_profile, profile_data['demographics'])
                
                # 4. Import environments
                if 'environments' in profile_data:
                    self._import_environments(user_profile, profile_data['environments'])
                
                # 5. Import personality traits
                if 'traits' in profile_data:
                    self._import_traits(user_profile, profile_data['traits'])
                
                # 6. Import beliefs
                if 'beliefs' in profile_data:
                    self._import_beliefs(user_profile, profile_data['beliefs'])
                
                # 7. Import goals (aspirations and intentions)
                if 'aspirations' in profile_data:
                    self._import_aspirations(user_profile, profile_data['aspirations'])
                    
                if 'intentions' in profile_data:
                    self._import_intentions(user_profile, profile_data['intentions'])
                
                # 8. Import inspirations
                if 'inspirations' in profile_data:
                    self._import_inspirations(user_profile, profile_data['inspirations'])
                
                # 9. Import skills
                if 'skills' in profile_data:
                    self._import_skills(user_profile, profile_data['skills'])
                
                # 10. Import resources
                if 'resources' in profile_data:
                    self._import_resources(user_profile, profile_data['resources'])
                
                # 11. Import limitations
                if 'limitations' in profile_data:
                    self._import_limitations(user_profile, profile_data['limitations'])
                
                # 12. Import preferences
                if 'preferences' in profile_data:
                    self._import_preferences(user_profile, profile_data['preferences'])
                
                # 13. Import current mood
                if 'current_mood' in profile_data:
                    self._import_current_mood(user_profile, profile_data['current_mood'])
                
                # 14. Import trust level
                if 'trust_level' in profile_data:
                    self._import_trust_level(user_profile, profile_data['trust_level'])
                
                logger.info(f"Successfully imported profile: {user_profile.profile_name}")
                
                return {
                    'success': True,
                    'profile_id': user_profile.id,
                    'profile_name': user_profile.profile_name,
                    'created_records': self.created_records,
                    'updated_records': self.updated_records,
                    'warnings': self.warnings
                }
                
        except Exception as e:
            logger.error(f"Profile import failed: {str(e)}")
            raise ProfileImportError(f"Import failed: {str(e)}")
    
    def _create_or_get_user(self, user_data: Dict[str, Any], options: Dict[str, Any]) -> User:
        """Create or retrieve user account"""
        username = user_data['username']
        email = user_data['email']
        
        # Check if user exists
        existing_user = User.objects.filter(username=username).first()
        if existing_user:
            if not options.get('overwriteExisting', False):
                raise ProfileImportError(f"User '{username}' already exists. Use overwriteExisting option to update.")
            
            # Update existing user
            for field, value in user_data.items():
                if field == 'password' and value:
                    existing_user.password = make_password(value)
                elif hasattr(existing_user, field):
                    setattr(existing_user, field, value)
            
            existing_user.save()
            self.updated_records += 1
            return existing_user
        else:
            # Create new user
            user_create_data = user_data.copy()
            if 'password' in user_create_data and user_create_data['password']:
                user_create_data['password'] = make_password(user_create_data['password'])
            else:
                # Generate a random password if none provided
                user_create_data['password'] = make_password(User.objects.make_random_password())
                self.warnings.append("No password provided, generated random password")
            
            user = User.objects.create(**user_create_data)
            self.created_records += 1
            return user
    
    def _create_or_get_user_profile(self, user: User, profile_data: Dict[str, Any], options: Dict[str, Any]) -> UserProfile:
        """Create or retrieve user profile"""
        profile_name = profile_data['profile_name']
        is_real = profile_data.get('is_real', True)
        
        # Check if profile exists
        existing_profile = UserProfile.objects.filter(user=user).first()
        if existing_profile:
            # Update existing profile
            existing_profile.profile_name = profile_name
            existing_profile.is_real = is_real
            existing_profile.save()
            self.updated_records += 1
            return existing_profile
        else:
            # Create new profile
            profile = UserProfile.objects.create(
                user=user,
                profile_name=profile_name,
                is_real=is_real
            )
            self.created_records += 1
            return profile
    
    def _import_demographics(self, user_profile: UserProfile, demographics_data: Dict[str, Any]):
        """Import demographics data"""
        demographics, created = Demographics.objects.update_or_create(
            user_profile=user_profile,
            defaults=demographics_data
        )
        
        if created:
            self.created_records += 1
        else:
            self.updated_records += 1
    
    def _import_environments(self, user_profile: UserProfile, environments_data: List[Dict[str, Any]]):
        """Import user environments"""
        current_environment = None
        
        for env_data in environments_data:
            # Extract nested data
            physical_props = env_data.pop('physical_properties', {})
            social_context = env_data.pop('social_context', {})
            activity_support = env_data.pop('activity_support', {})
            psychological_qualities = env_data.pop('psychological_qualities', {})
            
            # Get or create generic environment if code provided
            generic_env = None
            if env_data.get('generic_environment_code'):
                try:
                    generic_env = GenericEnvironment.objects.get(code=env_data['generic_environment_code'])
                except GenericEnvironment.DoesNotExist:
                    self.warnings.append(f"Generic environment '{env_data['generic_environment_code']}' not found")
            
            # Create or update environment
            environment, created = UserEnvironment.objects.update_or_create(
                user_profile=user_profile,
                environment_name=env_data['environment_name'],
                defaults={
                    **env_data,
                    'generic_environment': generic_env
                }
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
            
            # Create or update physical properties
            if physical_props:
                props, props_created = UserEnvironmentPhysicalProperties.objects.update_or_create(
                    user_environment=environment,
                    defaults=physical_props
                )
                if props_created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
            
            # Create or update social context
            if social_context:
                context, context_created = UserEnvironmentSocialContext.objects.update_or_create(
                    user_environment=environment,
                    defaults=social_context
                )
                if context_created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
            
            # Create or update activity support
            if activity_support:
                support, support_created = UserEnvironmentActivitySupport.objects.update_or_create(
                    user_environment=environment,
                    defaults=activity_support
                )
                if support_created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
            
            # Create or update psychological qualities
            if psychological_qualities:
                qualities, qualities_created = UserEnvironmentPsychologicalQualities.objects.update_or_create(
                    user_environment=environment,
                    defaults=psychological_qualities
                )
                if qualities_created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
            
            # Track current environment
            if env_data.get('is_current', False):
                current_environment = environment
        
        # Update user profile's current environment
        if current_environment:
            user_profile.current_environment = current_environment
            user_profile.save()
    
    def _import_traits(self, user_profile: UserProfile, traits_data: List[Dict[str, Any]]):
        """Import personality traits"""
        for trait_data in traits_data:
            try:
                generic_trait = GenericTrait.objects.get(code=trait_data['trait_code'])
                
                trait_inclination, created = UserTraitInclination.objects.update_or_create(
                    user_profile=user_profile,
                    generic_trait=generic_trait,
                    defaults={
                        'strength': trait_data['strength'],
                        'awareness': trait_data['awareness']
                    }
                )
                
                if created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
                    
            except GenericTrait.DoesNotExist:
                self.warnings.append(f"Generic trait '{trait_data['trait_code']}' not found")
    
    def _import_beliefs(self, user_profile: UserProfile, beliefs_data: List[Dict[str, Any]]):
        """Import beliefs and evidence"""
        for belief_data in beliefs_data:
            evidence_data = belief_data.pop('evidence', [])
            
            belief, created = Belief.objects.update_or_create(
                user_profile=user_profile,
                content=belief_data['content'],
                defaults={
                    **belief_data,
                    'last_updated': date.today()
                }
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
            
            # Import evidence
            for evidence in evidence_data:
                evidence_obj, evidence_created = BeliefEvidence.objects.update_or_create(
                    belief=belief,
                    evidence_type=evidence['evidence_type'],
                    description=evidence['description'],
                    defaults=evidence
                )
                
                if evidence_created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
    
    def _import_aspirations(self, user_profile: UserProfile, aspirations_data: List[Dict[str, Any]]):
        """Import aspirations"""
        for aspiration_data in aspirations_data:
            aspiration, created = Aspiration.objects.update_or_create(
                user_profile=user_profile,
                title=aspiration_data['title'],
                defaults=aspiration_data
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
    
    def _import_intentions(self, user_profile: UserProfile, intentions_data: List[Dict[str, Any]]):
        """Import intentions"""
        for intention_data in intentions_data:
            intention, created = Intention.objects.update_or_create(
                user_profile=user_profile,
                title=intention_data['title'],
                defaults=intention_data
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
    
    def _import_inspirations(self, user_profile: UserProfile, inspirations_data: List[Dict[str, Any]]):
        """Import inspirations"""
        for inspiration_data in inspirations_data:
            inspiration, created = Inspiration.objects.update_or_create(
                user_profile=user_profile,
                source=inspiration_data['source'],
                defaults=inspiration_data
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
    
    def _import_skills(self, user_profile: UserProfile, skills_data: List[Dict[str, Any]]):
        """Import skills"""
        for skill_data in skills_data:
            # Get or create generic skill
            generic_skill, skill_created = GenericSkill.objects.get_or_create(
                code=skill_data['skill_code'],
                defaults={'description': skill_data.get('description', skill_data['skill_code'])}
            )
            
            if skill_created:
                self.created_records += 1
                self.warnings.append(f"Created generic skill: {skill_data['skill_code']}")
            
            # Create or update user skill
            skill, created = Skill.objects.update_or_create(
                user_profile=user_profile,
                generic_skill=generic_skill,
                defaults=skill_data
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
    
    def _import_resources(self, user_profile: UserProfile, resources_data: List[Dict[str, Any]]):
        """Import resources"""
        # Get current environment for resource association
        current_env = user_profile.current_environment
        if not current_env:
            self.warnings.append("No current environment found for resources")
            return
        
        for resource_data in resources_data:
            # Get or create generic resource
            generic_resource, resource_created = GenericResource.objects.get_or_create(
                code=resource_data['resource_code'],
                defaults={
                    'resource_type': 'General',
                    'description': resource_data.get('specific_name', resource_data['resource_code']),
                    'op_cost': 0,
                    'acq_cost': 0
                }
            )
            
            if resource_created:
                self.created_records += 1
                self.warnings.append(f"Created generic resource: {resource_data['resource_code']}")
            
            # Create or update user resource
            user_resource, created = UserResource.objects.update_or_create(
                specific_name=resource_data['specific_name'],
                user_environment=current_env,
                defaults={
                    **resource_data,
                    'generic_resource': generic_resource
                }
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
    
    def _import_limitations(self, user_profile: UserProfile, limitations_data: List[Dict[str, Any]]):
        """Import limitations"""
        for limitation_data in limitations_data:
            # Get or create generic limitation
            generic_limitation, limitation_created = GenericUserLimitation.objects.get_or_create(
                code=limitation_data['limitation_code'],
                defaults={'description': limitation_data['limitation_code']}
            )
            
            if limitation_created:
                self.created_records += 1
                self.warnings.append(f"Created generic limitation: {limitation_data['limitation_code']}")
            
            # Create or update user limitation
            limitation, created = UserLimitation.objects.update_or_create(
                user_profile=user_profile,
                generic_limitation=generic_limitation,
                defaults=limitation_data
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
    
    def _import_preferences(self, user_profile: UserProfile, preferences_data: List[Dict[str, Any]]):
        """Import preferences"""
        for preference_data in preferences_data:
            # Get environment if environment_specific
            environment = None
            if preference_data.get('environment_specific', False):
                environment = user_profile.current_environment
            
            preference, created = Preference.objects.update_or_create(
                user_profile=user_profile,
                pref_name=preference_data['pref_name'],
                defaults={
                    **preference_data,
                    'environment': environment
                }
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
    
    def _import_current_mood(self, user_profile: UserProfile, mood_data: Dict[str, Any]):
        """Import current mood"""
        mood_data['processed_at'] = datetime.now()
        
        mood, created = CurrentMood.objects.update_or_create(
            user_profile=user_profile,
            defaults=mood_data
        )
        
        if created:
            self.created_records += 1
        else:
            self.updated_records += 1
    
    def _import_trust_level(self, user_profile: UserProfile, trust_data: Dict[str, Any]):
        """Import trust level"""
        trust_level, created = TrustLevel.objects.update_or_create(
            user_profile=user_profile,
            defaults=trust_data
        )
        
        if created:
            self.created_records += 1
        else:
            self.updated_records += 1
    
    def validate_profile_data(self, profile_data: Dict[str, Any]) -> List[str]:
        """
        Validate profile data before import.
        
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Validate required fields
        if 'user_account' not in profile_data:
            errors.append("user_account is required")
        elif not isinstance(profile_data['user_account'], dict):
            errors.append("user_account must be a dictionary")
        else:
            user_account = profile_data['user_account']
            if 'username' not in user_account:
                errors.append("username is required in user_account")
            if 'email' not in user_account:
                errors.append("email is required in user_account")
        
        if 'profile_name' not in profile_data:
            errors.append("profile_name is required")
        
        # Validate environments have only one current
        environments = profile_data.get('environments', [])
        if environments:
            current_count = sum(1 for env in environments if env.get('is_current', False))
            if current_count == 0:
                errors.append("At least one environment must be marked as current")
            elif current_count > 1:
                errors.append("Only one environment can be marked as current")
        
        # Validate trait codes
        traits = profile_data.get('traits', [])
        if traits:
            valid_trait_codes = set(GenericTrait.objects.values_list('code', flat=True))
            invalid_traits = [
                trait['trait_code'] for trait in traits 
                if trait.get('trait_code') not in valid_trait_codes
            ]
            if invalid_traits:
                errors.append(f"Invalid trait codes: {', '.join(invalid_traits)}")
        
        return errors
