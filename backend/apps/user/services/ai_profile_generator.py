import json
import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime
import openai
from django.conf import settings

logger = logging.getLogger(__name__)


class AIProfileGenerationError(Exception):
    """Custom exception for AI profile generation errors"""
    pass


class AIProfileGenerator:
    """
    Service for generating comprehensive user profiles from questionnaire data using AI.
    
    Uses advanced LLM prompting to analyze questionnaire responses and create
    structured user profiles that match the Game of Life schema.
    """
    
    def __init__(self):
        self.client = openai.OpenAI(api_key=settings.OPENAI_API_KEY) if hasattr(settings, 'OPENAI_API_KEY') else None
        self.model = getattr(settings, 'PROFILE_GENERATION_MODEL', 'gpt-4-turbo-preview')
        self.max_tokens = getattr(settings, 'PROFILE_GENERATION_MAX_TOKENS', 4000)
        
    def generate_profile(self, questionnaire_data: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate a comprehensive user profile from questionnaire responses.
        
        Args:
            questionnaire_data: Raw questionnaire responses or interview transcript
            options: Generation options (archetype analysis, environment inference, etc.)
            
        Returns:
            Dict containing the generated profile data and metadata
            
        Raises:
            AIProfileGenerationError: If generation fails
        """
        if options is None:
            options = {}
            
        if not self.client:
            raise AIProfileGenerationError("OpenAI API key not configured")
        
        start_time = time.time()
        
        try:
            # Load the advanced prompt template
            prompt = self._build_generation_prompt(questionnaire_data, options)
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_system_prompt()
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=0.7,
                response_format={"type": "json_object"}
            )
            
            # Parse the response
            generated_content = response.choices[0].message.content
            profile_data = json.loads(generated_content)
            
            # Calculate generation metadata
            processing_time = time.time() - start_time
            tokens_used = response.usage.total_tokens
            
            # Extract metadata and clean profile data
            generation_metadata = profile_data.pop('_generation_metadata', {})
            generation_metadata.update({
                'processing_time': processing_time,
                'tokens_used': tokens_used,
                'model_used': self.model,
                'generation_timestamp': datetime.now().isoformat()
            })
            
            # Validate and clean the generated profile
            profile_data = self._validate_and_clean_profile(profile_data)
            
            logger.info(f"Successfully generated profile in {processing_time:.2f}s using {tokens_used} tokens")
            
            return {
                'success': True,
                'profile_data': profile_data,
                'generation_metadata': generation_metadata
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            raise AIProfileGenerationError(f"Invalid JSON response from AI: {e}")
        except Exception as e:
            logger.error(f"Profile generation failed: {e}")
            raise AIProfileGenerationError(f"Generation failed: {e}")
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for profile generation"""
        return """You are an expert psychological profiler and user experience analyst specializing in creating comprehensive user profiles for the "Game of Life" personal development platform. 

Your task is to analyze questionnaire responses, interview transcripts, or personal narratives to generate a complete, nuanced user profile that captures both explicit information and underlying psychological patterns.

You must respond with valid JSON that matches the provided schema. Focus on psychological accuracy, behavioral relevance, and actionable insights while maintaining appropriate confidence levels about inferences.

Key principles:
- Ground all inferences in textual evidence
- Distinguish between explicit statements and inferences
- Provide realistic confidence ratings
- Focus on patterns relevant to behavior change and personal development
- Create profiles that enable effective activity recommendation and trust building"""
    
    def _build_generation_prompt(self, questionnaire_data: str, options: Dict[str, Any]) -> str:
        """Build the complete generation prompt"""
        # Load the base prompt template
        with open('/projects/goali/backend/schemas/llm_profile_generation_prompt.md', 'r') as f:
            base_prompt = f.read()
        
        # Add specific instructions based on options
        option_instructions = []
        
        if options.get('includeArchetypeAnalysis', True):
            option_instructions.append("""
FOCUS ON ARCHETYPE ANALYSIS:
- Conduct thorough HEXACO trait assessment
- Identify primary and secondary archetypes (Creative, Analytical, Emotional, Reflective)
- Provide detailed rationale for archetype classification
- Include archetype confidence scores
""")
        
        if options.get('includeEnvironmentInference', True):
            option_instructions.append("""
FOCUS ON ENVIRONMENT INFERENCE:
- Infer detailed environmental context from lifestyle descriptions
- Assess physical properties, social context, and activity support
- Map environmental constraints and opportunities
- Consider how environment impacts available activities
""")
        
        if options.get('includeGoalsExtraction', True):
            option_instructions.append("""
FOCUS ON GOALS EXTRACTION:
- Identify both explicit and implicit goals/aspirations
- Distinguish between intentions (short-term) and aspirations (long-term)
- Extract sources of inspiration and motivation
- Assess goal alignment and potential conflicts
""")
        
        # Combine everything into the final prompt
        final_prompt = f"""
{base_prompt}

SPECIFIC INSTRUCTIONS FOR THIS GENERATION:
{chr(10).join(option_instructions)}

QUESTIONNAIRE DATA TO ANALYZE:
{questionnaire_data}

Generate a complete JSON profile following the schema. Ensure all inferences are grounded in the provided text and include appropriate confidence scores for uncertain elements.
"""
        
        return final_prompt
    
    def _validate_and_clean_profile(self, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean the generated profile data"""
        # Ensure required fields exist
        if 'user_account' not in profile_data:
            # Generate basic user account if missing
            profile_data['user_account'] = {
                'username': 'generated_user_' + str(int(time.time())),
                'email': '<EMAIL>',
                'first_name': '',
                'last_name': ''
            }
        
        if 'profile_name' not in profile_data:
            profile_data['profile_name'] = 'Generated Profile'
        
        # Ensure is_real is set appropriately for generated profiles
        profile_data['is_real'] = profile_data.get('is_real', False)
        
        # Validate environments have current marker
        environments = profile_data.get('environments', [])
        if environments:
            current_count = sum(1 for env in environments if env.get('is_current', False))
            if current_count == 0:
                # Mark first environment as current
                environments[0]['is_current'] = True
            elif current_count > 1:
                # Keep only the first current environment
                marked_current = False
                for env in environments:
                    if env.get('is_current', False) and not marked_current:
                        marked_current = True
                    else:
                        env['is_current'] = False
        
        # Clean up any invalid date formats
        profile_data = self._clean_date_fields(profile_data)
        
        # Ensure numeric fields are within valid ranges
        profile_data = self._validate_numeric_ranges(profile_data)
        
        return profile_data
    
    def _clean_date_fields(self, data: Any) -> Any:
        """Recursively clean date fields to ensure proper format"""
        if isinstance(data, dict):
            cleaned = {}
            for key, value in data.items():
                if key.endswith(('_date', '_start', '_end')) and isinstance(value, str):
                    # Try to parse and reformat date
                    try:
                        if value and value != 'null':
                            # Parse various date formats and convert to YYYY-MM-DD
                            from datetime import datetime
                            parsed_date = datetime.fromisoformat(value.replace('Z', '+00:00'))
                            cleaned[key] = parsed_date.date().isoformat()
                        else:
                            cleaned[key] = None
                    except:
                        # If parsing fails, set to None
                        cleaned[key] = None
                else:
                    cleaned[key] = self._clean_date_fields(value)
            return cleaned
        elif isinstance(data, list):
            return [self._clean_date_fields(item) for item in data]
        else:
            return data
    
    def _validate_numeric_ranges(self, data: Any) -> Any:
        """Recursively validate numeric fields are within expected ranges"""
        if isinstance(data, dict):
            validated = {}
            for key, value in data.items():
                if isinstance(value, (int, float)):
                    # Apply range validation based on field name patterns
                    if key in ['strength', 'awareness', 'user_confidence', 'system_confidence', 
                              'emotionality', 'stability', 'user_awareness', 'importance_according_user',
                              'importance_according_system', 'level', 'user_enjoyment', 'severity',
                              'pref_strength', 'height', 'value'] or key.endswith('_level'):
                        # These should be 0-100
                        validated[key] = max(0, min(100, value))
                    elif key == 'credibility_score':
                        # This should be 0-1
                        validated[key] = max(0, min(1, value))
                    elif key == 'age':
                        # Age should be reasonable
                        validated[key] = max(13, min(120, value))
                    else:
                        validated[key] = value
                else:
                    validated[key] = self._validate_numeric_ranges(value)
            return validated
        elif isinstance(data, list):
            return [self._validate_numeric_ranges(item) for item in data]
        else:
            return data
    
    def get_generation_cost_estimate(self, questionnaire_length: int) -> Dict[str, Any]:
        """Estimate the cost and time for generating a profile"""
        # Rough estimates based on typical usage
        estimated_input_tokens = len(questionnaire_data.split()) * 1.3  # Account for prompt overhead
        estimated_output_tokens = 3500  # Typical profile size
        estimated_total_tokens = estimated_input_tokens + estimated_output_tokens
        
        # Cost estimates (these would need to be updated based on current pricing)
        cost_per_1k_tokens = 0.03  # Rough estimate for GPT-4
        estimated_cost = (estimated_total_tokens / 1000) * cost_per_1k_tokens
        
        # Time estimate
        estimated_time_seconds = estimated_total_tokens / 100  # Rough throughput estimate
        
        return {
            'estimated_tokens': int(estimated_total_tokens),
            'estimated_cost_usd': round(estimated_cost, 4),
            'estimated_time_seconds': int(estimated_time_seconds),
            'model': self.model
        }
