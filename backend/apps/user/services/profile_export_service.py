"""
Comprehensive Profile Export Service

Exports user profiles to JSON format following the enhanced schema structure.
Ensures all fields from the models are included in the export.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from django.db import models
from django.core.serializers.json import DjangoJSONEncoder

from apps.user.models import (
    UserProfile, Demographics, UserEnvironment, UserTraitInclination,
    Belief, BeliefEvidence, BeliefInfluence, Aspiration, Intention, 
    Inspiration, GoalInspiration, Skill, UserResource, UserLimitation, 
    Preference, CurrentMood, TrustLevel, GenericTrait, GenericSkill, 
    GenericResource, GenericUserLimitation, GenericEnvironment,
    UserEnvironmentPhysicalProperties, UserEnvironmentSocialContext,
    UserEnvironmentActivitySupport, UserEnvironmentPsychologicalQualities
)
from apps.activity.models import ActivityTailored, GenericActivity

logger = logging.getLogger(__name__)


class ProfileExportService:
    """Service for exporting user profiles to structured JSON format"""
    
    def __init__(self):
        self.exported_count = 0
        self.warnings = []
    
    def export_profiles(self, profile_ids: List[int]) -> Dict[str, Any]:
        """
        Export multiple user profiles to structured JSON format
        
        Args:
            profile_ids: List of UserProfile IDs to export
            
        Returns:
            Dict containing exported profiles and metadata
        """
        self.exported_count = 0
        self.warnings = []
        
        try:
            # Get profiles with all related data
            profiles = UserProfile.objects.filter(id__in=profile_ids).select_related(
                'user', 'demographics', 'current_environment'
            ).prefetch_related(
                'environments__generic_environment',
                'environments__physical_properties',
                'environments__social_context', 
                'environments__activity_support',
                'environments__psychological_qualities',
                'trait_inclinations__generic_trait',
                'beliefs__evidence_set',
                'beliefs__influence_set',
                'aspirations',
                'intentions',
                'inspirations',
                'skills__generic_skill',
                'resources__generic_resource',
                'limitations__generic_limitation',
                'preferences',
                'current_mood',
                'trust_level'
            )
            
            exported_profiles = []
            
            for profile in profiles:
                try:
                    exported_profile = self._export_single_profile(profile)
                    exported_profiles.append(exported_profile)
                    self.exported_count += 1
                except Exception as e:
                    logger.error(f"Error exporting profile {profile.id}: {e}")
                    self.warnings.append(f"Failed to export profile {profile.profile_name}: {str(e)}")
            
            return {
                'export_metadata': {
                    'exported_at': datetime.now().isoformat(),
                    'exported_by': 'ProfileExportService',
                    'schema_version': '1.0.0',
                    'total_profiles': len(profile_ids),
                    'successfully_exported': self.exported_count,
                    'warnings': self.warnings
                },
                'profiles': exported_profiles
            }
            
        except Exception as e:
            logger.error(f"Error in batch export: {e}")
            raise
    
    def _export_single_profile(self, profile: UserProfile) -> Dict[str, Any]:
        """Export a single user profile to structured format"""
        
        profile_data = {
            'profile_name': profile.profile_name,
            'is_real': profile.is_real,
            'user_account': self._export_user_account(profile.user),
            'demographics': self._export_demographics(profile.demographics) if hasattr(profile, 'demographics') and profile.demographics else None,
            'environments': self._export_environments(profile.environments.all()),
            'traits': self._export_traits(profile.trait_inclinations.all()),
            'beliefs': self._export_beliefs(profile.beliefs.all()),
            'aspirations': self._export_aspirations(profile.aspirations.all()),
            'intentions': self._export_intentions(profile.intentions.all()),
            'inspirations': self._export_inspirations(profile.inspirations.all()),
            'skills': self._export_skills(profile.skills.all()),
            'resources': self._export_resources(profile.resources.all()),
            'limitations': self._export_limitations(profile.limitations.all()),
            'preferences': self._export_preferences(profile.preferences.all()),
            'current_mood': self._export_current_mood(profile.current_mood) if hasattr(profile, 'current_mood') and profile.current_mood else None,
            'trust_level': self._export_trust_level(profile.trust_level) if hasattr(profile, 'trust_level') and profile.trust_level else None,
            'tailored_activities': self._export_tailored_activities(profile)
        }
        
        # Remove None values to keep export clean
        return {k: v for k, v in profile_data.items() if v is not None}
    
    def _export_user_account(self, user) -> Dict[str, Any]:
        """Export user account information"""
        return {
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name or '',
            'last_name': user.last_name or '',
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            'date_joined': user.date_joined.isoformat() if user.date_joined else None,
            'last_login': user.last_login.isoformat() if user.last_login else None
        }
    
    def _export_demographics(self, demographics: Demographics) -> Dict[str, Any]:
        """Export demographics information"""
        return {
            'full_name': demographics.full_name,
            'age': demographics.age,
            'gender': demographics.gender,
            'location': demographics.location,
            'language': demographics.language,
            'occupation': demographics.occupation
        }
    
    def _export_environments(self, environments) -> List[Dict[str, Any]]:
        """Export user environments"""
        exported_environments = []
        
        for env in environments:
            env_data = {
                'environment_name': env.environment_name,
                'environment_description': env.environment_description,
                'is_current': env.is_current,
                'effective_start': env.effective_start.isoformat() if env.effective_start else None,
                'effective_end': env.effective_end.isoformat() if env.effective_end else None,
                'generic_environment_code': env.generic_environment.code if env.generic_environment else None,
                'environment_details': env.environment_details if hasattr(env, 'environment_details') else None
            }
            
            # Add related environment properties
            if hasattr(env, 'physical_properties') and env.physical_properties:
                env_data['physical_properties'] = self._export_physical_properties(env.physical_properties)
            
            if hasattr(env, 'social_context') and env.social_context:
                env_data['social_context'] = self._export_social_context(env.social_context)
            
            if hasattr(env, 'activity_support') and env.activity_support:
                env_data['activity_support'] = self._export_activity_support(env.activity_support)
            
            if hasattr(env, 'psychological_qualities') and env.psychological_qualities:
                env_data['psychological_qualities'] = self._export_psychological_qualities(env.psychological_qualities)
            
            exported_environments.append(env_data)
        
        return exported_environments
    
    def _export_physical_properties(self, props: UserEnvironmentPhysicalProperties) -> Dict[str, Any]:
        """Export physical properties"""
        return {
            'rurality': props.rurality,
            'noise_level': props.noise_level,
            'light_quality': props.light_quality,
            'temperature_range': props.temperature_range,
            'accessibility': props.accessibility,
            'air_quality': props.air_quality,
            'has_natural_elements': props.has_natural_elements,
            'surface_type': props.surface_type,
            'water_proximity': props.water_proximity,
            'space_size': props.space_size
        }
    
    def _export_social_context(self, context: UserEnvironmentSocialContext) -> Dict[str, Any]:
        """Export social context"""
        return {
            'privacy_level': context.privacy_level,
            'typical_occupancy': context.typical_occupancy,
            'social_interaction_level': context.social_interaction_level,
            'formality_level': context.formality_level,
            'safety_level': context.safety_level,
            'supervision_level': context.supervision_level,
            'cultural_diversity': context.cultural_diversity
        }
    
    def _export_activity_support(self, support: UserEnvironmentActivitySupport) -> Dict[str, Any]:
        """Export activity support"""
        return {
            'digital_connectivity': support.digital_connectivity,
            'resource_availability': support.resource_availability,
            'domain_specific_support': support.domain_specific_support,
            'time_availability': support.time_availability
        }
    
    def _export_psychological_qualities(self, qualities: UserEnvironmentPsychologicalQualities) -> Dict[str, Any]:
        """Export psychological qualities"""
        return {
            'restorative_quality': qualities.restorative_quality,
            'stimulation_level': qualities.stimulation_level,
            'aesthetic_appeal': qualities.aesthetic_appeal,
            'novelty_level': qualities.novelty_level,
            'comfort_level': qualities.comfort_level,
            'personal_significance': qualities.personal_significance,
            'emotional_associations': qualities.emotional_associations
        }
    
    def _export_traits(self, traits) -> List[Dict[str, Any]]:
        """Export personality traits"""
        exported_traits = []
        
        for trait in traits:
            trait_data = {
                'trait_code': trait.generic_trait.code if trait.generic_trait else None,
                'strength': float(trait.strength) if trait.strength else 0,
                'awareness': trait.awareness
            }
            exported_traits.append(trait_data)
        
        return exported_traits
    
    def _export_beliefs(self, beliefs) -> List[Dict[str, Any]]:
        """Export beliefs with evidence"""
        exported_beliefs = []
        
        for belief in beliefs:
            belief_data = {
                'content': belief.content,
                'user_confidence': belief.user_confidence,
                'system_confidence': belief.system_confidence,
                'emotionality': belief.emotionality,
                'stability': belief.stability,
                'user_awareness': belief.user_awareness,
                'evidence': []
            }
            
            # Add evidence
            for evidence in belief.evidence_set.all():
                evidence_data = {
                    'evidence_type': evidence.evidence_type,
                    'description': evidence.description,
                    'credibility_score': float(evidence.credibility_score) if evidence.credibility_score else None,
                    'source': evidence.source
                }
                belief_data['evidence'].append(evidence_data)
            
            exported_beliefs.append(belief_data)
        
        return exported_beliefs

    def _export_aspirations(self, aspirations) -> List[Dict[str, Any]]:
        """Export aspirations"""
        exported_aspirations = []

        for aspiration in aspirations:
            aspiration_data = {
                'title': aspiration.title,
                'description': aspiration.description,
                'importance_according_user': aspiration.importance_according_user,
                'importance_according_system': aspiration.importance_according_system,
                'strength': aspiration.strength,
                'domain': aspiration.domain,
                'horizon': aspiration.horizon,
                'level_of_ambition': aspiration.level_of_ambition,
                'effective_start': aspiration.effective_start.isoformat() if aspiration.effective_start else None,
                'duration_estimate': aspiration.duration_estimate,
                'effective_end': aspiration.effective_end.isoformat() if aspiration.effective_end else None
            }
            exported_aspirations.append(aspiration_data)

        return exported_aspirations

    def _export_intentions(self, intentions) -> List[Dict[str, Any]]:
        """Export intentions"""
        exported_intentions = []

        for intention in intentions:
            intention_data = {
                'title': intention.title,
                'description': intention.description,
                'importance_according_user': intention.importance_according_user,
                'importance_according_system': intention.importance_according_system,
                'strength': intention.strength,
                'start_date': intention.start_date.isoformat() if intention.start_date else None,
                'due_date': intention.due_date.isoformat() if intention.due_date else None,
                'is_completed': intention.is_completed,
                'progress_notes': intention.progress_notes,
                'effective_start': intention.effective_start.isoformat() if intention.effective_start else None,
                'duration_estimate': intention.duration_estimate,
                'effective_end': intention.effective_end.isoformat() if intention.effective_end else None
            }
            exported_intentions.append(intention_data)

        return exported_intentions

    def _export_inspirations(self, inspirations) -> List[Dict[str, Any]]:
        """Export inspirations"""
        exported_inspirations = []

        for inspiration in inspirations:
            inspiration_data = {
                'source': inspiration.source,
                'description': inspiration.description,
                'strength': inspiration.strength,
                'reference_url': inspiration.reference_url
            }
            exported_inspirations.append(inspiration_data)

        return exported_inspirations

    def _export_skills(self, skills) -> List[Dict[str, Any]]:
        """Export skills"""
        exported_skills = []

        for skill in skills:
            skill_data = {
                'skill_code': skill.generic_skill.code if skill.generic_skill else None,
                'description': skill.description,
                'level': skill.level,
                'user_awareness': skill.user_awareness,
                'user_enjoyment': skill.user_enjoyment,
                'note': skill.note,
                'last_practiced': skill.last_practiced.isoformat() if skill.last_practiced else None,
                'acquisition_date': skill.acquisition_date.isoformat() if skill.acquisition_date else None
            }
            exported_skills.append(skill_data)

        return exported_skills

    def _export_resources(self, resources) -> List[Dict[str, Any]]:
        """Export resources"""
        exported_resources = []

        for resource in resources:
            resource_data = {
                'specific_name': resource.specific_name,
                'resource_code': resource.generic_resource.code if resource.generic_resource else None,
                'location_details': resource.location_details,
                'ownership_details': resource.ownership_details,
                'contact_info': resource.contact_info,
                'notes': resource.notes
            }
            exported_resources.append(resource_data)

        return exported_resources

    def _export_limitations(self, limitations) -> List[Dict[str, Any]]:
        """Export limitations"""
        exported_limitations = []

        for limitation in limitations:
            limitation_data = {
                'limitation_code': limitation.generic_limitation.code if limitation.generic_limitation else None,
                'severity': limitation.severity,
                'is_unlimited': limitation.is_unlimited,
                'user_awareness': limitation.user_awareness,
                'valid_until': limitation.valid_until.isoformat() if limitation.valid_until else None,
                'effective_start': limitation.effective_start.isoformat() if limitation.effective_start else None,
                'duration_estimate': limitation.duration_estimate,
                'effective_end': limitation.effective_end.isoformat() if limitation.effective_end else None
            }
            exported_limitations.append(limitation_data)

        return exported_limitations

    def _export_preferences(self, preferences) -> List[Dict[str, Any]]:
        """Export preferences"""
        exported_preferences = []

        for preference in preferences:
            preference_data = {
                'pref_name': preference.pref_name,
                'pref_description': preference.pref_description,
                'pref_strength': preference.pref_strength,
                'user_awareness': preference.user_awareness,
                'environment_specific': preference.environment_specific,
                'effective_start': preference.effective_start.isoformat() if preference.effective_start else None,
                'duration_estimate': preference.duration_estimate,
                'effective_end': preference.effective_end.isoformat() if preference.effective_end else None
            }
            exported_preferences.append(preference_data)

        return exported_preferences

    def _export_current_mood(self, mood: CurrentMood) -> Dict[str, Any]:
        """Export current mood"""
        return {
            'description': mood.description,
            'height': mood.height,
            'user_awareness': mood.user_awareness,
            'effective_start': mood.effective_start.isoformat() if mood.effective_start else None,
            'duration_estimate': mood.duration_estimate,
            'effective_end': mood.effective_end.isoformat() if mood.effective_end else None
        }

    def _export_trust_level(self, trust: TrustLevel) -> Dict[str, Any]:
        """Export trust level"""
        return {
            'value': trust.value,
            'aggregate_type': trust.aggregate_type,
            'aggregate_id': trust.aggregate_id,
            'notes': trust.notes
        }

    def _export_tailored_activities(self, profile: UserProfile) -> List[Dict[str, Any]]:
        """Export tailored activities for this user"""
        try:
            # Get tailored activities created by this user
            tailored_activities = ActivityTailored.objects.filter(
                created_by=profile.user
            ).select_related('generic_activity')

            exported_activities = []

            for activity in tailored_activities:
                activity_data = {
                    'activity_code': activity.generic_activity.code if activity.generic_activity else None,
                    'tailored_name': activity.tailored_name,
                    'tailored_description': activity.tailored_description,
                    'tailored_instructions': activity.tailored_instructions,
                    'estimated_duration_minutes': activity.estimated_duration_minutes,
                    'difficulty_level': activity.difficulty_level,
                    'required_resources': activity.required_resources or []
                }
                exported_activities.append(activity_data)

            return exported_activities

        except Exception as e:
            logger.warning(f"Could not export tailored activities for profile {profile.id}: {e}")
            return []


def export_profiles_to_json(profile_ids: List[int]) -> Dict[str, Any]:
    """
    Convenience function for exporting profiles to JSON

    Args:
        profile_ids: List of UserProfile IDs to export

    Returns:
        Dict containing exported profiles and metadata
    """
    service = ProfileExportService()
    return service.export_profiles(profile_ids)
