"""
Enhanced Profile Validation Service

Provides detailed validation with precise error messages and warnings
for user profile import data.
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from django.core.exceptions import ValidationError
from rest_framework import serializers

from apps.user.serializers.import_serializers import (
    CompleteUserProfileSerializer, UserProfileImportRequestSerializer
)

logger = logging.getLogger(__name__)


class ValidationResult:
    """Container for validation results with detailed feedback"""
    
    def __init__(self):
        self.is_valid = True
        self.errors: List[Dict[str, Any]] = []
        self.warnings: List[Dict[str, Any]] = []
        self.field_errors: Dict[str, List[str]] = {}
        self.summary: Dict[str, Any] = {}
    
    def add_error(self, field: str, message: str, code: str = 'invalid', context: Dict[str, Any] = None):
        """Add a validation error"""
        self.is_valid = False
        error = {
            'field': field,
            'message': message,
            'code': code,
            'context': context or {}
        }
        self.errors.append(error)
        
        if field not in self.field_errors:
            self.field_errors[field] = []
        self.field_errors[field].append(message)
    
    def add_warning(self, field: str, message: str, code: str = 'warning', context: Dict[str, Any] = None):
        """Add a validation warning"""
        warning = {
            'field': field,
            'message': message,
            'code': code,
            'context': context or {}
        }
        self.warnings.append(warning)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'is_valid': self.is_valid,
            'errors': self.errors,
            'warnings': self.warnings,
            'field_errors': self.field_errors,
            'summary': self.summary
        }


class ProfileValidationService:
    """Enhanced validation service for user profile imports"""
    
    def __init__(self):
        self.result = ValidationResult()
    
    def validate_import_request(self, data: Dict[str, Any]) -> ValidationResult:
        """
        Validate a complete import request with detailed error reporting
        
        Args:
            data: The import request data
            
        Returns:
            ValidationResult with detailed feedback
        """
        self.result = ValidationResult()
        
        try:
            # First, validate with DRF serializer
            serializer = UserProfileImportRequestSerializer(data=data)
            
            if not serializer.is_valid():
                self._process_serializer_errors(serializer.errors)
            else:
                # Additional custom validation
                profile_data = serializer.validated_data.get('profile_data', {})
                self._validate_profile_completeness(profile_data)
                self._validate_data_consistency(profile_data)
                self._validate_business_rules(profile_data)
                
                # Generate summary
                self._generate_validation_summary(profile_data)
            
        except Exception as e:
            logger.error(f"Unexpected validation error: {e}")
            self.result.add_error(
                'system', 
                f'Unexpected validation error: {str(e)}',
                'system_error'
            )
        
        return self.result
    
    def _process_serializer_errors(self, errors: Dict[str, Any], parent_field: str = ''):
        """Process DRF serializer errors into detailed messages"""
        for field, field_errors in errors.items():
            full_field = f"{parent_field}.{field}" if parent_field else field
            
            if isinstance(field_errors, dict):
                # Nested errors
                self._process_serializer_errors(field_errors, full_field)
            elif isinstance(field_errors, list):
                for error in field_errors:
                    if isinstance(error, dict):
                        # List item errors
                        for sub_field, sub_errors in error.items():
                            self._process_serializer_errors({sub_field: sub_errors}, f"{full_field}[item]")
                    else:
                        # Simple error message
                        self.result.add_error(
                            full_field,
                            self._enhance_error_message(str(error), full_field),
                            'validation_error'
                        )
    
    def _enhance_error_message(self, message: str, field: str) -> str:
        """Enhance error messages with more context"""
        enhancements = {
            'This field may not be blank.': f"The field '{field}' is required and cannot be empty.",
            'This field is required.': f"The field '{field}' is required but was not provided.",
            'Enter a valid URL.': f"The field '{field}' must be a valid URL (e.g., https://example.com).",
            'Enter a valid email address.': f"The field '{field}' must be a valid email address.",
            'Ensure this value is less than or equal to 100.': f"The field '{field}' must be 100 or less.",
            'Ensure this value is greater than or equal to 0.': f"The field '{field}' must be 0 or greater.",
            'Not a valid date.': f"The field '{field}' must be a valid date in YYYY-MM-DD format.",
        }
        
        return enhancements.get(message, message)
    
    def _validate_profile_completeness(self, profile_data: Dict[str, Any]):
        """Validate profile completeness and suggest improvements"""
        completeness_score = 0
        total_sections = 12
        
        # Check major sections
        sections = [
            ('user_account', 'User account information'),
            ('profile_name', 'Profile name'),
            ('demographics', 'Demographics information'),
            ('environments', 'Environment details'),
            ('traits', 'Personality traits'),
            ('beliefs', 'Personal beliefs'),
            ('aspirations', 'Goals and aspirations'),
            ('skills', 'Skills and abilities'),
            ('resources', 'Available resources'),
            ('limitations', 'Known limitations'),
            ('preferences', 'Personal preferences'),
            ('current_mood', 'Current mood state')
        ]
        
        for field, description in sections:
            if field in profile_data and profile_data[field]:
                completeness_score += 1
            else:
                self.result.add_warning(
                    field,
                    f"Consider adding {description} for a more complete profile",
                    'completeness'
                )
        
        completeness_percentage = (completeness_score / total_sections) * 100
        
        if completeness_percentage < 50:
            self.result.add_warning(
                'profile_completeness',
                f"Profile is only {completeness_percentage:.0f}% complete. Consider adding more sections.",
                'low_completeness'
            )
    
    def _validate_data_consistency(self, profile_data: Dict[str, Any]):
        """Validate data consistency across different sections"""
        # Check environment consistency
        environments = profile_data.get('environments', [])
        if environments:
            current_envs = [env for env in environments if env.get('is_current', False)]
            if len(current_envs) == 0:
                self.result.add_error(
                    'environments',
                    'At least one environment must be marked as current (is_current: true)',
                    'missing_current_environment'
                )
            elif len(current_envs) > 1:
                self.result.add_error(
                    'environments',
                    'Only one environment can be marked as current',
                    'multiple_current_environments'
                )
        
        # Check skill levels
        skills = profile_data.get('skills', [])
        for i, skill in enumerate(skills):
            level = skill.get('level', 0)
            awareness = skill.get('user_awareness')
            if awareness is not None and awareness > level + 20:
                self.result.add_warning(
                    f'skills[{i}].user_awareness',
                    f"User awareness ({awareness}) seems unusually high compared to skill level ({level})",
                    'inconsistent_awareness'
                )
    
    def _validate_business_rules(self, profile_data: Dict[str, Any]):
        """Validate business-specific rules"""
        # Check for realistic age ranges
        demographics = profile_data.get('demographics', {})
        if 'age' in demographics:
            age = demographics['age']
            if age < 13:
                self.result.add_warning(
                    'demographics.age',
                    'Age below 13 may require special handling for privacy compliance',
                    'privacy_concern'
                )
            elif age > 120:
                self.result.add_warning(
                    'demographics.age',
                    'Age above 120 seems unrealistic',
                    'unrealistic_value'
                )
        
        # Check for reasonable skill counts
        skills = profile_data.get('skills', [])
        if len(skills) > 50:
            self.result.add_warning(
                'skills',
                f'Profile has {len(skills)} skills, which is quite high. Consider focusing on key skills.',
                'high_skill_count'
            )
        
        # Check for balanced trait inclinations
        traits = profile_data.get('traits', [])
        if traits:
            extreme_traits = [
                trait for trait in traits 
                if trait.get('inclination', 50) in [0, 100]
            ]
            if len(extreme_traits) > len(traits) * 0.3:  # More than 30% extreme
                self.result.add_warning(
                    'traits',
                    'Many traits have extreme values (0 or 100). Consider more balanced inclinations.',
                    'extreme_traits'
                )
    
    def _generate_validation_summary(self, profile_data: Dict[str, Any]):
        """Generate a summary of the profile data"""
        summary = {
            'profile_name': profile_data.get('profile_name', 'Unknown'),
            'username': profile_data.get('user_account', {}).get('username', 'Unknown'),
            'sections_count': {},
            'completeness_score': 0
        }
        
        # Count items in each section
        countable_sections = [
            'environments', 'traits', 'beliefs', 'aspirations', 
            'intentions', 'inspirations', 'skills', 'resources', 
            'limitations', 'preferences'
        ]
        
        for section in countable_sections:
            items = profile_data.get(section, [])
            if isinstance(items, list):
                summary['sections_count'][section] = len(items)
        
        # Calculate completeness
        total_sections = 12
        present_sections = sum(1 for key in [
            'user_account', 'profile_name', 'demographics', 'environments',
            'traits', 'beliefs', 'aspirations', 'skills', 'resources',
            'limitations', 'preferences', 'current_mood'
        ] if key in profile_data and profile_data[key])
        
        summary['completeness_score'] = (present_sections / total_sections) * 100
        
        self.result.summary = summary


def validate_profile_import(data: Dict[str, Any]) -> ValidationResult:
    """
    Convenience function for validating profile import data
    
    Args:
        data: The import request data
        
    Returns:
        ValidationResult with detailed feedback
    """
    service = ProfileValidationService()
    return service.validate_import_request(data)
