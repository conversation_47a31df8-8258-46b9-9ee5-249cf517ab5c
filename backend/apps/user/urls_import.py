from django.urls import path
from apps.user.views.import_views import (
    ProfileImportView, AIProfileGenerateView, ProfileSchemaView, ProfileValidationView,
    ProfileSchemaValidationView, BatchProfileExportView, BatchProfileDeleteView
)

app_name = 'user_import'

urlpatterns = [
    # Profile import endpoints
    path('import/', ProfileImportView.as_view(), name='import_profile'),
    path('validate/', ProfileValidationView.as_view(), name='validate_profile'),
    path('validate-schema/', ProfileSchemaValidationView.as_view(), name='validate_schema'),
    path('ai-generate/', AIProfileGenerateView.as_view(), name='ai_generate_profile'),
    path('schema/', ProfileSchemaView.as_view(), name='profile_schema'),

    # Batch operations
    path('batch-export/', BatchProfileExportView.as_view(), name='batch_export'),
    path('batch-delete/', BatchProfileDeleteView.as_view(), name='batch_delete'),
]
