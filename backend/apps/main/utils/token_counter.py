"""
Accurate Token Counting Utility
Based on OpenAI's tiktoken library and best practices from OpenAI Cookbook
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TokenUsage:
    """Token usage information."""
    input_tokens: int
    output_tokens: int
    total_tokens: int
    
    @property
    def total(self) -> int:
        return self.total_tokens

class TokenCounter:
    """
    Accurate token counter using tiktoken library.
    Supports OpenAI models and function calls.
    """
    
    def __init__(self):
        self._encodings = {}
        self._initialize_tiktoken()
    
    def _initialize_tiktoken(self):
        """Initialize tiktoken library."""
        try:
            import tiktoken
            self.tiktoken = tiktoken
            logger.debug("tiktoken library initialized successfully")
        except ImportError:
            logger.warning("tiktoken library not available. Token counting will be approximate.")
            self.tiktoken = None
    
    def get_encoding_for_model(self, model_name: str):
        """Get encoding for a specific model."""
        if not self.tiktoken:
            return None
            
        if model_name not in self._encodings:
            try:
                self._encodings[model_name] = self.tiktoken.encoding_for_model(model_name)
            except KeyError:
                logger.warning(f"Model {model_name} not found in tiktoken. Using cl100k_base encoding.")
                self._encodings[model_name] = self.tiktoken.get_encoding("cl100k_base")
        
        return self._encodings[model_name]
    
    def count_tokens_in_string(self, text: str, model_name: str = "gpt-4o-mini") -> int:
        """Count tokens in a text string."""
        if not self.tiktoken or not text:
            # Fallback: approximate token count (1 token ≈ 4 characters for English)
            return len(text) // 4
        
        encoding = self.get_encoding_for_model(model_name)
        if not encoding:
            return len(text) // 4
        
        return len(encoding.encode(text))
    
    def count_tokens_in_messages(
        self, 
        messages: List[Dict[str, Any]], 
        model_name: str = "gpt-4o-mini"
    ) -> int:
        """
        Count tokens in chat completion messages.
        Based on OpenAI Cookbook implementation.
        """
        if not self.tiktoken or not messages:
            return 0
        
        encoding = self.get_encoding_for_model(model_name)
        if not encoding:
            # Fallback calculation
            total_chars = sum(len(str(msg.get('content', ''))) for msg in messages)
            return total_chars // 4
        
        # Token counting logic based on model
        if model_name in {
            "gpt-3.5-turbo-0125",
            "gpt-4-0314",
            "gpt-4-32k-0314", 
            "gpt-4-0613",
            "gpt-4-32k-0613",
            "gpt-4o-mini-2024-07-18",
            "gpt-4o-2024-08-06"
        }:
            tokens_per_message = 3
            tokens_per_name = 1
        elif "gpt-3.5-turbo" in model_name:
            tokens_per_message = 3
            tokens_per_name = 1
        elif "gpt-4o-mini" in model_name:
            tokens_per_message = 3
            tokens_per_name = 1
        elif "gpt-4o" in model_name:
            tokens_per_message = 3
            tokens_per_name = 1
        elif "gpt-4" in model_name:
            tokens_per_message = 3
            tokens_per_name = 1
        else:
            # Default values
            tokens_per_message = 3
            tokens_per_name = 1
        
        num_tokens = 0
        for message in messages:
            num_tokens += tokens_per_message
            for key, value in message.items():
                if value:  # Only count non-empty values
                    num_tokens += len(encoding.encode(str(value)))
                    if key == "name":
                        num_tokens += tokens_per_name
        
        num_tokens += 3  # Every reply is primed with <|start|>assistant<|message|>
        return num_tokens
    
    def count_tokens_with_functions(
        self,
        messages: List[Dict[str, Any]],
        functions: Optional[List[Dict[str, Any]]] = None,
        model_name: str = "gpt-4o-mini"
    ) -> int:
        """
        Count tokens including function definitions.
        Based on OpenAI Cookbook implementation.
        """
        if not functions:
            return self.count_tokens_in_messages(messages, model_name)
        
        if not self.tiktoken:
            # Fallback calculation
            message_tokens = self.count_tokens_in_messages(messages, model_name)
            function_chars = sum(len(str(func)) for func in functions)
            return message_tokens + (function_chars // 4)
        
        encoding = self.get_encoding_for_model(model_name)
        if not encoding:
            message_tokens = self.count_tokens_in_messages(messages, model_name)
            function_chars = sum(len(str(func)) for func in functions)
            return message_tokens + (function_chars // 4)
        
        # Function token counting settings by model
        if model_name in ["gpt-4o", "gpt-4o-mini"]:
            func_init = 7
            prop_init = 3
            prop_key = 3
            enum_init = -3
            enum_item = 3
            func_end = 12
        elif model_name in ["gpt-3.5-turbo", "gpt-4"]:
            func_init = 10
            prop_init = 3
            prop_key = 3
            enum_init = -3
            enum_item = 3
            func_end = 12
        else:
            # Default values
            func_init = 7
            prop_init = 3
            prop_key = 3
            enum_init = -3
            enum_item = 3
            func_end = 12
        
        func_token_count = 0
        if functions:
            for f in functions:
                func_token_count += func_init
                function = f.get("function", {})
                f_name = function.get("name", "")
                f_desc = function.get("description", "")
                
                if f_desc.endswith("."):
                    f_desc = f_desc[:-1]
                
                line = f_name + ":" + f_desc
                func_token_count += len(encoding.encode(line))
                
                properties = function.get("parameters", {}).get("properties", {})
                if properties:
                    func_token_count += prop_init
                    for key, prop in properties.items():
                        func_token_count += prop_key
                        p_name = key
                        p_type = prop.get("type", "")
                        p_desc = prop.get("description", "")
                        
                        if "enum" in prop:
                            func_token_count += enum_init
                            for item in prop["enum"]:
                                func_token_count += enum_item
                                func_token_count += len(encoding.encode(str(item)))
                        
                        if p_desc.endswith("."):
                            p_desc = p_desc[:-1]
                        
                        line = f"{p_name}:{p_type}:{p_desc}"
                        func_token_count += len(encoding.encode(line))
            
            func_token_count += func_end
        
        message_tokens = self.count_tokens_in_messages(messages, model_name)
        return message_tokens + func_token_count
    
    def estimate_cost(
        self, 
        input_tokens: int, 
        output_tokens: int,
        input_price_per_token: float,
        output_price_per_token: float
    ) -> float:
        """Estimate cost based on token usage and pricing."""
        input_cost = input_tokens * input_price_per_token
        output_cost = output_tokens * output_price_per_token
        return input_cost + output_cost

# Global instance
token_counter = TokenCounter()
