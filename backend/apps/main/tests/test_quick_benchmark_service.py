"""
Comprehensive unit tests for quick benchmark functionality
Follows Django testing best practices and existing test patterns
"""
from django.test import TestCase, TransactionTestCase
from django.core.exceptions import ValidationError
from unittest.mock import Mock, patch, AsyncMock
import asyncio

from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
from apps.main.services.simple_evaluation_adapter import SimpleEvaluationAdapter
from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from apps.user.models import UserProfile
from apps.main.models import GenericAgent, BenchmarkRun, BenchmarkScenario

class TestBenchmarkProfileFactory(TestCase):
    """Test benchmark profile factory functionality."""
    
    def test_create_benchmark_profile_success(self):
        """Test successful profile creation."""
        profile = BenchmarkProfileFactory.create_benchmark_profile('anxious_new_user')
        
        self.assertIsInstance(profile, UserProfile)
        self.assertFalse(profile.is_real)  # Key assertion
        self.assertEqual(profile.profile_name, 'Benchmark_Anxious_New_User')
        self.assertTrue(profile.trait_inclinations.exists())
        self.assertTrue(profile.goals.exists())
        self.assertTrue(profile.beliefs.exists())
    
    def test_create_benchmark_profile_invalid_template(self):
        """Test error handling for invalid template."""
        with self.assertRaises(ValidationError):
            BenchmarkProfileFactory.create_benchmark_profile('invalid_template')
    
    def test_get_or_create_idempotent(self):
        """Test that get_or_create is idempotent."""
        profile1 = BenchmarkProfileFactory.get_or_create_benchmark_profile('anxious_new_user')
        profile2 = BenchmarkProfileFactory.get_or_create_benchmark_profile('anxious_new_user')
        
        self.assertEqual(profile1.id, profile2.id)
    
    def test_list_available_templates(self):
        """Test listing available templates."""
        templates = BenchmarkProfileFactory.list_available_templates()
        
        self.assertIsInstance(templates, list)
        self.assertGreater(len(templates), 0)
        
        # Check template structure
        template = templates[0]
        self.assertIn('template_name', template)
        self.assertIn('profile_name', template)
        self.assertIn('description', template)

class TestSimpleEvaluationAdapter(TestCase):
    """Test simple evaluation adapter functionality."""
    
    def setUp(self):
        self.adapter = SimpleEvaluationAdapter()
        self.mock_benchmark_run = Mock(spec=BenchmarkRun)
    
    @patch('apps.main.services.simple_evaluation_adapter.SemanticEvaluator')
    def test_evaluate_with_simple_prompt_success(self, mock_evaluator_class):
        """Test successful evaluation with simple prompt."""
        # Setup mock
        mock_evaluator = Mock()
        mock_evaluator_class.return_value = mock_evaluator
        
        # Create async mock for evaluate_response
        async def mock_evaluate_response(*args, **kwargs):
            return {'score': 8.5, 'details': 'Good response'}
        
        mock_evaluator.evaluate_response = AsyncMock(side_effect=mock_evaluate_response)
        
        # Create adapter with mocked evaluator
        adapter = SimpleEvaluationAdapter(semantic_evaluator=mock_evaluator)
        
        # Run test
        async def run_test():
            result = await adapter.evaluate_with_simple_prompt(
                agent_response="Test response",
                evaluation_template="mentor_helpfulness",
                context={'scenario_context': 'Test scenario'},
                benchmark_run=self.mock_benchmark_run
            )
            return result
        
        result = asyncio.run(run_test())
        
        self.assertEqual(result['score'], 8.5)
        mock_evaluator.evaluate_response.assert_called_once()
    
    def test_evaluate_invalid_template(self):
        """Test error handling for invalid evaluation template."""
        async def run_test():
            with self.assertRaises(ValidationError):
                await self.adapter.evaluate_with_simple_prompt(
                    agent_response="Test",
                    evaluation_template="invalid_template",
                    context={},
                    benchmark_run=self.mock_benchmark_run
                )
        
        asyncio.run(run_test())
    
    def test_create_custom_evaluation(self):
        """Test creating custom evaluation template."""
        criteria_weights = {
            'quality': 0.5,
            'relevance': 0.3,
            'clarity': 0.2
        }
        
        template_key = self.adapter.create_custom_evaluation(
            prompt="Evaluate the response quality",
            criteria_weights=criteria_weights
        )
        
        self.assertIsInstance(template_key, str)
        self.assertIn(template_key, self.adapter.EVALUATION_TEMPLATES)
    
    def test_create_custom_evaluation_invalid_weights(self):
        """Test validation of criteria weights."""
        criteria_weights = {
            'quality': 0.5,
            'relevance': 0.3,
            'clarity': 0.3  # Total = 1.1, should fail
        }
        
        with self.assertRaises(ValidationError):
            self.adapter.create_custom_evaluation(
                prompt="Test prompt",
                criteria_weights=criteria_weights
            )

class TestQuickBenchmarkService(TransactionTestCase):
    """Test quick benchmark service integration."""
    
    def setUp(self):
        # Create test agent
        self.agent = GenericAgent.objects.create(
            name='test_mentor',
            role='mentor',
            description='Test mentor agent'
        )
        
        self.service = QuickBenchmarkService()
    
    def test_get_agent_success(self):
        """Test successful agent retrieval."""
        agent = self.service._get_agent('test_mentor')
        self.assertEqual(agent.name, 'test_mentor')
    
    def test_get_agent_not_found(self):
        """Test error handling for non-existent agent."""
        with self.assertRaises(ValidationError):
            self.service._get_agent('nonexistent_agent')
    
    def test_create_quick_scenario(self):
        """Test quick scenario creation."""
        # Create a test profile
        profile = BenchmarkProfileFactory.create_benchmark_profile('anxious_new_user')
        
        scenario = self.service._create_quick_scenario(
            agent=self.agent,
            user_profile=profile,
            context={'user_input': 'Test input'}
        )
        
        self.assertIsInstance(scenario, BenchmarkScenario)
        self.assertEqual(scenario.agent_role, 'mentor')
        self.assertIn('Quick_test_mentor', scenario.name)
        self.assertTrue(scenario.is_active)
    
    def test_get_available_options(self):
        """Test retrieval of available benchmark options."""
        options = self.service.get_available_options()
        
        self.assertIn('profile_templates', options)
        self.assertIn('evaluation_templates', options)
        self.assertIn('available_agents', options)
        self.assertGreater(len(options['profile_templates']), 0)
        self.assertGreater(len(options['evaluation_templates']), 0)
    
    @patch('apps.main.services.quick_benchmark_service.AgentBenchmarker')
    def test_run_agent_benchmark_mock(self, mock_benchmarker_class):
        """Test agent benchmark execution with mocked AgentBenchmarker."""
        # Setup mock
        mock_benchmarker = Mock()
        mock_benchmarker_class.return_value = mock_benchmarker
        
        # Create mock benchmark run
        mock_run = Mock(spec=BenchmarkRun)
        mock_run.agent_output = "Test agent response"
        mock_run.id = "test-run-id"
        
        # Create async mock for run_agent_benchmark
        async def mock_run_agent_benchmark(*args, **kwargs):
            return mock_run
        
        mock_benchmarker.run_agent_benchmark = AsyncMock(side_effect=mock_run_agent_benchmark)
        
        # Create test scenario and profile
        profile = BenchmarkProfileFactory.create_benchmark_profile('anxious_new_user')
        scenario = BenchmarkScenario.objects.create(
            name='Test Scenario',
            description='Test scenario',
            agent_role='mentor',
            input_data={'test': 'data'},
            is_active=True,
            version=1,
            is_latest=True
        )
        
        # Run test
        async def run_test():
            result = await self.service._run_agent_benchmark(
                scenario=scenario,
                user_profile=profile,
                agent=self.agent
            )
            return result
        
        result = asyncio.run(run_test())
        
        self.assertEqual(result, mock_run)
        mock_benchmarker.run_agent_benchmark.assert_called_once()
