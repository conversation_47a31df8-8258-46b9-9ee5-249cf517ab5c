# apps/main/agents/resource_agent.py

import logging # Import logging
from typing import Dict, Any, Optional # Import Optional
from pydantic import BaseModel

from apps.main.agents.base_agent import LangGraphAgent
from apps.main.services.database_service import RealDatabaseService
from apps.main.llm.service import Real<PERSON><PERSON>lient
# Do NOT import Django models at module level to avoid AppRegistryNotReady errors
# from apps.main.models import LLMConfig

logger = logging.getLogger(__name__) # Add logger

class ResourceAgent(LangGraphAgent):
    """
    Resource agent that analyzes user's available resources, environment, and constraints.
     Determines what activities are feasible given the user's current context.
     """

    def __init__(self,
                 user_profile_id: str,
                 db_service=None,
                 llm_client=None,
                 llm_config: Optional[Any] = None): # Changed to use Any instead of LLMConfig
        # CRITICAL DEBUG LOG - This should always appear when ResourceAgent is instantiated
        print("🚨🚨🚨 RESOURCEAGENT CONSTRUCTOR CALLED 🚨🚨🚨")
        logger.error("🚨🚨🚨 RESOURCEAGENT CONSTRUCTOR CALLED 🚨🚨🚨")

        # Pass LLM config up to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="resource",
            llm_config=llm_config # Pass the LLMConfig object
        )

        # Use provided dependencies or create defaults
        self.db_service = db_service or RealDatabaseService()

        # Instantiate LLM client using config from base class if not provided externally
        if llm_client:
            self.llm_client = llm_client
        else:
            # RealLLMClient now requires an LLMConfig object
            if not self.llm_config:
                logger.debug(f"Instantiating RealLLMClient for ResourceAgent without LLMConfig: will use default")

            logger.debug(f"Instantiating RealLLMClient for ResourceAgent with LLMConfig: {self.llm_config.name if self.llm_config else 'None'}")
            self.llm_client = RealLLMClient(llm_config=self.llm_config) # Pass the LLMConfig object

        # Initialize definition and tools to None. They will be loaded lazily.
        self.agent_definition = None
        self.available_tools = []
        self.run_id = None # Initialize run_id

    async def _ensure_loaded(self):
        """Ensures agent definition and tools are loaded asynchronously."""
        if self.agent_definition is not None:
            # Already loaded
            return True

        logger.debug(f"Loading definition and tools for {self.agent_role}...")
        logger.error(f"🔍 ResourceAgent: _ensure_loaded called for role {self.agent_role}")
        logger.error(f"🔍 ResourceAgent: db_service type = {type(self.db_service)}")
        logger.error(f"🔍 ResourceAgent: has load_agent_definition_async = {hasattr(self.db_service, 'load_agent_definition_async')}")
        logger.error(f"🔍 ResourceAgent: has load_agent_definition = {hasattr(self.db_service, 'load_agent_definition')}")

        try:
            # Load agent definition
            if hasattr(self.db_service, 'load_agent_definition_async'):
                # Use the async method directly if available
                logger.error(f"🔍 ResourceAgent: Using load_agent_definition_async")
                self.agent_definition = await self.db_service.load_agent_definition_async(self.agent_role)
            elif hasattr(self.db_service, 'load_agent_definition'):
                # Check if it's already an async method
                if hasattr(self.db_service.load_agent_definition, '__await__'):
                    # It's already an async method
                    logger.error(f"🔍 ResourceAgent: Using async load_agent_definition")
                    self.agent_definition = await self.db_service.load_agent_definition(self.agent_role)
                else:
                    # It's a sync method, wrap it
                    from asgiref.sync import sync_to_async
                    logger.error(f"🔍 ResourceAgent: Using sync load_agent_definition with sync_to_async wrapper")
                    load_def_sync = sync_to_async(self.db_service.load_agent_definition, thread_sensitive=True)
                    self.agent_definition = await load_def_sync(self.agent_role)
            else:
                # Fallback to a default definition if no method is available
                logger.error(f"🔍 ResourceAgent: db_service has no load_agent_definition method, using default definition")
                self.agent_definition = {
                    "role": self.agent_role,
                    "description": "Resource agent that analyzes user's available resources",
                    "system_instructions": "Analyze the user's environment, time availability, and resources",
                    "version": "1.0.0"
                }

            logger.error(f"🔍 ResourceAgent: agent_definition loaded, type = {type(self.agent_definition)}")
            logger.error(f"🔍 ResourceAgent: agent_definition = {self.agent_definition}")

            # Load tools if we have a definition
            if self.agent_definition:
                if hasattr(self.db_service, 'load_tools_async'):
                    # Use the async method directly if available
                    self.available_tools = await self.db_service.load_tools_async(self.agent_definition)
                elif hasattr(self.db_service, 'load_tools'):
                    # Check if it's already an async method
                    if hasattr(self.db_service.load_tools, '__await__'):
                        # It's already an async method
                        self.available_tools = await self.db_service.load_tools(self.agent_definition)
                    else:
                        # It's a sync method, wrap it
                        self.available_tools = await self.db_service.load_tools(self.agent_definition)
                else:
                    # Fallback to empty tools list if no method is available
                    logger.warning(f"db_service has no load_tools method, using empty tools list")
                    self.available_tools = []

                logger.debug(f"Successfully loaded definition and {len(self.available_tools)} tools for {self.agent_role}")
                return True
            else:
                logger.error(f"Agent definition not found for role: {self.agent_role}")
                self.agent_definition = None # Ensure it's None if not found
                self.available_tools = []
                return False

        except Exception as e:
            logger.error(f"Failed to load definition/tools for {self.agent_role}: {e}", exc_info=True)
            self.agent_definition = None
            self.available_tools = []
            # Re-raise or handle as appropriate for the process method
            raise RuntimeError(f"Failed to load agent configuration for {self.agent_role}") from e

    async def process(self, state: BaseModel) -> Dict[str, Any]: # Return type changed
        """Analyze user's resources, environment and constraints."""
        # CRITICAL DEBUG LOG - This should always appear
        print("🚨🚨🚨 RESOURCEAGENT PROCESS METHOD CALLED 🚨🚨🚨")
        logger.error("🚨🚨🚨 RESOURCEAGENT PROCESS METHOD CALLED 🚨🚨🚨")

        # --- Ensure Agent Definition and Tools are Loaded ---
        current_operation = "ensuring_agent_loaded"
        logger.error(f"🔍 ResourceAgent: About to call _ensure_loaded()")
        try:
            self.start_stage('resource_ensure_loaded')
            logger.error(f"🔍 ResourceAgent: Calling _ensure_loaded() now...")
            await self._ensure_loaded()
            logger.error(f"🔍 ResourceAgent: _ensure_loaded() completed successfully")
            self.stop_stage('resource_ensure_loaded')
            if self.agent_definition is None:
                logger.error(f"🔍 ResourceAgent: agent_definition is None after _ensure_loaded()")
                raise RuntimeError(f"Agent definition for role '{self.agent_role}' could not be loaded.")
            else:
                logger.error(f"🔍 ResourceAgent: agent_definition loaded successfully, type={type(self.agent_definition)}")
        except Exception as load_error:
            self.stop_stage('resource_ensure_loaded') # Stop stage even on error
            error_message = f"Critical error: Failed to load agent configuration during '{current_operation}': {str(load_error)}"
            logger.error(f"🔍 ResourceAgent: _ensure_loaded() failed with error: {load_error}")
            logger.error(error_message, exc_info=True)
            error_output_data = {
                "combined_resource_context": {},  # Include empty combined_resource_context
                "resource_context": {},  # Include empty resource_context for compatibility
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation}
            }
            return {"error": error_message, "output_data": error_output_data}
        # --- End Loading Check ---

        current_operation = "extracting_state"
        # Extract relevant data from state
        context_packet = getattr(state, "context_packet", {})

        # Get workflow_id from state (state is a Pydantic model, not a dict)
        workflow_id = getattr(state, "workflow_id", "unknown")

        # Convert user_profile_id to int for DB calls
        # CRITICAL FIX: Don't convert real user IDs that are passed in benchmarks
        try:
            # First, try to convert directly to int (for real user IDs like "2")
            user_profile_id_int = int(self.user_profile_id)
            logger.debug(f"Using real user_profile_id: {user_profile_id_int}")
        except ValueError:
            # Only if conversion fails, check if it's a test/benchmark ID
            if isinstance(self.user_profile_id, str) and (
                self.user_profile_id.startswith('test-user-') or
                self.user_profile_id.startswith('benchmark-user-') or
                self.user_profile_id.startswith('debugger-user-') or
                self.user_profile_id == 'benchmark_user' or
                'test-user-' in self.user_profile_id or
                'debugger-user-' in self.user_profile_id or
                'benchmark' in self.user_profile_id.lower()
            ):
                # For test/benchmark IDs, use a default integer ID
                user_profile_id_int = 1  # Use a default test/benchmark ID
                logger.debug(f"Using default user_profile_id (1) for test/benchmark ID: {self.user_profile_id}")
            else:
                error_message = f"Invalid user_profile_id format: {self.user_profile_id}"
                logger.error(f"{error_message}. Cannot convert to int for DB.")
                error_output_data = {
                    "combined_resource_context": {},  # Include empty combined_resource_context
                    "resource_context": {},  # Include empty resource_context for compatibility
                    "error": error_message,
                    "debug": {"last_error": error_message, "failed_operation": "user_id_conversion"}
                }
                return {"error": error_message, "output_data": error_output_data}

        current_operation = "starting_run"
        # Start profiling DB start
        self.start_stage('resource_db_start_run')
        # Start a run in the database
        logger.error(f"🔍 ResourceAgent: About to call start_run with user_profile_id={user_profile_id_int}")
        logger.error(f"🔍 ResourceAgent: agent_definition={self.agent_definition}")
        logger.error(f"🔍 ResourceAgent: db_service={self.db_service}")
        try:
            run = await self.db_service.start_run(
                agent_definition=self.agent_definition, # Use loaded definition
                user_profile_id=user_profile_id_int, # Use int ID
                input_data={
                    "context_packet": context_packet
                },
                state={"workflow_id": workflow_id}
            )
            self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id'))
            logger.error(f"🔍 ResourceAgent: start_run successful, run_id={self.run_id}")
        except Exception as e:
            logger.error(f"🔍 ResourceAgent: start_run failed: {e}")
            import traceback
            logger.error(f"🔍 ResourceAgent: start_run traceback: {traceback.format_exc()}")
            self.run_id = "mock-run-id"  # Fallback
        self.stop_stage('resource_db_start_run')

        try:
            current_operation = "analyzing_environment"
            # Analyze environment
            self.start_stage('resource_analyze_environment')
            environment_context = await self._analyze_environment(context_packet)
            self.stop_stage('resource_analyze_environment')

            current_operation = "analyzing_time"
            # Analyze time availability
            self.start_stage('resource_analyze_time')
            time_context = await self._analyze_time_availability(context_packet)
            self.stop_stage('resource_analyze_time')

            current_operation = "analyzing_resources"
            # Analyze available resources
            self.start_stage('resource_analyze_resources')
            resource_context = await self._analyze_resources(context_packet)
            self.stop_stage('resource_analyze_resources')

            current_operation = "combining_results"
            # Combine all analyses into a comprehensive resource context using LLM
            self.start_stage('resource_combine_results')

            # Use LLM to analyze and synthesize the resource data
            llm_analysis = await self._get_llm_analysis(
                environment_context, time_context, resource_context, context_packet
            )

            combined_resource_context = {
                "environment": environment_context,
                "time": time_context,
                "resources": resource_context,
                "analysis_summary": llm_analysis.get("analysis_summary", {}),
                "analysis_timestamp": context_packet.get("session_timestamp", ""),
                "user_id": self.user_profile_id
            }
            self.stop_stage('resource_combine_results')

            # Output data including resource context and routing
            analysis_summary = llm_analysis.get("analysis_summary", {})
            output_data = {
                "combined_resource_context": combined_resource_context,
                "resource_context": combined_resource_context,  # Keep both for compatibility
                "next_agent": "engagement",
                "feasibility_score": analysis_summary.get("overall_feasibility_score", 0.7),
                "constraints_count": len(analysis_summary.get("primary_constraints", [])),
                "opportunities_count": len(analysis_summary.get("key_opportunities", []))
            }

            # Prepare state updates dictionary
            state_updates = {"output_data": output_data}

            current_operation = "completing_run_success"
            # Start profiling DB complete (success)
            self.start_stage('resource_db_complete_run')
            # Complete the run in the database
            try:
                await self.db_service.complete_run(
                    run_id=self.run_id,
                    output_data=output_data,
                    state={"workflow_id": workflow_id},
                    status='completed'
                )
                logger.error(f"🔍 ResourceAgent: complete_run successful")
            except Exception as e:
                logger.error(f"🔍 ResourceAgent: complete_run failed: {e}")
            self.stop_stage('resource_db_complete_run')

            # Add run_id to state updates before returning
            state_updates["run_id"] = self.run_id

            # DEBUG: Log the output data being returned
            output_data_debug = state_updates.get("output_data", {})
            logger.error(f"🔍 ResourceAgent SUCCESS PATH: Returning output_data with keys: {list(output_data_debug.keys())}")
            if 'combined_resource_context' in output_data_debug:
                crc = output_data_debug['combined_resource_context']
                logger.error(f"🔍 ResourceAgent SUCCESS PATH: combined_resource_context type={type(crc)}, keys={list(crc.keys()) if isinstance(crc, dict) else 'not dict'}")
            else:
                logger.error(f"🔍 ResourceAgent SUCCESS PATH: NO combined_resource_context found!")

            logger.debug(f"ResourceAgent returning state updates: {state_updates}")
            return state_updates

        except Exception as e:
            error_type = type(e).__name__
            error_message = f"Error in resource agent during '{current_operation}': {str(e)}"
            logger.error(f"🔍 ResourceAgent ERROR PATH: Exception caught in ResourceAgent process ({error_type} during {current_operation}): {error_message}", exc_info=True)

            # Prepare output data including the error and routing info for persistence
            error_output_data = {
                "combined_resource_context": {},  # Include empty combined_resource_context
                "resource_context": {},  # Include empty resource_context for compatibility
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation},
                "forwardTo": "error_handler" # Keep routing info if needed by graph
            }

            # Attempt to complete the run as failed - WRAPPED
            try:
                current_operation = "completing_run_failure"
                self.start_stage('resource_db_complete_run_error')
                # Check if complete_run is already async
                if hasattr(self.db_service.complete_run, '__await__'):
                    # It's already an async method
                    await self.db_service.complete_run(
                        run_id=self.run_id,
                        output_data=error_output_data, # Pass error details in output
                        state={"error_details": error_message}, # Simple state
                        status='failed',
                        error_message=error_message # Pass explicit error message if arg exists
                    )
                else:
                    # It's a sync method, wrap it
                    await self.db_service.complete_run(
                        run_id=self.run_id,
                        output_data=error_output_data, # Pass error details in output
                        state={"error_details": error_message}, # Simple state
                        status='failed',
                        error_message=error_message # Pass explicit error message if arg exists
                    )
                self.stop_stage('resource_db_complete_run_error')
            except Exception as db_error:
                 self.stop_stage('resource_db_complete_run_error') # Stop stage even on error
                 logger.error(f"Failed to complete run in DB after agent error: {db_error}", exc_info=True)

            # Return the state update for LangGraph, including output_data
            error_updates = {
                "error": error_message,
                "output_data": error_output_data, # Ensure output_data is included
                "forwardTo": "error_handler" # Keep routing info
            }
            if hasattr(self, 'run_id') and self.run_id:
                 error_updates["run_id"] = self.run_id
            return error_updates

    async def _analyze_environment(self, context_packet):
        """Analyze the user's environment"""
        # Extract environment information from context
        # Ensure we always have a string, even if the key is missing or None
        reported_environment = context_packet.get("reported_environment", "")
        if reported_environment is None:
            reported_environment = ""

        logger.debug(f"Analyzing environment from context: '{reported_environment}'")

        # Use tool to get environment data
        try:
            environment_data = await self._call_tool(
                "get_environment_context",
                {
                    "user_profile_id": self.user_profile_id,
                    "reported_environment": reported_environment
                }
            )

            # Ensure environment_data is a dictionary
            if not isinstance(environment_data, dict):
                logger.warning(f"Tool returned non-dict environment data: {environment_data}")
                environment_data = {}

            # Extract environment context from tool response
            env_context = environment_data.get("environment_context", {})

            # Build comprehensive environment analysis
            result = {
                "reported": reported_environment,
                "current_environment": env_context.get("current_environment", {"name": "Unknown", "code": "unknown"}),
                "analyzed_type": environment_data.get("environment_type") or env_context.get("current_environment", {}).get("code", "unknown"),
                "privacy_level": env_context.get("privacy_level", 50),
                "space_size": env_context.get("space_size", "medium"),
                "noise_level": env_context.get("noise_level", "moderate"),
                "domain_support": environment_data.get("domain_support", {}),
                "social_context": env_context.get("social_context", {
                    "alone": False,
                    "with_others": True,
                    "public_space": False
                }),
                "activity_support": env_context.get("activity_support", {
                    "physical_activities": True,
                    "creative_activities": True,
                    "reflective_activities": True,
                    "social_activities": True
                }),
                "confidence": env_context.get("confidence", 0.7)
            }

            logger.debug(f"Environment analysis result: {result}")
            return result

        except Exception as e:
            logger.error(f"Error analyzing environment: {str(e)}", exc_info=True)
            # Return minimal data if tool fails, but ensure reported environment is included
            return {
                "reported": reported_environment,
                "current_environment": {"name": "Unknown", "code": "unknown"},
                "analyzed_type": "unknown",
                "privacy_level": 50,
                "space_size": "medium",
                "noise_level": "moderate",
                "domain_support": {},
                "social_context": {"alone": False, "with_others": True, "public_space": False},
                "activity_support": {
                    "physical_activities": True,
                    "creative_activities": True,
                    "reflective_activities": True,
                    "social_activities": True
                },
                "confidence": 0.3,
                "error": str(e)
            }

    async def _analyze_time_availability(self, context_packet):
        """Analyze the user's time availability"""
        # Extract time information from context
        reported_time = context_packet.get("reported_time_availability", "")

        # Parse time information
        try:
            time_data = await self._call_tool(
                "parse_time_availability",
                {
                    "user_profile_id": self.user_profile_id,
                    "reported_time_availability": reported_time,
                    "context": context_packet
                }
            )

            # Extract time analysis from tool response
            time_analysis = time_data.get("time_analysis", {})

            return {
                "reported": reported_time,
                "duration_minutes": time_analysis.get("duration_minutes", 30),
                "duration_category": time_analysis.get("duration_category", "medium"),
                "flexibility": time_analysis.get("flexibility", "moderate"),
                "urgency": time_analysis.get("urgency", "low"),
                "confidence": time_analysis.get("confidence", 0.7),
                "raw_input": time_analysis.get("raw_input", reported_time)
            }

        except Exception as e:
            logger.error(f"Error analyzing time availability: {str(e)}", exc_info=True)
            # Default to 30 minutes if parsing fails
            return {
                "reported": reported_time,
                "duration_minutes": 30,
                "duration_category": "medium",
                "flexibility": "moderate",
                "urgency": "low",
                "confidence": 0.3,
                "error": str(e)
            }

    async def _analyze_resources(self, context_packet):
        """Analyze the user's available resources"""
        try:
            resource_data = await self._call_tool(
                "get_available_resources",
                {
                    "user_profile_id": self.user_profile_id,
                    "context": context_packet
                }
            )

            # Build comprehensive resource analysis
            inventory_items = resource_data.get("inventory", [])
            limitations = resource_data.get("limitations", [])
            capabilities = resource_data.get("capabilities", {})

            # Calculate resource availability score
            resource_score = resource_data.get("confidence", 0.7)

            # Categorize inventory by type
            inventory_by_type = {}
            for item in inventory_items:
                item_type = item.get("type", "general")
                if item_type not in inventory_by_type:
                    inventory_by_type[item_type] = []
                inventory_by_type[item_type].append(item)

            return {
                "available_inventory": inventory_items,
                "inventory_by_type": inventory_by_type,
                "inventory_count": len(inventory_items),
                "reported_limitations": limitations,
                "limitations_count": len(limitations),
                "capabilities": capabilities,
                "capabilities_summary": {
                    "domains": list(capabilities.keys()),
                    "total_skills": sum(len(skills) for skills in capabilities.values())
                },
                "resource_availability_score": resource_score,
                "confidence": resource_score
            }

        except Exception as e:
            logger.error(f"Error analyzing resources: {str(e)}", exc_info=True)
            # Return minimal data if tool fails
            return {
                "available_inventory": [],
                "inventory_by_type": {},
                "inventory_count": 0,
                "reported_limitations": [],
                "limitations_count": 0,
                "capabilities": {},
                "capabilities_summary": {"domains": [], "total_skills": 0},
                "resource_availability_score": 0.3,
                "confidence": 0.3,
                "error": str(e)
            }

    def _get_recommended_activity_types(self, environment_context, time_context, resource_context):
        """Determine recommended activity types based on resource analysis"""
        recommendations = []

        # Check environment suitability
        activity_support = environment_context.get("activity_support", {})
        privacy_level = environment_context.get("privacy_level", 50)
        duration_minutes = time_context.get("duration_minutes", 30)

        if activity_support.get("physical_activities", False) and duration_minutes >= 20:
            recommendations.append("physical")
        if activity_support.get("creative_activities", False):
            recommendations.append("creativity")
        if activity_support.get("reflective_activities", False) and privacy_level > 60:
            recommendations.append("wellness")
        if activity_support.get("social_activities", False):
            recommendations.append("social")

        # Check time constraints
        if duration_minutes < 15:
            recommendations = [r for r in recommendations if r in ["wellness", "creativity"]]
            recommendations.append("quick_activities")
        elif duration_minutes > 60:
            recommendations.append("learning")
            recommendations.append("personal_growth")

        # Check capabilities
        capabilities = resource_context.get("capabilities", {})
        if capabilities:
            recommendations.append("skill_based")

        return recommendations[:5]  # Return top 5 recommendations

    async def _get_llm_analysis(self, environment_context, time_context, resource_context, context_packet):
        """Use LLM to analyze and synthesize resource data into actionable insights."""
        try:

            # Create system message for resource analysis
            system_message = f"""You are a Resource & Capacity Management Agent analyzing a user's current situation for activity planning.

Your role is to synthesize environmental, temporal, and resource data into actionable insights for activity selection.

ANALYSIS FRAMEWORK:
1. Calculate overall feasibility score (0.0-1.0) based on all three contexts
2. Identify 2-3 primary constraints that limit activity options
3. Highlight 2-3 key opportunities for enhanced activities
4. Recommend 3-5 activity types that best match the resource profile

SCORING GUIDELINES:
- Environment confidence: {environment_context.get('confidence', 0.7)}
- Time confidence: {time_context.get('confidence', 0.7)}
- Resource confidence: {resource_context.get('confidence', 0.7)}

CONSTRAINT IDENTIFICATION:
- Privacy level: {environment_context.get('privacy_level', 50)} (low if <30)
- Duration: {time_context.get('duration_minutes', 30)} minutes (limited if <15)
- Limitations: {resource_context.get('limitations_count', 0)} reported (concerning if >2)
- Inventory: {resource_context.get('inventory_count', 0)} items (limited if 0)

OPPORTUNITY IDENTIFICATION:
- High privacy (>70): Enables personal/reflective activities
- Extended time (>60 min): Allows complex/learning activities
- Rich skills ({resource_context.get('capabilities_summary', {}).get('total_skills', 0)}): Enables skill-based activities
- Creative environment support: {environment_context.get('activity_support', {}).get('creative_activities', False)}

Provide your analysis in this exact JSON format:
{{
    "overall_feasibility_score": 0.75,
    "primary_constraints": ["constraint1", "constraint2", "constraint3"],
    "key_opportunities": ["opportunity1", "opportunity2", "opportunity3"],
    "recommended_activity_types": ["type1", "type2", "type3", "type4", "type5"]
}}"""

            user_message = f"""Analyze this user's resource situation:

ENVIRONMENT ANALYSIS:
- Current environment: {environment_context.get('current_environment', {}).get('name', 'Unknown')}
- Privacy level: {environment_context.get('privacy_level', 50)}/100
- Space size: {environment_context.get('space_size', 'medium')}
- Noise level: {environment_context.get('noise_level', 'moderate')}
- Social context: {environment_context.get('social_context', {})}
- Activity support: {environment_context.get('activity_support', {})}

TIME ANALYSIS:
- Available duration: {time_context.get('duration_minutes', 30)} minutes
- Duration category: {time_context.get('duration_category', 'medium')}
- Flexibility: {time_context.get('flexibility', 'moderate')}
- Urgency: {time_context.get('urgency', 'low')}

RESOURCE ANALYSIS:
- Available inventory: {len(resource_context.get('available_inventory', []))} items
- Inventory types: {list(resource_context.get('inventory_by_type', {}).keys())}
- Reported limitations: {len(resource_context.get('reported_limitations', []))} items
- Capabilities: {resource_context.get('capabilities_summary', {})}
- Resource availability score: {resource_context.get('resource_availability_score', 0.7)}

Please provide a comprehensive analysis following the JSON format specified."""

            # Get LLM response
            response = await self.llm_client.chat_completion(
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.3  # Lower temperature for more consistent analysis
            )

            # Parse LLM response
            response_text = response.content.strip() if response.content else ""

            # Try to extract JSON from response
            import json
            import re

            # Look for JSON block in response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    analysis_result = json.loads(json_match.group())

                    # Validate required fields
                    required_fields = ['overall_feasibility_score', 'primary_constraints', 'key_opportunities', 'recommended_activity_types']
                    if all(field in analysis_result for field in required_fields):
                        return {"analysis_summary": analysis_result}
                    else:
                        logger.warning(f"LLM analysis missing required fields: {analysis_result}")

                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse LLM analysis JSON: {e}")

            # Fallback to hardcoded analysis if LLM parsing fails
            logger.warning("Using fallback analysis due to LLM parsing failure")
            return self._get_fallback_analysis(environment_context, time_context, resource_context)

        except Exception as e:
            logger.error(f"Error in LLM analysis: {e}", exc_info=True)
            # Fallback to hardcoded analysis
            return self._get_fallback_analysis(environment_context, time_context, resource_context)

    def _get_fallback_analysis(self, environment_context, time_context, resource_context):
        """Fallback analysis when LLM is unavailable."""
        # Calculate overall feasibility scores
        environment_score = environment_context.get("confidence", 0.7)
        time_score = time_context.get("confidence", 0.7)
        resource_score = resource_context.get("confidence", 0.7)
        overall_feasibility = (environment_score + time_score + resource_score) / 3

        # Identify key constraints
        constraints = []
        if environment_context.get("privacy_level", 50) < 30:
            constraints.append("Low privacy environment")
        if time_context.get("duration_minutes", 30) < 15:
            constraints.append("Very limited time")
        if resource_context.get("limitations_count", 0) > 2:
            constraints.append("Multiple user limitations")
        if resource_context.get("inventory_count", 0) == 0:
            constraints.append("No available inventory")

        # Identify opportunities
        opportunities = []
        if environment_context.get("privacy_level", 50) > 70:
            opportunities.append("High privacy for personal activities")
        if time_context.get("duration_minutes", 30) > 60:
            opportunities.append("Extended time available")
        if resource_context.get("capabilities_summary", {}).get("total_skills", 0) > 5:
            opportunities.append("Rich skill set available")
        if environment_context.get("activity_support", {}).get("creative_activities", False):
            opportunities.append("Environment supports creativity")

        return {
            "analysis_summary": {
                "overall_feasibility_score": round(overall_feasibility, 2),
                "primary_constraints": constraints[:3],  # Top 3 constraints
                "key_opportunities": opportunities[:3],  # Top 3 opportunities
                "recommended_activity_types": self._get_recommended_activity_types(
                    environment_context, time_context, resource_context
                )
            }
        }

    async def _call_tool(self, tool_code, tool_input):
        """Call a tool and record the usage"""
        try:
            from apps.main.agents.tools.tools_util import execute_tool
            # Wrap tool_input in input_data parameter as expected by resource tools
            wrapped_input = {"input_data": tool_input}
            result = await execute_tool(tool_code, wrapped_input, self.run_id)
            logger.debug(f"Tool {tool_code} executed successfully, result: {result}")
            return result
        except Exception as e:
            logger.error(f"Tool {tool_code} execution failed: {str(e)}", exc_info=True)
            # Return empty dict but log the error for debugging
            return {}