"""
AUTHORITATIVE FIELD MAPPINGS - Single Source of Truth
=====================================================

This file serves as the authoritative single source of truth for all field mappings
between agent tools and database models. Every entity must respect these mappings.

CRITICAL: This file prevents field errors that cause workflow failures and hanging issues.
"""

from typing import Dict, List, Any

# Demographics Model Field Mapping
DEMOGRAPHICS_FIELDS = {
    'user_profile': 'user_profile',  # OneToOneField to UserProfile
    'full_name': 'full_name',        # Char<PERSON>ield(max_length=255)
    'age': 'age',                    # IntegerField
    'gender': 'gender',              # Char<PERSON>ield(max_length=50)
    'location': 'location',          # CharField(max_length=255)
    'language': 'language',          # Char<PERSON>ield(max_length=50)
    'occupation': 'occupation',      # Char<PERSON>ield(max_length=255)
}

# UserGoal Model Field Mapping
USER_GOAL_FIELDS = {
    'user_profile': 'user_profile',                      # ForeignKey to UserProfile
    'title': 'title',                                    # Char<PERSON>ield(max_length=255)
    'description': 'description',                        # TextField
    'importance_according_user': 'importance_according_user',    # ValidatedRangeField
    'importance_according_system': 'importance_according_system', # ValidatedRangeField
    'strength': 'strength',                              # ValidatedRangeField
    'created_at': 'created_at',                         # DateTimeField(auto_now_add=True)
    'updated_at': 'updated_at',                         # DateTimeField(auto_now=True)
}

# Preference Model Field Mapping
PREFERENCE_FIELDS = {
    'user_profile': 'user_profile',          # ForeignKey to UserProfile
    'pref_name': 'pref_name',               # CharField(max_length=255)
    'pref_description': 'pref_description', # TextField
    'pref_strength': 'pref_strength',       # ValidatedRangeField
    'user_awareness': 'user_awareness',     # ValidatedRangeField
    'environment': 'environment',           # ForeignKey to UserEnvironment
    'effective_start': 'effective_start',   # DateField (from TemporalRecord)
    'effective_end': 'effective_end',       # DateField (from TemporalRecord)
    'duration_estimate': 'duration_estimate', # CharField (from TemporalRecord)
}

# Belief Model Field Mapping
BELIEF_FIELDS = {
    'user_profile': 'user_profile',         # ForeignKey to UserProfile
    'content': 'content',                   # TextField
    'last_updated': 'last_updated',         # DateField
    'user_confidence': 'user_confidence',   # ValidatedRangeField
    'system_confidence': 'system_confidence', # ValidatedRangeField
    'emotionality': 'emotionality',         # ExtendedRangeField
    'stability': 'stability',               # ValidatedRangeField
    'user_awareness': 'user_awareness',     # ValidatedRangeField
    'generic_belief': 'generic_belief',     # ForeignKey to GenericBelief
}

# UserTraitInclination Model Field Mapping
USER_TRAIT_INCLINATION_FIELDS = {
    'user_profile': 'user_profile',         # ForeignKey to UserProfile
    'generic_trait': 'generic_trait',       # ForeignKey to GenericTrait
    'strength': 'strength',                 # ValidatedRangeField
    'awareness': 'awareness',               # ValidatedRangeField
}

# GenericTrait Model Field Mapping
GENERIC_TRAIT_FIELDS = {
    'name': 'name',                         # CharField(max_length=255)
    'code': 'code',                         # CharField(max_length=50, unique=True)
    'description': 'description',           # TextField
    'trait_type': 'trait_type',             # CharField with choices
}

def validate_demographics_data(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validates and filters demographics data to only include valid fields.
    
    Args:
        input_data: Raw input data from agent tools
        
    Returns:
        Filtered data containing only valid Demographics model fields
    """
    valid_data = {}
    
    # Map input fields to valid Demographics fields
    field_mapping = {
        'full_name': input_data.get('full_name', ''),
        'age': input_data.get('age', None),
        'gender': input_data.get('gender', ''),
        'location': input_data.get('location', ''),
        'language': input_data.get('language', ''),
        'occupation': input_data.get('occupation', ''),
    }
    
    # Only include fields that have values and are valid
    for field_name, value in field_mapping.items():
        if field_name in DEMOGRAPHICS_FIELDS and value is not None:
            valid_data[field_name] = value
    
    return valid_data

def validate_user_goal_data(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validates and filters user goal data to only include valid fields.
    """
    valid_data = {}
    
    field_mapping = {
        'title': input_data.get('title'),
        'description': input_data.get('description'),
        'importance_according_user': input_data.get('importance', 70),
        'importance_according_system': input_data.get('importance', 70),
        'strength': input_data.get('strength', 60),
    }
    
    for field_name, value in field_mapping.items():
        if field_name in USER_GOAL_FIELDS and value is not None:
            valid_data[field_name] = value
    
    return valid_data

def validate_preference_data(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validates and filters preference data to only include valid fields.
    """
    from datetime import date, timedelta
    
    valid_data = {}
    
    field_mapping = {
        'pref_name': input_data.get('pref_name', 'General Preference'),
        'pref_description': input_data.get('pref_description', 'Preference identified during onboarding'),
        'pref_strength': input_data.get('pref_strength', 70),
        'user_awareness': input_data.get('user_awareness', 70),
        'effective_start': input_data.get('effective_start', date.today()),
        'duration_estimate': input_data.get('duration_estimate', 'ongoing'),
        'effective_end': input_data.get('effective_end', date.today() + timedelta(days=365)),
    }
    
    for field_name, value in field_mapping.items():
        if field_name in PREFERENCE_FIELDS and value is not None:
            valid_data[field_name] = value
    
    return valid_data

def validate_belief_data(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validates and filters belief data to only include valid fields.
    """
    from datetime import date
    
    valid_data = {}
    
    field_mapping = {
        'content': input_data.get('content', 'Belief identified during onboarding'),
        'last_updated': input_data.get('last_updated', date.today()),
        'user_confidence': input_data.get('user_confidence', 70),
        'system_confidence': input_data.get('system_confidence', 70),
        'emotionality': input_data.get('emotionality', 50),
        'stability': input_data.get('stability', 70),
        'user_awareness': input_data.get('user_awareness', 70),
    }
    
    for field_name, value in field_mapping.items():
        if field_name in BELIEF_FIELDS and value is not None:
            valid_data[field_name] = value
    
    return valid_data

def validate_trait_data(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validates and filters trait data to only include valid fields.
    """
    valid_data = {}
    
    field_mapping = {
        'strength': input_data.get('strength', 70),
        'awareness': input_data.get('awareness', 70),
    }
    
    for field_name, value in field_mapping.items():
        if field_name in USER_TRAIT_INCLINATION_FIELDS and value is not None:
            valid_data[field_name] = value
    
    return valid_data

def validate_generic_trait_data(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validates and filters generic trait data to only include valid fields.
    """
    valid_data = {}
    
    trait_name = input_data.get('trait_name', input_data.get('name', ''))
    
    field_mapping = {
        'name': trait_name,
        'code': trait_name.lower().replace(' ', '_') if trait_name else '',
        'description': input_data.get('description', f'Trait identified during onboarding: {trait_name}'),
        'trait_type': input_data.get('trait_type', 'UNDEFINED'),
    }
    
    for field_name, value in field_mapping.items():
        if field_name in GENERIC_TRAIT_FIELDS and value is not None:
            valid_data[field_name] = value
    
    return valid_data

# CRITICAL VALIDATION RULES
VALIDATION_RULES = {
    'demographics': {
        'required_fields': [],  # No required fields for demographics
        'forbidden_fields': ['personal_prefs_json', 'preferences', 'prefs'],  # These don't exist!
        'validator': validate_demographics_data,
    },
    'user_goal': {
        'required_fields': ['title', 'description'],
        'forbidden_fields': [],
        'validator': validate_user_goal_data,
    },
    'preference': {
        'required_fields': ['pref_name'],
        'forbidden_fields': [],
        'validator': validate_preference_data,
    },
    'belief': {
        'required_fields': ['content'],
        'forbidden_fields': [],
        'validator': validate_belief_data,
    },
    'trait': {
        'required_fields': ['trait_name'],
        'forbidden_fields': [],
        'validator': validate_trait_data,
    },
}

def get_model_fields(model_name: str) -> Dict[str, str]:
    """
    Returns the authoritative field mapping for a given model.
    
    Args:
        model_name: Name of the model ('demographics', 'user_goal', etc.)
        
    Returns:
        Dictionary mapping field names to their database column names
    """
    field_mappings = {
        'demographics': DEMOGRAPHICS_FIELDS,
        'user_goal': USER_GOAL_FIELDS,
        'preference': PREFERENCE_FIELDS,
        'belief': BELIEF_FIELDS,
        'user_trait_inclination': USER_TRAIT_INCLINATION_FIELDS,
        'generic_trait': GENERIC_TRAIT_FIELDS,
    }
    
    return field_mappings.get(model_name, {})

def validate_model_data(model_name: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validates input data for a specific model using the authoritative field mappings.
    
    Args:
        model_name: Name of the model to validate for
        input_data: Raw input data from agent tools
        
    Returns:
        Validated and filtered data ready for database operations
        
    Raises:
        ValueError: If required fields are missing or forbidden fields are present
    """
    if model_name not in VALIDATION_RULES:
        raise ValueError(f"Unknown model: {model_name}")
    
    rules = VALIDATION_RULES[model_name]
    
    # Check for forbidden fields
    for forbidden_field in rules['forbidden_fields']:
        if forbidden_field in input_data:
            raise ValueError(f"Forbidden field '{forbidden_field}' found in {model_name} data")
    
    # Validate using the specific validator
    validated_data = rules['validator'](input_data)
    
    # Check for required fields
    for required_field in rules['required_fields']:
        if required_field not in validated_data or validated_data[required_field] is None:
            raise ValueError(f"Required field '{required_field}' missing in {model_name} data")
    
    return validated_data
