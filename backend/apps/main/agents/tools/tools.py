from apps.main.agents.tools.tools_util import register_tool
import asyncio
from django.db import transaction
from django.utils import timezone
from asgiref.sync import sync_to_async
import logging
from typing import Dict, Any, List, Optional, Union

logger = logging.getLogger(__name__)




@register_tool('record_user_feedback')
async def record_user_feedback(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Records and processes user feedback on activities, interactions, or the system.
    
    Input:
        user_profile_id: UUID of the user profile
        feedback_type: Type of feedback (pre_spin, post_spin, activity_completion, system)
        content_type: Type of the entity the feedback is about (optional)
        object_id: ID of the entity the feedback is about (optional)
        user_comment: User's feedback text
        rating: Numerical rating if applicable (0-100)
        sentiment: Analyzed sentiment of the feedback (positive, neutral, negative)
        context_data: Additional contextual data for the feedback
        criticality: Importance level of the feedback (1-5)
        
    Output:
        feedback: Dictionary with feedback details
            id: ID of the created feedback record
            status: Status of the feedback recording
            action_taken: Any immediate actions taken based on feedback
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    feedback_type = input_data.get('feedback_type')
    content_type_str = input_data.get('content_type')
    object_id = input_data.get('object_id')
    user_comment = input_data.get('user_comment', '')
    criticality = input_data.get('criticality', 3)  # Default medium criticality
    context_data = input_data.get('context_data', {})
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    if not feedback_type:
        return {"error": "feedback_type is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile
        from apps.main.models import UserFeedback, HistoryEvent
        from django.contrib.contenttypes.models import ContentType
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # Get content type if provided
        content_type = None
        if content_type_str and object_id:
            app_label, model = content_type_str.split('.')
            content_type = ContentType.objects.get(app_label=app_label, model=model)
        
        # Create feedback record
        feedback = UserFeedback(
            feedback_type=feedback_type,
            user_profile=user_profile,
            user_comment=user_comment,
            criticality=criticality,
            context_data=context_data
        )
        
        # Add content type and object_id if provided
        if content_type and object_id:
            feedback.content_type = content_type
            feedback.object_id = object_id
        
        # Save the feedback
        feedback.save()
        
        # Create a history event for this feedback
        event_type = f"feedback_{feedback_type}"
        event_details = {
            "user_comment": user_comment,
            "criticality": criticality
        }
        
        # Add additional context data to the event details
        for key, value in context_data.items():
            if key not in event_details:
                event_details[key] = value
        
        history_event = HistoryEvent(
            event_type=event_type,
            user_profile=user_profile,
            details=event_details
        )
        
        # Add content type and object_id to the history event if provided
        if content_type and object_id:
            history_event.content_type = content_type
            history_event.object_id = object_id
        
        # Save the history event
        history_event.save()
        
        # Determine if any immediate actions should be taken based on feedback
        action_taken = None
        if criticality >= 4 and feedback_type in ['activity_refusal', 'negative_experience']:
            # For high-criticality negative feedback, you might want to take immediate action
            # This could include scheduling a follow-up, adjusting user preferences, etc.
            action_taken = "Scheduled for priority analysis"
        
        return {
            "feedback": {
                "id": feedback.id,
                "status": "recorded",
                "action_taken": action_taken
            }
        }
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error recording user feedback")
        return {"error": str(e)}


@register_tool('get_recent_interactions')
async def get_recent_interactions(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Retrieves recent user interactions and history to maintain context in conversations.
    
    Input:
        user_profile_id: UUID of the user profile
        max_items: Maximum number of history items to retrieve (default: 10)
        event_types: List of event types to filter by (optional)
        since_timestamp: Only retrieve events after this timestamp (optional)
        include_feedback: Whether to include feedback events (default: True)
        
    Output:
        interactions: List of recent interactions in chronological order
            id: ID of the history event
            timestamp: When the event occurred
            event_type: Type of the event
            details: Additional details about the event
            related_entity: Information about the related entity if applicable
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    max_items = input_data.get('max_items', 10)
    event_types = input_data.get('event_types', [])
    since_timestamp = input_data.get('since_timestamp')
    include_feedback = input_data.get('include_feedback', True)
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile
        from apps.main.models import HistoryEvent, UserFeedback
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # Build query for history events
        history_query = HistoryEvent.objects.filter(user_profile=user_profile)
        
        # Apply event type filter if provided
        if event_types:
            history_query = history_query.filter(event_type__in=event_types)
        
        # Apply timestamp filter if provided
        if since_timestamp:
            from django.utils.dateparse import parse_datetime
            timestamp = parse_datetime(since_timestamp)
            if timestamp:
                history_query = history_query.filter(timestamp__gt=timestamp)
        
        # Order by timestamp descending and limit results
        history_events = history_query.order_by('-timestamp')[:max_items]
        
        # Get feedback events if requested
        feedback_events = []
        if include_feedback:
            feedback_query = UserFeedback.objects.filter(user_profile=user_profile)
            
            # Apply timestamp filter if provided
            if since_timestamp:
                feedback_query = feedback_query.filter(created_on__gt=timestamp)
            
            # Order by timestamp descending and limit results
            feedback_events = feedback_query.order_by('-created_on')[:max_items]
        
        # Combine history and feedback events
        all_events = []
        
        # Process history events
        for event in history_events:
            event_data = {
                "id": str(event.id),
                "timestamp": event.timestamp.isoformat(),
                "event_type": event.event_type,
                "details": event.details
            }
            
            # Add related entity information if available
            if event.content_type and event.object_id:
                event_data["related_entity"] = {
                    "type": event.content_type.model,
                    "id": event.object_id
                }
            
            all_events.append(event_data)
        
        # Process feedback events
        for feedback in feedback_events:
            feedback_data = {
                "id": str(feedback.id),
                "timestamp": feedback.created_on.isoformat(),
                "event_type": f"feedback_{feedback.feedback_type}",
                "details": {
                    "user_comment": feedback.user_comment,
                    "criticality": feedback.criticality,
                    "context_data": feedback.context_data
                }
            }
            
            # Add related entity information if available
            if feedback.content_type and feedback.object_id:
                feedback_data["related_entity"] = {
                    "type": feedback.content_type.model,
                    "id": feedback.object_id
                }
            
            all_events.append(feedback_data)
        
        # Sort all events by timestamp
        all_events.sort(key=lambda x: x["timestamp"], reverse=True)
        
        # Limit to requested number of items
        all_events = all_events[:max_items]
        
        # Reverse to get chronological order
        all_events.reverse()
        
        return {"interactions": all_events}
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error retrieving recent interactions")
        return {"error": str(e)}



@register_tool('generate_reflection_prompt')
async def generate_reflection_prompt(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generates personalized reflection prompts for users based on their activities and growth journey.
    
    Input:
        user_profile_id: UUID of the user profile
        activity_id: UUID of the activity to reflect on (optional)
        prompt_type: Type of reflection prompt (post_activity, daily_check_in, weekly_review)
        focus_areas: List of specific areas to focus on in the reflection (optional)
        trust_phase: Current trust phase (foundation/expansion) if known (optional)
        
    Output:
        prompt: Dictionary with reflection prompt details
            text: The reflection prompt text
            questions: List of specific reflection questions
            custom_framing: Any custom framing for the reflection based on user context
            suggested_focus: Suggested areas to focus reflection on
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    activity_id = input_data.get('activity_id')
    prompt_type = input_data.get('prompt_type', 'post_activity')
    focus_areas = input_data.get('focus_areas', [])
    trust_phase = input_data.get('trust_phase')
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, TrustLevel
        from apps.activity.models import ActivityTailored
        from apps.main.models import HistoryEvent
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # Determine trust phase if not provided
        if not trust_phase:
            try:
                trust_level = TrustLevel.objects.get(user_profile=user_profile)
                trust_phase = "Expansion" if trust_level.value >= 60 else "Foundation"
            except TrustLevel.DoesNotExist:
                trust_phase = "Foundation"  # Default to foundation phase
        
        # Get activity details if activity_id is provided
        activity = None
        activity_domains = []
        activity_challenge_areas = []
        if activity_id:
            activity = ActivityTailored.objects.prefetch_related('domain_relationships__domain').get(id=activity_id)
            
            # Extract domains
            for rel in activity.domain_relationships.all():
                activity_domains.append({
                    "name": rel.domain.name,
                    "strength": rel.strength
                })
            
            # Extract challenge areas from challengingness data
            if activity.challengingness:
                for dimension, value in activity.challengingness.items():
                    if value > 50:  # Only include dimensions with significant challenge
                        activity_challenge_areas.append({
                            "dimension": dimension,
                            "value": value
                        })
        
        # Generate appropriate reflection prompt based on type
        if prompt_type == 'post_activity':
            return _generate_post_activity_reflection(user_profile, activity, activity_domains, activity_challenge_areas, trust_phase)
        elif prompt_type == 'daily_check_in':
            return _generate_daily_check_in(user_profile, focus_areas, trust_phase)
        elif prompt_type == 'weekly_review':
            return _generate_weekly_review(user_profile, focus_areas, trust_phase)
        else:
            return {"error": f"Unsupported prompt_type: {prompt_type}"}
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except ActivityTailored.DoesNotExist:
        return {"error": f"Activity with ID {activity_id} not found"}
    except Exception as e:
        logger.exception("Error generating reflection prompt")
        return {"error": str(e)}


def _generate_post_activity_reflection(user_profile, activity, activity_domains, activity_challenge_areas, trust_phase):
    """Helper function to generate post-activity reflection prompts"""
    # Base questions for all trust phases
    base_questions = [
        "What part of the activity did you find most engaging?",
        "Did anything surprise you during this experience?"
    ]
    
    # Phase-specific questions
    if trust_phase == "Foundation":
        # Foundation phase focuses on comfort and positive experiences
        specific_questions = [
            "What aspects of this activity felt comfortable or enjoyable?",
            "Would you like to try a similar activity in the future?"
        ]
        
        # Create main reflection prompt
        prompt_text = f"As you reflect on completing \"{activity.name}\", take a moment to notice what went well and what you enjoyed about the experience."
        
        custom_framing = "This reflection helps build awareness of your preferences and strengths."
        
    else:  # Expansion phase
        # Expansion phase encourages deeper reflection and challenge
        specific_questions = [
            "What challenged you during this activity, and how did you respond?",
            "What does this experience tell you about your growth areas?"
        ]
        
        # Add domain-specific question if available
        if activity_domains:
            primary_domain = max(activity_domains, key=lambda x: x["strength"])
            specific_questions.append(f"How did this activity develop your abilities in {primary_domain['name']}?")
        
        # Create main reflection prompt
        prompt_text = f"Having completed \"{activity.name}\", consider both what you learned and how this experience might support your growth journey."
        
        custom_framing = "This reflection helps connect your experiences to your broader growth and development."
    
    # Add challenge-specific questions for either phase
    if activity_challenge_areas and trust_phase == "Expansion":
        # Find the highest challenge dimension
        top_challenge = max(activity_challenge_areas, key=lambda x: x["value"])
        dimension = top_challenge["dimension"]
        
        # Map dimensions to user-friendly terms
        dimension_map = {
            "openness": "trying new experiences",
            "conscientiousness": "planning and organization",
            "extraversion": "social engagement",
            "agreeableness": "cooperation with others",
            "neuroticism": "emotional self-regulation",
            "honestyhumility": "authenticity and humility"
        }
        
        friendly_term = dimension_map.get(dimension.lower(), dimension)
        specific_questions.append(f"This activity involved {friendly_term}. How did that aspect feel for you?")
    
    # Combine base and specific questions
    all_questions = base_questions + specific_questions
    
    # Suggest focus areas
    suggested_focus = []
    if activity_domains:
        for domain in activity_domains[:2]:  # Top two domains
            suggested_focus.append(domain["name"])
    
    if activity_challenge_areas:
        for challenge in activity_challenge_areas[:2]:  # Top two challenge dimensions
            dimension = challenge["dimension"]
            dimension_map = {
                "openness": "Openness to new experiences",
                "conscientiousness": "Organization and follow-through",
                "extraversion": "Social engagement",
                "agreeableness": "Cooperation with others",
                "neuroticism": "Emotional regulation",
                "honestyhumility": "Authenticity and humility"
            }
            suggested_focus.append(dimension_map.get(dimension.lower(), dimension))
    
    # Create response
    return {
        "prompt": {
            "text": prompt_text,
            "questions": all_questions,
            "custom_framing": custom_framing,
            "suggested_focus": suggested_focus
        }
    }


def _generate_daily_check_in(user_profile, focus_areas, trust_phase):
    """Helper function to generate daily check-in reflection prompts"""
    # Base questions for all trust phases
    base_questions = [
        "How would you describe your energy level today?",
        "What are you looking forward to today?"
    ]
    
    # Phase-specific questions
    if trust_phase == "Foundation":
        # Foundation phase focuses on comfort and immediate context
        specific_questions = [
            "What would make today feel successful for you?",
            "Is there anything specific you'd like support with today?"
        ]
        
        # Create main reflection prompt
        prompt_text = "As we start our day together, let's check in on how you're feeling and what might make today meaningful for you."
        
        custom_framing = "This check-in helps us tailor today's experience to your current needs and energy."
        
    else:  # Expansion phase
        # Expansion phase encourages more growth-oriented thinking
        specific_questions = [
            "What growth opportunity are you most excited about today?",
            "Is there a specific area where you'd like to challenge yourself?"
        ]
        
        # Create main reflection prompt
        prompt_text = "Let's start the day by checking in on your current state and intentions for growth."
        
        custom_framing = "This check-in helps connect today's experiences to your longer-term development goals."
    
    # Add focus-area questions if provided
    if focus_areas:
        for area in focus_areas[:1]:  # Just add one question to avoid overwhelming
            specific_questions.append(f"Regarding {area}, what's on your mind today?")
    
    # Combine base and specific questions
    all_questions = base_questions + specific_questions
    
    # Create response
    return {
        "prompt": {
            "text": prompt_text,
            "questions": all_questions,
            "custom_framing": custom_framing,
            "suggested_focus": ["Today's energy", "Immediate priorities"]
        }
    }


def _generate_weekly_review(user_profile, focus_areas, trust_phase):
    """Helper function to generate weekly review reflection prompts"""
    # Base questions for all trust phases
    base_questions = [
        "What activities or experiences stood out to you this week?",
        "What patterns or themes did you notice in your week?"
    ]
    
    # Phase-specific questions
    if trust_phase == "Foundation":
        # Foundation phase focuses on comfort and building awareness
        specific_questions = [
            "What activities felt most comfortable or enjoyable this week?",
            "What would you like to experience more of next week?"
        ]
        
        # Create main reflection prompt
        prompt_text = "As we review this week together, let's reflect on your experiences and what they tell us about your preferences."
        
        custom_framing = "This weekly review helps build awareness of patterns and preferences to better support your journey."
        
    else:  # Expansion phase
        # Expansion phase encourages deeper reflection and growth orientation
        specific_questions = [
            "How did you challenge yourself this week?",
            "What did you learn about yourself from this week's experiences?",
            "How might you apply these insights in the coming week?"
        ]
        
        # Create main reflection prompt
        prompt_text = "Looking back on this week, let's explore how your experiences have contributed to your growth and what insights you can carry forward."
        
        custom_framing = "This weekly review helps integrate your experiences into meaningful patterns that support continued development."
    
    # Add focus-area questions if provided
    if focus_areas:
        for area in focus_areas[:2]:  # Add up to two questions for focus areas
            specific_questions.append(f"Regarding your focus on {area}, what progress or insights did you gain this week?")
    
    # Combine base and specific questions
    all_questions = base_questions + specific_questions
    
    # Create response
    return {
        "prompt": {
            "text": prompt_text,
            "questions": all_questions,
            "custom_framing": custom_framing,
            "suggested_focus": ["Patterns and themes", "Progress indicators", "Next week preparation"]
        }
    }


@register_tool('get_user_wheels')
async def get_user_wheels(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Retrieves all wheels for a specific user, ordered by creation date (most recent first).

    Input:
        user_profile_id: UUID of the user profile
        include_activities: Whether to include detailed activity information (default: True)
        limit: Maximum number of wheels to return (default: 10)

    Output:
        wheels: List of wheel dictionaries, each containing:
            id: ID of the wheel
            name: Name of the wheel
            created_at: When the wheel was created
            created_by: Agent that created the wheel
            items: List of wheel items with probability percentages and activity summaries
    """
    from asgiref.sync import sync_to_async

    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    include_activities = input_data.get('include_activities', True)
    limit = input_data.get('limit', 10)

    if not user_profile_id:
        return {"error": "user_profile_id is required"}

    try:
        # Import models
        from apps.main.models import Wheel, WheelItem
        from apps.user.models import UserProfile

        # Define sync functions to be called with sync_to_async
        @sync_to_async
        def get_user_profile(profile_id):
            return UserProfile.objects.get(id=profile_id)

        @sync_to_async
        def get_wheels_for_user(profile_name, limit_count):
            wheels_query = Wheel.objects.filter(
                name__icontains=profile_name
            ).prefetch_related('items__activity_tailored__domain_relationships__domain').order_by('-created_at', '-id')

            if limit_count:
                wheels_query = wheels_query[:limit_count]

            return list(wheels_query)

        # Verify user profile exists
        user_profile = await get_user_profile(user_profile_id)

        # Find wheels associated with this user by checking wheel names that contain the user's profile name
        # This is based on the pattern seen in the codebase where wheels are named with user info
        wheels = await get_wheels_for_user(user_profile.profile_name, limit)

        wheels_list = []

        for wheel in wheels:
            # Build the wheel response
            wheel_data = {
                "id": wheel.id,
                "name": wheel.name,
                "created_at": wheel.created_at.isoformat(),
                "created_by": wheel.created_by,
                "items": []
            }

            # Add wheel items
            for item in wheel.items.all():
                item_data = {
                    "id": item.id,
                    "percentage": item.percentage
                }

                # Add activity data if requested
                if include_activities:
                    activity = item.activity_tailored
                    activity_data = {
                        "id": str(activity.id),
                        "name": activity.name,
                        "description": activity.description,
                        "challenge_rating": activity.base_challenge_rating,
                        "duration_range": activity.duration_range
                    }

                    # Add domain information
                    domains = []
                    for rel in activity.domain_relationships.all():
                        domains.append({
                            "id": rel.domain.id,
                            "name": rel.domain.name,
                            "code": rel.domain.code,
                            "strength": rel.strength
                        })

                    activity_data["domains"] = domains
                    item_data["activity"] = activity_data

                wheel_data["items"].append(item_data)

            wheels_list.append(wheel_data)

        return {"wheels": wheels_list}

    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error retrieving user wheels")
        return {"error": str(e)}


@register_tool('get_wheel_details')
async def get_wheel_details(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Retrieves details about a wheel and its current state for presentation to the user.
    
    Input:
        wheel_id: ID of the wheel to retrieve
        include_activities: Whether to include detailed activity information (default: True)
        
    Output:
        wheel: Dictionary with wheel details
            id: ID of the wheel
            name: Name of the wheel
            created_at: When the wheel was created
            items: List of wheel items with probability percentages and activity summaries
    """
    # Extract required parameters
    wheel_id = input_data.get('wheel_id')
    include_activities = input_data.get('include_activities', True)
    
    if not wheel_id:
        return {"error": "wheel_id is required"}
    
    try:
        # Import models
        from apps.main.models import Wheel, WheelItem
        
        # Get the wheel with prefetched items for efficiency
        wheel = Wheel.objects.prefetch_related('items__activity_tailored').get(id=wheel_id)
        
        # Build the response
        result = {
            "id": wheel.id,
            "name": wheel.name,
            "created_at": wheel.created_at.isoformat(),
            "items": []
        }
        
        # Add wheel items
        for item in wheel.items.all():
            item_data = {
                "id": item.id,
                "percentage": item.percentage
            }
            
            # Add activity data if requested
            if include_activities:
                activity = item.activity_tailored
                activity_data = {
                    "id": str(activity.id),
                    "name": activity.name,
                    "description": activity.description,
                    "challenge_rating": activity.base_challenge_rating,
                    "duration_range": activity.duration_range
                }
                
                # Add domain information
                domains = []
                for rel in activity.domain_relationships.all():
                    domains.append({
                        "id": rel.domain.id,
                        "name": rel.domain.name,
                        "code": rel.domain.code,
                        "strength": rel.strength
                    })
                
                activity_data["domains"] = domains
                item_data["activity"] = activity_data
            
            result["items"].append(item_data)
        
        return {"wheel": result}
    
    except Wheel.DoesNotExist:
        return {"error": f"Wheel with ID {wheel_id} not found"}
    except Exception as e:
        logger.exception("Error retrieving wheel details")
        return {"error": str(e)}


@register_tool('present_activity_options')
async def present_activity_options(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generates user-friendly presentation of activity options from a wheel or set of activities.
    
    Input:
        user_profile_id: UUID of the user profile
        wheel_id: ID of the wheel containing activities (optional if activities provided)
        activities: List of activity IDs to present (optional if wheel_id provided)
        trust_phase: Current trust phase (foundation/expansion) if known
        presentation_style: Preferred presentation style (brief, standard, detailed)
        
    Output:
        presentation: Dictionary with presentation details
            activity_summaries: List of activity summaries for presentation
            framing: Suggested framing for presenting the options
            focus_message: Message highlighting the focus or theme of these options
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    wheel_id = input_data.get('wheel_id')
    activities = input_data.get('activities', [])
    trust_phase = input_data.get('trust_phase')
    presentation_style = input_data.get('presentation_style', 'standard')
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    if not wheel_id and not activities:
        return {"error": "Either wheel_id or activities must be provided"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, TrustLevel
        from apps.activity.models import ActivityTailored
        from apps.main.models import Wheel, WheelItem
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # Determine trust phase if not provided
        if not trust_phase:
            try:
                trust_level = TrustLevel.objects.get(user_profile=user_profile)
                trust_phase = "Expansion" if trust_level.value >= 60 else "Foundation"
            except TrustLevel.DoesNotExist:
                trust_phase = "Foundation"  # Default to foundation phase
        
        # Get activities from wheel if wheel_id is provided
        activity_objects = []
        if wheel_id:
            wheel = Wheel.objects.prefetch_related('items__activity_tailored').get(id=wheel_id)
            
            for item in wheel.items.all():
                activity_objects.append({
                    'activity': item.activity_tailored,
                    'percentage': item.percentage
                })
        
        # Otherwise, get activities from the provided list
        elif activities:
            for activity_id in activities:
                activity = ActivityTailored.objects.get(id=activity_id)
                activity_objects.append({
                    'activity': activity,
                    'percentage': None  # No percentage for manually specified activities
                })
        
        # Generate activity summaries
        activity_summaries = []
        domains_covered = {}
        challenge_levels = []
        
        for activity_obj in activity_objects:
            activity = activity_obj['activity']
            percentage = activity_obj['percentage']
            
            # Create basic summary
            summary = {
                "id": str(activity.id),
                "name": activity.name,
                "challenge_level": activity.base_challenge_rating
            }
            
            # Add wheel percentage if available
            if percentage is not None:
                summary["wheel_percentage"] = percentage
            
            # Add description based on presentation style
            if presentation_style == 'brief':
                # Just a one-line summary
                summary["description"] = activity.description.split('.')[0] + '.'
            else:
                # Full description
                summary["description"] = activity.description
            
            # Add domain information
            domains = []
            for rel in activity.domain_relationships.all():
                domain_name = rel.domain.name
                domains.append(domain_name)
                
                # Track domains covered across all activities
                domains_covered[domain_name] = domains_covered.get(domain_name, 0) + 1
            
            summary["domains"] = domains
            
            # Add duration if available
            if activity.duration_range:
                summary["duration"] = activity.duration_range
            
            # Add detailed information for 'detailed' presentation style
            if presentation_style == 'detailed':
                # Add challenge breakdown
                summary["challenge_breakdown"] = activity.challengingness
                
                # Add value proposition
                value_proposition = []
                if activity.base_challenge_rating < 40:
                    value_proposition.append("Comfortable experience that builds confidence")
                elif activity.base_challenge_rating < 70:
                    value_proposition.append("Balanced challenge for steady growth")
                else:
                    value_proposition.append("Stretches your boundaries for meaningful growth")
                
                # Add domain-specific value
                if domains:
                    primary_domain = domains[0]  # Assuming first domain is primary
                    value_proposition.append(f"Develops skills in {primary_domain}")
                
                summary["value_proposition"] = value_proposition
            
            # Track challenge levels for framing
            challenge_levels.append(activity.base_challenge_rating)
            
            activity_summaries.append(summary)
        
        # Generate appropriate framing based on trust phase
        if trust_phase == "Foundation":
            framing = "Here are some activities selected to help you build confidence and discover your preferences:"
            focus_message = "These options provide a range of experiences to explore what resonates with you."
        else:  # Expansion phase
            framing = "Here are some activities designed to support your growth and development:"
            focus_message = "These options offer opportunities to expand your comfort zone and build new capabilities."
        
        # Adjust framing based on domains covered
        if domains_covered:
            top_domains = sorted(domains_covered.items(), key=lambda x: x[1], reverse=True)[:2]
            domain_names = [d[0] for d in top_domains]
            
            if len(domain_names) == 1:
                focus_message = f"These options focus on developing your capabilities in {domain_names[0]}."
            elif len(domain_names) == 2:
                focus_message = f"These options focus on developing your capabilities in {domain_names[0]} and {domain_names[1]}."
        
        # Adjust framing based on challenge levels
        avg_challenge = sum(challenge_levels) / len(challenge_levels) if challenge_levels else 50
        if avg_challenge < 40:
            challenge_framing = "comfortable"
        elif avg_challenge < 70:
            challenge_framing = "balanced"
        else:
            challenge_framing = "stretching"
        
        framing = framing.replace("some activities", f"some {challenge_framing} activities")
        
        return {
            "presentation": {
                "activity_summaries": activity_summaries,
                "framing": framing,
                "focus_message": focus_message
            }
        }
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Wheel.DoesNotExist:
        return {"error": f"Wheel with ID {wheel_id} not found"}
    except Exception as e:
        logger.exception("Error generating activity presentation")
        return {"error": str(e)}


@register_tool('get_communication_guidelines')
async def get_communication_guidelines(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Retrieves personalized communication guidelines for interacting with a specific user.
    
    Input:
        user_profile_id: UUID of the user profile
        
    Output:
        guidelines: Dictionary with communication guidelines
            tone: Preferred communication tone
            detail_level: Preferred level of detail in explanations
            metaphor_types: Types of metaphors that resonate with the user
            pacing: Recommended pacing of information delivery
            formatting: Preferred formatting styles
            customizations: Any user-specific customizations
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, TrustLevel, UserTraitInclination
        from apps.main.models import AgentMemory
        from django.db.models import Avg
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # Get agent memories for communication preferences
        communication_preferences = {}
        agent_memories = AgentMemory.objects.filter(
            custom_agent__user_profile=user_profile,
            custom_agent__generic_agent__role='mentor'
        ).order_by('-timestamp')[:10]
        
        for memory in agent_memories:
            if 'communication_preferences' in memory.content:
                communication_preferences = memory.content['communication_preferences']
                break
        
        # If no stored preferences, generate them based on trait inclinations
        if not communication_preferences:
            # Get relevant trait inclinations
            trait_inclinations = UserTraitInclination.objects.filter(
                user_profile=user_profile,
                generic_trait__trait_type__in=['OPEN', 'EXTR', 'CONS']  # Relevant traits for communication
            ).select_related('generic_trait')
            
            # Default preferences
            communication_preferences = {
                "tone": "balanced",
                "detail_level": "moderate",
                "metaphor_types": ["practical", "nature"],
                "pacing": "moderate",
                "formatting": ["clear_sections", "bullet_points"],
                "customizations": {}
            }
            
            # Adjust based on traits if available
            for trait in trait_inclinations:
                trait_type = trait.generic_trait.trait_type
                strength = trait.strength
                
                if trait_type == 'OPEN' and strength >= 70:
                    # High openness - prefers creative, varied communication
                    communication_preferences["tone"] = "enthusiastic"
                    communication_preferences["metaphor_types"] = ["abstract", "creative", "nature"]
                    communication_preferences["detail_level"] = "high"
                elif trait_type == 'OPEN' and strength <= 30:
                    # Low openness - prefers straightforward, practical communication
                    communication_preferences["tone"] = "direct"
                    communication_preferences["metaphor_types"] = ["practical", "concrete"]
                    communication_preferences["detail_level"] = "moderate"
                
                if trait_type == 'EXTR' and strength >= 70:
                    # High extraversion - prefers engaging, energetic communication
                    communication_preferences["tone"] = "enthusiastic"
                    communication_preferences["pacing"] = "brisk"
                elif trait_type == 'EXTR' and strength <= 30:
                    # Low extraversion - prefers calm, thoughtful communication
                    communication_preferences["tone"] = "calm"
                    communication_preferences["pacing"] = "measured"
                
                if trait_type == 'CONS' and strength >= 70:
                    # High conscientiousness - prefers structured, detailed communication
                    communication_preferences["formatting"] = ["clear_sections", "numbered_steps", "bullet_points"]
                    communication_preferences["detail_level"] = "high"
                elif trait_type == 'CONS' and strength <= 30:
                    # Low conscientiousness - prefers concise, flexible communication
                    communication_preferences["formatting"] = ["brief_summaries", "bullet_points"]
                    communication_preferences["detail_level"] = "low"
            
            # Store these preferences for future use
            mentor_agent = user_profile.custom_agents.filter(generic_agent__role='mentor').first()
            if mentor_agent:
                AgentMemory.objects.create(
                    custom_agent=mentor_agent,
                    content={
                        'communication_preferences': communication_preferences,
                        'auto_generated': True
                    },
                    timestamp=timezone.datetime.now(),
                    history_log_id=None  # No specific history log entry
                )
        
        # Get trust level to adjust communication based on phase
        try:
            trust_level = TrustLevel.objects.get(user_profile=user_profile)
            trust_phase = "Expansion" if trust_level.value >= 60 else "Foundation"
            
            # Adjust communication for trust phase
            if trust_phase == "Foundation":
                # More supportive, straightforward communication in Foundation phase
                communication_preferences["tone"] = "supportive"
                communication_preferences["customizations"]["trust_phase"] = "foundation"
                communication_preferences["customizations"]["reassurance_level"] = "high"
            else:
                # More challenging, growth-oriented communication in Expansion phase
                communication_preferences["customizations"]["trust_phase"] = "expansion"
                communication_preferences["customizations"]["challenge_level"] = "appropriate"
                communication_preferences["customizations"]["reassurance_level"] = "moderate"
        except TrustLevel.DoesNotExist:
            # Default to Foundation phase communication
            communication_preferences["customizations"]["trust_phase"] = "foundation"
            communication_preferences["customizations"]["reassurance_level"] = "high"
        
        return {"guidelines": communication_preferences}
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error retrieving communication guidelines")
        return {"error": str(e)}


@register_tool('explain_activity_selection')
async def explain_activity_selection(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generates a personalized explanation of why an activity was selected for a user.
    
    Input:
        user_profile_id: UUID of the user profile
        activity_id: UUID of the activity to explain
        detail_level: Desired level of detail (brief, standard, detailed)
        include_context: Whether to include contextual factors in the explanation
        
    Output:
        explanation: Dictionary with explanation details
            summary: Brief summary of why the activity was selected
            alignment_points: List of ways the activity aligns with user needs/goals
            growth_opportunities: Growth opportunities this activity provides
            tailoring_aspects: How the activity was tailored to the user
            contextual_factors: How contextual factors influenced selection (if requested)
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    activity_id = input_data.get('activity_id')
    detail_level = input_data.get('detail_level', 'standard')
    include_context = input_data.get('include_context', True)
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    if not activity_id:
        return {"error": "activity_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, TrustLevel
        from apps.activity.models import ActivityTailored, ActivityInfluencedBy
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # Get the activity with prefetched relationships
        activity = ActivityTailored.objects.select_related(
            'user_profile', 'generic_activity'
        ).prefetch_related(
            'domain_relationships__domain',
            'influences__content_type'
        ).get(id=activity_id)
        
        # Verify the activity belongs to the user
        if activity.user_profile.id != user_profile.id:
            return {"error": "Activity does not belong to the specified user profile"}
        
        # Extract influencing factors
        goal_influences = []
        belief_influences = []
        trait_influences = []
        other_influences = []
        
        for influence in activity.influences.all():
            content_type = influence.content_type.model
            strength = influence.influence_strength
            note = influence.note
            
            if content_type == 'usergoal':
                goal_influences.append({
                    'entity': influence.influencing_entity,
                    'strength': strength,
                    'note': note
                })
            elif content_type == 'belief':
                belief_influences.append({
                    'entity': influence.influencing_entity,
                    'strength': strength,
                    'note': note
                })
            elif content_type == 'usertraitinclination':
                trait_influences.append({
                    'entity': influence.influencing_entity,
                    'strength': strength,
                    'note': note
                })
            else:
                other_influences.append({
                    'type': content_type,
                    'entity': influence.influencing_entity,
                    'strength': strength,
                    'note': note
                })
        
        # Extract activity domains
        activity_domains = []
        for rel in activity.domain_relationships.all():
            activity_domains.append({
                'domain': rel.domain,
                'strength': rel.strength
            })
        
        # Get trust level for framing
        try:
            trust_level = TrustLevel.objects.get(user_profile=user_profile)
            trust_phase = "Expansion" if trust_level.value >= 60 else "Foundation"
        except TrustLevel.DoesNotExist:
            trust_phase = "Foundation"  # Default to Foundation phase
        
        # Generate explanation
        # Create a basic summary of why the activity was selected
        if goal_influences:
            primary_goal = max(goal_influences, key=lambda x: x['strength'])
            goal_title = primary_goal['entity'].title
            summary = f"This activity was selected primarily to support your goal: {goal_title}."
        elif activity_domains:
            primary_domain = max(activity_domains, key=lambda x: x['strength'])
            domain_name = primary_domain['domain'].name
            summary = f"This activity was selected to develop your capabilities in {domain_name}."
        else:
            summary = "This activity was selected based on your profile and current development needs."
            
        # Add trust phase specific framing
        if trust_phase == "Foundation":
            summary += " It provides a supportive environment to build confidence and explore your preferences."
        else:
            summary += " It offers meaningful challenge to extend your growth in important areas."
        
        # Generate alignment points
        alignment_points = []
        
        # Goal alignment points
        for goal_influence in goal_influences[:2]:  # Top 2 goals
            goal = goal_influence['entity']
            alignment_points.append(f"Supports your goal: {goal.title}")
        
        # Domain alignment points
        for domain_rel in activity_domains[:2]:  # Top 2 domains
            domain = domain_rel['domain']
            alignment_points.append(f"Develops skills in {domain.name}")
        
        # Trait alignment points
        for trait_influence in trait_influences[:2]:  # Top 2 traits
            trait = trait_influence['entity']
            alignment_points.append(f"Aligns with your {trait.generic_trait.name} tendencies")
        
        # Generate growth opportunities
        growth_opportunities = []
        
        # Challenge-based growth opportunities
        if activity.base_challenge_rating <= 40:
            growth_opportunities.append("Builds confidence through accessible challenge")
        elif activity.base_challenge_rating <= 70:
            growth_opportunities.append("Provides balanced challenge for steady growth")
        else:
            growth_opportunities.append("Stretches your capabilities for significant development")
        
        # Domain-based growth opportunities
        if activity_domains:
            primary_domain = max(activity_domains, key=lambda x: x['strength'])
            domain = primary_domain['domain']
            growth_opportunities.append(f"Strengthens your {domain.name} capabilities")
        
        # Belief-based growth opportunities
        for belief_influence in belief_influences:
            belief = belief_influence['entity']
            strength = belief_influence['strength']
            
            if strength >= 70:
                growth_opportunities.append("Reinforces positive beliefs you hold")
            elif 30 <= strength <= 70:
                growth_opportunities.append("Gently challenges limiting beliefs")
        
        # Generate tailoring aspects
        tailoring_aspects = []
        
        tailoring_aspects.append(f"Challenge level ({activity.base_challenge_rating}/100) calibrated to your current development stage")
        
        if activity.tailorization_level >= 70:
            tailoring_aspects.append("Highly customized to your specific profile and preferences")
        elif activity.tailorization_level >= 40:
            tailoring_aspects.append("Moderately customized to your profile")
        else:
            tailoring_aspects.append("Lightly adapted to your general preferences")
        
        # Add specific tailoring information based on influences
        if goal_influences:
            tailoring_aspects.append("Instructions customized to connect with your personal goals")
        
        if activity.challengingness:
            personality_dimensions = []
            for dimension, value in activity.challengingness.items():
                if value > 60:  # Only mention high challenge dimensions
                    personality_dimensions.append(dimension)
            
            if personality_dimensions:
                if len(personality_dimensions) == 1:
                    tailoring_aspects.append(f"Challenge calibrated for your {personality_dimensions[0]} tendencies")
                else:
                    tailoring_aspects.append(f"Challenge balanced across your personality dimensions")
        
        # Generate contextual factors if requested
        contextual_factors = []
        if include_context:
            # Current environment factors
            if hasattr(user_profile, 'current_environment') and user_profile.current_environment:
                env = user_profile.current_environment
                contextual_factors.append(f"Compatible with your current {env.environment_name} environment")
            
            # Current mood if available
            if hasattr(user_profile, 'current_mood'):
                mood = user_profile.current_mood
                if mood.height > 70:
                    contextual_factors.append("Selected to leverage your current positive emotional state")
                elif mood.height < 30:
                    contextual_factors.append("Selected to provide appropriate engagement for your current mood")
            
            # Other context-specific factors
            for influence in other_influences:
                if influence['type'] == 'currentmood':
                    # Already handled above
                    pass
                elif influence['type'] == 'preference':
                    contextual_factors.append("Aligned with your expressed preferences")
        
        # Adjust based on detail level
        if detail_level == 'brief':
            # Limit everything to just the most important points
            alignment_points = alignment_points[:2]
            growth_opportunities = growth_opportunities[:1]
            tailoring_aspects = tailoring_aspects[:1]
            contextual_factors = contextual_factors[:1]
        elif detail_level == 'detailed':
            # Include everything
            pass
        else:  # 'standard'
            # Middle ground
            alignment_points = alignment_points[:3]
            growth_opportunities = growth_opportunities[:2]
            tailoring_aspects = tailoring_aspects[:2]
            contextual_factors = contextual_factors[:2]
        
        # Create response
        explanation = {
            "summary": summary,
            "alignment_points": alignment_points,
            "growth_opportunities": growth_opportunities,
            "tailoring_aspects": tailoring_aspects
        }
        
        if include_context:
            explanation["contextual_factors"] = contextual_factors
        
        return {"explanation": explanation}
    except Exception as e:
        logger.error("Error while creating explanation :"+str(e))

from apps.main.agents.tools.tools_util import register_tool
from django.contrib.contenttypes.models import ContentType
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

@register_tool('calculate_trust_level')
async def calculate_trust_level(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculates the current trust level and phase for a user based on interaction history and feedback.
    
    Input:
        user_profile_id: UUID of the user profile
        include_history: Whether to include trust history data (default: False)
        time_period: Time period for recent trust analysis (last_week, last_month, all_time)
        
    Output:
        trust_data: Dictionary with trust metrics
            unified_score: Overall trust score (0-100)
            phase: Current trust phase (foundation/expansion)
            dimensions: Detailed metrics for different trust dimensions
            critical_factors: Key factors influencing current trust level
            history: Trust development over time (if requested)
            phase_transition: Information about recent or upcoming phase transitions
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    include_history = input_data.get('include_history', False)
    time_period = input_data.get('time_period', 'all_time')
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, TrustLevel
        from apps.main.models import HistoryEvent, UserFeedback
        from django.utils import timezone
        from django.db.models import Avg, Count
        import datetime
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # Get or create trust level object
        trust_level, created = TrustLevel.objects.get_or_create(
            user_profile=user_profile,
            defaults={
                'value': 50,  # Default starting value
                'aggregate_type': 'Initial',
                'aggregate_id': 'INITIAL',
                'notes': 'Initial trust level'
            }
        )
        
        # Calculate time threshold based on time_period
        now = timezone.datetime.now()
        if time_period == 'last_week':
            time_threshold = now - datetime.timedelta(days=7)
        elif time_period == 'last_month':
            time_threshold = now - datetime.timedelta(days=30)
        else:  # all_time
            time_threshold = None
            
        # Calculate engagement trust (willingness to interact)
        query = HistoryEvent.objects.filter(user_profile=user_profile)
        if time_threshold:
            query = query.filter(timestamp__gte=time_threshold)
        
        interaction_count = query.count()
        
        # Calculate action trust (completion of activities)
        completed_activities = query.filter(event_type='activity_completed').count()
        activity_interactions = query.filter(event_type__in=['activity_reaction', 'activity_completed']).count()
        
        action_trust = 0
        if activity_interactions > 0:
            action_trust = (completed_activities / activity_interactions) * 100
            
        # Calculate disclosure trust (openness in sharing)
        feedback_entries = UserFeedback.objects.filter(user_profile=user_profile)
        if time_threshold:
            feedback_entries = feedback_entries.filter(created_on__gte=time_threshold)
            
        avg_comment_length = 0
        if feedback_entries.exists():
            avg_comment_length = sum(len(f.user_comment) for f in feedback_entries) / feedback_entries.count()
            
        # Normalize comment length to 0-100 scale (assume max length of 500 chars for normalization)
        disclosure_trust = min(100, (avg_comment_length / 500) * 100)
        
        # Calculate unified trust score (weighted average of dimensions)
        engagement_weight = 0.3
        action_weight = 0.5
        disclosure_weight = 0.2
        
        # Use existing trust level as baseline and adjust based on recent metrics
        baseline_trust = trust_level.value
        
        # Simple trust adjustment algorithm
        recent_metrics = (action_trust * action_weight) + (disclosure_trust * disclosure_weight)
        if interaction_count > 0:
            # Blend baseline with recent metrics
            unified_score = (baseline_trust * 0.7) + (recent_metrics * 0.3)
        else:
            unified_score = baseline_trust
            
        # Determine trust phase
        # Foundation Phase: Trust score < 60
        # Expansion Phase: Trust score >= 60
        current_phase = "foundation" if unified_score < 60 else "expansion"
        
        # Collect critical factors
        critical_factors = []
        
        if action_trust < 40:
            critical_factors.append({
                "factor": "low_completion_rate",
                "impact": "negative",
                "description": "Low activity completion rate is limiting trust development"
            })
        elif action_trust > 80:
            critical_factors.append({
                "factor": "high_completion_rate",
                "impact": "positive",
                "description": "Consistent activity completion is building strong trust"
            })
            
        if disclosure_trust < 30:
            critical_factors.append({
                "factor": "limited_disclosure",
                "impact": "negative",
                "description": "Limited sharing in feedback is slowing trust formation"
            })
        elif disclosure_trust > 70:
            critical_factors.append({
                "factor": "open_disclosure",
                "impact": "positive",
                "description": "Open communication is accelerating trust development"
            })
            
        # Check for recent phase transition
        phase_transition = None
        if trust_level.value < 60 and unified_score >= 60:
            phase_transition = {
                "type": "foundation_to_expansion",
                "status": "occurred",
                "timing": "recent"
            }
        elif trust_level.value >= 60 and unified_score < 60:
            phase_transition = {
                "type": "expansion_to_foundation",
                "status": "occurred",
                "timing": "recent"
            }
        elif unified_score >= 55 and unified_score < 60:
            phase_transition = {
                "type": "foundation_to_expansion",
                "status": "approaching",
                "timing": "imminent"
            }
            
        # Prepare trust history if requested
        history_data = None
        if include_history:
            # For simplicity, we'll just provide a summary
            # In a real implementation, you would query historical trust values
            history_data = {
                "starting_value": trust_level.value,
                "current_value": unified_score,
                "net_change": unified_score - trust_level.value,
                "trend": "increasing" if unified_score > trust_level.value else "decreasing"
            }
            
        # Update trust level in database if significant change
        if abs(unified_score - trust_level.value) > 5:
            trust_level.value = unified_score
            trust_level.notes = f"Updated from {trust_level.value} to {unified_score} based on recent interactions"
            trust_level.save()
            
        # Prepare response
        trust_data = {
            "unified_score": round(unified_score, 1),
            "phase": current_phase,
            "dimensions": {
                "engagement_trust": interaction_count,  # Using count as a proxy
                "action_trust": round(action_trust, 1),
                "disclosure_trust": round(disclosure_trust, 1)
            },
            "critical_factors": critical_factors
        }
        
        if phase_transition:
            trust_data["phase_transition"] = phase_transition
            
        if include_history:
            trust_data["history"] = history_data
        
        return {"trust_data": trust_data}
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error calculating trust level")
        return {"error": str(e)}
    
from apps.main.agents.tools.tools_util import register_tool
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

@register_tool('generate_wheel')
async def generate_wheel(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Creates a new activity wheel with tailored activities based on the strategy framework.

    Input:
        user_profile_id: UUID of the user profile
        strategy_framework: Dictionary with the selection strategy parameters
            domain_distribution: Dictionary with domain weights
            challenge_calibration: Dictionary with challenge parameters
            selection_constraints: Dictionary with constraints and filters
            trust_phase: Current trust phase (foundation/expansion)
        activity_count: Number of activities to include in the wheel (default: 5)

    Output:
        wheel: Dictionary with wheel details
            id: ID of the created wheel
            name: Name of the wheel
            items: List of wheel items with probability percentages and activity summaries
            activities: List of tailored activities created for this wheel
            domain_coverage: Actual domain distribution achieved
            challenge_metrics: Challenge metrics for the selected activities
    """
    return await sync_to_async(_generate_wheel_sync)(input_data)

def _generate_wheel_sync(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Synchronous implementation of generate_wheel for database operations.
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    strategy_framework = input_data.get('strategy_framework', {})
    activity_count = input_data.get('activity_count', 5)

    if not user_profile_id:
        return {"error": "user_profile_id is required"}

    if not strategy_framework:
        return {"error": "strategy_framework is required"}

    # Extract key strategy components
    domain_distribution = strategy_framework.get('domain_distribution', {})
    challenge_calibration = strategy_framework.get('challenge_calibration', {})
    selection_constraints = strategy_framework.get('selection_constraints', {})
    trust_phase = strategy_framework.get('trust_phase', 'foundation')

    try:
        # Import models
        from apps.user.models import UserProfile
        from apps.activity.models import GenericActivity, ActivityTailored, GenericDomain, EntityDomainRelationship # Renamed model
        from apps.main.models import Wheel, WheelItem
        from django.utils import timezone
        from django.contrib.contenttypes.models import ContentType
        import datetime
        import uuid
        import random
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)

        # Get the user's current environment
        from apps.user.models import UserEnvironment
        user_environment = UserEnvironment.objects.filter(
            user_profile=user_profile,
            is_current=True
        ).first()

        if not user_environment:
            # Create a default environment if none exists
            from apps.user.models import GenericEnvironment
            generic_env = GenericEnvironment.objects.first()
            user_environment = UserEnvironment.objects.create(
                user_profile=user_profile,
                environment_name=f"{user_profile.profile_name}'s Environment",
                environment_description="Default environment created during wheel generation",
                generic_environment=generic_env,
                effective_start=timezone.datetime.now().date(),
                is_current=True
            )
        
        # Query available activity domains
        available_domains = GenericDomain.objects.all() # Use renamed model
        domain_map = {domain.code: domain for domain in available_domains}

        # First pass: query generic activities based on domain distribution
        selected_activities = []

        # 1. For each domain in the distribution, select activities
        for domain_code, weight in domain_distribution.items():
            if weight <= 0 or domain_code not in domain_map:
                continue

            # Calculate how many activities to select from this domain
            # based on the weight and total activity count
            domain_activity_count = max(1, int((weight / 100) * activity_count))

            # Get the domain object
            domain = domain_map[domain_code]

            # Query activities in this domain using the correct relationship
            # Use the EntityDomainRelationship to find activities linked to this domain

            activity_content_type = ContentType.objects.get_for_model(GenericActivity)
            domain_relationships = EntityDomainRelationship.objects.filter(
                domain=domain,
                content_type=activity_content_type
            ).select_related('domain')

            # Get the actual activities from the relationships
            domain_activities = [rel.content_object for rel in domain_relationships if rel.content_object]

            # Shuffle and take the needed count
            import random
            random.shuffle(domain_activities)
            selected_activities.extend(domain_activities[:domain_activity_count])
        
        # If we don't have enough activities, fill in with random activities
        if len(selected_activities) < activity_count:
            remaining_count = activity_count - len(selected_activities)
            
            # Get IDs of already selected activities
            selected_ids = [a.id for a in selected_activities]
            
            # Get additional random activities
            additional_activities = GenericActivity.objects.exclude(
                id__in=selected_ids
            ).order_by('?')[:remaining_count]
            
            selected_activities.extend(additional_activities)
        
        # If we have too many activities, trim the list
        if len(selected_activities) > activity_count:
            selected_activities = selected_activities[:activity_count]
        
        # Create the wheel
        wheel = Wheel.objects.create(
            name=f"{user_profile.profile_name}'s Wheel - {timezone.datetime.now().strftime('%Y-%m-%d')}",
            created_by="wheel_activity_agent",
            created_at=timezone.datetime.now().date()
        )
        
        # Create tailored activities and wheel items
        wheel_items = []
        created_activities = []
        actual_domain_coverage = {}
        challenge_metrics = {
            'average': 0,
            'min': 100,
            'max': 0,
            'distribution': {}
        }
        
        # Base challenge calibration based on trust phase
        base_challenge = 40 if trust_phase == 'foundation' else 60
        
        # Process each selected activity
        for generic_activity in selected_activities:
            # Calculate challenge rating based on strategy
            # In a real implementation, this would be much more sophisticated
            challenge_variation = random.randint(-10, 10)
            challenge_rating = base_challenge + challenge_variation
            
            # Apply domain-specific challenge adjustments if available
            primary_domain = generic_activity.get_primary_domain()
            if primary_domain and primary_domain.code in challenge_calibration:
                domain_adjustment = challenge_calibration[primary_domain.code]
                challenge_rating += domain_adjustment
            
            # Ensure challenge rating is within bounds
            challenge_rating = max(10, min(90, challenge_rating))
            
            # Check if a tailored activity already exists for this user, generic activity, and environment
            existing_tailored = ActivityTailored.objects.filter(
                user_profile=user_profile,
                generic_activity=generic_activity,
                user_environment=user_environment
            ).order_by('-version').first()

            if existing_tailored:
                # Reuse existing tailored activity if it's recent (within last 24 hours)
                from datetime import datetime, timedelta
                if existing_tailored.created_on >= (datetime.now().date() - timedelta(days=1)):
                    logger.info(f"Reusing existing tailored activity {existing_tailored.id} for user {user_profile_id}, generic activity {generic_activity.id}")
                    tailored_activity = existing_tailored
                else:
                    # Create new version if existing is old
                    next_version = existing_tailored.version + 1
                    logger.info(f"Creating new version {next_version} of tailored activity for user {user_profile_id}, generic activity {generic_activity.id}")
                    tailored_activity = ActivityTailored.objects.create(
                        name=generic_activity.name,
                        description=generic_activity.description,
                        created_on=timezone.datetime.now().date(),
                        duration_range=generic_activity.duration_range,
                        instructions=generic_activity.instructions,
                        social_requirements=generic_activity.social_requirements,
                        user_profile=user_profile,
                        generic_activity=generic_activity,
                        user_environment=user_environment,
                        base_challenge_rating=challenge_rating,
                        challengingness={
                            'openness': max(0, min(100, challenge_rating + random.randint(-15, 15))),
                            'conscientiousness': max(0, min(100, challenge_rating + random.randint(-15, 15))),
                            'extraversion': max(0, min(100, challenge_rating + random.randint(-15, 15))),
                            'agreeableness': max(0, min(100, challenge_rating + random.randint(-15, 15))),
                            'neuroticism': max(0, min(100, challenge_rating + random.randint(-15, 15))),
                            'honestyhumility': max(0, min(100, challenge_rating + random.randint(-15, 15)))
                        },
                        version=next_version,
                        tailorization_level=75  # Placeholder for actual tailoring logic
                    )
            else:
                # Create first version
                logger.info(f"Creating first tailored activity for user {user_profile_id}, generic activity {generic_activity.id}")
                tailored_activity = ActivityTailored.objects.create(
                    name=generic_activity.name,
                    description=generic_activity.description,
                    created_on=timezone.datetime.now().date(),
                    duration_range=generic_activity.duration_range,
                    instructions=generic_activity.instructions,
                    social_requirements=generic_activity.social_requirements,
                    user_profile=user_profile,
                    generic_activity=generic_activity,
                    user_environment=user_environment,
                    base_challenge_rating=challenge_rating,
                    challengingness={
                        'openness': max(0, min(100, challenge_rating + random.randint(-15, 15))),
                        'conscientiousness': max(0, min(100, challenge_rating + random.randint(-15, 15))),
                        'extraversion': max(0, min(100, challenge_rating + random.randint(-15, 15))),
                        'agreeableness': max(0, min(100, challenge_rating + random.randint(-15, 15))),
                        'neuroticism': max(0, min(100, challenge_rating + random.randint(-15, 15))),
                        'honestyhumility': max(0, min(100, challenge_rating + random.randint(-15, 15)))
                    },
                    version=1,
                    tailorization_level=75  # Placeholder for actual tailoring logic
                )
            
            # Add domains from generic activity to tailored activity using relationships
            # Only create domain relationships if this is a newly created tailored activity
            if not existing_tailored or tailored_activity.id != existing_tailored.id:
                # Get domains from the generic activity's relationships
                generic_relationships = generic_activity.domain_relationships.select_related('domain').all()
                for rel in generic_relationships:
                    # Check if relationship already exists to avoid duplicates
                    existing_rel = EntityDomainRelationship.objects.filter(
                        content_type=ContentType.objects.get_for_model(tailored_activity),
                        object_id=tailored_activity.id,
                        domain=rel.domain
                    ).first()

                    if not existing_rel:
                        # Create a corresponding relationship for the tailored activity
                        EntityDomainRelationship.objects.create(
                            content_object=tailored_activity,
                            domain=rel.domain,
                            strength=rel.strength
                        )

            # Update actual domain coverage (always do this regardless of reuse)
            # Get domains from the tailored activity's relationships
            tailored_relationships = tailored_activity.domain_relationships.select_related('domain').all()
            for rel in tailored_relationships:
                domain_code = rel.domain.code
                if domain_code in actual_domain_coverage:
                    actual_domain_coverage[domain_code] += 1
                else:
                    actual_domain_coverage[domain_code] = 1
            
            # Create wheel item with probability
            # In a real implementation, probabilities would be more sophisticated
            # For simplicity, we'll use equal probabilities
            wheel_item = WheelItem.objects.create(
                id=f"item_{str(uuid.uuid4())[:8]}",
                wheel=wheel,
                percentage=100 / activity_count,
                activity_tailored=tailored_activity
            )
            
            # Track created objects
            wheel_items.append({
                'id': wheel_item.id,
                'percentage': wheel_item.percentage,
                'activity_name': tailored_activity.name,
                'activity_id': tailored_activity.id
            })
            
            created_activities.append({
                'id': tailored_activity.id,
                'name': tailored_activity.name,
                'challenge_rating': tailored_activity.base_challenge_rating,
                'primary_domain': primary_domain.code if primary_domain else 'unknown'
            })
            
            # Update challenge metrics
            challenge_metrics['min'] = min(challenge_metrics['min'], challenge_rating)
            challenge_metrics['max'] = max(challenge_metrics['max'], challenge_rating)
            challenge_metrics['average'] += challenge_rating / activity_count
            
            # Update challenge distribution
            challenge_bracket = f"{(challenge_rating // 10) * 10}-{(challenge_rating // 10) * 10 + 9}"
            if challenge_bracket in challenge_metrics['distribution']:
                challenge_metrics['distribution'][challenge_bracket] += 1
            else:
                challenge_metrics['distribution'][challenge_bracket] = 1
        
        # Convert actual domain coverage to percentages
        for domain_code in actual_domain_coverage:
            actual_domain_coverage[domain_code] = (actual_domain_coverage[domain_code] / activity_count) * 100
        
        # Prepare response
        wheel_data = {
            "id": wheel.id,
            "name": wheel.name,
            "items": wheel_items,
            "activities": created_activities,
            "domain_coverage": actual_domain_coverage,
            "challenge_metrics": challenge_metrics
        }
        
        return {"wheel": wheel_data}
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error generating wheel")
        return {"error": str(e)}
    
from apps.main.agents.tools.tools_util import register_tool
import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

@register_tool('validate_activities')
async def validate_activities(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Performs ethical validation of activities and wheel composition for alignment with system principles.
    
    Input:
        user_profile_id: UUID of the user profile
        activity_ids: List of activity IDs to validate, or None to validate all activities in a wheel
        wheel_id: ID of the wheel to validate (required if activity_ids not provided)
        trust_phase: Current trust phase (foundation/expansion)
        validation_level: Depth of validation (standard, comprehensive)
        
    Output:
        validation: Dictionary with validation results
            approved: Whether all activities are approved
            activities: List of activity validation results
                id: Activity ID
                name: Activity name
                approved: Whether this activity is approved
                concerns: List of ethical concerns if any
                recommendations: List of modification recommendations if not approved
            wheel_assessment: Wheel-level validation results (if wheel_id provided)
                approved: Whether the wheel composition is approved
                concerns: List of wheel-level ethical concerns if any
                recommendations: List of wheel-level recommendations
            ethical_rationale: Overall ethical assessment rationale
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    activity_ids = input_data.get('activity_ids')
    wheel_id = input_data.get('wheel_id')
    trust_phase = input_data.get('trust_phase', 'foundation')
    validation_level = input_data.get('validation_level', 'standard')
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    if not activity_ids and not wheel_id:
        return {"error": "Either activity_ids or wheel_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, TrustLevel
        from apps.activity.models import ActivityTailored
        from apps.main.models import Wheel, WheelItem
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # Get activities to validate
        activities_to_validate = []
        
        if activity_ids:
            # Validate specific activities
            for activity_id in activity_ids:
                try:
                    activity = ActivityTailored.objects.get(id=activity_id)
                    activities_to_validate.append(activity)
                except ActivityTailored.DoesNotExist:
                    logger.warning(f"Activity with ID {activity_id} not found, skipping")
        elif wheel_id:
            # Validate all activities in a wheel
            try:
                wheel = Wheel.objects.get(id=wheel_id)
                wheel_items = WheelItem.objects.filter(wheel=wheel)
                
                for item in wheel_items:
                    activities_to_validate.append(item.activity_tailored)
            except Wheel.DoesNotExist:
                return {"error": f"Wheel with ID {wheel_id} not found"}
        
        if not activities_to_validate:
            return {"error": "No valid activities found to validate"}
        
        # Get the trust level for reference
        trust_level, created = TrustLevel.objects.get_or_create(
            user_profile=user_profile,
            defaults={
                'value': 50,
                'aggregate_type': 'Initial',
                'aggregate_id': 'INITIAL',
                'notes': 'Initial trust level'
            }
        )
        
        # Validate each activity
        activity_validations = []
        overall_approved = True
        
        for activity in activities_to_validate:
            # Perform ethical validation
            activity_validation = validate_single_activity(activity, user_profile, trust_level, trust_phase, validation_level)
            
            # Add to results
            activity_validations.append(activity_validation)
            
            # Update overall approval
            if not activity_validation['approved']:
                overall_approved = False
        
        # Perform wheel-level validation if applicable
        wheel_assessment = None
        if wheel_id:
            wheel_assessment = validate_wheel_composition(activities_to_validate, user_profile, trust_phase, validation_level)
        
        # Generate ethical rationale
        ethical_rationale = generate_ethical_rationale(activity_validations, wheel_assessment, trust_phase)
        
        # Prepare response
        validation_results = {
            "approved": overall_approved,
            "activities": activity_validations,
            "ethical_rationale": ethical_rationale
        }
        
        if wheel_assessment:
            validation_results["wheel_assessment"] = wheel_assessment
        
        return {"validation": validation_results}
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error validating activities")
        return {"error": str(e)}

def validate_single_activity(activity, user_profile, trust_level, trust_phase, validation_level):
    """Helper function to validate a single activity"""
    
    # Initialize validation result
    validation = {
        "id": activity.id,
        "name": activity.name,
        "approved": True,
        "concerns": [],
        "recommendations": []
    }
    
    # Check challenge level appropriateness
    challenge_rating = activity.base_challenge_rating
    
    # Different thresholds based on trust phase
    max_challenge = 60 if trust_phase == 'foundation' else 85
    
    if challenge_rating > max_challenge:
        validation["approved"] = False
        validation["concerns"].append({
            "type": "excessive_challenge",
            "severity": "high",
            "principle": "benevolence",
            "description": f"Challenge rating of {challenge_rating} exceeds the maximum of {max_challenge} for {trust_phase} phase"
        })
        validation["recommendations"].append({
            "type": "reduce_challenge",
            "action": "adjust_challenge_rating",
            "target_value": max_challenge - 5,
            "description": f"Reduce challenge rating to below {max_challenge} to maintain psychological safety"
        })
    
    # Check for clarity and transparency in instructions
    if not activity.instructions or len(activity.instructions) < 50:
        validation["approved"] = False
        validation["concerns"].append({
            "type": "insufficient_clarity",
            "severity": "medium",
            "principle": "transparency",
            "description": "Activity instructions lack sufficient detail for transparent understanding"
        })
        validation["recommendations"].append({
            "type": "enhance_instructions",
            "action": "expand_instructions",
            "description": "Provide more detailed step-by-step instructions to ensure transparency"
        })
    
    # Check for connections to user goals
    # In a real implementation, this would analyze connections to the user's actual goals
    # For simplicity, we'll check if the activity has influences
    if hasattr(activity, 'influences') and activity.influences.count() == 0:
        validation["concerns"].append({
            "type": "weak_goal_alignment",
            "severity": "low",
            "principle": "autonomy",
            "description": "Activity lacks explicit connections to user's stated goals"
        })
        validation["recommendations"].append({
            "type": "enhance_relevance",
            "action": "add_goal_connections",
            "description": "Add explicit connections to the user's goals to enhance relevance"
        })
    
    # Additional checks for comprehensive validation
    if validation_level == 'comprehensive':
        # Check for domain balance
        # In a real implementation, this would be more sophisticated
        primary_domain = activity.get_primary_domain()
        if not primary_domain:
            validation["concerns"].append({
                "type": "missing_domain",
                "severity": "low",
                "principle": "fairness",
                "description": "Activity lacks a primary domain designation"
            })
            validation["recommendations"].append({
                "type": "add_domain",
                "action": "assign_primary_domain",
                "description": "Assign a primary domain to facilitate proper categorization"
            })
        
        # Check for resource feasibility
        # In a real implementation, this would check against user's available resources
        if activity.resource_requirements.count() > 0:
            validation["concerns"].append({
                "type": "resource_verification",
                "severity": "low",
                "principle": "feasibility",
                "description": "Activity has resource requirements that should be verified against user inventory"
            })
            validation["recommendations"].append({
                "type": "verify_resources",
                "action": "check_resource_availability",
                "description": "Confirm user has access to required resources before presenting"
            })
    
    return validation

def validate_wheel_composition(activities, user_profile, trust_phase, validation_level):
    """Helper function to validate wheel composition"""
    
    # Initialize wheel assessment
    wheel_assessment = {
        "approved": True,
        "concerns": [],
        "recommendations": []
    }
    
    # Check for sufficient variety
    # Count unique domains
    domains = set()
    for activity in activities:
        primary_domain = activity.get_primary_domain()
        if primary_domain:
            domains.add(primary_domain.id)
    
    if len(domains) < 3:
        wheel_assessment["approved"] = False
        wheel_assessment["concerns"].append({
            "type": "insufficient_variety",
            "severity": "medium",
            "principle": "growth",
            "description": f"Wheel only contains {len(domains)} unique domains, which is less than the minimum of 3"
        })
        wheel_assessment["recommendations"].append({
            "type": "increase_variety",
            "action": "add_diverse_domains",
            "description": "Include activities from at least 3 different domains to promote balanced growth"
        })
    
    # Check challenge distribution
    challenge_levels = [activity.base_challenge_rating for activity in activities]
    avg_challenge = sum(challenge_levels) / len(challenge_levels)
    
    # Different expectations based on trust phase
    if trust_phase == 'foundation':
        if avg_challenge > 50:
            wheel_assessment["concerns"].append({
                "type": "excessive_average_challenge",
                "severity": "medium",
                "principle": "benevolence",
                "description": f"Average challenge level of {avg_challenge:.1f} is too high for foundation phase"
            })
            wheel_assessment["recommendations"].append({
                "type": "reduce_overall_challenge",
                "action": "lower_challenge_levels",
                "description": "Lower overall challenge levels to maintain trust in foundation phase"
            })
    else:  # expansion phase
        if avg_challenge < 40:
            wheel_assessment["concerns"].append({
                "type": "insufficient_challenge",
                "severity": "low",
                "principle": "growth",
                "description": f"Average challenge level of {avg_challenge:.1f} may not provide sufficient growth opportunity"
            })
            wheel_assessment["recommendations"].append({
                "type": "increase_challenge",
                "action": "raise_challenge_levels",
                "description": "Consider including more challenging activities to promote growth"
            })
    
    # Check for excessive similarity
    # In a real implementation, this would be more sophisticated
    if len(activities) >= 3:
        has_similarity_concern = False
        for i in range(len(activities)):
            for j in range(i+1, len(activities)):
                # Simple name-based similarity check
                if activities[i].name == activities[j].name:
                    has_similarity_concern = True
                    break
        
        if has_similarity_concern:
            wheel_assessment["concerns"].append({
                "type": "activity_similarity",
                "severity": "low",
                "principle": "engagement",
                "description": "Some activities appear too similar, which may reduce engagement"
            })
            wheel_assessment["recommendations"].append({
                "type": "increase_diversity",
                "action": "replace_similar_activities",
                "description": "Replace similar activities to ensure variety and engagement"
            })
    
    return wheel_assessment

def generate_ethical_rationale(activity_validations, wheel_assessment, trust_phase):
    """Helper function to generate overall ethical rationale"""
    
    # Initialize ethical rationale
    rationale = {
        "principles": {
            "benevolence": {
                "assessment": "upheld",
                "evidence": []
            },
            "fairness": {
                "assessment": "upheld",
                "evidence": []
            },
            "transparency": {
                "assessment": "upheld",
                "evidence": []
            },
            "autonomy": {
                "assessment": "upheld", 
                "evidence": []
            }
        },
        "trust_phase_appropriateness": "appropriate",
        "overall_assessment": "Ethically sound"
    }
    
    # Analyze activity validations
    for validation in activity_validations:
        for concern in validation.get("concerns", []):
            principle = concern.get("principle")
            if principle in rationale["principles"]:
                # If we find a severe concern, mark principle as compromised
                if concern.get("severity") == "high":
                    rationale["principles"][principle]["assessment"] = "compromised"
                # Otherwise, mark as qualified if it's not already compromised
                elif rationale["principles"][principle]["assessment"] != "compromised":
                    rationale["principles"][principle]["assessment"] = "qualified"
                
                # Add evidence
                rationale["principles"][principle]["evidence"].append({
                    "activity_name": validation["name"],
                    "issue": concern["description"]
                })
    
    # Analyze wheel assessment if available
    if wheel_assessment:
        for concern in wheel_assessment.get("concerns", []):
            principle = concern.get("principle")
            if principle in rationale["principles"]:
                # Add wheel-level evidence
                rationale["principles"][principle]["evidence"].append({
                    "scope": "wheel_composition",
                    "issue": concern["description"]
                })
    
    # Determine overall assessment
    any_compromised = any(p["assessment"] == "compromised" for p in rationale["principles"].values())
    any_qualified = any(p["assessment"] == "qualified" for p in rationale["principles"].values())
    
    if any_compromised:
        rationale["overall_assessment"] = "Ethically concerning - modifications required"
        rationale["trust_phase_appropriateness"] = "inappropriate"
    elif any_qualified:
        rationale["overall_assessment"] = "Generally ethical with minor concerns"
        rationale["trust_phase_appropriateness"] = "qualified"
    else:
        rationale["overall_assessment"] = "Ethically sound"
        rationale["trust_phase_appropriateness"] = "appropriate"
    
    # Add trust phase context
    rationale["trust_phase_context"] = {
        "phase": trust_phase,
        "implications": f"{'Lower' if trust_phase == 'foundation' else 'Higher'} challenge tolerance, "
                        f"{'more' if trust_phase == 'foundation' else 'less'} detailed instructions, "
                        f"{'stronger' if trust_phase == 'foundation' else 'lighter'} safety guardrails"
    }
    
    return rationale

from apps.main.agents.tools.tools_util import register_tool
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

@register_tool('analyze_trait_gap')
async def analyze_trait_gap(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyzes the gap between user traits and activity requirements to calculate challenge levels.
    
    Input:
        user_profile_id: UUID of the user profile
        activity_id: UUID of the activity to analyze (optional)
        trait_codes: List of specific trait codes to analyze (optional)
        include_growth_opportunities: Whether to include detailed growth information
        
    Output:
        analysis: Dictionary with trait gap analysis
            overall_gap: Overall trait gap score (0-100)
            trait_gaps: Detailed breakdown of gaps by trait
            challenge_recommendations: Challenge level recommendations by trait dimension
            growth_opportunities: Identified growth areas based on gaps (if requested)
            strategy_implications: Implications for activity selection strategy
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    activity_id = input_data.get('activity_id')
    trait_codes = input_data.get('trait_codes', [])
    include_growth_opportunities = input_data.get('include_growth_opportunities', True)
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, UserTraitInclination, GenericTrait
        from apps.activity.models import ActivityTailored, GenericActivityUserRequirement
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # Determine which traits to analyze
        if trait_codes:
            # Use specific trait codes provided
            traits_to_analyze = GenericTrait.objects.filter(code__in=trait_codes)
        elif activity_id:
            # Use traits required by the specified activity
            try:
                activity = ActivityTailored.objects.get(id=activity_id)
                generic_activity = activity.generic_activity
                
                # Get trait requirements for this activity
                trait_requirements = GenericActivityUserRequirement.objects.filter(
                    generic_activity=generic_activity,
                    content_type__model='generictrait'
                ).select_related('content_type')
                
                # Get the trait IDs
                trait_ids = [req.object_id for req in trait_requirements]
                traits_to_analyze = GenericTrait.objects.filter(id__in=trait_ids)
                
            except ActivityTailored.DoesNotExist:
                return {"error": f"Activity with ID {activity_id} not found"}
        else:
            # Use all HEXACO traits for a general analysis
            traits_to_analyze = GenericTrait.objects.filter(
                trait_type__in=['OPEN', 'CONS', 'EXTR', 'AGRE', 'EMO', 'HONHUM']
            )
        
        if not traits_to_analyze.exists():
            return {"error": "No traits found to analyze"}
        
        # Get user's trait inclinations
        user_traits = UserTraitInclination.objects.filter(
            user_profile=user_profile,
            generic_trait__in=traits_to_analyze
        ).select_related('generic_trait')
        
        # Create map for faster lookup
        user_trait_map = {t.generic_trait.id: t.strength for t in user_traits}
        
        # Get activity requirements if activity_id was provided
        activity_requirements = {}
        if activity_id:
            try:
                activity = ActivityTailored.objects.get(id=activity_id)
                generic_activity = activity.generic_activity
                
                requirements = GenericActivityUserRequirement.objects.filter(
                    generic_activity=generic_activity,
                    content_type__model='generictrait'
                )
                
                for req in requirements:
                    activity_requirements[req.object_id] = {
                        'level_required': req.level_required,
                        'optional': req.optional
                    }
            except ActivityTailored.DoesNotExist:
                # This was already checked earlier, but keeping the try-except for robustness
                pass
        
        # Analyze trait gaps
        trait_gaps = []
        total_gap = 0
        total_weight = 0
        
        for trait in traits_to_analyze:
            # Get user's strength in this trait
            user_strength = user_trait_map.get(trait.id, 0)  # Default to 0 if not found
            
            # Get activity's requirement for this trait
            if activity_id and trait.id in activity_requirements:
                req_info = activity_requirements[trait.id]
                required_level = req_info['level_required'] or 50  # Default to medium if not specified
                optional = req_info['optional']
                
                # Calculate gap
                gap = max(0, required_level - user_strength)
                
                # Determine weight based on optionality
                weight = 1.0 if not optional else 0.5
            else:
                # For general analysis without activity context
                # Use HEXACO baseline expectations
                # This is a simplified approach - in a real implementation,
                # this would be more sophisticated
                baseline_level = 50  # Generic baseline expectation
                gap = max(0, baseline_level - user_strength)
                weight = 1.0
                required_level = baseline_level
                optional = False
            
            # Add to total gap calculation
            total_gap += gap * weight
            total_weight += weight
            
            # Add to trait gaps list
            trait_gaps.append({
                'trait_id': trait.id,
                'trait_code': trait.code,
                'trait_name': trait.name,
                'trait_type': trait.trait_type,
                'user_strength': user_strength,
                'required_level': required_level,
                'gap': gap,
                'significance': 'high' if gap > 30 else 'medium' if gap > 15 else 'low',
                'optional': optional
            })
        
        # Calculate overall gap
        overall_gap = total_gap / total_weight if total_weight > 0 else 0
        
        # Generate challenge recommendations
        challenge_recommendations = generate_challenge_recommendations(trait_gaps, overall_gap)
        
        # Generate growth opportunities if requested
        growth_opportunities = None
        if include_growth_opportunities:
            growth_opportunities = generate_growth_opportunities(trait_gaps, user_profile)
        
        # Generate strategy implications
        strategy_implications = generate_strategy_implications(overall_gap, trait_gaps)
        
        # Prepare response
        analysis = {
            'overall_gap': overall_gap,
            'trait_gaps': trait_gaps,
            'challenge_recommendations': challenge_recommendations,
            'strategy_implications': strategy_implications
        }
        
        if growth_opportunities:
            analysis['growth_opportunities'] = growth_opportunities
        
        return {'analysis': analysis}
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error analyzing trait gap")
        return {"error": str(e)}

def generate_challenge_recommendations(trait_gaps, overall_gap):
    """Helper function to generate challenge recommendations based on trait gaps"""
    
    # Initialize base challenge level based on overall gap
    base_challenge = 50
    
    if overall_gap > 30:
        base_challenge = 30  # Lower base challenge for significant gaps
    elif overall_gap > 15:
        base_challenge = 40  # Moderately lower base challenge
    elif overall_gap < 5:
        base_challenge = 60  # Higher base challenge for minimal gaps
    
    # Prepare trait dimension recommendations
    trait_dimensions = {
        'OPEN': {'name': 'Openness', 'adjustment': 0},
        'CONS': {'name': 'Conscientiousness', 'adjustment': 0},
        'EXTR': {'name': 'Extraversion', 'adjustment': 0},
        'AGRE': {'name': 'Agreeableness', 'adjustment': 0},
        'EMO': {'name': 'Emotionality', 'adjustment': 0},
        'HONHUM': {'name': 'Honesty-Humility', 'adjustment': 0}
    }
    
    # Calculate adjustments for each dimension based on trait gaps
    for gap in trait_gaps:
        trait_type = gap['trait_type']
        if trait_type in trait_dimensions:
            # Calculate adjustment - larger gaps mean lower challenge in that dimension
            adjustment = -1 * (gap['gap'] / 2)  # Divide by 2 to moderate effect
            
            # Update the dimension adjustment
            trait_dimensions[trait_type]['adjustment'] += adjustment
    
    # Generate final recommendations
    challenge_recommendations = {
        'base_challenge': base_challenge,
        'explanation': f"Base challenge level of {base_challenge} recommended based on overall trait gap of {overall_gap:.1f}",
        'dimension_adjustments': {}
    }
    
    for trait_type, info in trait_dimensions.items():
        # Only include dimensions that have traits in the analysis
        if any(gap['trait_type'] == trait_type for gap in trait_gaps):
            # Calculate final adjusted challenge for this dimension
            adjustment = info['adjustment']
            adjusted_challenge = max(10, min(90, base_challenge + adjustment))
            
            challenge_recommendations['dimension_adjustments'][trait_type] = {
                'dimension': info['name'],
                'challenge_level': adjusted_challenge,
                'adjustment': adjustment,
                'explanation': get_adjustment_explanation(adjustment, info['name'])
            }
    
    return challenge_recommendations

def get_adjustment_explanation(adjustment, dimension_name):
    """Helper function to generate explanation for challenge adjustment"""
    
    if adjustment < -15:
        return f"Significantly lower challenge for {dimension_name} due to large trait gaps"
    elif adjustment < -5:
        return f"Moderately lower challenge for {dimension_name} due to trait gaps"
    elif adjustment < 5 and adjustment >= -5:
        return f"Minimal adjustment for {dimension_name}"
    elif adjustment < 15:
        return f"Moderately higher challenge for {dimension_name} due to strong trait alignment"
    else:
        return f"Significantly higher challenge for {dimension_name} due to excellent trait alignment"

def generate_growth_opportunities(trait_gaps, user_profile):
    """Helper function to generate growth opportunities based on trait gaps"""
    
    # Sort trait gaps by gap size (largest first)
    sorted_gaps = sorted(trait_gaps, key=lambda x: x['gap'], reverse=True)
    
    # Focus on top 3 gaps for growth opportunities
    focus_gaps = sorted_gaps[:3]
    
    # Generate growth opportunities
    opportunities = []
    
    for gap in focus_gaps:
        # Only consider meaningful gaps
        if gap['gap'] > 10:
            opportunity = {
                'trait_code': gap['trait_code'],
                'trait_name': gap['trait_name'],
                'current_level': gap['user_strength'],
                'target_level': gap['required_level'],
                'gap': gap['gap'],
                'development_potential': 'high' if gap['gap'] > 30 else 'moderate' if gap['gap'] > 15 else 'low',
                'development_strategies': generate_development_strategies(gap['trait_type'], gap['trait_name'])
            }
            
            opportunities.append(opportunity)
    
    return opportunities

def generate_development_strategies(trait_type, trait_name):
    """Helper function to generate development strategies for a trait"""
    
    # This is a simplified implementation - in a real system,
    # this would be much more sophisticated with personalized strategies
    
    strategies = []
    
    if trait_type == 'OPEN':
        strategies.append("Engage in creative activities that challenge conventional thinking")
        strategies.append("Explore unfamiliar subjects or cultural experiences")
        strategies.append("Practice mindfulness to become more aware of new experiences")
    elif trait_type == 'CONS':
        strategies.append("Develop structured routines for daily tasks")
        strategies.append("Set specific, measurable goals with deadlines")
        strategies.append("Practice breaking large tasks into smaller steps")
    elif trait_type == 'EXTR':
        strategies.append("Gradually increase participation in social situations")
        strategies.append("Practice initiating conversations with new people")
        strategies.append("Join group activities aligned with personal interests")
    elif trait_type == 'AGRE':
        strategies.append("Practice active listening in conversations")
        strategies.append("Look for common ground in disagreements")
        strategies.append("Volunteer for community service activities")
    elif trait_type == 'EMO':
        strategies.append("Learn and practice emotional regulation techniques")
        strategies.append("Develop awareness of emotional triggers")
        strategies.append("Practice self-compassion during emotional experiences")
    elif trait_type == 'HONHUM':
        strategies.append("Practice modesty when discussing achievements")
        strategies.append("Look for ways to contribute without recognition")
        strategies.append("Reflect on personal values and integrity")
    
    # Return 2-3 strategies
    return strategies[:min(3, len(strategies))]

def generate_strategy_implications(overall_gap, trait_gaps):
    """Helper function to generate strategy implications based on gap analysis"""
    
    # Basic implication categories
    implications = {
        'challenge_approach': {
            'strategy': '',
            'rationale': ''
        },
        'domain_recommendations': [],
        'feedback_focus': {
            'areas': [],
            'approach': ''
        },
        'progression_plan': {
            'approach': '',
            'timeline': ''
        }
    }
    
    # Determine challenge approach
    if overall_gap > 30:
        implications['challenge_approach']['strategy'] = 'scaffold_heavily'
        implications['challenge_approach']['rationale'] = f"High overall trait gap ({overall_gap:.1f}) indicates need for substantial scaffolding and gradual progression"
    elif overall_gap > 15:
        implications['challenge_approach']['strategy'] = 'scaffold_moderately'
        implications['challenge_approach']['rationale'] = f"Moderate overall trait gap ({overall_gap:.1f}) suggests need for some scaffolding with periodic stretch challenges"
    elif overall_gap > 5:
        implications['challenge_approach']['strategy'] = 'balanced'
        implications['challenge_approach']['rationale'] = f"Low overall trait gap ({overall_gap:.1f}) indicates good alignment with moderate challenge appropriate"
    else:
        implications['challenge_approach']['strategy'] = 'challenge_actively'
        implications['challenge_approach']['rationale'] = f"Minimal overall trait gap ({overall_gap:.1f}) suggests readiness for substantial challenges to promote growth"
    
    # Generate domain recommendations based on trait types
    trait_type_domains = {
        'OPEN': ['creative', 'exploratory', 'intellectual'],
        'CONS': ['productive', 'organizational', 'achievement'],
        'EXTR': ['social', 'interactive', 'expressive'],
        'AGRE': ['cooperative', 'supportive', 'community'],
        'EMO': ['emotional', 'reflective', 'mindful'],
        'HONHUM': ['ethical', 'fair', 'modest']
    }
    
    # Identify trait types with significant gaps
    significant_gap_types = [gap['trait_type'] for gap in trait_gaps if gap['gap'] > 20]
    minimal_gap_types = [gap['trait_type'] for gap in trait_gaps if gap['gap'] < 10]
    
    # Add recommendations for domains to approach carefully
    for trait_type in significant_gap_types:
        if trait_type in trait_type_domains:
            for domain in trait_type_domains[trait_type]:
                implications['domain_recommendations'].append({
                    'domain': domain,
                    'approach': 'careful_scaffolding',
                    'rationale': f"Significant gap in {trait_type} indicates need for careful approach to {domain} activities"
                })
    
    # Add recommendations for domains to leverage for engagement
    for trait_type in minimal_gap_types:
        if trait_type in trait_type_domains:
            for domain in trait_type_domains[trait_type]:
                implications['domain_recommendations'].append({
                    'domain': domain,
                    'approach': 'leverage_strength',
                    'rationale': f"Strong alignment with {trait_type} suggests {domain} activities will build confidence and engagement"
                })
    
    # Determine feedback focus
    high_gap_traits = [gap for gap in trait_gaps if gap['gap'] > 20]
    if high_gap_traits:
        implications['feedback_focus']['areas'] = [gap['trait_name'] for gap in high_gap_traits[:2]]
        implications['feedback_focus']['approach'] = 'supportive_guidance'
    else:
        implications['feedback_focus']['areas'] = ['overall growth', 'challenge calibration']
        implications['feedback_focus']['approach'] = 'growth_oriented'
    
    # Determine progression plan
    if overall_gap > 25:
        implications['progression_plan']['approach'] = 'slow_steady'
        implications['progression_plan']['timeline'] = 'extended'
    elif overall_gap > 15:
        implications['progression_plan']['approach'] = 'moderate_pace'
        implications['progression_plan']['timeline'] = 'standard'
    else:
        implications['progression_plan']['approach'] = 'accelerated'
        implications['progression_plan']['timeline'] = 'compressed'
    
    return implications

from apps.main.agents.tools.tools_util import register_tool
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

@register_tool('formulate_strategy')
async def formulate_strategy(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Formulates a comprehensive activity selection strategy based on multi-agent inputs.
    
    Input:
        user_profile_id: UUID of the user profile
        resource_context: Resource analysis from Resource & Capacity Agent
        engagement_patterns: Engagement analysis from Engagement & Pattern Agent
        psychological_assessment: Psychological state from Psychological Agent
        trait_gap_analysis: Gap analysis from Gap Analysis tool
        trust_phase: Current trust phase (foundation/expansion)
        strategy_focus: Emphasis area for strategy (growth, engagement, balance)
        
    Output:
        strategy: Dictionary with comprehensive strategy framework
            domain_distribution: Recommended distribution across domains
            challenge_calibration: Challenge parameters across personality dimensions
            selection_constraints: Filtering constraints for activity selection
            value_emphasis: Areas to emphasize in activity value propositions
            rationale: Detailed explanation of strategy decisions
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    resource_context = input_data.get('resource_context', {})
    engagement_patterns = input_data.get('engagement_patterns', {})
    psychological_assessment = input_data.get('psychological_assessment', {})
    trait_gap_analysis = input_data.get('trait_gap_analysis', {})
    trust_phase = input_data.get('trust_phase', 'foundation')
    strategy_focus = input_data.get('strategy_focus', 'balance')
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, UserGoal
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # 1. Generate domain distribution
        domain_distribution = generate_domain_distribution(
            engagement_patterns, 
            psychological_assessment, 
            trait_gap_analysis,
            strategy_focus
        )
        
        # 2. Calculate challenge calibration
        challenge_calibration = calculate_challenge_calibration(
            trait_gap_analysis, 
            psychological_assessment, 
            trust_phase
        )
        
        # 3. Define selection constraints
        selection_constraints = define_selection_constraints(
            resource_context, 
            psychological_assessment, 
            trust_phase
        )
        
        # 4. Determine value emphasis areas
        value_emphasis = determine_value_emphasis(
            psychological_assessment, 
            user_profile_id
        )
        
        # 5. Generate comprehensive rationale
        rationale = generate_strategy_rationale(
            domain_distribution,
            challenge_calibration,
            selection_constraints,
            value_emphasis,
            trust_phase,
            strategy_focus
        )
        
        # Prepare complete strategy framework
        strategy = {
            "domain_distribution": domain_distribution,
            "challenge_calibration": challenge_calibration,
            "selection_constraints": selection_constraints,
            "value_emphasis": value_emphasis,
            "trust_phase": trust_phase,
            "strategy_focus": strategy_focus,
            "rationale": rationale
        }
        
        return {"strategy": strategy}
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error formulating strategy")
        return {"error": str(e)}


def define_selection_constraints(resource_context, psychological_assessment, trust_phase):
    """Helper function to define selection constraints"""
    
    # Initialize constraints
    constraints = {
        "environment_requirements": {},
        "resource_requirements": {},
        "time_constraints": {},
        "safety_boundaries": {},
        "exclusion_criteria": []
    }
    
    # Apply resource context constraints
    if resource_context:
        # Environment constraints
        environment_info = resource_context.get('environmental_assessment', {})
        for domain, support in environment_info.get('domain_support_ratings', {}).items():
            if support < 20:
                # Domain is poorly supported in current environment
                constraints['environment_requirements'][domain] = 'avoid'
            elif support > 80:
                # Domain is well supported in current environment
                constraints['environment_requirements'][domain] = 'preferred'
        
        # Resource constraints
        resource_inventory = resource_context.get('resource_inventory', {})
        for resource, status in resource_inventory.items():
            if status.get('availability', 'unknown') == 'unavailable':
                constraints['resource_requirements'][resource] = 'exclude'
        
        # Time constraints
        time_availability = resource_context.get('time_availability', {})
        max_duration = time_availability.get('max_duration_minutes', 60)
        constraints['time_constraints'] = {
            'max_duration_minutes': max_duration,
            'preferred_duration_minutes': max(15, max_duration // 2)
        }
    
    # Apply psychological assessment constraints
    if psychological_assessment:
        vulnerability_flags = psychological_assessment.get('emotional_vulnerability_flags', [])
        safety_boundaries = {}
        
        for flag in vulnerability_flags:
            category = flag.get('category')
            severity = flag.get('severity', 'medium')
            
            if category:
                safety_boundaries[category] = severity
        
        constraints['safety_boundaries'] = safety_boundaries
        
        # Add exclusion criteria based on vulnerability
        for category, severity in safety_boundaries.items():
            if severity == 'high':
                constraints['exclusion_criteria'].append(f"high_{category}_challenge")
    
    # Apply trust phase constraints
    if trust_phase == 'foundation':
        # Foundation phase has stricter constraints
        constraints['time_constraints']['max_duration_minutes'] = min(
            constraints['time_constraints'].get('max_duration_minutes', 60),
            45  # Cap at 45 minutes for foundation phase
        )
        
        # Add foundation-specific exclusion criteria
        constraints['exclusion_criteria'].extend([
            "high_social_pressure",
            "high_performance_pressure",
            "limited_instructions"
        ])
    
    return constraints


def determine_value_emphasis(psychological_assessment, user_profile_id):
    """Helper function to determine value emphasis areas"""
    
    # Initialize value emphasis
    emphasis = {
        "primary_values": [],
        "framing_approach": "",
        "goal_connections": [],
        "beneficiary_focus": "self"
    }
    
    # Determine primary values based on psychological assessment
    if psychological_assessment:
        beliefs = psychological_assessment.get('beliefs', [])
        
        # Extract belief themes (simplified)
        belief_themes = set()
        for belief in beliefs:
            category = belief.get('category')
            if category:
                belief_themes.add(category)
        
        # Map belief themes to primary values
        value_mappings = {
            "GROWTH": "personal_development",
            "ACHIEVEMENT": "accomplishment",
            "CONNECTEDNESS": "relationship",
            "AUTONOMY": "freedom",
            "SECURITY": "stability",
            "MEANING": "purpose",
            "CREATIVITY": "expression"
        }
        
        for theme in belief_themes:
            if theme in value_mappings:
                emphasis['primary_values'].append(value_mappings[theme])
        
        # Get trust phase info
        trust_phase = psychological_assessment.get('trust_phase', 'foundation')
        
        # Set framing approach based on trust phase
        if trust_phase == 'foundation':
            emphasis['framing_approach'] = "supportive_safety"
        else:  # expansion
            emphasis['framing_approach'] = "growth_opportunity"
    
    # If we don't have enough primary values, add defaults
    if len(emphasis['primary_values']) < 2:
        defaults = ["personal_development", "accomplishment", "well_being"]
        for val in defaults:
            if val not in emphasis['primary_values']:
                emphasis['primary_values'].append(val)
                if len(emphasis['primary_values']) >= 3:
                    break
    
    # Get goal connections
    try:
        from apps.user.models import UserGoal
        
        # Get user's top goals
        goals = UserGoal.objects.filter(
            user_profile_id=user_profile_id
        ).order_by('-importance_according_user')[:3]
        
        # Add to emphasis
        for goal in goals:
            emphasis['goal_connections'].append({
                "goal_id": goal.id,
                "goal_title": goal.title,
                "importance": goal.importance_according_user
            })
    except Exception as e:
        logger.warning(f"Error getting user goals: {str(e)}")
    
    # Set beneficiary focus (could be more sophisticated)
    emphasis['beneficiary_focus'] = "self"  # Default to self-focused
    
    return emphasis


def generate_strategy_rationale(domain_distribution, challenge_calibration, selection_constraints, value_emphasis, trust_phase, strategy_focus):
    """Helper function to generate comprehensive strategy rationale"""
    
    # Initialize rationale
    rationale = {
        "strategic_approach": "",
        "domain_rationale": {},
        "challenge_rationale": "",
        "constraint_rationale": "",
        "value_rationale": ""
    }
    
    # Generate strategic approach explanation
    if strategy_focus == 'growth':
        rationale['strategic_approach'] = (
            f"Growth-focused strategy in {trust_phase} phase prioritizes developmental opportunities "
            f"while maintaining appropriate safety guardrails. The emphasis is on activities that expand "
            f"comfort zones in manageable increments."
        )
    elif strategy_focus == 'engagement':
        rationale['strategic_approach'] = (
            f"Engagement-focused strategy in {trust_phase} phase prioritizes activities that build "
            f"connection and enjoyment. The emphasis is on creating positive experiences that strengthen "
            f"trust and participation."
        )
    else:  # balanced
        rationale['strategic_approach'] = (
            f"Balanced strategy in {trust_phase} phase seeks equilibrium between growth and engagement. "
            f"Activities are selected to provide both enjoyable experiences and appropriate developmental challenges."
        )
    
    # Generate domain rationale
    # Sort domains by weight
    sorted_domains = sorted(domain_distribution.items(), key=lambda x: x[1], reverse=True)
    
    # Explain top 3 domains
    for domain, weight in sorted_domains[:3]:
        if weight >= 20:
            rationale['domain_rationale'][domain] = f"High emphasis ({weight}%) due to strong alignment with user profile and strategy focus"
        elif weight >= 15:
            rationale['domain_rationale'][domain] = f"Moderate emphasis ({weight}%) to provide balanced exposure and development"
        else:
            rationale['domain_rationale'][domain] = f"Included at {weight}% to maintain variety and provide occasional exposure"
    
    # Generate challenge rationale
    base_challenge = challenge_calibration.get('base_challenge', 50)
    trust_modifier = challenge_calibration.get('trust_modifier', 0)
    mood_modifier = challenge_calibration.get('mood_modifier', 0)
    
    rationale['challenge_rationale'] = (
        f"Base challenge level of {base_challenge} recommended with a trust phase modifier of {trust_modifier} "
        f"and mood-based adjustment of {mood_modifier}. This yields effective base challenge targets between "
        f"{base_challenge + trust_modifier + mood_modifier - 10} and {base_challenge + trust_modifier + mood_modifier + 10}, "
        f"with specific personality dimension adjustments applied as indicated."
    )
    
    # Generate constraint rationale
    safety_boundaries = selection_constraints.get('safety_boundaries', {})
    exclusion_criteria = selection_constraints.get('exclusion_criteria', [])
    
    if safety_boundaries or exclusion_criteria:
        constraints_text = "Constraints established for psychological safety include: "
        
        if safety_boundaries:
            boundaries_text = ", ".join([f"{category} ({severity})" for category, severity in safety_boundaries.items()])
            constraints_text += f"targeted safety boundaries for {boundaries_text}"
            
            if exclusion_criteria:
                constraints_text += " and "
        
        if exclusion_criteria:
            criteria_text = ", ".join([f"'{criterion}'" for criterion in exclusion_criteria])
            constraints_text += f"complete exclusion of activities matching {criteria_text}"
        
        constraints_text += "."
        rationale['constraint_rationale'] = constraints_text
    else:
        rationale['constraint_rationale'] = "No significant constraints required beyond standard trust phase considerations."
    
    # Generate value rationale
    primary_values = value_emphasis.get('primary_values', [])
    framing_approach = value_emphasis.get('framing_approach', '')
    
    if primary_values:
        values_text = ", ".join(primary_values)
        rationale['value_rationale'] = (
            f"Value emphasis will focus on {values_text} using a {framing_approach} framing approach. "
            f"Activity explanations should highlight these core values and connect explicitly to user goals."
        )
    else:
        rationale['value_rationale'] = (
            f"Standard value framing using {framing_approach} approach recommended without specific value emphasis."
        )
    
    return rationale

def generate_domain_distribution(engagement_patterns, psychological_assessment, trait_gap_analysis, strategy_focus):
    """Helper function to generate domain distribution"""
    
    # This is a simplified implementation - in a real system, 
    # this would be much more sophisticated with real data analysis
    
    # Initialize with default distribution
    distribution = {
        "physical": 15,
        "social": 15,
        "creative": 15,
        "intellectual": 15,
        "reflective": 15,
        "emotional": 15,
        "exploratory": 10
    }
    
    # Adjust based on engagement patterns
    if engagement_patterns:
        domain_preferences = engagement_patterns.get('domain_preferences', {})
        
        # Increase weight for highly engaged domains
        for domain, metrics in domain_preferences.items():
            if domain in distribution:
                engagement_level = metrics.get('engagement_level', 0)
                if engagement_level > 70:
                    # Highly engaged domains get more weight
                    distribution[domain] = distribution.get(domain, 0) + 10
                elif engagement_level < 30:
                    # Low engagement domains get less weight
                    distribution[domain] = max(5, distribution.get(domain, 0) - 10)
    
    # Adjust based on psychological assessment
    if psychological_assessment:
        current_mood = psychological_assessment.get('current_mood', {})
        
        # If mood is low, increase reflective and emotional
        mood_height = current_mood.get('height', 50)
        if mood_height < 40:
            distribution['reflective'] = distribution.get('reflective', 0) + 10
            distribution['emotional'] = distribution.get('emotional', 0) + 5
            distribution['social'] = max(5, distribution.get('social', 0) - 5)
        elif mood_height > 70:
            # If mood is high, increase social and exploratory
            distribution['social'] = distribution.get('social', 0) + 5
            distribution['exploratory'] = distribution.get('exploratory', 0) + 5
    
    # Adjust based on trait gap analysis
    if trait_gap_analysis:
        strategy_implications = trait_gap_analysis.get('strategy_implications', {})
        domain_recommendations = strategy_implications.get('domain_recommendations', [])
        
        for recommendation in domain_recommendations:
            domain = recommendation.get('domain')
            approach = recommendation.get('approach')
            
            if domain in distribution:
                if approach == 'leverage_strength':
                    # Increase weight for strength domains
                    distribution[domain] = distribution.get(domain, 0) + 5
                elif approach == 'careful_scaffolding':
                    # Moderate weight for gap domains if in expansion phase
                    if strategy_focus == 'growth':
                        # Keep same weight for growth focus
                        pass
                    else:
                        # Reduce weight for other focuses
                        distribution[domain] = max(5, distribution.get(domain, 0) - 5)
    
    # Adjust based on strategy focus
    if strategy_focus == 'growth':
        # For growth focus, increase weight for reflective and challenging domains
        distribution['reflective'] = distribution.get('reflective', 0) + 5
        distribution['intellectual'] = distribution.get('intellectual', 0) + 5
    elif strategy_focus == 'engagement':
        # For engagement focus, increase weight for social and creative domains
        distribution['social'] = distribution.get('social', 0) + 10
        distribution['creative'] = distribution.get('creative', 0) + 5
        distribution['exploratory'] = distribution.get('exploratory', 0) + 5
    
    # Ensure minimum values
    for domain in distribution:
        distribution[domain] = max(5, distribution[domain])
    
    # Normalize to ensure total of 100%
    total = sum(distribution.values())
    normalized_distribution = {domain: (weight / total) * 100 for domain, weight in distribution.items()}
    
    # Round to whole numbers
    final_distribution = {domain: round(weight) for domain, weight in normalized_distribution.items()}
    
    # Ensure we still sum to 100% after rounding
    total = sum(final_distribution.values())
    if total != 100:
        # Adjust the largest value to make total exactly 100
        largest_domain = max(final_distribution, key=final_distribution.get)
        final_distribution[largest_domain] += (100 - total)
    
    return final_distribution

def calculate_challenge_calibration(trait_gap_analysis, psychological_assessment, trust_phase):
    """Helper function to calculate challenge calibration"""
    
    # Initialize default calibration
    calibration = {
        "base_challenge": 50,
        "dimension_adjustments": {
            "OPEN": 0,
            "CONS": 0,
            "EXTR": 0,
            "AGRE": 0,
            "EMO": 0,
            "HONHUM": 0
        },
        "trust_modifier": 0,
        "mood_modifier": 0
    }
    
    # Apply trait gap recommendations if available
    if trait_gap_analysis:
        recommendations = trait_gap_analysis.get('challenge_recommendations', {})
        base_challenge = recommendations.get('base_challenge')
        
        if base_challenge:
            calibration['base_challenge'] = base_challenge
        
        dimension_adjustments = recommendations.get('dimension_adjustments', {})
        for dimension, details in dimension_adjustments.items():
            if dimension in calibration['dimension_adjustments']:
                calibration['dimension_adjustments'][dimension] = details.get('adjustment', 0)
    
    # Apply trust phase adjustment
    if trust_phase == 'foundation':
        # Foundation phase has lower base challenge
        calibration['trust_modifier'] = -10
    else:  # expansion phase
        # Expansion phase can have higher challenge
        calibration['trust_modifier'] = 10
    
    # Apply psychological assessment adjustment
    if psychological_assessment:
        current_mood = psychological_assessment.get('current_mood', {})
        mood_height = current_mood.get('height', 50)
        
        # Adjust challenge based on mood
        if mood_height < 30:
            # Very low mood - reduce challenge
            calibration['mood_modifier'] = -15
        elif mood_height < 50:
            # Somewhat low mood - slightly reduce challenge
            calibration['mood_modifier'] = -5
        elif mood_height > 80:
            # Very high mood - can increase challenge
            calibration['mood_modifier'] = 10
        elif mood_height > 60:
            # Somewhat high mood - slightly increase challenge
            calibration['mood_modifier'] = 5
        else:
            # Neutral mood - no adjustment
            calibration['mood_modifier'] = 0

from apps.main.agents.tools.tools_util import register_tool
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

@register_tool('analyze_environment_compatibility')
async def analyze_environment_compatibility(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyzes the compatibility between user's environment and activities for resource-aware recommendations.
    
    Input:
        user_profile_id: UUID of the user profile
        environment_id: UUID of the specific environment to analyze (optional, uses current if not provided)
        activity_id: UUID of a specific activity to analyze compatibility for (optional)
        include_domain_analysis: Whether to include detailed domain support analysis (default: True)
        include_resource_analysis: Whether to include resource availability analysis (default: True)
        
    Output:
        analysis: Dictionary with environment compatibility analysis
            current_environment: Information about the analyzed environment
            domain_support: Ratings for how well the environment supports different activity domains
            resource_availability: Analysis of resources available in this environment
            activity_compatibility: Compatibility ratings for specific activities (if requested)
            environmental_constraints: Constraints that should inform activity selection
            recommended_adaptations: Suggested adaptations to improve compatibility
    """
    # Extract required parameters
    user_profile_id = input_data.get('user_profile_id')
    environment_id = input_data.get('environment_id')
    activity_id = input_data.get('activity_id')
    include_domain_analysis = input_data.get('include_domain_analysis', True)
    include_resource_analysis = input_data.get('include_resource_analysis', True)
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, UserEnvironment, GenericEnvironment
        from apps.utils.activity_compatibility import ActivityEnvironmentCompatibility
        from apps.activity.models import GenericDomain, ActivityTailored, GenericActivity
        
        # Get the user profile
        user_profile = UserProfile.objects.get(id=user_profile_id)
        
        # Get the environment to analyze
        if environment_id:
            try:
                environment = UserEnvironment.objects.get(id=environment_id, user_profile=user_profile)
            except UserEnvironment.DoesNotExist:
                return {"error": f"Environment with ID {environment_id} not found for this user"}
        else:
            # Use current environment
            environment = user_profile.current_environment
            if not environment:
                return {"error": "User has no current environment set and no environment_id was provided"}
        
        # Initialize analysis result
        analysis = {
            "current_environment": {
                "id": environment.id,
                "name": environment.environment_name,
                "description": environment.environment_description,
                "generic_type": environment.generic_environment.name if environment.generic_environment else "Custom"
            }
        }
        
        # Perform domain analysis if requested
        if include_domain_analysis:
            domain_support = analyze_domain_support(environment)
            analysis["domain_support"] = domain_support
        
        # Perform resource analysis if requested
        if include_resource_analysis:
            resource_availability = analyze_resource_availability(environment)
            analysis["resource_availability"] = resource_availability
        
        # Analyze specific activity compatibility if requested
        if activity_id:
            try:
                # Try to get as tailored activity first
                try:
                    activity = ActivityTailored.objects.get(id=activity_id)
                except ActivityTailored.DoesNotExist:
                    # Try as generic activity
                    activity = GenericActivity.objects.get(id=activity_id)
                
                activity_compatibility = analyze_activity_compatibility(environment, activity)
                analysis["activity_compatibility"] = activity_compatibility
            except (ActivityTailored.DoesNotExist, GenericActivity.DoesNotExist):
                return {"error": f"Activity with ID {activity_id} not found"}
        
        # Generate environmental constraints
        constraints = generate_environmental_constraints(
            environment, 
            domain_support if include_domain_analysis else None,
            resource_availability if include_resource_analysis else None
        )
        analysis["environmental_constraints"] = constraints
        
        # Generate recommended adaptations
        adaptations = generate_recommended_adaptations(constraints)
        analysis["recommended_adaptations"] = adaptations
        
        return {"analysis": analysis}
    
    except UserProfile.DoesNotExist:
        return {"error": f"User profile with ID {user_profile_id} not found"}
    except Exception as e:
        logger.exception("Error analyzing environment compatibility")
        return {"error": str(e)}

def analyze_domain_support(environment):
    """Helper function to analyze domain support for an environment"""
    
    # Initialize result
    domain_support = {
        "overall_rating": 0,
        "domain_ratings": {},
        "primary_supported_domains": [],
        "limited_support_domains": []
    }
    
    # Get the domain support summary from the environment
    summary = environment.get_domain_support_summary()
    
    # Process each domain
    total_rating = 0
    count = 0
    
    for domain_code, details in summary.items():
        rating = details.get('rating', 0)
        domain_name = details.get('name', domain_code)
        is_override = details.get('is_override', False)
        
        # Add to domain ratings
        domain_support["domain_ratings"][domain_code] = {
            "name": domain_name,
            "rating": rating,
            "is_override": is_override,
            "support_level": get_support_level_text(rating)
        }
        
        # Update overall rating calculation
        total_rating += rating
        count += 1
        
        # Categorize by support level
        if rating >= 70:
            domain_support["primary_supported_domains"].append({
                "code": domain_code,
                "name": domain_name,
                "rating": rating
            })
        elif rating <= 30:
            domain_support["limited_support_domains"].append({
                "code": domain_code,
                "name": domain_name,
                "rating": rating
            })
    
    # Calculate overall rating
    if count > 0:
        domain_support["overall_rating"] = total_rating / count
    
    # Sort the domain lists by rating
    domain_support["primary_supported_domains"] = sorted(
        domain_support["primary_supported_domains"], 
        key=lambda x: x["rating"], 
        reverse=True
    )
    
    domain_support["limited_support_domains"] = sorted(
        domain_support["limited_support_domains"], 
        key=lambda x: x["rating"]
    )
    
    return domain_support

def get_support_level_text(rating):
    """Helper function to convert numeric rating to text description"""
    if rating >= 80:
        return "excellent"
    elif rating >= 60:
        return "good"
    elif rating >= 40:
        return "moderate"
    elif rating >= 20:
        return "limited"
    elif rating >= 0:
        return "minimal"
    elif rating >= -20:
        return "slightly_discouraged"
    elif rating >= -40:
        return "moderately_discouraged"
    elif rating >= -60:
        return "strongly_discouraged"
    else:
        return "extremely_discouraged"

def analyze_resource_availability(environment):
    """Helper function to analyze resource availability in an environment"""
    
    # Initialize result
    resource_availability = {
        "available_resources": [],
        "missing_resources": [],
        "limited_resources": [],
        "time_availability": {}
    }
    
    # Get resources associated with this environment
    user_resources = environment.user_resources.all()
    
    # Process resources
    for resource in user_resources:
        resource_info = {
            "id": resource.id,
            "name": resource.specific_name,
            "type": resource.generic_resource.resource_type if resource.generic_resource else "custom",
            "location": resource.location_details
        }
        
        # For simplicity, we'll consider all resources as available
        # In a real implementation, you'd have more sophisticated availability logic
        resource_availability["available_resources"].append(resource_info)
    
    # Get time availability from activity support if available
    if hasattr(environment, 'activity_support'):
        time_avail = environment.activity_support.time_availability
        resource_availability["time_availability"] = time_avail
    else:
        # Default time availability
        resource_availability["time_availability"] = {
            "weekdays": ["evening"],
            "weekends": ["all_day"]
        }
    
    # Add basic environment properties as "resources"
    if hasattr(environment, 'physical_properties'):
        props = environment.physical_properties
        
        resource_availability["environment_properties"] = {
            "space_size": props.space_size,
            "noise_level": props.noise_level,
            "light_quality": props.light_quality,
            "privacy": environment.social_context.privacy_level if hasattr(environment, 'social_context') else 50
        }
    
    return resource_availability

def analyze_activity_compatibility(environment, activity):
    """Helper function to analyze compatibility between an activity and environment"""
    
    # Initialize result
    compatibility = {
        "activity_id": activity.id,
        "activity_name": activity.name,
        "overall_score": 0,
        "domain_compatibility": [],
        "resource_compatibility": {},
        "adaptations_required": []
    }
    
    # Use the utility class for compatibility calculations
    from apps.utils.activity_compatibility import ActivityEnvironmentCompatibility
    
    # Calculate overall match score
    match_score = ActivityEnvironmentCompatibility.calculate_environment_activity_match_score(
        activity, environment
    )
    compatibility["overall_score"] = match_score
    
    # Assess compatibility level
    if match_score >= 80:
        compatibility["compatibility_level"] = "excellent"
    elif match_score >= 60:
        compatibility["compatibility_level"] = "good"
    elif match_score >= 40:
        compatibility["compatibility_level"] = "moderate"
    elif match_score >= 20:
        compatibility["compatibility_level"] = "limited"
    else:
        compatibility["compatibility_level"] = "poor"
    
    # Analyze domain compatibility
    if hasattr(activity, 'domain_relationships'):
        for rel in activity.domain_relationships.all():
            domain = rel.domain
            support_rating = environment.get_domain_support_rating(domain_id=domain.id)
            
            domain_compat = {
                "domain_id": domain.id,
                "domain_name": domain.name,
                "activity_relevance": rel.strength,
                "environment_support": support_rating,
                "compatibility": min(100, max(0, support_rating + 50))  # Normalize to 0-100
            }
            
            compatibility["domain_compatibility"].append(domain_compat)
        
        # Sort by compatibility
        compatibility["domain_compatibility"] = sorted(
            compatibility["domain_compatibility"],
            key=lambda x: x["compatibility"],
            reverse=True
        )
    
    # Analyze resource compatibility
    # This would check activity resource requirements against environment resources
    # For simplicity, we'll provide a placeholder
    compatibility["resource_compatibility"] = {
        "sufficient_resources": True,
        "missing_resources": []
    }
    
    # Determine if adaptations are required
    if match_score < 40:
        compatibility["adaptations_required"].append({
            "type": "environment_change",
            "description": "Consider a more suitable environment for this activity"
        })
    
    if match_score < 60:
        compatibility["adaptations_required"].append({
            "type": "activity_modification",
            "description": "Modify activity instructions to better fit current environment"
        })
    
    return compatibility

def generate_environmental_constraints(environment, domain_support=None, resource_availability=None):
    """Helper function to generate environmental constraints"""
    
    # Initialize constraints
    constraints = {
        "domain_constraints": [],
        "resource_constraints": [],
        "time_constraints": {},
        "physical_constraints": []
    }
    
    # Add domain constraints based on domain support
    if domain_support:
        # Find domains with poor support
        for domain_code, details in domain_support.get("domain_ratings", {}).items():
            rating = details.get("rating", 0)
            if rating < 30:
                constraints["domain_constraints"].append({
                    "domain_code": domain_code,
                    "domain_name": details.get("name", domain_code),
                    "constraint_type": "avoid" if rating < 0 else "minimize",
                    "reason": f"Limited environment support ({rating})"
                })
    
    # Add resource constraints
    if resource_availability:
        # Gather missing essential resources
        for resource in resource_availability.get("missing_resources", []):
            constraints["resource_constraints"].append({
                "resource_type": resource.get("type", "unknown"),
                "constraint_type": "require_alternative",
                "reason": "Essential resource unavailable"
            })
        
        # Add time constraints
        time_avail = resource_availability.get("time_availability", {})
        
        # Convert time availability to constraints
        weekday_avail = time_avail.get("weekdays", [])
        weekend_avail = time_avail.get("weekends", [])
        
        constraints["time_constraints"] = {
            "max_duration": 60,  # Default 60 minutes
            "preferred_time_slots": {
                "weekdays": weekday_avail,
                "weekends": weekend_avail
            }
        }
        
        # Add physical environment constraints
        env_props = resource_availability.get("environment_properties", {})
        
        if env_props.get("noise_level", 50) > 70:
            constraints["physical_constraints"].append({
                "constraint_type": "noise_sensitive",
                "reason": "High ambient noise level"
            })
        
        if env_props.get("space_size") == "tiny" or env_props.get("space_size") == "small":
            constraints["physical_constraints"].append({
                "constraint_type": "space_limited",
                "reason": f"Limited space size: {env_props.get('space_size', 'unknown')}"
            })
    
    return constraints

def generate_recommended_adaptations(constraints):
    """Helper function to generate recommended adaptations based on constraints"""
    
    # Initialize adaptations
    adaptations = []
    
    # Process domain constraints
    for constraint in constraints.get("domain_constraints", []):
        if constraint.get("constraint_type") == "avoid":
            adaptations.append({
                "type": "domain_adaptation",
                "domain": constraint.get("domain_code"),
                "recommendation": f"Select activities from domains other than {constraint.get('domain_name')}",
                "priority": "high"
            })
        elif constraint.get("constraint_type") == "minimize":
            adaptations.append({
                "type": "domain_adaptation",
                "domain": constraint.get("domain_code"),
                "recommendation": f"Limit activities from {constraint.get('domain_name')} or modify to reduce domain emphasis",
                "priority": "medium"
            })
    
    # Process resource constraints
    for constraint in constraints.get("resource_constraints", []):
        adaptations.append({
            "type": "resource_adaptation",
            "resource_type": constraint.get("resource_type"),
            "recommendation": f"Ensure activities provide alternatives to {constraint.get('resource_type')} or include substitutions",
            "priority": "high"
        })
    
    # Process time constraints
    time_constraints = constraints.get("time_constraints", {})
    max_duration = time_constraints.get("max_duration")
    if max_duration:
        adaptations.append({
            "type": "time_adaptation",
            "recommendation": f"Select activities with duration under {max_duration} minutes or that can be split into sessions",
            "priority": "medium"
        })
    
    # Process physical constraints
    for constraint in constraints.get("physical_constraints", []):
        if constraint.get("constraint_type") == "noise_sensitive":
            adaptations.append({
                "type": "physical_adaptation",
                "recommendation": "Select activities with low noise requirements or modify to reduce noise",
                "priority": "medium"
            })
        elif constraint.get("constraint_type") == "space_limited":
            adaptations.append({
                "type": "physical_adaptation",
                "recommendation": "Select activities with minimal space requirements or modify to work in confined spaces",
                "priority": "high"
            })
    
    return adaptations
