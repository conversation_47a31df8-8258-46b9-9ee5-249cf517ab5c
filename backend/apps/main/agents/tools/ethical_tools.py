"""
Ethical Tools

This module contains tools for ethical validation, safety assessment, and compliance checking.
"""

import logging
from typing import Dict, Any, List
from .tools_util import register_tool

logger = logging.getLogger(__name__)


@register_tool('validate_activity_ethics')
async def validate_activity_ethics(user_profile_id: str, activity: Dict[str, Any],
                                 psychological_assessment: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Validates an individual activity for ethical alignment and safety.
    
    Args:
        user_profile_id: ID of the user profile
        activity: Activity data to validate
        psychological_assessment: User's psychological assessment
        
    Returns:
        Dict containing validation results and recommendations
    """
    try:
        logger.info(f"Validating activity ethics for user {user_profile_id}")
        
        # Handle benchmark and debugger user IDs
        if isinstance(user_profile_id, str) and (
            user_profile_id.startswith('benchmark-user-') or
            user_profile_id.startswith('debugger-user-')
        ):
            return _validate_activity_default(activity, psychological_assessment)
        
        # For real users, perform comprehensive validation
        validation_result = await _validate_activity_comprehensive(
            user_profile_id, activity, psychological_assessment
        )
        
        return validation_result
        
    except Exception as e:
        logger.exception("Error validating activity ethics")
        return {"error": str(e), "status": "Not Approved"}


def _validate_activity_default(activity: Dict[str, Any], 
                             psychological_assessment: Dict[str, Any] = None) -> Dict[str, Any]:
    """Default activity validation for benchmark users."""
    
    activity_title = activity.get("title", "Unknown Activity")
    domain = activity.get("domain", "general")
    difficulty_level = activity.get("difficulty_level", 2)
    
    # Extract trust phase for validation
    trust_phase = "Foundation"
    if psychological_assessment:
        trust_phase_info = psychological_assessment.get("trust_phase", {})
        trust_phase = trust_phase_info.get("phase", "Foundation")
    
    # Basic ethical validation
    concerns = []
    principles_assessed = ["benevolence", "fairness", "transparency", "autonomy"]
    
    # Check difficulty appropriateness for trust phase
    if trust_phase == "Foundation" and difficulty_level > 3:
        concerns.append("Activity difficulty may be too high for Foundation phase user")
    
    # Check for potentially triggering content
    triggering_keywords = ["extreme", "intense", "challenging", "difficult", "stressful"]
    activity_text = f"{activity_title} {activity.get('description', '')}".lower()
    
    for keyword in triggering_keywords:
        if keyword in activity_text and trust_phase == "Foundation":
            concerns.append(f"Activity contains potentially overwhelming language: '{keyword}'")
    
    # Determine status
    status = "Approved" if not concerns else "Conditional"
    
    return {
        "status": status,
        "concerns": concerns,
        "principles_assessed": principles_assessed,
        "confidence": 0.8,
        "trust_phase_consideration": f"Validated for {trust_phase} phase user",
        "recommendations": _generate_activity_recommendations(concerns, trust_phase)
    }


async def _validate_activity_comprehensive(user_profile_id: str, activity: Dict[str, Any],
                                         psychological_assessment: Dict[str, Any] = None) -> Dict[str, Any]:
    """Comprehensive activity validation for real users."""
    
    # For now, use default validation with enhanced analysis
    # This could be enhanced with user history analysis and ML models
    default_result = _validate_activity_default(activity, psychological_assessment)
    
    # Add user-specific considerations
    try:
        user_id = int(user_profile_id)
        # Could query user's past activity feedback, preferences, limitations here
        # For now, return enhanced default validation
        default_result["validation_method"] = "comprehensive"
        return default_result
    except (ValueError, TypeError):
        return default_result


def _generate_activity_recommendations(concerns: List[str], trust_phase: str) -> List[str]:
    """Generate recommendations based on concerns and trust phase."""
    
    recommendations = []
    
    if concerns:
        if trust_phase == "Foundation":
            recommendations.extend([
                "Consider simplifying activity instructions",
                "Reduce time commitment if possible",
                "Add more supportive language and encouragement",
                "Provide clear exit strategies if user feels overwhelmed"
            ])
        else:
            recommendations.extend([
                "Review activity complexity for user readiness",
                "Ensure clear instructions and expectations",
                "Consider gradual progression approach"
            ])
    else:
        recommendations.append("Activity meets ethical standards for current user context")
    
    return recommendations


@register_tool('validate_wheel_ethics')
async def validate_wheel_ethics(user_profile_id: str, wheel: Dict[str, Any],
                              psychological_assessment: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Validates the overall wheel composition for ethical alignment.
    
    Args:
        user_profile_id: ID of the user profile
        wheel: Complete wheel data to validate
        psychological_assessment: User's psychological assessment
        
    Returns:
        Dict containing wheel validation results
    """
    try:
        logger.info(f"Validating wheel ethics for user {user_profile_id}")
        
        # Handle benchmark user IDs
        if isinstance(user_profile_id, str) and user_profile_id.startswith('benchmark-user-'):
            return _validate_wheel_default(wheel, psychological_assessment)
        
        # For real users, perform comprehensive validation
        validation_result = await _validate_wheel_comprehensive(
            user_profile_id, wheel, psychological_assessment
        )
        
        return validation_result
        
    except Exception as e:
        logger.exception("Error validating wheel ethics")
        return {"error": str(e), "status": "Not Approved"}


def _validate_wheel_default(wheel: Dict[str, Any], 
                          psychological_assessment: Dict[str, Any] = None) -> Dict[str, Any]:
    """Default wheel validation for benchmark users."""
    
    activities = wheel.get("activities", [])
    wheel_items = wheel.get("items", [])
    
    # Extract trust phase
    trust_phase = "Foundation"
    if psychological_assessment:
        trust_phase_info = psychological_assessment.get("trust_phase", {})
        trust_phase = trust_phase_info.get("phase", "Foundation")
    
    concerns = []
    principles_assessed = ["benevolence", "fairness", "transparency", "autonomy"]
    
    # Check domain balance
    domains = [activity.get("domain", "general") for activity in activities]
    domain_counts = {}
    for domain in domains:
        domain_counts[domain] = domain_counts.get(domain, 0) + 1
    
    # Ensure no single domain dominates (more than 60% of activities)
    total_activities = len(activities)
    if total_activities > 0:
        for domain, count in domain_counts.items():
            if count / total_activities > 0.6:
                concerns.append(f"Domain '{domain}' dominates wheel composition ({count}/{total_activities} activities)")
    
    # Check difficulty distribution for trust phase
    if trust_phase == "Foundation":
        high_difficulty_count = sum(1 for activity in activities 
                                  if activity.get("difficulty_level", 2) > 3)
        if high_difficulty_count > total_activities * 0.3:
            concerns.append("Too many high-difficulty activities for Foundation phase user")
    
    # Check activity count appropriateness
    if total_activities < 3:
        concerns.append("Insufficient activity variety (minimum 3 recommended)")
    elif total_activities > 8:
        concerns.append("Too many activities may overwhelm user (maximum 8 recommended)")
    
    # Determine status
    status = "Approved" if not concerns else "Conditional"
    domain_balance = "Appropriate" if len(concerns) == 0 else "Needs Adjustment"
    challenge_calibration = "Appropriate" if trust_phase != "Foundation" or not any("difficulty" in c for c in concerns) else "Too High"
    
    return {
        "status": status,
        "domain_balance": domain_balance,
        "challenge_calibration": challenge_calibration,
        "concerns": concerns,
        "principles_assessed": principles_assessed,
        "confidence": 0.8,
        "domain_distribution": domain_counts,
        "total_activities": total_activities,
        "trust_phase_consideration": f"Validated for {trust_phase} phase user"
    }


async def _validate_wheel_comprehensive(user_profile_id: str, wheel: Dict[str, Any],
                                      psychological_assessment: Dict[str, Any] = None) -> Dict[str, Any]:
    """Comprehensive wheel validation for real users."""
    
    # For now, use default validation with enhanced analysis
    default_result = _validate_wheel_default(wheel, psychological_assessment)
    
    # Add user-specific considerations
    try:
        user_id = int(user_profile_id)
        # Could query user's preferences, past wheel feedback, success patterns here
        default_result["validation_method"] = "comprehensive"
        return default_result
    except (ValueError, TypeError):
        return default_result


@register_tool('identify_safety_considerations')
async def identify_safety_considerations(user_profile_id: str, activities: List[Dict[str, Any]],
                                       psychological_assessment: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Identifies safety considerations and potential risks for the user.
    
    Args:
        user_profile_id: ID of the user profile
        activities: List of activities to assess
        psychological_assessment: User's psychological assessment
        
    Returns:
        Dict containing safety considerations and recommendations
    """
    try:
        logger.info(f"Identifying safety considerations for user {user_profile_id}")
        
        # Handle benchmark user IDs
        if isinstance(user_profile_id, str) and user_profile_id.startswith('benchmark-user-'):
            return _identify_safety_default(activities, psychological_assessment)
        
        # For real users, perform comprehensive safety assessment
        safety_result = await _identify_safety_comprehensive(
            user_profile_id, activities, psychological_assessment
        )
        
        return safety_result
        
    except Exception as e:
        logger.exception("Error identifying safety considerations")
        return {"error": str(e), "vulnerability_areas": []}


def _identify_safety_default(activities: List[Dict[str, Any]], 
                           psychological_assessment: Dict[str, Any] = None) -> Dict[str, Any]:
    """Default safety assessment for benchmark users."""
    
    # Extract trust phase and psychological context
    trust_phase = "Foundation"
    vulnerability_areas = []
    trigger_warnings = []
    supervision_recommendations = []
    
    if psychological_assessment:
        trust_phase_info = psychological_assessment.get("trust_phase", {})
        trust_phase = trust_phase_info.get("phase", "Foundation")
        
        # Check for specific vulnerabilities mentioned in assessment
        if "anxiety" in str(psychological_assessment).lower():
            vulnerability_areas.append("anxiety_sensitivity")
        if "stress" in str(psychological_assessment).lower():
            vulnerability_areas.append("stress_management")
    
    # Analyze activities for potential safety concerns
    for activity in activities:
        domain = activity.get("domain", "general")
        difficulty = activity.get("difficulty_level", 2)
        title = activity.get("title", "").lower()
        description = activity.get("description", "").lower()
        
        # Check for physical activities that might need supervision
        if domain == "physical" and difficulty > 2:
            supervision_recommendations.append(f"Consider supervision for physical activity: {activity.get('title', 'Unknown')}")
        
        # Check for potentially triggering content
        triggering_words = ["intense", "challenging", "difficult", "pressure", "performance"]
        activity_text = f"{title} {description}"
        
        for word in triggering_words:
            if word in activity_text and trust_phase == "Foundation":
                trigger_warnings.append(f"Activity '{activity.get('title', 'Unknown')}' contains potentially triggering language")
    
    # Define challenge boundaries based on trust phase
    challenge_boundaries = {
        "max_difficulty_level": 3 if trust_phase == "Foundation" else 5,
        "max_duration_minutes": 30 if trust_phase == "Foundation" else 60,
        "recommended_supervision": trust_phase == "Foundation",
        "exit_strategy_required": True
    }
    
    return {
        "vulnerability_areas": vulnerability_areas,
        "challenge_boundaries": challenge_boundaries,
        "trigger_warnings": trigger_warnings,
        "supervision_recommendations": supervision_recommendations,
        "confidence": 0.8,
        "trust_phase_consideration": f"Safety assessment for {trust_phase} phase user",
        "general_recommendations": [
            "Ensure user has clear exit strategies for all activities",
            "Encourage user to stop if feeling overwhelmed",
            "Provide supportive contact information if needed",
            f"Activities calibrated for {trust_phase} phase comfort level"
        ]
    }


async def _identify_safety_comprehensive(user_profile_id: str, activities: List[Dict[str, Any]],
                                       psychological_assessment: Dict[str, Any] = None) -> Dict[str, Any]:
    """Comprehensive safety assessment for real users."""
    
    # For now, use default assessment with enhanced analysis
    default_result = _identify_safety_default(activities, psychological_assessment)
    
    # Add user-specific safety considerations
    try:
        user_id = int(user_profile_id)
        # Could query user's medical history, past safety incidents, preferences here
        default_result["assessment_method"] = "comprehensive"
        return default_result
    except (ValueError, TypeError):
        return default_result
