# backend/apps/main/agents/tools/profile_extraction_tools.py

from apps.main.agents.tools.tools_util import register_tool
import logging
from typing import Dict, Any, List
from datetime import date, timedelta

logger = logging.getLogger(__name__)


@register_tool('extract_demographics_from_text')
async def extract_demographics_from_text(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract demographic information from user text using LLM-based analysis.
    
    Phase 3 Enhancement: Pure data extraction tool for profile completion workflow.
    Analyzes user text to identify demographic information like age, location, occupation.
    
    Input:
        text: User text to analyze for demographic information
        
    Output:
        demographics: Dictionary containing extracted demographic data
    """
    try:
        text = input_data.get('text', '')
        if not text:
            return {"demographics": {}}
        
        # Use pattern-based extraction for now (can be enhanced with LLM later)
        import re
        demographics = {}
        
        # Extract age
        age_patterns = [
            r'\b(?:i am|i\'m|age)\s*(\d{1,2})\b',
            r'\b(\d{1,2})\s*(?:years?\s*old|year-old)\b',
            r'\b(\d{1,2})-year-old\b'
        ]
        
        for pattern in age_patterns:
            age_match = re.search(pattern, text.lower())
            if age_match:
                age = int(age_match.group(1))
                if 13 <= age <= 100:  # Reasonable age range
                    demographics["age"] = age
                    break
        
        # Extract occupation
        if any(word in text.lower() for word in ['student', 'studying', 'school', 'university', 'college', 'exam', 'exams']):
            demographics["occupation"] = "Student"
        elif any(word in text.lower() for word in ['work', 'job', 'career', 'employed', 'working']):
            demographics["occupation"] = "Employed"
        
        # Extract location
        location_patterns = [
            r'\bin\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b',
            r'\bfrom\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b',
            r'\blive\s+in\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b'
        ]
        
        for pattern in location_patterns:
            location_match = re.search(pattern, text)
            if location_match:
                location = location_match.group(1)
                if len(location) > 2:  # Avoid single letters
                    demographics["location"] = location
                    break
        
        # Extract gender (basic patterns)
        if any(word in text.lower() for word in ['female', 'woman', 'girl', 'she', 'her']):
            demographics["gender"] = "Female"
        elif any(word in text.lower() for word in ['male', 'man', 'boy', 'he', 'him']):
            demographics["gender"] = "Male"
        
        logger.info(f"Extracted demographics: {demographics}")
        return {"demographics": demographics}
        
    except Exception as e:
        logger.error(f"Error extracting demographics from text: {str(e)}")
        return {"demographics": {}, "error": str(e)}


@register_tool('extract_goals_from_text')
async def extract_goals_from_text(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract goals and aspirations from user text using pattern analysis.
    
    Phase 3 Enhancement: Pure data extraction tool for profile completion workflow.
    Analyzes user text to identify goals, aspirations, and objectives.
    
    Input:
        text: User text to analyze for goals and aspirations
        
    Output:
        goals: List of extracted goal dictionaries
    """
    try:
        text = input_data.get('text', '')
        if not text:
            return {"goals": []}
        
        goals = []
        
        # Enhanced goal detection patterns
        goal_patterns = [
            ('focus', r'need help focusing|focus|concentration|attention', 'Improve Focus and Concentration', 'Develop better focus and concentration skills for academic success'),
            ('stress', r'manage.*stress|stressed|anxiety|anxious', 'Stress Management', 'Learn effective stress management techniques'),
            ('exam', r'exam|test|academic', 'Academic Success', 'Achieve success in upcoming exams and academic challenges'),
            ('adhd', r'adhd|attention.*deficit', 'ADHD Management', 'Develop strategies to manage ADHD symptoms effectively'),
            ('study', r'study|studying|learning', 'Study Skills', 'Improve study habits and learning techniques'),
            ('time', r'time.*management|organize|schedule', 'Time Management', 'Develop better time management and organizational skills'),
            ('health', r'health|wellness|exercise|fitness', 'Health and Wellness', 'Improve physical and mental health through wellness activities')
        ]
        
        import re
        for goal_type, pattern, title, description in goal_patterns:
            if re.search(pattern, text.lower()):
                goal = {
                    "title": title,
                    "description": description,
                    "importance_according_user": 80,  # Default high importance
                    "importance_according_system": 75,
                    "strength": 70
                }
                goals.append(goal)
        
        # If no specific patterns match, look for general goal indicators
        if not goals:
            goal_indicators = ['want to', 'need to', 'goal', 'achieve', 'improve', 'get better', 'help with']
            if any(indicator in text.lower() for indicator in goal_indicators):
                goal = {
                    "title": "Personal Development",
                    "description": "General personal development and improvement goals",
                    "importance_according_user": 70,
                    "importance_according_system": 65,
                    "strength": 60
                }
                goals.append(goal)
        
        logger.info(f"Extracted {len(goals)} goals from text")
        return {"goals": goals}
        
    except Exception as e:
        logger.error(f"Error extracting goals from text: {str(e)}")
        return {"goals": [], "error": str(e)}


@register_tool('extract_preferences_from_text')
async def extract_preferences_from_text(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract preferences and interests from user text using pattern analysis.
    
    Phase 3 Enhancement: Pure data extraction tool for profile completion workflow.
    Analyzes user text to identify preferences, interests, and activity preferences.
    
    Input:
        text: User text to analyze for preferences and interests
        
    Output:
        preferences: List of extracted preference dictionaries
    """
    try:
        text = input_data.get('text', '')
        if not text:
            return {"preferences": []}
        
        preferences = []
        
        # Enhanced preference detection patterns
        preference_patterns = [
            ('stress_management', r'stressed|stress|anxiety|anxious', 'Stress Relief Activities', 'Prefers activities that help manage stress and anxiety', 85),
            ('focus_activities', r'focus|concentration|attention', 'Focus-Enhancing Activities', 'Prefers activities that improve focus and concentration', 80),
            ('academic_support', r'exam|study|academic|learning', 'Academic Support', 'Prefers activities that support academic success', 75),
            ('adhd_friendly', r'adhd|attention.*deficit', 'ADHD-Friendly Activities', 'Prefers activities suitable for ADHD management', 90),
            ('physical_activity', r'exercise|physical|sport|active|movement', 'Physical Activities', 'Prefers physical and movement-based activities', 70),
            ('creative_activities', r'creative|art|music|draw|write', 'Creative Activities', 'Prefers creative and artistic activities', 75),
            ('social_activities', r'social|friends|group|together', 'Social Activities', 'Prefers social and group-based activities', 65),
            ('quiet_activities', r'quiet|calm|peaceful|alone|solitude', 'Quiet Activities', 'Prefers quiet and solitary activities', 70),
            ('outdoor_activities', r'outdoor|nature|outside|fresh air', 'Outdoor Activities', 'Prefers outdoor and nature-based activities', 75)
        ]
        
        import re
        for pref_type, pattern, name, description, strength in preference_patterns:
            if re.search(pattern, text.lower()):
                preference = {
                    "pref_name": name,
                    "pref_description": description,
                    "pref_strength": strength,
                    "user_awareness": max(60, strength - 10),  # Awareness slightly lower than strength
                    "effective_start": date.today().isoformat(),
                    "duration_estimate": "ongoing",
                    "effective_end": (date.today() + timedelta(days=365)).isoformat()
                }
                preferences.append(preference)
        
        # Fallback: create a general preference if specific patterns don't match
        if not preferences and any(word in text.lower() for word in ['like', 'enjoy', 'prefer', 'love', 'favorite', 'need', 'want']):
            preference = {
                "pref_name": "Positive Engagement",
                "pref_description": "User shows positive engagement patterns in conversation",
                "pref_strength": 70,
                "user_awareness": 60,
                "effective_start": date.today().isoformat(),
                "duration_estimate": "ongoing",
                "effective_end": (date.today() + timedelta(days=365)).isoformat()
            }
            preferences.append(preference)
        
        logger.info(f"Extracted {len(preferences)} preferences from text")
        return {"preferences": preferences}
        
    except Exception as e:
        logger.error(f"Error extracting preferences from text: {str(e)}")
        return {"preferences": [], "error": str(e)}
