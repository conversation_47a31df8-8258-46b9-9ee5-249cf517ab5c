from apps.main.agents.tools.tools_util import register_tool

import logging
from typing import Dict, Any, Optional
from asgiref.sync import sync_to_async
from .FIELD_MAPPINGS_AUTHORITATIVE import validate_model_data

logger = logging.getLogger(__name__)


@register_tool('create_user_demographics')
async def create_user_demographics(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Creates or updates user demographics information during onboarding.
    
    Input:
        user_profile_id: UUID of the user profile
        full_name: User's full name (optional)
        age: User's age (optional)
        gender: User's gender (optional)
        location: User's location (optional)
        language: User's language(s) (optional)
        occupation: User's occupation (optional)
        personal_prefs: Dictionary of personal preferences (optional)
    
    Output:
        success: Boolean indicating if the operation was successful
        demographics_id: ID of the created/updated demographics record
        created: <PERSON>olean indicating if a new record was created
    """
    try:
        from apps.user.models import UserProfile, Demographics
        
        user_profile_id = input_data.get('user_profile_id')
        if not user_profile_id:
            return {"success": False, "error": "user_profile_id is required"}
        
        # Get user profile
        @sync_to_async
        def get_user_profile():
            return UserProfile.objects.get(id=user_profile_id)
        
        user_profile = await get_user_profile()
        
        # Use authoritative field validation to prevent field errors
        try:
            demographics_data = validate_model_data('demographics', input_data)
        except ValueError as ve:
            logger.error(f"Field validation error for demographics: {str(ve)}")
            return {"success": False, "error": f"Field validation error: {str(ve)}"}

        # Ensure age is provided with a sensible default
        if 'age' not in demographics_data or demographics_data['age'] is None:
            demographics_data['age'] = 25  # Default age for onboarding users

        # Ensure age is an integer
        try:
            demographics_data['age'] = int(demographics_data['age'])
        except (ValueError, TypeError):
            demographics_data['age'] = 25  # Default age if conversion fails
        
        # Create or update demographics
        @sync_to_async
        def create_or_update_demographics():
            demographics, created = Demographics.objects.update_or_create(
                user_profile=user_profile,
                defaults=demographics_data
            )
            return demographics, created
        
        demographics, created = await create_or_update_demographics()

        # Log successful creation/update
        action = "Created" if created else "Updated"
        logger.info(f"{action} demographics for user {user_profile_id}: {demographics_data}")

        return {
            "success": True,
            "demographics_id": str(demographics.id),
            "created": created,
            "updated_fields": list(demographics_data.keys()),
            "user_profile_id": str(user_profile_id)
        }

    except Exception as e:
        logger.error(f"Error creating user demographics: {str(e)}", exc_info=True)
        logger.error(f"Input data was: {input_data}")
        logger.error(f"Prepared demographics_data was: {demographics_data}")
        return {
            "success": False,
            "error": str(e)
        }


@register_tool('create_user_goal')
async def create_user_goal(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Creates a user goal during onboarding.
    
    Input:
        user_profile_id: UUID of the user profile
        title: Goal title
        description: Goal description
        importance: Importance level (1-100, default: 70)
        goal_type: Type of goal (aspiration, intention, etc.)
    
    Output:
        success: Boolean indicating if the operation was successful
        goal_id: ID of the created goal
    """
    try:
        from apps.user.models import UserProfile, UserGoal
        
        user_profile_id = input_data.get('user_profile_id')

        if not user_profile_id:
            return {"success": False, "error": "user_profile_id is required"}

        # Use authoritative field validation
        try:
            goal_data = validate_model_data('user_goal', input_data)
        except ValueError as ve:
            logger.error(f"Field validation error for user_goal: {str(ve)}")
            return {"success": False, "error": f"Field validation error: {str(ve)}"}

        # Get user profile
        @sync_to_async
        def get_user_profile():
            return UserProfile.objects.get(id=user_profile_id)

        user_profile = await get_user_profile()

        # Create goal
        @sync_to_async
        def create_goal():
            return UserGoal.objects.create(
                user_profile=user_profile,
                **goal_data
            )
        
        goal = await create_goal()
        
        return {
            "success": True,
            "goal_id": str(goal.id),
            "title": goal.title
        }
        
    except Exception as e:
        logger.error(f"Error creating user goal: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }


@register_tool('create_user_preference')
async def create_user_preference(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Creates a user preference during onboarding.

    Input:
        user_profile_id: UUID of the user profile
        pref_name: Name of the preference (e.g., "Morning Activities")
        pref_description: Description of the preference
        pref_strength: Strength of preference (1-100, default: 70)
        user_awareness: User's awareness of this preference (1-100, default: 70)

    Output:
        success: Boolean indicating if the operation was successful
        preference_id: ID of the created preference
    """
    try:
        from apps.user.models import UserProfile, Preference
        from datetime import date, timedelta

        user_profile_id = input_data.get('user_profile_id')

        if not user_profile_id:
            return {"success": False, "error": "user_profile_id is required"}

        # Use authoritative field validation
        try:
            preference_data = validate_model_data('preference', input_data)
        except ValueError as ve:
            logger.error(f"Field validation error for preference: {str(ve)}")
            return {"success": False, "error": f"Field validation error: {str(ve)}"}

        # Get user profile
        @sync_to_async
        def get_user_profile():
            return UserProfile.objects.get(id=user_profile_id)

        user_profile = await get_user_profile()

        # Create preference with proper temporal fields
        @sync_to_async
        def create_preference():
            return Preference.objects.create(
                user_profile=user_profile,
                **preference_data
            )

        preference = await create_preference()

        return {
            "success": True,
            "preference_id": str(preference.id),
            "pref_name": preference.pref_name,
            "pref_description": preference.pref_description
        }

    except Exception as e:
        logger.error(f"Error creating user preference: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }


@register_tool('create_user_belief')
async def create_user_belief(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Creates a user belief during onboarding.

    Input:
        user_profile_id: UUID of the user profile
        content: Belief content/statement
        user_confidence: User's confidence in this belief (1-100, default: 70)
        system_confidence: System's confidence in this belief (1-100, default: 70)
        emotionality: Emotional intensity of the belief (1-100, default: 50)
        stability: Stability of the belief over time (1-100, default: 70)
        user_awareness: User's awareness of this belief (1-100, default: 70)

    Output:
        success: Boolean indicating if the operation was successful
        belief_id: ID of the created belief
    """
    try:
        from apps.user.models import UserProfile, Belief
        from datetime import date

        user_profile_id = input_data.get('user_profile_id')

        if not user_profile_id:
            return {"success": False, "error": "user_profile_id is required"}

        # Use authoritative field validation
        try:
            belief_data = validate_model_data('belief', input_data)
        except ValueError as ve:
            logger.error(f"Field validation error for belief: {str(ve)}")
            return {"success": False, "error": f"Field validation error: {str(ve)}"}

        # Get user profile
        @sync_to_async
        def get_user_profile():
            return UserProfile.objects.get(id=user_profile_id)

        user_profile = await get_user_profile()

        # Create belief
        @sync_to_async
        def create_belief():
            return Belief.objects.create(
                user_profile=user_profile,
                **belief_data
            )

        belief = await create_belief()

        return {
            "success": True,
            "belief_id": str(belief.id),
            "content": belief.content
        }

    except Exception as e:
        logger.error(f"Error creating user belief: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }


@register_tool('create_user_trait')
async def create_user_trait(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Creates a user trait inclination during onboarding.

    Input:
        user_profile_id: UUID of the user profile
        trait_name: Name of the trait (should match a GenericTrait)
        strength: Strength of the trait inclination (1-100, default: 70)
        awareness: User awareness of this trait (1-100, default: 70)

    Output:
        success: Boolean indicating if the operation was successful
        trait_id: ID of the created trait inclination
    """
    try:
        from apps.user.models import UserProfile, UserTraitInclination, GenericTrait

        user_profile_id = input_data.get('user_profile_id')
        trait_name = input_data.get('trait_name')

        if not all([user_profile_id, trait_name]):
            return {"success": False, "error": "user_profile_id and trait_name are required"}

        # Use authoritative field validation for trait data
        try:
            trait_data = validate_model_data('trait', input_data)
        except ValueError as ve:
            logger.error(f"Field validation error for trait: {str(ve)}")
            return {"success": False, "error": f"Field validation error: {str(ve)}"}

        # Use authoritative field validation for generic trait data
        try:
            generic_trait_data = validate_model_data('generic_trait', input_data)
        except ValueError as ve:
            logger.error(f"Field validation error for generic_trait: {str(ve)}")
            return {"success": False, "error": f"Field validation error: {str(ve)}"}

        # Get user profile
        @sync_to_async
        def get_user_profile():
            return UserProfile.objects.get(id=user_profile_id)

        user_profile = await get_user_profile()

        # Find or create the generic trait
        @sync_to_async
        def get_or_create_generic_trait():
            trait, created = GenericTrait.objects.get_or_create(
                name=trait_name,
                defaults=generic_trait_data
            )
            return trait, created

        generic_trait, _ = await get_or_create_generic_trait()

        # Create or update trait inclination
        @sync_to_async
        def create_or_update_trait():
            trait_inclination, created = UserTraitInclination.objects.update_or_create(
                user_profile=user_profile,
                generic_trait=generic_trait,
                defaults=trait_data
            )
            return trait_inclination, created

        trait_inclination, created = await create_or_update_trait()

        return {
            "success": True,
            "trait_id": str(trait_inclination.id),
            "trait_name": trait_name,
            "strength": trait_inclination.strength,
            "created": created
        }

    except Exception as e:
        logger.error(f"Error creating user trait: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }
