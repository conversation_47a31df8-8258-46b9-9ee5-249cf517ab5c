from apps.main.agents.tools.tools_util import register_tool
import asyncio
from django.db import transaction
from django.utils import timezone
import logging
from typing import Dict, Any, List, Optional, Union
from channels.db import database_sync_to_async

logger = logging.getLogger(__name__)


@register_tool('get_environment_context')
async def get_environment_context(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyzes the user's current environment context and capabilities.
    
    Input:
        user_profile_id: UUID of the user profile
        reported_environment: Environment reported by user (optional)
        context: Additional context from user input
        
    Output:
        environment_context: Dictionary with environment analysis
            current_environment: Detected or reported environment
            privacy_level: Privacy level of the environment (0-100)
            space_size: Available space size (small, medium, large)
            noise_level: Ambient noise level
            social_context: Social context information
            activity_support: How well the environment supports activities
            confidence: Confidence in the analysis (0-1)
    """
    user_profile_id = input_data.get('user_profile_id')
    reported_environment = input_data.get('reported_environment')
    context = input_data.get('context', {})
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    try:
        # Import models
        from apps.user.models import UserProfile, UserEnvironment, GenericEnvironment
        
        # Get user profile
        user_profile = await database_sync_to_async(UserProfile.objects.get)(id=user_profile_id)
        
        # Try to get user's current environment
        current_env = None
        if reported_environment:
            # Look up the reported environment
            try:
                generic_env = await database_sync_to_async(
                    GenericEnvironment.objects.get
                )(code=reported_environment)
                current_env = {
                    "name": generic_env.name,
                    "code": generic_env.code,
                    "privacy_level": generic_env.typical_privacy_level,
                    "space_size": generic_env.typical_space_size,
                    "is_indoor": generic_env.is_indoor,
                    "category": generic_env.primary_category
                }
            except:
                # Fallback to basic environment info
                current_env = {
                    "name": reported_environment.replace('_', ' ').title(),
                    "code": reported_environment,
                    "privacy_level": 50,  # Default medium privacy
                    "space_size": "medium",
                    "is_indoor": True,
                    "category": "residential"
                }
        else:
            # Try to get user's current environment
            try:
                user_env = await database_sync_to_async(
                    UserEnvironment.objects.select_related('generic_environment').get
                )(user_profile=user_profile, is_current=True)

                logger.debug(f"Found user environment: {user_env.environment_name}")

                # Get environment properties if available
                privacy_level = 80  # Default
                space_size = "medium"  # Default

                # Get social context properties asynchronously
                try:
                    social_context = await database_sync_to_async(
                        lambda: getattr(user_env, 'social_context', None)
                    )()
                    if social_context:
                        privacy_level = social_context.privacy_level
                except:
                    pass  # Use default

                # Get physical properties asynchronously
                try:
                    physical_properties = await database_sync_to_async(
                        lambda: getattr(user_env, 'physical_properties', None)
                    )()
                    if physical_properties:
                        # Could map space size from physical properties
                        space_size = "medium"  # Default for now
                except:
                    pass  # Use default

                current_env = {
                    "name": user_env.environment_name,
                    "code": user_env.generic_environment.code if user_env.generic_environment else "custom",
                    "privacy_level": privacy_level,
                    "space_size": space_size,
                    "is_indoor": user_env.generic_environment.is_indoor if user_env.generic_environment else True,
                    "category": user_env.generic_environment.primary_category if user_env.generic_environment else "residential"
                }
                logger.debug(f"Created environment context: {current_env}")
            except Exception as e:
                logger.error(f"Error getting user environment for user {user_profile_id}: {e}")
                # Default fallback environment
                current_env = {
                    "name": "Home",
                    "code": "home",
                    "privacy_level": 80,
                    "space_size": "medium",
                    "is_indoor": True,
                    "category": "residential"
                }
        
        # Analyze noise level based on environment
        noise_level = "quiet"
        if current_env["category"] in ["commercial", "transportation"]:
            noise_level = "moderate"
        elif current_env["category"] in ["professional", "educational"]:
            noise_level = "low"
        
        # Determine social context
        social_context = {
            "alone": current_env["privacy_level"] > 70,
            "with_others": current_env["privacy_level"] < 50,
            "public_space": current_env["category"] in ["commercial", "cultural", "transportation"]
        }
        
        # Assess activity support
        activity_support = {
            "physical_activities": current_env["space_size"] in ["medium", "large"] and current_env["privacy_level"] > 50,
            "creative_activities": current_env["privacy_level"] > 60,
            "reflective_activities": current_env["privacy_level"] > 70 and noise_level in ["quiet", "low"],
            "social_activities": current_env["privacy_level"] < 60 or not social_context["alone"]
        }
        
        return {
            "environment_context": {
                "current_environment": current_env,
                "privacy_level": current_env["privacy_level"],
                "space_size": current_env["space_size"],
                "noise_level": noise_level,
                "social_context": social_context,
                "activity_support": activity_support,
                "confidence": 0.8 if reported_environment else 0.6
            }
        }
        
    except Exception as e:
        logger.exception("Error analyzing environment context")
        return {
            "environment_context": {
                "current_environment": {"name": "Unknown", "code": "unknown"},
                "privacy_level": 50,
                "space_size": "medium",
                "noise_level": "moderate",
                "social_context": {"alone": False, "with_others": True, "public_space": False},
                "activity_support": {
                    "physical_activities": True,
                    "creative_activities": True,
                    "reflective_activities": True,
                    "social_activities": True
                },
                "confidence": 0.3,
                "error": str(e)
            }
        }


@register_tool('parse_time_availability')
async def parse_time_availability(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parses and analyzes user's reported time availability.
    
    Input:
        user_profile_id: UUID of the user profile
        reported_time_availability: Time availability reported by user
        context: Additional context from user input
        
    Output:
        time_analysis: Dictionary with time availability analysis
            duration_minutes: Estimated duration in minutes
            duration_category: Category (quick, short, medium, long)
            flexibility: How flexible the time constraint is
            urgency: Whether there's time pressure
            confidence: Confidence in the parsing (0-1)
    """
    user_profile_id = input_data.get('user_profile_id')
    reported_time = input_data.get('reported_time_availability', '')
    context = input_data.get('context', {})
    
    if not user_profile_id:
        return {"error": "user_profile_id is required"}
    
    try:
        import re
        
        # Default values
        duration_minutes = 30  # Default 30 minutes
        duration_category = "medium"
        flexibility = "moderate"
        urgency = "low"
        confidence = 0.5
        
        if reported_time:
            reported_time_lower = reported_time.lower()
            
            # Extract numeric values
            numbers = re.findall(r'\d+', reported_time)
            
            # Look for time indicators
            if 'minute' in reported_time_lower:
                if numbers:
                    duration_minutes = int(numbers[0])
                    confidence = 0.9
            elif 'hour' in reported_time_lower:
                if numbers:
                    duration_minutes = int(numbers[0]) * 60
                    confidence = 0.9
            elif any(word in reported_time_lower for word in ['quick', 'fast', 'brief', 'short']):
                duration_minutes = 15
                duration_category = "quick"
                confidence = 0.7
            elif any(word in reported_time_lower for word in ['long', 'extended', 'plenty']):
                duration_minutes = 90
                duration_category = "long"
                confidence = 0.7
            elif any(word in reported_time_lower for word in ['little', 'bit', 'few']):
                duration_minutes = 20
                duration_category = "short"
                confidence = 0.6
            elif numbers:
                # If we found numbers but no time unit, assume minutes
                duration_minutes = int(numbers[0])
                if duration_minutes > 10:  # Reasonable assumption it's minutes
                    confidence = 0.7
                else:
                    # Might be hours
                    duration_minutes = int(numbers[0]) * 60
                    confidence = 0.5
        
        # Categorize duration
        if duration_minutes <= 10:
            duration_category = "quick"
        elif duration_minutes <= 30:
            duration_category = "short"
        elif duration_minutes <= 60:
            duration_category = "medium"
        else:
            duration_category = "long"
        
        # Assess flexibility based on language
        if reported_time:
            if any(word in reported_time.lower() for word in ['exactly', 'must', 'need to', 'have to']):
                flexibility = "low"
                urgency = "high"
            elif any(word in reported_time.lower() for word in ['about', 'around', 'roughly', 'maybe']):
                flexibility = "high"
            elif any(word in reported_time.lower() for word in ['up to', 'maximum', 'at most']):
                flexibility = "moderate"
                urgency = "medium"
        
        return {
            "time_analysis": {
                "duration_minutes": duration_minutes,
                "duration_category": duration_category,
                "flexibility": flexibility,
                "urgency": urgency,
                "confidence": confidence,
                "raw_input": reported_time
            }
        }
        
    except Exception as e:
        logger.exception("Error parsing time availability")
        return {
            "time_analysis": {
                "duration_minutes": 30,
                "duration_category": "medium",
                "flexibility": "moderate",
                "urgency": "low",
                "confidence": 0.3,
                "error": str(e)
            }
        }


@register_tool('get_available_resources')
async def get_available_resources(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Retrieves and analyzes user's available resources and capabilities.

    Input:
        user_profile_id: UUID of the user profile
        context: Additional context from user input

    Output:
        resources: Dictionary with resource analysis
            inventory: List of available inventory items
            limitations: List of reported limitations
            capabilities: Dictionary of user capabilities
            confidence: Confidence in the analysis (0-1)
    """
    user_profile_id = input_data.get('user_profile_id')
    context = input_data.get('context', {})

    if not user_profile_id:
        return {"error": "user_profile_id is required"}

    try:
        # Import models
        from apps.user.models import UserProfile, Inventory, UserLimitation, Skill, UserResource, UserEnvironment

        # Get user profile
        user_profile = await database_sync_to_async(UserProfile.objects.get)(id=user_profile_id)

        # Get inventory items from user's current environment
        inventory_items = []
        try:
            # Get user's current environment
            current_env = await database_sync_to_async(
                UserEnvironment.objects.get
            )(user_profile=user_profile, is_current=True)

            # Get user resources from the current environment
            user_resources = await database_sync_to_async(
                lambda: list(UserResource.objects.filter(user_environment=current_env).select_related('generic_resource'))
            )()

            for resource in user_resources:
                inventory_items.append({
                    "name": resource.specific_name,
                    "category": resource.generic_resource.resource_type if resource.generic_resource else "unknown",
                    "code": resource.generic_resource.code if resource.generic_resource else "custom",
                    "location": resource.location_details,
                    "ownership": resource.ownership_details,
                    "notes": resource.notes
                })
        except Exception as e:
            logger.debug(f"Could not fetch inventory for user {user_profile_id}: {e}")

        # Get limitations
        limitations = []
        try:
            limitations_qs = await database_sync_to_async(
                lambda: list(UserLimitation.objects.filter(user_profile=user_profile).select_related('generic_limitation'))
            )()

            for limitation in limitations_qs:
                limitations.append({
                    "type": limitation.generic_limitation.limitation_type,
                    "description": limitation.generic_limitation.description,
                    "severity": limitation.severity,
                    "is_active": getattr(limitation, 'is_currently_active', True)
                })
        except Exception as e:
            logger.debug(f"Could not fetch limitations for user {user_profile_id}: {e}")

        # Get skills/capabilities
        capabilities = {}
        try:
            skills_qs = await database_sync_to_async(
                lambda: list(Skill.objects.filter(user_profile=user_profile).select_related('generic_skill'))
            )()

            for skill in skills_qs:
                # Use generic skill description as the skill name
                skill_name = skill.generic_skill.description if skill.generic_skill else "Unknown Skill"

                # Group by skill type or use a general category
                domain_name = "General"  # Could be enhanced to use actual domain relationships

                if domain_name not in capabilities:
                    capabilities[domain_name] = []

                capabilities[domain_name].append({
                    "name": skill_name,
                    "level": skill.level,
                    "awareness": skill.user_awareness,
                    "enjoyment": skill.user_enjoyment
                })
        except Exception as e:
            logger.debug(f"Could not fetch skills for user {user_profile_id}: {e}")

        # Analyze overall resource availability
        resource_score = 0.7  # Default moderate resource availability

        if inventory_items:
            resource_score += 0.1
        if len(inventory_items) > 5:
            resource_score += 0.1
        if limitations:
            resource_score -= len(limitations) * 0.05
        if capabilities:
            resource_score += len(capabilities) * 0.02

        resource_score = max(0.1, min(1.0, resource_score))

        return {
            "inventory": inventory_items,
            "limitations": limitations,
            "capabilities": capabilities,
            "confidence": resource_score
        }

    except Exception as e:
        logger.exception("Error retrieving available resources")
        return {
            "inventory": [],
            "limitations": [],
            "capabilities": {},
            "confidence": 0.3,
            "error": str(e)
        }
