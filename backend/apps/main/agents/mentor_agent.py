# apps/main/agents/mentor_agent.py

from typing import Dict, Any, List, Tuple, Optional, TYPE_CHECKING
from pydantic import BaseModel

from apps.main.agents.base_agent import LangGraphAgent
# Import from the new state_models file
from apps.main.graphs.state_models import WorkflowTransitionRequest, DiscussionState
from apps.main.services.database_service import RealDatabaseService
from apps.main.llm.service import RealLLMClient
# Import LLMConfig for type hinting only during type checking
if TYPE_CHECKING:
    from apps.main.models import LLMConfig
from apps.main.llm.response import LLMResponse, ResponseType
import json
import logging
import time
import uuid
from datetime import datetime
from django.utils import timezone
import pprint # For pretty printing dicts/objects in logs
from asgiref.sync import sync_to_async # Import sync_to_async

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
class MentorAgent(LangGraphAgent):
    """
    Mentor agent that handles user interaction and communication.
     Enhanced with workflow transition capabilities to detect when a user
     request would be better handled by a different workflow.
     """

    def __init__(self,
                 user_profile_id: str,
                 db_service=None,
                 llm_client=None,
                 llm_config: Optional['LLMConfig'] = None): # Use string literal for type hint
        # Pass LLM config up to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="mentor",
            llm_config=llm_config # Pass the LLMConfig object
        )

        # Use provided dependencies or create defaults
        self.db_service = db_service or RealDatabaseService()

        # Instantiate LLM client using config from base class if not provided externally
        if llm_client:
            self.llm_client = llm_client
        else:
            # RealLLMClient now requires an LLMConfig object
            if not self.llm_config:
                logger.debug(f"Instantiating RealLLMClient for MentorAgent without LLMConfig: will use default")

            logger.debug(f"Instantiating RealLLMClient for MentorAgent with LLMConfig: {self.llm_config.name if self.llm_config else 'None'}")
            self.llm_client = RealLLMClient(llm_config=self.llm_config) # Pass the LLMConfig object


        # Initialize definition and tools to None. They will be loaded lazily.
        self.agent_definition = None
        self.available_tools = []

        # Runtime tool injection support
        self.runtime_tools = []
        self.runtime_instructions = ""
        self.base_tools_loaded = False

    def inject_tools(self, tools: list):
        """Inject tools at runtime for contextual enhancement."""
        self.runtime_tools = tools if tools else []
        logger.debug(f"Injected {len(self.runtime_tools)} runtime tools")

    def inject_instructions(self, instructions: str):
        """Inject additional instructions at runtime for contextual enhancement."""
        self.runtime_instructions = instructions if instructions else ""
        logger.debug(f"Injected runtime instructions: {len(self.runtime_instructions)} chars")

    def clear_runtime_enhancements(self):
        """Clear all runtime enhancements to return to base configuration."""
        self.runtime_tools = []
        self.runtime_instructions = ""
        logger.debug("Cleared all runtime enhancements")

    @property
    def effective_tools(self):
        """Get the effective tools list (base + runtime)."""
        return self.available_tools + self.runtime_tools

    @property
    def effective_instructions(self):
        """Get the effective system instructions (base + runtime)."""
        base_instructions = self.agent_definition.system_instructions if self.agent_definition else ""
        if self.runtime_instructions:
            return f"{base_instructions}\n\n{self.runtime_instructions}"
        return base_instructions

    async def _ensure_loaded(self, minimal_tools=False):
        """
        Ensures agent definition and tools are loaded asynchronously.

        Args:
            minimal_tools (bool): If True, load only essential tools for base configuration
        """
        if self.agent_definition is not None and self.base_tools_loaded:
            # Already loaded
            return True

        logger.debug(f"Loading definition and tools for {self.agent_role} (minimal={minimal_tools})...")
        try:
            # Wrap synchronous DB call for definition
            load_def_sync = sync_to_async(self.db_service.load_agent_definition, thread_sensitive=True)

            self.agent_definition = await load_def_sync(self.agent_role)
            if self.agent_definition:
                if minimal_tools:
                    # Load only essential tools for base configuration
                    # For now, we'll load no tools by default - they'll be injected at runtime
                    self.available_tools = []
                    logger.debug(f"Loaded minimal configuration for {self.agent_role} with 0 base tools")
                else:
                    # Load all tools (legacy behavior)
                    self.available_tools = await self.db_service.load_tools(self.agent_definition)
                    logger.debug(f"Successfully loaded definition and {len(self.available_tools)} tools for {self.agent_role}")

                self.base_tools_loaded = True
                return True
            else:
                logger.error(f"Agent definition not found for role: {self.agent_role}")
                self.agent_definition = None # Ensure it's None if not found
                self.available_tools = []
                return False

        except Exception as e:
            logger.error(f"Failed to load definition/tools for {self.agent_role}: {e}", exc_info=True)
            self.agent_definition = None
            self.available_tools = []
            # Don't re-raise - let the process method handle the None definition
            return False

    async def process(self, state: BaseModel) -> Dict[str, Any]:
        """
        Process the input state and generate mentor agent output.
        Enhanced with workflow transition detection.

        Args:
            state: The current workflow state

        Returns:
            Dict[str, Any]: State updates dictionary, as expected by LangGraph
        """
        logger.info(f"🚀 MENTOR AGENT PROCESS: Starting process method for user {self.user_profile_id}")
        logger.debug(f"MentorAgent process started. Input state:\n{pprint.pformat(state)}")

        # --- Ensure Agent Definition and Tools are Loaded ---
        current_operation = "ensuring_agent_loaded"
        try:
            self.start_stage("mentor_ensure_loaded") # Profile loading
            # Load with minimal tools by default - tools will be injected at runtime
            await self._ensure_loaded(minimal_tools=True)
            self.stop_stage("mentor_ensure_loaded") # Stop profiling loading
            # Check if loading failed (e.g., definition not found)
            if self.agent_definition is None:
                 raise RuntimeError(f"Agent definition for role '{self.agent_role}' could not be loaded.")
        except Exception as load_error:
            self.stop_stage("mentor_ensure_loaded") # Ensure stop is called on error
            error_message = f"Critical error: Failed to load agent configuration during '{current_operation}': {str(load_error)}"
            logger.error(error_message, exc_info=True)
            # Return error state immediately with standardized structure
            error_output_data = {
                "error": error_message,
                "debug": {
                    "last_error": error_message,
                    "failed_operation": current_operation,
                    "exception_type": type(load_error).__name__
                },
                "user_response": "I'm sorry, but I'm having trouble processing your request right now.",
                "context_packet": {},
                "next_agent": "mentor"  # Keep the agent active or adjust as needed
            }
            return {
                "error": error_message,
                "output_data": error_output_data
            }
        # --- End Loading Check ---

        current_operation = "extracting_state"
        # Extract relevant data from state
        input_data = getattr(state, "context_packet", {})
        workflow_id = getattr(state, "workflow_id", None)
        current_stage = getattr(state, "current_stage", "initial_conversation")
        conversation_history = getattr(state, "conversation_history", [])

        # Ensure workflow_id is not None for database constraints
        if workflow_id is None:
            workflow_id = str(uuid.uuid4())
            logger.debug(f"Generated fallback workflow_id: {workflow_id}")

        logger.debug(f"Extracted initial variables: workflow_id={workflow_id}, current_stage={current_stage}, input_data keys={list(input_data.keys())}, history_len={len(conversation_history)}") # DEBUG LOG

        # Add text from initial context packet if available for first conversation
        if not hasattr(state, "conversation_history") or not state.conversation_history:
            if hasattr(state, "initial_context_packet") and state.initial_context_packet:
                input_data["text"] = state.initial_context_packet.get("text", "")

        # Start a run in the database
        # Convert string user_profile_id to int for the database call
        # CRITICAL FIX: Don't convert real user IDs that are passed in benchmarks
        try:
            # First, try to convert directly to int (for real user IDs like "2")
            user_profile_id_int = int(self.user_profile_id)
            logger.debug(f"Using real user_profile_id: {user_profile_id_int}")
        except ValueError:
            # Only if conversion fails, check if it's a test/benchmark ID
            if isinstance(self.user_profile_id, str) and (
                self.user_profile_id.startswith('test-user-') or
                self.user_profile_id.startswith('benchmark-user-') or
                self.user_profile_id.startswith('debugger-user-') or
                self.user_profile_id == 'benchmark_user' or
                'test-user-' in self.user_profile_id or
                'debugger-user-' in self.user_profile_id or
                'benchmark' in self.user_profile_id.lower()
            ):
                # For test and benchmark IDs, use a default integer ID
                user_profile_id_int = 1  # Use a default test/benchmark ID
                logger.debug(f"Using default user_profile_id (1) for test/benchmark ID: {self.user_profile_id}")
            else:
                # Handle cases where the ID might not be a valid integer string
                error_message = f"Invalid user_profile_id format: {self.user_profile_id}"
                logger.error(f"{error_message}. Cannot convert to int for DB.")
                # Return consistent error structure including output_data
                error_output_data = {"error": error_message, "debug": {"last_error": error_message}}
                return {
                    "error": error_message,
                    "output_data": error_output_data
                    # No run_id available here yet
                }

        current_operation = "starting_run"
        # Call the now-async start_run directly
        self.start_stage("mentor_db_start_run") # Profile DB start
        run = await self.db_service.start_run(
            agent_definition=self.agent_definition, # Now guaranteed to be loaded
            user_profile_id=user_profile_id_int, # Pass the integer ID to the DB service
            input_data=input_data,
            state={"workflow_id": workflow_id} # Pass as keyword argument 'state'
        )
        self.stop_stage("mentor_db_start_run") # Stop profiling DB start
        # Store the AgentRun's UUID (not the user profile ID)
        self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id'))
        current_operation = "starting_process" # For logging

        try:
            current_operation = "accessing_context_packet"
            # Access context from the state directly
            # Assuming context_packet is passed in the initial state by the dispatcher/graph entry
            context_packet = getattr(state, "context_packet", {})
            if not context_packet:
                 # Attempt to get it from initial_context_packet if it's the very first run
                 context_packet = getattr(state, "initial_context_packet", {})
                 logger.warning("Using initial_context_packet as context_packet was empty.") # Add warning if fallback is used

            # Ensure text is included in the context_packet if not already present
            if "text" not in context_packet and input_data.get("text"):
                context_packet["text"] = input_data.get("text")

            # Check for conversation state metadata for state-aware processing
            metadata = context_packet.get('metadata', {})
            conversation_phase = metadata.get('conversation_phase', 'initial')
            awaiting_response = metadata.get('awaiting_response_type')
            session_context = metadata.get('session_context', {})

            logger.debug(f"Using context packet from state:\n{pprint.pformat(context_packet)}") # DEBUG LOG
            logger.debug(f"Conversation state - Phase: {conversation_phase}, Awaiting: {awaiting_response}") # DEBUG LOG

            current_operation = "detecting_workflow_transition"
            # Check for workflow transition requests using the context from state
            # Ensure 'text' is available in the context_packet for transition detection
            message_text_for_transition = context_packet.get("text", input_data.get("text", "")) # Get text from context or input_data
            self.start_stage("mentor_detect_transition") # Profile transition detection
            transition_request = await self._detect_workflow_transition(
                message_text_for_transition, # Use extracted text
                context_packet, # Pass the context packet from state
                current_stage,
                conversation_history
            )
            self.stop_stage("mentor_detect_transition") # Stop profiling transition detection
            logger.debug(f"Detected transition request: {pprint.pformat(transition_request)}") # DEBUG LOG

            # Initialize state updates dictionary
            state_updates = {}

            # If transition detected, prepare transition response
            if transition_request:
                # Format a transition explanation for the user
                user_response = await self._format_transition_response(transition_request)

                # Store in output_data
                output_data = {
                    "user_response": user_response,
                    "context_packet": context_packet,
                    "transition_request": transition_request,
                    "next_agent": "end"  # Signal to end this workflow
                }

                # Create a WorkflowTransitionRequest instance
                try:
                    transition_model = WorkflowTransitionRequest(**transition_request)
                    state_updates["transition_request"] = transition_model
                except Exception as pydantic_error:
                    logger.error(f"Failed to create WorkflowTransitionRequest model: {pydantic_error}")
                    # Handle error - maybe return an error state or proceed without transition
                    state_updates["error"] = "Failed to process transition request."
                    # Fall through to normal completion might be problematic, consider returning error state directly
                    # For now, let's add the raw dict to output_data for debugging if needed
                    output_data["raw_transition_request"] = transition_request


                state_updates["output_data"] = output_data

                # Update conversation history if available
                if hasattr(state, "conversation_history"):
                    new_history = conversation_history + [{
                        "role": "assistant",
                        "content": user_response,
                        "timestamp": timezone.now() # Use timezone.now()
                    }]
                state_updates["conversation_history"] = new_history

                # Call the now-async complete_run directly
                await self.db_service.complete_run(
                    run_id=self.run_id,
                    output_data=output_data,
                    state={"workflow_id": workflow_id, "transition_requested": True},
                    status='completed'
                )

                # Add run_id to state updates before returning
                state_updates["run_id"] = self.run_id # Corrected indentation
                return state_updates

            # --- Start: Refactored LLM call and Response Handling ---
            current_operation = "getting_llm_response"
            # Add logging before the call
            logger.debug(f"Calling _get_llm_response with context from state:\n{pprint.pformat(context_packet)}") # DEBUG LOG (Updated formatting)
            # Get the LLM response with state-aware processing
            self.start_stage("mentor_llm_call") # Profile LLM call
            llm_response = await self._get_state_aware_llm_response(
                context_packet, current_stage, conversation_history,
                conversation_phase, awaiting_response
            )
            self.stop_stage("mentor_llm_call") # Stop profiling LLM call
            # Add logging after the call to inspect the response
            logger.debug(f"Received LLMResponse:\n{pprint.pformat(llm_response)}") # DEBUG LOG (Updated formatting)

            current_operation = "processing_llm_response"
            # Determine the user_response based on LLM response type
            if hasattr(llm_response, 'response_type') and llm_response.response_type == ResponseType.TOOL_CALL and llm_response.tool_calls:
                tool_results = []
                current_operation = "calling_tools"
                for tool_call in llm_response.tool_calls:
                    tool_stage_name = f"mentor_tool_{tool_call.tool_name}"
                    logger.debug(f"Calling tool: {tool_call.tool_name} with input: {tool_call.tool_input}")
                    self.start_stage(tool_stage_name) # Profile tool call
                    # AgentTestRunner patches _call_tool to use MockToolRegistry
                    tool_result = await self._call_tool(tool_call.tool_name, tool_call.tool_input)
                    self.stop_stage(tool_stage_name) # Stop profiling tool call
                    logger.debug(f"Tool {tool_call.tool_name} result: {tool_result}")
                    tool_results.append(tool_result)

                current_operation = "formatting_tool_response"
                # Process tool results and generate natural language response
                user_response = await self._process_tool_results_to_response(
                    tool_results,
                    llm_response.tool_calls,
                    context_packet.get("text", "")
                )

            elif hasattr(llm_response, 'response_type') and llm_response.response_type == ResponseType.TEXT:
                user_response = llm_response.content if llm_response.content else "I'm not sure how to respond to that." # Response based on text content
            else: # Fallback
                user_response = llm_response.content if hasattr(llm_response, 'content') and llm_response.content else "I'm processing that request."
            logger.debug(f"Determined final user_response: '{user_response}'") # DEBUG LOG
            # --- End: Refactored LLM call and Response Handling ---

            # Output data for next agents in workflow
            output_data = {
                "user_response": user_response,
                "context_packet": context_packet,
                "next_agent": "orchestrator" if current_stage == "initial_conversation" else "mentor"
            }

            # Determine the next stage based on the current stage
            if current_stage == "initial_conversation":
                next_stage = "context_deepening"
            elif current_stage == "context_deepening":
                next_stage = "guidance_preparation"
            else: # Includes guidance_preparation and any unexpected stages
                next_stage = "workflow_complete" # Signal completion

            # Check if we're in onboarding workflow and should complete
            current_onboarding_stage = getattr(state, 'onboarding_stage', None)
            if current_onboarding_stage is not None:
                # We're in onboarding workflow - try to enrich profile with conversation data
                # ENHANCED: Also pass the initial context packet for better data extraction
                initial_context = getattr(state, 'initial_context_packet', {})
                await self._enrich_profile_from_conversation(conversation_history, user_response, initial_context)

                # Check if we should complete onboarding
                should_complete = await self._should_complete_onboarding(context_packet, conversation_history)
                if should_complete:
                    state_updates["onboarding_stage"] = "completed"
                    logger.info(f"MentorAgent setting onboarding_stage to 'completed' for user {self.user_profile_id}")

            # Check if we're in post_activity workflow and should complete
            current_feedback_stage = getattr(state, 'feedback_stage', None)
            if current_feedback_stage is not None:
                # We're in post_activity workflow - collect feedback and create report
                feedback_report = await self._collect_activity_feedback(conversation_history, user_response, context_packet)

                # Check if we should complete feedback collection
                should_complete = await self._should_complete_feedback_collection(context_packet, conversation_history, feedback_report)
                if should_complete:
                    state_updates["feedback_stage"] = "completed"
                    state_updates["feedback_report"] = feedback_report
                    logger.info(f"MentorAgent setting feedback_stage to 'completed' for user {self.user_profile_id}")

            # Add output_data, next_stage, and run_id to state updates
            state_updates["output_data"] = output_data
            state_updates["current_stage"] = next_stage # Explicitly update the stage
            state_updates["run_id"] = self.run_id # Add the run_id

            # Add token counts from the LLM response to state updates
            # Accumulate tokens if there were previous LLM calls in this run
            current_input_tokens = 0
            current_output_tokens = 0
            if hasattr(llm_response, 'input_tokens') and llm_response.input_tokens is not None:
                current_input_tokens = llm_response.input_tokens
            if hasattr(llm_response, 'output_tokens') and llm_response.output_tokens is not None:
                current_output_tokens = llm_response.output_tokens

            # Accumulate with any existing tokens from previous calls in this run
            existing_input = state.get("llm_input_tokens", 0) if hasattr(state, "get") else 0
            existing_output = state.get("llm_output_tokens", 0) if hasattr(state, "get") else 0

            state_updates["llm_input_tokens"] = existing_input + current_input_tokens
            state_updates["llm_output_tokens"] = existing_output + current_output_tokens

            logger.debug(f"Token tracking: Current call ({current_input_tokens} in, {current_output_tokens} out), "
                        f"Total accumulated ({state_updates['llm_input_tokens']} in, {state_updates['llm_output_tokens']} out)")

            # --- REMOVED: Unnecessary trigger_end addition ---
            # The graph routing handles the end of the turn based on
            # whether state_updates["transition_request"] is None or a model instance.
            # if not transition_request: # Check if we are NOT in the transition block
            #      state_updates["transition_request"] = {"trigger_end": True}
            # --- End of removal ---


            # Store assistant message in persistent conversation history
            logger.info(f"🔍 MENTOR AGENT: About to store assistant message for user {self.user_profile_id}")
            try:
                store_result = await self._call_tool("store_conversation_message", {
                    "user_profile_id": self.user_profile_id,
                    "message_content": user_response,
                    "message_role": "assistant",
                    "message_metadata": {
                        "workflow_id": getattr(state, 'workflow_id', None),
                        "onboarding_stage": getattr(state, 'onboarding_stage', None)
                    }
                })
                logger.info(f"✅ MENTOR AGENT: Stored assistant message in conversation history for user {self.user_profile_id}: {store_result}")
            except Exception as e:
                logger.error(f"❌ MENTOR AGENT: Failed to store assistant message in conversation history: {e}", exc_info=True)

            # Update conversation history if available
            # Ensure history is updated even if transition fails but processing continues
            if hasattr(state, "conversation_history"):
                    new_history = conversation_history + [{
                        "role": "assistant",
                        "content": user_response,
                        "timestamp": timezone.now() # Use timezone.now()
                    }]
                    state_updates["conversation_history"] = new_history

            current_operation = "completing_run_success"
            # Call the now-async complete_run directly
            self.start_stage("mentor_db_complete_run") # Profile DB complete
            await self.db_service.complete_run(
                run_id=self.run_id,
                output_data=output_data,
                state={"workflow_id": workflow_id},
                status='completed'
            )
            self.stop_stage("mentor_db_complete_run") # Stop profiling DB complete

            # Return the state updates dictionary
            logger.debug(f"Returning state updates:\n{pprint.pformat(state_updates)}") # DEBUG LOG
            return state_updates

        except Exception as e:
            # --- Improved Error Logging ---
            error_type = type(e).__name__
            error_message = f"Error in mentor agent during '{current_operation}': {str(e)}"
            # Log the error with type and location
            logger.error(f"Exception caught in MentorAgent process ({error_type} during {current_operation}): {error_message}", exc_info=True)
            # --- End Improved Error Logging ---

            # Complete the run with status 'failed', passing the current state
            # Remove the unexpected 'error_message' argument
            # Pass simpler metadata instead of the full state object
            try:
                # Call the now-async complete_run directly
                await self.db_service.complete_run(
                    run_id=self.run_id,
                    output_data={"error": error_message}, # Pass error in output field
                    state={"error_details": error_message}, # Pass simple metadata for the 'state' arg
                    status='failed',
                    # Pass the improved error message if the arg exists, otherwise rely on output_data
                    error_message=error_message # Pass improved error message explicitly if arg exists
                )
            except Exception as db_error:
                 logger.error(f"Failed to complete run in DB after agent error: {db_error}", exc_info=True)

            # Return error in state updates format, ensuring output_data is present
            # Include the operation stage in the debug info
            error_output_data = {
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation},
                "user_response": error_message,
                "context_packet": {},
                "next_agent": "mentor"  # Keep the agent active or adjust as needed
            } # Include error and stage in output_data
            error_updates = {
                "error": error_message, # Keep the improved error message here too
                "output_data": error_output_data # Add output_data key
            }
            if hasattr(self, 'run_id') and self.run_id:
                error_updates["run_id"] = self.run_id
                # Optionally add run_id to output_data as well if needed by downstream
                # error_output_data["run_id"] = self.run_id
            return error_updates


    async def _gather_user_context(self, input_data):
        """Gather context from user input"""
        # Call tool to extract context
        try:
            context_result = await self._call_tool(
                "extract_message_context",
                {
                    "message": input_data.get("text", ""),
                    "user_profile_id": self.user_profile_id,
                    "extraction_level": "comprehensive"
                }
            )

            # Get the extracted context
            context = context_result.get("extracted_context", {})

             # Format into a context packet
            context_packet = {
                 "user_id": self.user_profile_id,
                 "text": input_data.get("text", ""), # Add original text back
                 "mood": context.get("mood", ""),
                 "environment": context.get("environment", ""),
                 "time_availability": context.get("time_availability", ""),
                 "focus": context.get("focus", ""),
                 "workflow_type": input_data.get("workflow_type", "None")
             }

            return context_packet

        except Exception as e:
            # If tool fails, return minimal context
            logger.error(f"Error gathering user context: {str(e)}")
            # Log the error but return an empty context packet to allow processing to continue if possible,
            # or re-raise if context is critical. For now, log and return empty.
            logger.error(f"Error gathering user context via tool: {str(e)}", exc_info=True)
            # Returning empty context here as the method is being removed.
            # If this method were kept, it should probably raise an error.
            # Log the error but return an empty context packet to allow processing to continue if possible,
            # or re-raise if context is critical. For now, log and return empty.
            logger.error(f"Error gathering user context via tool: {str(e)}", exc_info=True)
            # Returning empty context here as the method is being removed.
            # If this method were kept, it should probably raise an error.
            return {} # Return empty dict as this method is being removed

    # Removed _gather_user_context method entirely as it's redundant - Ensure correct class level indentation below

    async def _get_llm_response(self,
                                context_packet: Dict[str, Any], # Context now comes from state
                                current_stage: str,
                                conversation_history: List[Dict[str, str]]
                               ) -> LLMResponse: # Corrected return type hint
        """
        Gets a response from the LLM based on context, stage, and history.
        """
        # Get effective system instructions (base + runtime enhancements)
        base_system_instructions = self.effective_instructions

        # CRITICAL FIX: Check for specific instructions from ConversationDispatcher in context packet
        # This enables cross-process communication of gap-based instructions
        specific_instructions = context_packet.get('mentor_specific_instructions', {})
        if specific_instructions and specific_instructions.get('instruction_type') == 'profile_gap_targeted':
            priority_field = specific_instructions.get('priority_field', 'information')
            specific_question = specific_instructions.get('specific_question', 'Could you tell me more about yourself?')
            context_hint = specific_instructions.get('context_hint', '')
            critical_gaps_count = specific_instructions.get('critical_gaps_count', 0)

            logger.info(f"🎯 Using specific gap-based instructions: {priority_field} - {specific_question[:50]}...")

            # Override with specific gap-based instructions
            system_message = f"""You are a friendly and supportive mentor helping users complete their profile for personalized activity recommendations.

CRITICAL PRIORITY: The user needs to provide information about their {priority_field}.

YOUR SPECIFIC TASK:
- Ask this exact question: "{specific_question}"
- Explain why this helps: {context_hint}
- Be warm and encouraging
- Focus ONLY on this specific information need
- Don't ask about other topics like name or general questions

CONTEXT: The user has {critical_gaps_count} critical gap(s) in their profile that need to be addressed for quality recommendations.

APPROACH:
- Be direct but friendly about what you need to know
- Explain how this specific information helps create better activity suggestions
- Keep the conversation focused on the missing information
- Use profile enrichment tools to store any information provided"""
        else:
            # If no database instructions found, use fallback
            if not base_system_instructions:
                base_system_instructions = """You are a friendly and supportive mentor in a conversational context.
                Create a warm, thoughtful response that engages with the user's current message and context.
                Tailor your response to the current stage of the conversation."""
                logger.warning("No system instructions found in database agent definition, using fallback")

            # Enhance database instructions with tool usage guidance (only if not using specific instructions)
            system_message = f"""{base_system_instructions}

            IMPORTANT: You have access to tools that can help you provide personalized responses:
            - Use 'get_user_profile' to learn about the user's name, demographics, goals, and preferences
            - Use 'get_communication_guidelines' to understand how to best communicate with this specific user
            - When a user asks about their name, goals, or personal information, use the appropriate tools to provide accurate information

            TOOL USAGE GUIDANCE:
            - Always use tools when users ask about personal information (name, goals, preferences)
            - Convert tool results into natural, conversational language
            - Don't show raw technical data to users"""

        # Format context and stage into a readable message
        context_str = "\n".join([f"{k}: {v}" for k, v in context_packet.items() if k != "user_id" and k != "text"]) # Exclude text from context display

        # Add stage-specific instructions with first-time user support
        stage_instructions = {
            "initial_conversation": "Keep your response welcoming and open-ended to encourage sharing. For first-time users mentioning wellness/productivity, ask about their current mood and energy level before suggesting activities.",
            "context_deepening": "Explore the user's thoughts more deeply. Ask about their feelings, goals, or challenges. For new users, focus on understanding their current state.",
            "guidance_preparation": "Offer insights and gentle guidance based on what you've learned. For first-time users, provide reassurance and simple next steps rather than complex activity suggestions.",
            "reflection": "Help the user reflect on what they've shared. Summarize key points and identify potential patterns."
        }

        instruction = stage_instructions.get(
            current_stage,
            "Engage thoughtfully with the user's message and provide a supportive response."
        )

        # Extract latest user message text from context_packet if available
        latest_user_text = context_packet.get("text", "User provided no text.") # Use context_packet now

        # Include conversation history in the prompt if available
        history_str = ""
        if conversation_history:
             history_str = "\n\nConversation History:\n" + "\n".join([
                 f"{msg.get('role', 'unknown')}: {msg.get('content', '')}" for msg in conversation_history
             ])

        user_message = f"""
        Context:
        {context_str}
        User ID: {self.user_profile_id}

        Current conversation stage: {current_stage}

        Instructions:
        {instruction}

        User's latest message: "{latest_user_text}"

        {history_str}

        IMPORTANT: When using tools like get_user_profile, use the User ID provided above ({self.user_profile_id}).
        If you call tools and get results, provide a natural, conversational response based on the tool results, not the raw technical output.

        Create a thoughtful response to the user that moves the conversation forward.
        """

        # Get response from LLM using effective tools (base + runtime)
        response = await self.llm_client.chat_completion(
            messages=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_message}
            ],
            tools=self.effective_tools # Pass effective tools (base + runtime) to LLM
        )

        # Return the full LLMResponse object
        return response

    async def _get_state_aware_llm_response(self,
                                           context_packet: Dict[str, Any],
                                           current_stage: str,
                                           conversation_history: List[Dict[str, str]],
                                           conversation_phase: str,
                                           awaiting_response: Optional[str]
                                          ) -> LLMResponse:
        """
        Get LLM response with conversation state awareness.

        Args:
            context_packet: Context from state
            current_stage: Current workflow stage
            conversation_history: Previous messages
            conversation_phase: Current conversation phase
            awaiting_response: Type of response being awaited

        Returns:
            LLMResponse: Enhanced response based on conversation state
        """
        # Enhance system instructions based on conversation state
        base_response = await self._get_llm_response(context_packet, current_stage, conversation_history)

        # If we're awaiting a specific response type, adjust the response
        if awaiting_response:
            if awaiting_response == 'onboarding_response':
                # Continue onboarding flow with follow-up questions
                logger.debug("Adjusting response for onboarding follow-up")
            elif awaiting_response == 'reflection_answer':
                # Process reflection and provide guidance
                logger.debug("Adjusting response for reflection processing")
            elif awaiting_response == 'activity_selection':
                # Handle activity feedback
                logger.debug("Adjusting response for activity selection feedback")

        # Adjust response based on conversation phase
        if conversation_phase == 'awaiting_reflection':
            # Add reflection processing context
            logger.debug("Processing in awaiting_reflection phase")
        elif conversation_phase == 'activity_selection':
            # Add activity selection context
            logger.debug("Processing in activity_selection phase")

        return base_response

    async def _should_complete_onboarding(self, context_packet: Dict[str, Any], conversation_history: list) -> bool:
        """
        Determine if onboarding should be completed based on conversation progress.

        Args:
            context_packet: Current context information
            conversation_history: Previous messages in the conversation

        Returns:
            bool: True if onboarding should be completed
        """
        try:
            # Check if we have enough conversation turns (minimum 3 exchanges)
            assistant_responses = [msg for msg in conversation_history if msg.get('role') == 'assistant']
            if len(assistant_responses) < 2:
                return False

            # Check if we've gathered basic user information
            try:
                profile_result = await self._call_tool(
                    "get_user_profile",
                    {"input_data": {"user_profile_id": self.user_profile_id}}
                )

                user_profile = profile_result.get("user_profile", {})
                profile_completion = user_profile.get("profile_completion", 0.0)

                # CRITICAL FIX: More lenient completion criteria to handle async data creation
                # Complete onboarding if we have ANY profile improvement or enough conversation
                # This fixes the hanging issue where profile data is created but completion % is still 0.0%
                if profile_completion > 0.05 or len(assistant_responses) >= 2:
                    logger.info(f"Onboarding completion criteria met: profile_completion={profile_completion}, responses={len(assistant_responses)}")
                    return True

            except Exception as e:
                logger.warning(f"Error checking profile completion for onboarding: {e}")
                # Fallback: complete after 2 assistant responses (reduced from 3)
                if len(assistant_responses) >= 2:
                    return True

            return False

        except Exception as e:
            logger.error(f"Error in _should_complete_onboarding: {e}")
            # Fallback: complete after some conversation
            assistant_responses = [msg for msg in conversation_history if msg.get('role') == 'assistant']
            return len(assistant_responses) >= 2

    async def _enrich_profile_from_conversation(self, conversation_history: list, user_response: str, initial_context: Dict[str, Any] = None) -> None:
        """
        Extract information from conversation and create profile data.

        Args:
            conversation_history: Previous messages in the conversation
            user_response: Latest user message
            initial_context: Initial context packet with user's first message
        """
        try:
            # CRITICAL FIX: Allow profile enrichment from the first meaningful interaction
            # Changed from requiring 2+ messages to allowing enrichment with any user input
            # This ensures onboarding workflow can enrich profiles immediately

            # Collect all available user text from multiple sources
            all_user_messages = []

            # Add messages from conversation history
            all_user_messages.extend([msg.get('content', '') for msg in conversation_history if msg.get('role') == 'user'])

            # Add current user response
            if user_response:
                all_user_messages.append(user_response)

            # Add initial context text if available
            if initial_context and initial_context.get('text'):
                all_user_messages.append(initial_context.get('text'))

            # Check if we have any user input to work with
            if not any(msg.strip() for msg in all_user_messages):
                logger.debug("No meaningful user input available for profile enrichment")
                return

            logger.info(f"Enriching profile for user {self.user_profile_id} with {len(all_user_messages)} user messages")

            # Simple extraction logic - look for common patterns
            user_text = ' '.join(all_user_messages).lower()

            # Try to extract and create basic demographics
            await self._extract_and_create_demographics(user_text)

            # Try to extract and create goals
            await self._extract_and_create_goals(user_text)

            # Try to extract and create preferences
            await self._extract_and_create_preferences(user_text)

            # Try to extract and create traits
            await self._extract_and_create_traits(user_text)

            # Try to extract and create beliefs
            await self._extract_and_create_beliefs(user_text)

        except Exception as e:
            logger.warning(f"Error enriching profile from conversation: {e}")

    async def _extract_and_create_demographics(self, user_text: str) -> None:
        """Extract demographic information and create demographics record."""
        try:
            demographics_data = {"user_profile_id": self.user_profile_id}

            # Enhanced pattern matching for age
            import re
            age_patterns = [
                r'\b(?:i am|i\'m|age)\s*(\d{1,2})\b',
                r'\b(\d{1,2})\s*(?:years?\s*old|year-old)\b',
                r'\b(\d{1,2})-year-old\b'
            ]

            for pattern in age_patterns:
                age_match = re.search(pattern, user_text)
                if age_match:
                    demographics_data["age"] = int(age_match.group(1))
                    break

            # Enhanced occupation detection
            if any(word in user_text for word in ['student', 'studying', 'school', 'university', 'college', 'exam', 'exams']):
                demographics_data["occupation"] = "Student"
            elif any(word in user_text for word in ['work', 'job', 'career', 'employed', 'working']):
                demographics_data["occupation"] = "Employed"

            # Location detection
            location_patterns = [
                r'\bin\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b',
                r'\bfrom\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b',
                r'\blive\s+in\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b'
            ]

            for pattern in location_patterns:
                location_match = re.search(pattern, user_text)
                if location_match:
                    location = location_match.group(1)
                    if len(location) > 2:  # Avoid single letters
                        demographics_data["location"] = location
                        break

            # Only create if we have some meaningful data
            if len(demographics_data) > 1:
                result = await self._call_tool("create_user_demographics", demographics_data)
                if result.get("success"):
                    logger.info(f"Created demographics for user {self.user_profile_id}: {demographics_data}")

        except Exception as e:
            logger.warning(f"Error extracting demographics: {e}")

    async def _extract_and_create_goals(self, user_text: str) -> None:
        """Extract goals and create goal records."""
        try:
            # Enhanced goal detection patterns
            goal_patterns = [
                ('focus', 'need help focusing', 'Improve Focus and Concentration', 'Develop better focus and concentration skills for academic success'),
                ('stress', 'manage.*stress|stressed', 'Stress Management', 'Learn effective stress management techniques'),
                ('exam', 'exam|test', 'Academic Success', 'Achieve success in upcoming exams and academic challenges'),
                ('adhd', 'adhd|attention', 'ADHD Management', 'Develop strategies to manage ADHD symptoms effectively'),
                ('study', 'study|studying', 'Study Skills', 'Improve study habits and learning techniques')
            ]

            goals_created = 0
            for goal_type, pattern, title, description in goal_patterns:
                import re
                if re.search(pattern, user_text, re.IGNORECASE):
                    goal_data = {
                        "user_profile_id": self.user_profile_id,
                        "title": title,
                        "description": description,
                        "importance": 80 if goal_type in ['focus', 'stress', 'adhd'] else 70
                    }

                    result = await self._call_tool("create_user_goal", goal_data)
                    if result.get("success"):
                        logger.info(f"Created {goal_type} goal for user {self.user_profile_id}: {title}")
                        goals_created += 1

            # Fallback: create a general goal if specific patterns don't match
            if goals_created == 0:
                general_keywords = ['goal', 'want to', 'hope to', 'trying to', 'working on', 'improve', 'better at', 'need', 'help']
                if any(keyword in user_text for keyword in general_keywords):
                    goal_data = {
                        "user_profile_id": self.user_profile_id,
                        "title": "Personal Development",
                        "description": "General personal development goal identified during conversation",
                        "importance": 70
                    }

                    result = await self._call_tool("create_user_goal", goal_data)
                    if result.get("success"):
                        logger.info(f"Created general goal for user {self.user_profile_id}")

        except Exception as e:
            logger.warning(f"Error extracting goals: {e}")

    async def _extract_and_create_preferences(self, user_text: str) -> None:
        """Extract preferences and create preference records."""
        try:
            # Enhanced preference detection patterns
            preference_patterns = [
                ('stress_management', r'stressed|stress|anxiety|anxious', 'Stress Relief Activities', 'Prefers activities that help manage stress and anxiety', 85),
                ('focus_activities', r'focus|concentration|attention', 'Focus-Enhancing Activities', 'Prefers activities that improve focus and concentration', 80),
                ('academic_support', r'exam|study|academic|learning', 'Academic Support', 'Prefers activities that support academic success', 75),
                ('adhd_friendly', r'adhd|attention.*deficit', 'ADHD-Friendly Activities', 'Prefers activities suitable for ADHD management', 90),
                ('quick_activities', r'quick|short|brief|fast', 'Short Duration Activities', 'Prefers shorter, more manageable activities', 70),
                ('interactive', r'interactive|engaging|fun', 'Interactive Activities', 'Prefers engaging and interactive experiences', 65)
            ]

            preferences_created = 0
            for pref_type, pattern, name, description, strength in preference_patterns:
                import re
                if re.search(pattern, user_text, re.IGNORECASE):
                    preference_data = {
                        "user_profile_id": self.user_profile_id,
                        "pref_name": name,
                        "pref_description": description,
                        "pref_strength": strength,
                        "user_awareness": max(60, strength - 10)  # Awareness slightly lower than strength
                    }

                    result = await self._call_tool("create_user_preference", preference_data)
                    if result.get("success"):
                        logger.info(f"Created {pref_type} preference for user {self.user_profile_id}: {name}")
                        preferences_created += 1

            # Fallback: create a general preference if specific patterns don't match
            if preferences_created == 0 and any(word in user_text for word in ['like', 'enjoy', 'prefer', 'love', 'favorite', 'need', 'want']):
                preference_data = {
                    "user_profile_id": self.user_profile_id,
                    "pref_name": "Positive Engagement",
                    "pref_description": "User shows positive engagement patterns in conversation",
                    "pref_strength": 70,
                    "user_awareness": 60
                }

                result = await self._call_tool("create_user_preference", preference_data)
                if result.get("success"):
                    logger.info(f"Created general preference for user {self.user_profile_id}")

        except Exception as e:
            logger.warning(f"Error extracting preferences: {e}")

    async def _extract_and_create_traits(self, user_text: str) -> None:
        """Extract personality traits and create trait inclination records."""
        try:
            # ADHD-related trait patterns
            trait_patterns = [
                ('openness', r'creative|curious|open.*mind|explore|new.*ideas', 'Openness to Experience', 75),
                ('conscientiousness', r'organized|plan|schedule|structure|discipline', 'Conscientiousness', 60),  # Lower for ADHD
                ('extraversion', r'social|outgoing|energetic|talkative', 'Extraversion', 70),
                ('agreeableness', r'helpful|kind|cooperative|friendly', 'Agreeableness', 75),
                ('neuroticism', r'stressed|anxious|worry|nervous|adhd', 'Neuroticism', 80),  # Higher for ADHD/stress
                ('attention_challenges', r'adhd|attention.*deficit|focus.*problem|distract', 'Attention Challenges', 85)
            ]

            traits_created = 0
            for trait_type, pattern, trait_name, strength in trait_patterns:
                import re
                if re.search(pattern, user_text, re.IGNORECASE):
                    # Check if we have the create_user_trait tool
                    try:
                        trait_data = {
                            "user_profile_id": self.user_profile_id,
                            "trait_name": trait_name,
                            "strength": strength,
                            "awareness": 70  # Fixed: Use 'awareness' field instead of 'confidence'
                        }

                        # Try to create trait (this tool might not exist, so we'll handle gracefully)
                        result = await self._call_tool("create_user_trait", trait_data)
                        if result.get("success"):
                            logger.info(f"Created {trait_type} trait for user {self.user_profile_id}: {trait_name}")
                            traits_created += 1
                    except Exception as tool_error:
                        logger.debug(f"Trait creation tool not available: {tool_error}")
                        break  # Stop trying if tool doesn't exist

        except Exception as e:
            logger.warning(f"Error extracting traits: {e}")

    async def _extract_and_create_beliefs(self, user_text: str) -> None:
        """Extract beliefs and create belief records."""
        try:
            # Belief patterns based on user statements
            belief_patterns = [
                ('academic_success', r'exam|test|study|academic', 'Academic achievement is important for my future', 80),
                ('stress_management', r'stressed|stress|manage.*stress', 'I need to learn better stress management techniques', 85),
                ('self_improvement', r'improve|better|help|focus', 'I can improve myself with the right support and tools', 75),
                ('adhd_acceptance', r'adhd|attention.*deficit', 'ADHD is a part of who I am and I can work with it', 70),
                ('support_seeking', r'help|support|need|propose', 'Seeking help and guidance is a sign of strength', 80)
            ]

            beliefs_created = 0
            for belief_type, pattern, belief_content, strength in belief_patterns:
                import re
                if re.search(pattern, user_text, re.IGNORECASE):
                    try:
                        belief_data = {
                            "user_profile_id": self.user_profile_id,
                            "content": belief_content,
                            "strength": strength,
                            "confidence": 70
                        }

                        # Try to create belief (this tool might not exist, so we'll handle gracefully)
                        result = await self._call_tool("create_user_belief", belief_data)
                        if result.get("success"):
                            logger.info(f"Created {belief_type} belief for user {self.user_profile_id}: {belief_content}")
                            beliefs_created += 1
                    except Exception as tool_error:
                        logger.debug(f"Belief creation tool not available: {tool_error}")
                        break  # Stop trying if tool doesn't exist

        except Exception as e:
            logger.warning(f"Error extracting beliefs: {e}")

    async def _collect_activity_feedback(self, conversation_history: list, user_response: str, context_packet: Dict[str, Any]) -> Dict[str, Any]:
        """
        Collect comprehensive activity feedback following post_activity_FLOW.md specification.

        Args:
            conversation_history: Previous messages in the conversation
            user_response: Latest user message
            context_packet: Current context information

        Returns:
            dict: Structured feedback report
        """
        try:
            # Initialize feedback report structure
            feedback_report = {
                'experience_narrative': '',
                'obstacles_encountered': [],
                'perceived_value': 0,
                'satisfaction_level': 0,
                'challenge_level': 0,
                'trust_feedback': {},
                'aspiration_updates': [],
                'system_feedback': {},
                'collection_timestamp': datetime.now().isoformat()
            }

            # Extract activity context
            activity_id = context_packet.get('activity_id')
            activity_name = context_packet.get('activity_name', 'the activity')

            # Analyze user response for feedback components
            user_text = user_response.lower()

            # Extract experience narrative (simplified)
            feedback_report['experience_narrative'] = user_response

            # Extract satisfaction indicators
            if any(word in user_text for word in ['great', 'amazing', 'loved', 'enjoyed', 'fantastic']):
                feedback_report['satisfaction_level'] = 8
            elif any(word in user_text for word in ['good', 'nice', 'okay', 'fine']):
                feedback_report['satisfaction_level'] = 6
            elif any(word in user_text for word in ['bad', 'terrible', 'hated', 'awful']):
                feedback_report['satisfaction_level'] = 2
            else:
                feedback_report['satisfaction_level'] = 5  # Neutral

            # Extract challenge level feedback
            if any(word in user_text for word in ['too easy', 'boring', 'simple']):
                feedback_report['challenge_level'] = 3
            elif any(word in user_text for word in ['too hard', 'difficult', 'challenging']):
                feedback_report['challenge_level'] = 8
            else:
                feedback_report['challenge_level'] = 5  # Appropriate

            # Extract obstacles
            if any(word in user_text for word in ['problem', 'issue', 'difficult', 'struggle', 'hard']):
                feedback_report['obstacles_encountered'].append('User reported difficulties during activity')

            # Extract trust feedback
            if any(word in user_text for word in ['trust', 'recommend', 'helpful', 'useful']):
                feedback_report['trust_feedback']['positive_indicators'] = True
            elif any(word in user_text for word in ['useless', 'unhelpful', 'waste']):
                feedback_report['trust_feedback']['negative_indicators'] = True

            logger.info(f"Collected activity feedback for user {self.user_profile_id}: satisfaction={feedback_report['satisfaction_level']}")

            return feedback_report

        except Exception as e:
            logger.warning(f"Error collecting activity feedback: {e}")
            return {'error': str(e), 'collection_timestamp': datetime.now().isoformat()}

    async def _should_complete_feedback_collection(self, context_packet: Dict[str, Any], conversation_history: list, feedback_report: Dict[str, Any]) -> bool:
        """
        Determine if feedback collection should be completed.

        Args:
            context_packet: Current context information
            conversation_history: Previous messages in the conversation
            feedback_report: Current feedback report

        Returns:
            bool: True if feedback collection is complete
        """
        try:
            # Check if we have basic feedback components
            has_narrative = bool(feedback_report.get('experience_narrative'))
            has_satisfaction = feedback_report.get('satisfaction_level', 0) > 0

            # Check conversation length (don't collect feedback forever)
            assistant_responses = [msg for msg in conversation_history if msg.get('role') == 'assistant']

            # Complete if we have basic feedback or enough conversation
            if (has_narrative and has_satisfaction) or len(assistant_responses) >= 3:
                logger.info(f"Feedback collection completion criteria met for user {self.user_profile_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error in _should_complete_feedback_collection: {e}")
            # Fallback: complete after some conversation
            assistant_responses = [msg for msg in conversation_history if msg.get('role') == 'assistant']
            return len(assistant_responses) >= 2

    async def _detect_workflow_transition(
        self,
        message: str,
        context_packet: Dict[str, Any],
        current_stage: str,
        conversation_history: list
    ) -> Optional[Dict[str, Any]]:
        """
        Detect if the user's message indicates a need to transition to a different workflow.

        Args:
            message: The user's message text
            context_packet: Extracted context data
            current_stage: Current conversation stage
            conversation_history: Previous messages in the conversation

        Returns:
            Optional[Dict[str, Any]]: Transition request or None if no transition needed
        """
        try:
            # Don't check for transitions too early in the conversation
            if current_stage == "initial_conversation" and len(conversation_history) < 2:
                return None

            # Prepare prompt for workflow classification
            system_message = """You are an expert workflow classifier. Your task is to determine if a user message
            within an ongoing conversation should trigger a transition to a different workflow type.

            Available workflows:
            - discussion (current): Ongoing conversation and reflection.
            - wheel_generation: User wants activity suggestions or recommendations.
            - activity_feedback: User is giving feedback about a completed activity.
            - progress_review: User wants to review their progress or growth.

            Only recommend a transition if the user's message strongly indicates a need for a specific workflow.
            If the message can be handled well within the current discussion workflow, do not recommend a transition.
            """

            # Prepare conversation context
            if conversation_history:
                convo_context = "\n\n".join([
                    f"{msg.get('role', 'unknown')}: {msg.get('content', '')}"
                    for msg in conversation_history[-3:]  # Include last 3 messages for context
                ])
            else:
                convo_context = "No previous conversation"

            user_message = f"""
            Current workflow: discussion
            Current stage: {current_stage}

            Recent conversation:
            {convo_context}

            User's latest message: "{message}"

            Extracted context:
            Mood: {context_packet.get('mood', 'Unknown')}
            Focus: {context_packet.get('focus', 'Unknown')}
            Time availability: {context_packet.get('time_availability', 'Unknown')}

            Should this trigger a workflow transition? If so, to which workflow type?
            Answer ONLY in JSON format with the following structure:
            {{
              "transition_needed": boolean,
              "target_workflow": "workflow_name" or null,
              "confidence": float (0.0-1.0),
              "reasoning": "string"
            }}
            Ensure the key for the workflow type is "target_workflow".
            """

            # Get classification from LLM
            response = await self.llm_client.chat_completion(
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.2  # Lower temperature for more precise classification
            )

            # Log token usage for this additional LLM call
            if hasattr(response, 'input_tokens') and hasattr(response, 'output_tokens'):
                logger.debug(f"Workflow transition detection LLM call: {response.input_tokens} in, {response.output_tokens} out tokens")

            # Parse the response
            classification = self._parse_classification_response(response.content)

            # Check if transition is needed and target is valid
            if classification and classification.get("transition_needed") is True:
                target_workflow = classification.get("target_workflow")
                confidence = classification.get("confidence", 0.0)

                # Only transition with sufficient confidence and a valid target
                if target_workflow and confidence >= 0.7:
                    logger.info(f"Detected transition to '{target_workflow}' with confidence {confidence}.")
                    # Return dict matching WorkflowTransitionRequest structure implicitly
                    # (Pydantic will validate when creating the instance in `process`)
                    return {
                        "target_workflow": target_workflow,
                        "message": message,
                        "context": context_packet,
                        # Add confidence and reason to context if needed by next workflow,
                        # otherwise they are just for logging/decision here.
                        # "confidence": confidence, # Optional: Add if needed downstream
                        # "reason": classification.get("reasoning", "Based on user request") # Optional
                    }
                else:
                    logger.debug(f"Transition detected but confidence ({confidence}) or target ('{target_workflow}') insufficient.")

            return None # No transition needed or confidence too low

        except Exception as e:
            # Log the error but don't disrupt the conversation
            import logging
            logging.error(f"Error in workflow transition detection: {str(e)}")
            return None

    def _parse_classification_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """Parse LLM response for classification information"""
        try:
            # Try to extract JSON from the response
            import json
            import re

            # First, try direct JSON parsing
            try:
                return json.loads(response_text)
            except json.JSONDecodeError:
                # Look for JSON-like structure with regex
                json_match = re.search(r'(\{.*"transition_needed".*\})', response_text, re.DOTALL)
                if json_match:
                    try:
                        return json.loads(json_match.group(1))
                    except json.JSONDecodeError:
                        pass

                # Try to extract from code blocks
                json_block = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', response_text)
                if json_block:
                    try:
                        return json.loads(json_block.group(1))
                    except json.JSONDecodeError:
                        pass

            # Fallback: manual extraction
            transition_needed = "true" in response_text.lower() and "transition_needed" in response_text.lower()

            # If transition seems needed, extract other fields using the correct key
            if transition_needed:
                workflow_match = re.search(r'"target_workflow"[:\s]+"([^"]+)"', response_text) # Use target_workflow
                confidence_match = re.search(r'"confidence"[:\s]+(0\.\d+|1\.0)', response_text) # Allow 1.0
                reasoning_match = re.search(r'"reasoning"[:\s]+"([^"]+)"', response_text)

                return {
                    "transition_needed": True,
                    "target_workflow": workflow_match.group(1) if workflow_match else None, # Return None if not found
                    "confidence": float(confidence_match.group(1)) if confidence_match else 0.0, # Default 0
                    "reasoning": reasoning_match.group(1) if reasoning_match else "Fallback reasoning"
                }

            # If transition_needed is explicitly false or not found
            return {"transition_needed": False}

        except Exception:
            return None

    async def _format_transition_response(self, transition_request: Dict[str, Any]) -> str:
        """
        Format a response explaining the workflow transition.

        Args:
            transition_request: Details about the workflow transition

        Returns:
            str: Formatted response for the user
        """
        workflow_type = transition_request.get("workflow_type")

        # Prepare transition explanations for different workflows
        transition_explanations = {
            "wheel_generation": "I'd be happy to suggest some activities for you! Let me think about what might be a good fit based on our conversation.",
            "activity_feedback": "I'd like to hear more about your experience with that activity. Let me get some details about how it went.",
            "progress_review": "I'd be glad to help you review your progress. Let me gather some information about your journey so far."
        }

        # Get the appropriate explanation or use a default
        explanation = transition_explanations.get(
            workflow_type,
            "I think I can help you better by shifting our conversation a bit. Let me adjust my approach."
        )

        # Add a smooth transition phrase
        return f"{explanation} I'll adjust my approach to better address your needs."

    async def _process_tool_results_to_response(self, tool_results, tool_calls, user_message):
        """
        Process tool results and generate a natural language response.

        Args:
            tool_results: List of tool execution results
            tool_calls: List of ToolCall objects that were executed
            user_message: The original user message

        Returns:
            str: Natural language response based on tool results
        """
        try:
            # Handle the case where tools were called but failed
            if not tool_results:
                return "I tried to look up some information to help you, but I'm having trouble accessing it right now. Let me try to help you in another way."

            # Process each tool result and generate appropriate responses
            responses = []

            for i, (tool_result, tool_call) in enumerate(zip(tool_results, tool_calls)):
                tool_name = tool_call.tool_name

                # Handle get_user_profile tool specifically
                if tool_name == "get_user_profile":
                    if "error" in tool_result:
                        responses.append("I'm having trouble accessing your profile information right now.")
                    else:
                        user_profile = tool_result.get("user_profile", {})
                        demographics = user_profile.get("demographics", {})

                        if demographics and demographics.get("full_name"):
                            name = demographics.get("full_name")
                            # Respond naturally to the user's question about their name
                            if "name" in user_message.lower():
                                responses.append(f"Your name is {name}! Nice to meet you, {name.split()[0]}.")
                            else:
                                responses.append(f"I can see from your profile that you're {name}.")
                        else:
                            responses.append("I can see your profile, but I don't have your name information available.")

                # Handle other tools generically
                else:
                    if "error" in tool_result:
                        responses.append(f"I tried to use a tool to help you, but encountered an issue.")
                    else:
                        responses.append(f"I've gathered some information that might be helpful.")

            # Combine responses naturally
            if responses:
                return " ".join(responses)
            else:
                return "I've processed your request, but I'm not sure how to respond right now. Could you tell me more about what you're looking for?"

        except Exception as e:
            logger.error(f"Error processing tool results: {e}", exc_info=True)
            return "I tried to help you with that, but I'm having some technical difficulties. How else can I assist you?"

    async def _call_tool(self, tool_code, tool_input):
        """Call a tool and record the usage"""
        try:
            from apps.main.agents.tools.tools_util import execute_tool
            logger.debug(f"Attempting to execute tool '{tool_code}' with input: {tool_input}")

            # CRITICAL FIX: Handle different tool parameter formats
            # Mentor tools expect parameters directly, while some other tools expect input_data wrapping
            mentor_tools = [
                'store_conversation_message',
                'store_conversation_memory',
                'get_conversation_history'
            ]

            if tool_code in mentor_tools:
                # Mentor tools expect parameters directly
                formatted_input = tool_input
            elif isinstance(tool_input, dict) and 'input_data' not in tool_input:
                # Other tools expect input_data wrapping
                formatted_input = {'input_data': tool_input}
            else:
                formatted_input = tool_input

            result = await execute_tool(tool_code, formatted_input, self.run_id)
            logger.debug(f"Tool '{tool_code}' executed successfully.")
            return result
        except Exception as e:
            # Log the specific error and re-raise to ensure benchmark records the failure
            logger.error(f"Error executing tool '{tool_code}': {str(e)}", exc_info=True)
            raise # Re-raise the exception
