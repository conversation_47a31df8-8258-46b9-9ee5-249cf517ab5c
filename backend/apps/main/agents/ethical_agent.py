# apps/main/agents/ethical_agent.py

from typing import Dict, Any, <PERSON><PERSON>
from pydantic import BaseModel

import logging
import pprint
import asyncio
from asgiref.sync import sync_to_async # Import sync_to_async

from apps.main.agents.base_agent import LangGraphAgent
from apps.main.services.database_service import RealDatabaseService
from apps.main.llm.service import RealLLMClient
# Import LLMConfig for type hinting
from apps.main.models import LLMConfig
from typing import Optional # Import Optional

logger = logging.getLogger(__name__)

class EthicalAgent(LangGraphAgent):
    """
    Agent that reviews activities for ethical alignment, ensures
     psychological safety, and validates challenge calibration.
     """

    def __init__(self,
                 user_profile_id: str,
                 db_service=None,
                 llm_client=None,
                 llm_config: Optional[LLMConfig] = None): # Changed to accept LLMConfig
        # Pass LLM config up to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="ethical",
            llm_config=llm_config # Pass the LLMConfig object
        )

        # Use provided dependencies or create defaults
        self.db_service = db_service or RealDatabaseService()

        # Instantiate LLM client using config from base class if not provided externally
        if llm_client:
            self.llm_client = llm_client
        else:
            # RealLLMClient now requires an LLMConfig object
            if not self.llm_config:
                logger.debug(f"Instantiating RealLLMClient for EthicalAgent without LLMConfig: will use default")

            logger.debug(f"Instantiating RealLLMClient for EthicalAgent with LLMConfig: {self.llm_config.name if self.llm_config else 'None'}")
            self.llm_client = RealLLMClient(llm_config=self.llm_config) # Pass the LLMConfig object

        # Initialize definition and tools to None. They will be loaded lazily.
        self.agent_definition = None
        self.available_tools = []

    async def _ensure_loaded(self):
        """Ensures agent definition and tools are loaded asynchronously."""
        if self.agent_definition is not None:
            # Already loaded
            return True

        logger.debug(f"Loading definition and tools for {self.agent_role}...")
        try:
            # Check if load_agent_definition is already async
            if asyncio.iscoroutinefunction(self.db_service.load_agent_definition):
                self.agent_definition = await self.db_service.load_agent_definition(self.agent_role)
            else:
                # Wrap synchronous DB call for definition
                load_def_sync = sync_to_async(self.db_service.load_agent_definition, thread_sensitive=True)
                self.agent_definition = await load_def_sync(self.agent_role)

            if self.agent_definition:
                # Check if load_tools is already async
                if asyncio.iscoroutinefunction(self.db_service.load_tools):
                    self.available_tools = await self.db_service.load_tools(self.agent_definition)
                else:
                    # Wrap synchronous DB call for tools
                    self.available_tools = await self.db_service.load_tools(self.agent_definition)

                logger.debug(f"Successfully loaded definition and {len(self.available_tools)} tools for {self.agent_role}")
                return True
            else:
                logger.error(f"Agent definition not found for role: {self.agent_role}")
                self.agent_definition = None # Ensure it's None if not found
                self.available_tools = []
                return False

        except Exception as e:
            logger.error(f"Failed to load definition/tools for {self.agent_role}: {e}", exc_info=True)
            self.agent_definition = None
            self.available_tools = []
            # Re-raise or handle as appropriate for the process method
            raise RuntimeError(f"Failed to load agent configuration for {self.agent_role}") from e

    async def process(self, state: BaseModel) -> Dict[str, Any]: # Return type changed to Dict
        """Review activities and wheel for ethical alignment and safety."""
        logger.debug(f"EthicalAgent process started. Input state:\n{pprint.pformat(state)}") # Added logging

        # --- Ensure Agent Definition and Tools are Loaded ---
        current_operation = "ensuring_agent_loaded"
        try:
            self.start_stage('ethical_ensure_loaded')
            await self._ensure_loaded()
            self.stop_stage('ethical_ensure_loaded')
            if self.agent_definition is None:
                 raise RuntimeError(f"Agent definition for role '{self.agent_role}' could not be loaded.")
        except Exception as load_error:
            self.stop_stage('ethical_ensure_loaded') # Stop stage even on error
            error_message = f"Critical error: Failed to load agent configuration during '{current_operation}': {str(load_error)}"
            logger.error(error_message, exc_info=True)
            error_output_data = {
                "ethical_validation": {},  # Include empty ethical_validation
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation}
            }
            return {"error": error_message, "output_data": error_output_data}
        # --- End Loading Check ---

        current_operation = "extracting_state"
        # Extract relevant data from state
        context_packet = getattr(state, "context_packet", {})
        psychological_assessment = getattr(state, "psychological_assessment", {}) # Keep
        wheel = getattr(state, "wheel", {}) # Keep
        workflow_id = getattr(state, "workflow_id", None) # Keep

        # Convert user_profile_id to int for DB calls
        # CRITICAL FIX: Don't convert real user IDs that are passed in benchmarks
        try:
            # First, try to convert directly to int (for real user IDs like "2")
            user_profile_id_int = int(self.user_profile_id)
            logger.debug(f"Using real user_profile_id: {user_profile_id_int}")
        except ValueError:
            # Only if conversion fails, check if it's a test/benchmark ID
            if isinstance(self.user_profile_id, str) and (
                self.user_profile_id.startswith('test-user-') or
                self.user_profile_id.startswith('benchmark-user-') or
                self.user_profile_id.startswith('debugger-user-') or
                self.user_profile_id == 'benchmark_user' or
                'test-user-' in self.user_profile_id or
                'debugger-user-' in self.user_profile_id or
                'benchmark' in self.user_profile_id.lower()
            ):
                # For test/benchmark IDs, use a default integer ID
                user_profile_id_int = 1  # Use a default test/benchmark ID
                logger.debug(f"Using default user_profile_id (1) for test/benchmark ID: {self.user_profile_id}")
            else:
                # For real IDs and unit test IDs, try to convert to int
                error_message = f"Invalid user_profile_id format: {self.user_profile_id}"
                logger.error(f"{error_message}. Cannot convert to int for DB.")
                error_output_data = {"error": error_message, "debug": {"last_error": error_message}}
                return {"error": error_message, "output_data": error_output_data}
        except ValueError:
            error_message = f"Invalid user_profile_id format: {self.user_profile_id}"
            logger.error(f"{error_message}. Cannot convert to int for DB.")
            error_output_data = {"error": error_message, "debug": {"last_error": error_message}}
            return {"error": error_message, "output_data": error_output_data}

        current_operation = "starting_run"
        # Start profiling DB start
        self.start_stage('ethical_db_start_run')
        # Start a run in the database
        try:
            # Check if start_run is already async
            if asyncio.iscoroutinefunction(self.db_service.start_run):
                run = await self.db_service.start_run(
                    agent_definition=self.agent_definition, # Now guaranteed loaded
                    user_profile_id=user_profile_id_int, # Use int ID
                    input_data={ # Pass as input_data kwarg
                        "context_packet": context_packet, # Keep these nested
                        "psychological_assessment": psychological_assessment, # Keep these nested
                        "wheel": wheel # Keep these nested
                    },
                    state={"workflow_id": workflow_id} # Pass as state kwarg
                )
            else:
                # Wrap synchronous DB call
                run = await self.db_service.start_run(
                    agent_definition=self.agent_definition, # Now guaranteed loaded
                    user_profile_id=user_profile_id_int, # Use int ID
                    input_data={ # Pass as input_data kwarg
                        "context_packet": context_packet, # Keep these nested
                        "psychological_assessment": psychological_assessment, # Keep these nested
                        "wheel": wheel # Keep these nested
                    },
                    state={"workflow_id": workflow_id} # Pass as state kwarg
                )
            self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id')) # Keep run_id assignment
            logger.debug(f"Started run {self.run_id} for user {user_profile_id_int}")
        except Exception as e:
            error_message = f"Failed to start run: {str(e)}"
            logger.error(error_message)
            error_output_data = {"error": error_message, "debug": {"last_error": error_message}}
            return {"error": error_message, "output_data": error_output_data}
        self.stop_stage('ethical_db_start_run')

        try:
            current_operation = "extracting_activities"
            # Extract activities from wheel
            activities = wheel.get("activities", []) # Keep

            current_operation = "validating_activities"
            # Review each activity for ethical alignment
            self.start_stage('ethical_validate_activities')
            activity_validations = await self._validate_activities(
                activities,
                psychological_assessment
            ) # Keep
            self.stop_stage('ethical_validate_activities')

            current_operation = "validating_wheel"
            # Review wheel composition
            self.start_stage('ethical_validate_wheel')
            wheel_validation = await self._validate_wheel(
                wheel,
                psychological_assessment
            ) # Keep
            self.stop_stage('ethical_validate_wheel')

            current_operation = "identifying_safety"
            # Identify safety considerations
            self.start_stage('ethical_identify_safety')
            safety_considerations = await self._identify_safety_considerations(
                activities,
                psychological_assessment
            ) # Keep
            self.stop_stage('ethical_identify_safety')

            current_operation = "generating_recommendations"
            # Generate modification recommendations
            self.start_stage('ethical_generate_recommendations')
            modification_recommendations = await self._generate_recommendations(
                activity_validations,
                wheel_validation,
                safety_considerations
            ) # Keep
            self.stop_stage('ethical_generate_recommendations')

            current_operation = "documenting_rationales"
            # Document ethical rationales, including trust phase context
            self.start_stage('ethical_document_rationales')
            ethical_rationales = await self._document_rationales(
                activity_validations,
                wheel_validation,
                safety_considerations,
                psychological_assessment  # Pass assessment here # Keep
            ) # Keep
            self.stop_stage('ethical_document_rationales')

            current_operation = "combining_validation"
            # Combine into ethical validation
            self.start_stage('ethical_combine_results')
            ethical_validation = { # Keep structure
                "activity_validations": activity_validations, # Keep
                "wheel_validation": wheel_validation, # Keep
                "safety_considerations": safety_considerations, # Keep
                "modification_recommendations": modification_recommendations, # Keep
                "ethical_rationales": ethical_rationales, # Keep
                "timestamp": context_packet.get("session_timestamp", ""), # Keep
                "user_id": self.user_profile_id # Keep
            } # Keep
            self.stop_stage('ethical_combine_results')

            # Output data including ethical validation and routing
            output_data = { # Keep structure
                "ethical_validation": ethical_validation, # Keep
                "next_agent": "orchestrator" # Keep
            } # Keep

            # No specific state updates needed here, just output_data
            state_updates = {"output_data": output_data} # Keep

            current_operation = "completing_run_success"
            # Start profiling DB complete (success)
            self.start_stage('ethical_db_complete_run')
            # Complete the run
            try:
                # Check if complete_run is already async
                if asyncio.iscoroutinefunction(self.db_service.complete_run):
                    await self.db_service.complete_run(
                        run_id=self.run_id, # Use run_id kwarg
                        output_data=output_data, # Use output_data kwarg
                        state={"workflow_id": workflow_id}, # Use state kwarg
                        status='completed' # Use status kwarg
                    )
                else:
                    # Wrap synchronous DB call
                    await self.db_service.complete_run(
                        run_id=self.run_id, # Use run_id kwarg
                        output_data=output_data, # Use output_data kwarg
                        state={"workflow_id": workflow_id}, # Use state kwarg
                        status='completed' # Use status kwarg
                    )
                logger.debug(f"Completed run {self.run_id} successfully")
            except Exception as e:
                error_message = f"Failed to complete run: {str(e)}"
                logger.error(error_message)
                # Continue execution even if complete_run fails
            self.stop_stage('ethical_db_complete_run')

            # Add run_id to state updates before returning
            state_updates["run_id"] = self.run_id
            logger.debug(f"EthicalAgent returning state updates:\n{pprint.pformat(state_updates)}") # Added logging
            return state_updates # Keep

        except Exception as e:
            error_type = type(e).__name__
            error_message = f"Error in ethical agent during '{current_operation}': {str(e)}"
            logger.error(f"Exception caught in EthicalAgent process ({error_type} during {current_operation}): {error_message}", exc_info=True)

            # Prepare output data including the error, routing info, and empty ethical_validation for persistence
            error_output_data = {
                "ethical_validation": {}, # Include ethical_validation key even on error
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation},
                "forwardTo": "error_handler" # Keep routing info if needed by graph
            }

            # Attempt to complete the run as failed
            try:
                self.start_stage('ethical_db_complete_run_error')
                # Check if complete_run is already async
                if asyncio.iscoroutinefunction(self.db_service.complete_run):
                    await self.db_service.complete_run(
                        run_id=self.run_id,
                        output_data=error_output_data, # Pass error details in output
                        state={"error_details": error_message}, # Simple state
                        status='failed',
                        error_message=error_message # Pass explicit error message if arg exists
                    )
                else:
                    # Wrap synchronous DB call
                    await self.db_service.complete_run(
                        run_id=self.run_id,
                        output_data=error_output_data, # Pass error details in output
                        state={"error_details": error_message}, # Simple state
                        status='failed',
                        error_message=error_message # Pass explicit error message if arg exists
                    )
                self.stop_stage('ethical_db_complete_run_error')
            except Exception as db_error:
                 self.stop_stage('ethical_db_complete_run_error') # Stop stage even on error
                 logger.error(f"Failed to complete run in DB after agent error: {db_error}", exc_info=True)

            # Return the state update for LangGraph, including output_data
            error_updates = {
                "error": error_message,
                "output_data": error_output_data, # Ensure output_data is included
                "forwardTo": "error_handler" # Keep routing info
            }
            if hasattr(self, 'run_id') and self.run_id:
                 error_updates["run_id"] = self.run_id
            return error_updates

    async def _validate_activities(self, activities, psychological_assessment):
        """Validate activities for ethical alignment"""
        activity_validations = []

        for activity in activities:
            try:
                # Call tool to validate activity
                validation = await self._call_tool(
                    "validate_activity_ethics",
                    {
                        "user_profile_id": self.user_profile_id,
                        "activity": activity,
                        "psychological_assessment": psychological_assessment
                    }
                )

                activity_validations.append({
                    "activity_id": activity.get("id", "unknown"),
                    "status": validation.get("status", "Not Approved"),
                    "concerns": validation.get("concerns", []),
                    "principles_assessed": validation.get("principles_assessed", []),
                    "confidence": validation.get("confidence", 0.7)
                })

            except Exception as e:
                # Re-raise the exception to be caught by the main process handler
                raise e

        return activity_validations

    async def _validate_wheel(self, wheel, psychological_assessment):
        """Validate wheel composition for ethical alignment"""
        try:
            # Call tool to validate wheel
            validation = await self._call_tool(
                "validate_wheel_ethics",
                {
                    "user_profile_id": self.user_profile_id,
                    "wheel": wheel,
                    "psychological_assessment": psychological_assessment
                }
            )

            return {
                "status": validation.get("status", "Approved"),
                "domain_balance": validation.get("domain_balance", "Appropriate"),
                "challenge_calibration": validation.get("challenge_calibration", "Appropriate"),
                "concerns": validation.get("concerns", []),
                "principles_assessed": validation.get("principles_assessed", []),
                "confidence": validation.get("confidence", 0.7)
            }

        except Exception as e:
            # Re-raise the exception to be caught by the main process handler
            raise e

    async def _identify_safety_considerations(self, activities, psychological_assessment):
        """Identify safety considerations for the user"""
        try:
            # Call tool to identify safety considerations
            safety = await self._call_tool(
                "identify_safety_considerations",
                {
                    "user_profile_id": self.user_profile_id,
                    "activities": activities,
                    "psychological_assessment": psychological_assessment
                }
            )

            return {
                "vulnerability_areas": safety.get("vulnerability_areas", []),
                "challenge_boundaries": safety.get("challenge_boundaries", {}),
                "trigger_warnings": safety.get("trigger_warnings", []),
                "supervision_recommendations": safety.get("supervision_recommendations", []),
                "confidence": safety.get("confidence", 0.7)
            }

        except Exception as e:
            # Re-raise the exception to be caught by the main process handler
            raise e

    async def _generate_recommendations(self, activity_validations, wheel_validation, safety_considerations):
        """Generate recommendations for modifications if needed"""
        recommendations = []

        # Check for non-approved activities
        for validation in activity_validations:
            if validation.get("status") != "Approved":
                activity_id = validation.get("activity_id", "unknown")
                concerns = validation.get("concerns", [])

                recommendations.append({
                    "type": "activity_modification",
                    "activity_id": activity_id,
                    "concerns": concerns,
                    "suggestion": "Modify or replace activity based on concerns"
                })

        # Check wheel validation
        if wheel_validation.get("status") != "Approved":
            concerns = wheel_validation.get("concerns", [])

            recommendations.append({
                "type": "wheel_modification",
                "concerns": concerns,
                "suggestion": "Adjust wheel composition to address concerns"
            })

        # Check safety considerations
        vulnerability_areas = safety_considerations.get("vulnerability_areas", [])
        if vulnerability_areas:
            recommendations.append({
                "type": "safety_enhancement",
                "vulnerability_areas": vulnerability_areas,
                "suggestion": "Enhance safety measures for identified vulnerability areas"
            })

        return recommendations

    async def _document_rationales(self, activity_validations, wheel_validation, safety_considerations, psychological_assessment):
        """Document ethical rationales for decisions, including trust phase context."""

        # Extract trust phase for rationale
        trust_phase_info = psychological_assessment.get("trust_phase", {})
        trust_phase_name = trust_phase_info.get("phase", "Unknown")
        trust_phase_rationale = f"Validation performed considering the user's current trust phase: {trust_phase_name}."

        rationales = {
            "trust_phase_consideration": trust_phase_rationale, # Added trust phase info
            "ethical_principles": {
                "benevolence": "Activities must support user well-being and growth",
                "fairness": "Challenge calibration must respect user capabilities",
                "transparency": "Instructions and expectations must be clear",
                "autonomy": "User's boundaries must be respected"
            },
            "activity_rationales": {},
            "wheel_rationale": wheel_validation.get("principles_assessed", []),
            "safety_rationale": "Safety considerations address identified vulnerability areas"
        }

        # Add activity-specific rationales
        for validation in activity_validations:
            activity_id = validation.get("activity_id", "unknown")
            principles = validation.get("principles_assessed", [])
            rationales["activity_rationales"][activity_id] = principles

        return rationales

    async def _call_tool(self, tool_code, tool_input):
        """Call a tool and record the usage"""
        try:
            from apps.main.agents.tools.tools_util import execute_tool
            return await execute_tool(tool_code, tool_input, self.run_id)
        except Exception as e:
            # For testing environments, return a mock response
            return {}
