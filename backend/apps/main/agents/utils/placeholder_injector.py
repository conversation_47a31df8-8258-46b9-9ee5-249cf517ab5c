"""
Placeholder injection system for agent instructions.

This module provides functionality to inject context variables into agent instruction
templates using the placeholder system defined in AGENT_INSTRUCTION_PLACEHOLDERS.md.
"""

import logging
import re
from datetime import datetime
from typing import Dict, Any, Optional
from django.utils import timezone
from asgiref.sync import sync_to_async
import pytz

logger = logging.getLogger(__name__)


class PlaceholderInjector:
    """
    Handles injection of context variables into instruction templates.
    """
    
    def __init__(self):
        self.placeholder_pattern = re.compile(r'\{\{([A-Z_][A-Z0-9_]*)\}\}')
    
    def inject_placeholders(self, instruction_template: str, context: Dict[str, Any]) -> str:
        """
        Inject context variables into instruction template.
        
        Args:
            instruction_template: Template string with placeholders
            context: Dictionary containing placeholder values
            
        Returns:
            Instruction string with placeholders replaced
        """
        if not instruction_template:
            return instruction_template
            
        def replace_placeholder(match):
            placeholder_name = match.group(1)
            if placeholder_name in context:
                value = context[placeholder_name]
                # Convert to string, handling None values
                if value is None:
                    return "not specified"
                return str(value)
            else:
                logger.warning(f"Undefined placeholder: {placeholder_name}")
                return match.group(0)  # Return original placeholder
        
        result = self.placeholder_pattern.sub(replace_placeholder, instruction_template)
        
        # Log the injection process for debugging
        placeholders_found = self.placeholder_pattern.findall(instruction_template)
        logger.debug(f"Injected {len(placeholders_found)} placeholders: {placeholders_found}")
        
        return result
    
    def build_context(self, user_profile_id: int, context_packet: Dict[str, Any] = None, 
                     resource_context: Dict[str, Any] = None, 
                     strategy_framework: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Build comprehensive context dictionary for placeholder injection.
        
        Args:
            user_profile_id: User profile ID
            context_packet: Context packet from workflow
            resource_context: Resource context from resource agent
            strategy_framework: Strategy framework from strategy agent
            
        Returns:
            Dictionary containing all placeholder values
        """
        context = {}
        
        try:
            # Import here to avoid circular imports
            from apps.user.models import UserProfile, CurrentMood, UserEnvironment, UserTraitInclination
            from apps.main.models import HistoryEvent
            from django.db.models import Q
            
            # Get user profile
            try:
                user_profile = UserProfile.objects.get(id=user_profile_id)
            except UserProfile.DoesNotExist:
                logger.warning(f"User profile {user_profile_id} not found")
                user_profile = None
            
            # Build temporal context
            now = timezone.now()
            user_timezone = pytz.timezone(getattr(user_profile, 'timezone', 'UTC') if user_profile else 'UTC')
            local_now = now.astimezone(user_timezone)
            
            context.update({
                'LOCAL_DATE': local_now.strftime('%Y-%m-%d'),
                'LOCAL_TIME': local_now.strftime('%H:%M'),
                'LOCAL_DATETIME': local_now.strftime('%Y-%m-%d %H:%M'),
                'DAY_OF_WEEK': local_now.strftime('%A'),
                'TIME_OF_DAY': self._get_time_of_day(local_now.hour),
            })
            
            # Build user profile context
            if user_profile:
                context.update({
                    'USER_NAME': user_profile.profile_name or "User",
                    'USER_AGE': getattr(user_profile, 'age', 'not specified'),
                    'USER_LOCATION': getattr(user_profile, 'location', 'not specified'),
                    'PROFILE_COMPLETION': f"{getattr(user_profile, 'completion_percentage', 0)}%",
                })
            
            # Build psychological context
            if user_profile:
                try:
                    current_mood = CurrentMood.objects.filter(user_profile=user_profile).first()
                    if current_mood:
                        # Use the actual field names from the model
                        mood_description = current_mood.description or 'neutral'
                        # Extract mood keywords from description
                        mood_keywords = ['excited', 'anxious', 'creative', 'inspired', 'impatient']
                        detected_mood = 'neutral'
                        for keyword in mood_keywords:
                            if keyword.lower() in mood_description.lower():
                                detected_mood = keyword
                                break

                        context.update({
                            'CURRENT_MOOD': detected_mood,
                            'ENERGY_LEVEL': f"height {current_mood.height}/100" if current_mood.height else 'moderate',
                            'STRESS_LEVEL': 'moderate',  # Default since not in model
                        })
                except Exception as e:
                    logger.warning(f"Could not load current mood: {e}")

                # Get trait inclinations
                try:
                    traits = UserTraitInclination.objects.filter(user_profile=user_profile)
                    trait_dict = {}
                    trait_names = []

                    for trait in traits:
                        trait_name = trait.generic_trait.name if trait.generic_trait else 'unknown'
                        trait_strength = trait.strength or 0
                        trait_dict[trait_name.lower()] = trait_strength
                        if trait_strength > 60:  # High strength traits
                            trait_names.append(trait_name)

                    context.update({
                        'DOMINANT_TRAITS': ', '.join(trait_names) if trait_names else 'balanced',
                        'TRAIT_OPENNESS': self._format_trait(trait_dict.get('openness')),
                        'TRAIT_CONSCIENTIOUSNESS': self._format_trait(trait_dict.get('conscientiousness')),
                        'TRAIT_EXTRAVERSION': self._format_trait(trait_dict.get('extraversion')),
                        'TRAIT_AGREEABLENESS': self._format_trait(trait_dict.get('agreeableness')),
                        'TRAIT_NEUROTICISM': self._format_trait(trait_dict.get('neuroticism')),
                        'TRAIT_HONESTY_HUMILITY': self._format_trait(trait_dict.get('honesty_humility')),
                    })
                except Exception as e:
                    logger.warning(f"Could not load trait inclinations: {e}")
            
            # Build environmental context from resource_context
            if resource_context:
                env_analysis = resource_context.get('environment_analysis', {})
                context.update({
                    'CURRENT_ENVIRONMENT': env_analysis.get('environment_description', 'not specified'),
                    'ENVIRONMENT_TYPE': env_analysis.get('environment_type', 'not specified'),
                    'PRIVACY_LEVEL': env_analysis.get('privacy_level', 'not specified'),
                    'SPACE_SIZE': env_analysis.get('space_size', 'not specified'),
                    'NOISE_LEVEL': env_analysis.get('noise_level', 'not specified'),
                    'SOCIAL_CONTEXT': env_analysis.get('social_context', 'not specified'),
                })
                
                # Time and resource context
                time_analysis = resource_context.get('time_analysis', {})
                resource_analysis = resource_context.get('resource_analysis', {})
                
                context.update({
                    'TIME_AVAILABLE': str(time_analysis.get('duration_minutes', 'not specified')),
                    'TIME_FLEXIBILITY': time_analysis.get('flexibility_level', 'not specified'),
                    'AVAILABLE_RESOURCES': ', '.join(resource_analysis.get('inventory_items', [])),
                    'PHYSICAL_LIMITATIONS': ', '.join(resource_analysis.get('physical_limitations', [])),
                    'COGNITIVE_LIMITATIONS': ', '.join(resource_analysis.get('cognitive_limitations', [])),
                    'CAPABILITIES': ', '.join(resource_analysis.get('capabilities', [])),
                })
            
            # Build goal and aspiration context from context_packet
            if context_packet:
                context.update({
                    'PRIMARY_GOALS': context_packet.get('primary_goals', 'not specified'),
                    'CURRENT_ASPIRATIONS': context_packet.get('aspirations', 'not specified'),
                    'FOCUS_AREAS': context_packet.get('focus_areas', 'not specified'),
                    'GROWTH_PRIORITIES': context_packet.get('growth_priorities', 'not specified'),
                })
            
            # Build trust and relationship context
            if user_profile:
                try:
                    from apps.user.models import TrustLevel
                    trust_level = TrustLevel.objects.filter(user_profile=user_profile).first()
                    if trust_level:
                        context.update({
                            'TRUST_LEVEL': f"{trust_level.value}/100" if trust_level.value else 'building',
                            'TRUST_PHASE': trust_level.aggregate_type or 'foundation',
                        })
                except Exception as e:
                    logger.warning(f"Could not load trust level: {e}")
            
            # Add fallback values for any missing critical placeholders
            fallbacks = {
                'USER_NAME': 'User',
                'CURRENT_MOOD': 'neutral',
                'ENERGY_LEVEL': 'moderate',
                'LOCAL_DATE': now.strftime('%Y-%m-%d'),
                'LOCAL_TIME': now.strftime('%H:%M'),
                'TIME_AVAILABLE': 'flexible',
                'CURRENT_ENVIRONMENT': 'comfortable space',
                'TRUST_PHASE': 'foundation',
            }
            
            for key, fallback_value in fallbacks.items():
                if key not in context or not context[key] or context[key] == 'not specified':
                    context[key] = fallback_value
            
        except Exception as e:
            logger.error(f"Error building context: {e}", exc_info=True)
            # Provide minimal context to prevent complete failure
            context = {
                'USER_NAME': 'User',
                'LOCAL_DATE': timezone.now().strftime('%Y-%m-%d'),
                'LOCAL_TIME': timezone.now().strftime('%H:%M'),
                'CURRENT_MOOD': 'neutral',
                'ENERGY_LEVEL': 'moderate',
            }
        
        logger.debug(f"Built context with {len(context)} placeholders")
        return context

    async def build_context_async(self, user_profile_id: int, context_packet: Dict[str, Any] = None,
                                 resource_context: Dict[str, Any] = None,
                                 strategy_framework: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Async version of build_context for use in async contexts.

        Args:
            user_profile_id: User profile ID
            context_packet: Context packet from workflow
            resource_context: Resource context from resource agent
            strategy_framework: Strategy framework from strategy agent

        Returns:
            Dictionary containing all placeholder values
        """
        context = {}

        try:
            # Import here to avoid circular imports
            from apps.user.models import UserProfile, CurrentMood, UserEnvironment, UserTraitInclination
            from apps.main.models import HistoryEvent
            from django.db.models import Q

            # Get user profile (async)
            try:
                user_profile = await sync_to_async(UserProfile.objects.get)(id=user_profile_id)
            except UserProfile.DoesNotExist:
                logger.warning(f"User profile {user_profile_id} not found")
                user_profile = None

            # Build temporal context
            now = timezone.now()
            user_timezone = pytz.timezone(getattr(user_profile, 'timezone', 'UTC') if user_profile else 'UTC')
            local_now = now.astimezone(user_timezone)

            context.update({
                'LOCAL_DATE': local_now.strftime('%Y-%m-%d'),
                'LOCAL_TIME': local_now.strftime('%H:%M'),
                'LOCAL_DATETIME': local_now.strftime('%Y-%m-%d %H:%M'),
                'DAY_OF_WEEK': local_now.strftime('%A'),
                'TIME_OF_DAY': self._get_time_of_day(local_now.hour),
            })

            # Build user profile context
            if user_profile:
                context.update({
                    'USER_NAME': user_profile.profile_name or "User",
                    'USER_AGE': getattr(user_profile, 'age', 'not specified'),
                    'USER_LOCATION': getattr(user_profile, 'location', 'not specified'),
                    'PROFILE_COMPLETION': f"{getattr(user_profile, 'completion_percentage', 0)}%",
                })

            # Build psychological context (async)
            if user_profile:
                try:
                    current_mood = await sync_to_async(CurrentMood.objects.filter(user_profile=user_profile).first)()
                    if current_mood:
                        # Use the actual field names from the model
                        mood_description = current_mood.description or 'neutral'
                        # Extract mood keywords from description
                        mood_keywords = ['excited', 'anxious', 'creative', 'inspired', 'impatient']
                        detected_mood = 'neutral'
                        for keyword in mood_keywords:
                            if keyword.lower() in mood_description.lower():
                                detected_mood = keyword
                                break

                        context.update({
                            'CURRENT_MOOD': detected_mood,
                            'ENERGY_LEVEL': f"height {current_mood.height}/100" if current_mood.height else 'moderate',
                            'STRESS_LEVEL': 'moderate',  # Default since not in model
                        })
                except Exception as e:
                    logger.warning(f"Could not load current mood: {e}")

                # Get trait inclinations (async)
                try:
                    traits = await sync_to_async(list)(UserTraitInclination.objects.filter(user_profile=user_profile).select_related('generic_trait'))
                    trait_dict = {}
                    trait_names = []

                    for trait in traits:
                        trait_name = trait.generic_trait.name if trait.generic_trait else 'unknown'
                        trait_strength = trait.strength or 0
                        trait_dict[trait_name.lower()] = trait_strength
                        if trait_strength > 60:  # High strength traits
                            trait_names.append(trait_name)

                    context.update({
                        'DOMINANT_TRAITS': ', '.join(trait_names) if trait_names else 'balanced',
                        'TRAIT_OPENNESS': self._format_trait(trait_dict.get('openness')),
                        'TRAIT_CONSCIENTIOUSNESS': self._format_trait(trait_dict.get('conscientiousness')),
                        'TRAIT_EXTRAVERSION': self._format_trait(trait_dict.get('extraversion')),
                        'TRAIT_AGREEABLENESS': self._format_trait(trait_dict.get('agreeableness')),
                        'TRAIT_NEUROTICISM': self._format_trait(trait_dict.get('neuroticism')),
                        'TRAIT_HONESTY_HUMILITY': self._format_trait(trait_dict.get('honesty_humility')),
                    })
                except Exception as e:
                    logger.warning(f"Could not load trait inclinations: {e}")

            # Build environmental context from resource_context
            if resource_context:
                env_analysis = resource_context.get('environment_analysis', {})
                context.update({
                    'CURRENT_ENVIRONMENT': env_analysis.get('environment_description', 'not specified'),
                    'ENVIRONMENT_TYPE': env_analysis.get('environment_type', 'not specified'),
                    'PRIVACY_LEVEL': env_analysis.get('privacy_level', 'not specified'),
                    'SPACE_SIZE': env_analysis.get('space_size', 'not specified'),
                    'NOISE_LEVEL': env_analysis.get('noise_level', 'not specified'),
                    'SOCIAL_CONTEXT': env_analysis.get('social_context', 'not specified'),
                })

                # Time and resource context
                time_analysis = resource_context.get('time_analysis', {})
                resource_analysis = resource_context.get('resource_analysis', {})

                context.update({
                    'TIME_AVAILABLE': str(time_analysis.get('duration_minutes', 'not specified')),
                    'TIME_FLEXIBILITY': time_analysis.get('flexibility_level', 'not specified'),
                    'AVAILABLE_RESOURCES': ', '.join(resource_analysis.get('inventory_items', [])),
                    'PHYSICAL_LIMITATIONS': ', '.join(resource_analysis.get('physical_limitations', [])),
                    'COGNITIVE_LIMITATIONS': ', '.join(resource_analysis.get('cognitive_limitations', [])),
                    'CAPABILITIES': ', '.join(resource_analysis.get('capabilities', [])),
                })

            # Build goal and aspiration context from context_packet
            if context_packet:
                context.update({
                    'PRIMARY_GOALS': context_packet.get('primary_goals', 'not specified'),
                    'CURRENT_ASPIRATIONS': context_packet.get('aspirations', 'not specified'),
                    'FOCUS_AREAS': context_packet.get('focus_areas', 'not specified'),
                    'GROWTH_PRIORITIES': context_packet.get('growth_priorities', 'not specified'),
                })

            # Build trust and relationship context (async)
            if user_profile:
                try:
                    from apps.user.models import TrustLevel
                    trust_level = await sync_to_async(TrustLevel.objects.filter(user_profile=user_profile).first)()
                    if trust_level:
                        context.update({
                            'TRUST_LEVEL': f"{trust_level.value}/100" if trust_level.value else 'building',
                            'TRUST_PHASE': trust_level.aggregate_type or 'foundation',
                        })
                except Exception as e:
                    logger.warning(f"Could not load trust level: {e}")

            # Add fallback values for any missing critical placeholders
            fallbacks = {
                'USER_NAME': 'User',
                'CURRENT_MOOD': 'neutral',
                'ENERGY_LEVEL': 'moderate',
                'LOCAL_DATE': now.strftime('%Y-%m-%d'),
                'LOCAL_TIME': now.strftime('%H:%M'),
                'TIME_AVAILABLE': 'flexible',
                'CURRENT_ENVIRONMENT': 'comfortable space',
                'TRUST_PHASE': 'foundation',
            }

            for key, fallback_value in fallbacks.items():
                if key not in context or not context[key] or context[key] == 'not specified':
                    context[key] = fallback_value

        except Exception as e:
            logger.error(f"Error building context: {e}", exc_info=True)
            # Provide minimal context to prevent complete failure
            context = {
                'USER_NAME': 'User',
                'LOCAL_DATE': timezone.now().strftime('%Y-%m-%d'),
                'LOCAL_TIME': timezone.now().strftime('%H:%M'),
                'CURRENT_MOOD': 'neutral',
                'ENERGY_LEVEL': 'moderate',
            }

        logger.debug(f"Built context with {len(context)} placeholders")
        return context

    def _get_time_of_day(self, hour: int) -> str:
        """Get time of day description from hour."""
        if 5 <= hour < 12:
            return 'morning'
        elif 12 <= hour < 17:
            return 'afternoon'
        elif 17 <= hour < 21:
            return 'evening'
        else:
            return 'night'
    
    def _format_trait(self, score: Optional[float]) -> str:
        """Format trait score for display."""
        if score is None:
            return 'not assessed'
        
        if score < 0.3:
            return f'low ({score:.1f})'
        elif score < 0.7:
            return f'moderate ({score:.1f})'
        else:
            return f'high ({score:.1f})'


# Global instance for easy access
placeholder_injector = PlaceholderInjector()
