# apps/main/agents/wheel_activity_agent.py

import logging # Import logging
from typing import Dict, Any, Tu<PERSON>, Optional # Import Optional
from pydantic import BaseModel
from asgiref.sync import sync_to_async # Import sync_to_async
from typing import TYPE_CHECKING # Import TYPE_CHECKING

from apps.main.agents.base_agent import LangGraphAgent
from apps.main.agents.tools.activity_tools import _get_activity_color
from apps.main.agents.utils.placeholder_injector import placeholder_injector
# Defer imports that might depend on Django models
# from apps.main.services.database_service import RealDatabaseService
# from apps.main.llm.service import RealLLMClient
# Import LLMConfig for type hinting


logger = logging.getLogger(__name__) # Add logger

# Use TYPE_CHECKING block for type hints if needed
if TYPE_CHECKING:
    from apps.main.services.database_service import RealDatabaseService
    from apps.main.llm.service import RealLL<PERSON>lient
    from apps.main.agents.tools.tools_util import ToolExecutionError
    from apps.main.models import LLMConfig

class WheelAndActivityAgent(LangGraphAgent):
    """
     Agent that selects activities based on the strategy framework and
      constructs the wheel with appropriate probability distributions.
      """
    def __init__(self, # Removed leading space
                 user_profile_id: str,
                 db_service=None,
                  llm_client=None,
                  llm_config: Optional['LLMConfig'] = None): # Changed to accept LLMConfig, use string literal
        # Pass LLM config up to the base class constructor
        super().__init__(
            user_profile_id=user_profile_id,
            agent_role="activity",
            llm_config=llm_config # Pass the LLMConfig object
        )

        # Import services here, inside __init__
        from apps.main.services.database_service import RealDatabaseService
        from apps.main.llm.service import RealLLMClient

        # Use provided dependencies or create defaults
        self.db_service = db_service or RealDatabaseService()

        # Instantiate LLM client using config from base class if not provided externally
        if llm_client:
            self.llm_client = llm_client
        else:
            # RealLLMClient now requires an LLMConfig object
            if not self.llm_config:
                logger.debug(f"Instantiating RealLLMClient for WheelAndActivityAgent without LLMConfig: will use default")

            logger.debug(f"Instantiating RealLLMClient for WheelAndActivityAgent with LLMConfig: {self.llm_config.name if self.llm_config else 'None'}")
            self.llm_client = RealLLMClient(llm_config=self.llm_config) # Pass the LLMConfig object

        # Initialize definition and tools to None. They will be loaded lazily.
        self.agent_definition = None
        self.available_tools = []
        self.run_id = None # Initialize run_id
        self.contextualized_instructions = None  # Store contextualized instructions

    async def _ensure_loaded(self):
        """Ensures agent definition and tools are loaded asynchronously."""
        if self.agent_definition is not None:
            # Already loaded
            return True

        logger.debug(f"Loading definition and tools for {self.agent_role}...")
        try:
            # Wrap synchronous DB call for definition
            load_def_sync = sync_to_async(self.db_service.load_agent_definition, thread_sensitive=True)

            self.agent_definition = await load_def_sync(self.agent_role)
            if self.agent_definition:
                # Wrap synchronous DB call for tools
                self.available_tools = await self.db_service.load_tools(self.agent_definition)
                logger.debug(f"Successfully loaded definition and {len(self.available_tools)} tools for {self.agent_role}")
                return True
            else:
                logger.error(f"Agent definition not found for role: {self.agent_role}")
                self.agent_definition = None # Ensure it's None if not found
                self.available_tools = []
                return False

        except Exception as e:
            logger.error(f"Failed to load definition/tools for {self.agent_role}: {e}", exc_info=True)
            self.agent_definition = None
            self.available_tools = []
            # Re-raise or handle as appropriate for the process method
            raise RuntimeError(f"Failed to load agent configuration for {self.agent_role}") from e

    def _contextualize_instructions(self, context_packet: Dict[str, Any] = None,
                                  resource_context: Dict[str, Any] = None,
                                  strategy_framework: Dict[str, Any] = None) -> str:
        """
        Contextualize agent instructions by injecting placeholders with user-specific data.

        Args:
            context_packet: Context packet from workflow
            resource_context: Resource context from resource agent
            strategy_framework: Strategy framework from strategy agent

        Returns:
            Contextualized instruction string with placeholders replaced
        """
        if not self.agent_definition or not self.agent_definition.system_instructions:
            logger.warning("No agent definition or system instructions available for contextualization")
            return "No instructions available"

        try:
            # Convert user_profile_id to int for context building
            user_profile_id_int = int(self.user_profile_id) if isinstance(self.user_profile_id, str) and self.user_profile_id.isdigit() else 1

            # Build comprehensive context for placeholder injection
            context = placeholder_injector.build_context(
                user_profile_id=user_profile_id_int,
                context_packet=context_packet,
                resource_context=resource_context,
                strategy_framework=strategy_framework
            )

            # Inject placeholders into system instructions
            contextualized = placeholder_injector.inject_placeholders(
                self.agent_definition.system_instructions,
                context
            )

            # Log the complete contextualized instructions for debugging
            logger.info(f"🎯 WHEEL ACTIVITY AGENT CONTEXTUALIZED INSTRUCTIONS for user {self.user_profile_id}:")
            logger.info(f"📋 FULL INSTRUCTIONS SENT TO LLM:")
            logger.info("=" * 80)
            logger.info(contextualized)
            logger.info("=" * 80)

            self.contextualized_instructions = contextualized
            return contextualized

        except Exception as e:
            logger.error(f"Error contextualizing instructions: {e}", exc_info=True)
            # Return original instructions as fallback
            return self.agent_definition.system_instructions

    async def process(self, state: BaseModel) -> Dict[str, Any]: # Return type changed
        """Select activities and construct the wheel based on strategy framework."""
        logger.info(f"🚀 WheelAndActivityAgent STARTING for user {self.user_profile_id}")
        # --- Ensure Agent Definition and Tools are Loaded ---
        current_operation = "ensuring_agent_loaded"
        try:
            self.start_stage('activity_ensure_loaded')
            await self._ensure_loaded()
            self.stop_stage('activity_ensure_loaded')
            if self.agent_definition is None:
                 raise RuntimeError(f"Agent definition for role '{self.agent_role}' could not be loaded.")
        except Exception as load_error:
            self.stop_stage('activity_ensure_loaded') # Stop stage even on error
            error_message = f"Critical error: Failed to load agent configuration during '{current_operation}': {str(load_error)}"
            logger.error(error_message, exc_info=True)
            # Include a fallback wheel structure to maintain the output contract
            error_output_data = {
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation},
                "wheel": {
                    "metadata": {"name": "Error Wheel", "trust_phase": "Foundation", "error": error_message},
                    "items": [],
                    "activities": [],
                    "value_propositions": {}
                },
                "next_agent": "error_handler"
            }
            return {"error": error_message, "output_data": error_output_data}
        # --- End Loading Check ---

        current_operation = "extracting_state"
        # Extract relevant data from state
        context_packet = getattr(state, "context_packet", {})
        resource_context = getattr(state, "resource_context", {})
        strategy_framework = getattr(state, "strategy_framework", {})
        workflow_id = getattr(state, "workflow_id", None)

        # Debug logging to understand what we're receiving
        logger.debug(f"Activity agent extracting state:")
        logger.debug(f"  context_packet type: {type(context_packet)}, empty: {not context_packet}")
        logger.debug(f"  resource_context type: {type(resource_context)}, empty: {not resource_context}")
        logger.debug(f"  strategy_framework type: {type(strategy_framework)}, is None: {strategy_framework is None}")
        if strategy_framework:
            logger.debug(f"  strategy_framework keys: {list(strategy_framework.keys()) if isinstance(strategy_framework, dict) else 'not a dict'}")

        # Contextualize instructions with user-specific data
        logger.info("🎯 Contextualizing agent instructions with user-specific data...")
        self._contextualize_instructions(context_packet, resource_context, strategy_framework)

        # Check if strategy_framework is None and try to get it from output_data
        if strategy_framework is None:
            logger.warning("Activity agent: strategy_framework is None, checking state.output_data")
            output_data = getattr(state, "output_data", {})
            if output_data and isinstance(output_data, dict):
                strategy_framework = output_data.get("strategy_framework")
                logger.debug(f"  Found strategy_framework in output_data: {strategy_framework is not None}")
                if strategy_framework:
                    logger.debug(f"  strategy_framework from output_data keys: {list(strategy_framework.keys()) if isinstance(strategy_framework, dict) else 'not a dict'}")
            else:
                logger.warning(f"  state.output_data is empty or not a dict: {type(output_data)}")

        # If still None, provide a fallback
        if strategy_framework is None:
            logger.error("Activity agent: strategy_framework is still None after all attempts, using empty dict fallback")
            strategy_framework = {}

        # Convert user_profile_id to int for DB calls
        # CRITICAL FIX: Don't convert real user IDs that are passed in benchmarks
        try:
            # First, try to convert directly to int (for real user IDs like "2")
            user_profile_id_int = int(self.user_profile_id)
            logger.debug(f"Using real user_profile_id: {user_profile_id_int}")
        except ValueError:
            # Only if conversion fails, check if it's a test/benchmark ID
            if isinstance(self.user_profile_id, str) and (
                self.user_profile_id.startswith('test-user-') or
                self.user_profile_id.startswith('benchmark-user-') or
                self.user_profile_id.startswith('debugger-user-') or
                self.user_profile_id == 'benchmark_user' or
                'test-user-' in self.user_profile_id or
                'debugger-user-' in self.user_profile_id or
                'benchmark' in self.user_profile_id.lower()
            ):
                # For test/benchmark IDs, use a default integer ID
                user_profile_id_int = 1  # Use a default test/benchmark ID
                logger.debug(f"Using default user_profile_id (1) for test/benchmark ID: {self.user_profile_id}")
            else:
                # For real IDs and unit test IDs, try to convert to int
                error_message = f"Invalid user_profile_id format: {self.user_profile_id}"
                logger.error(f"{error_message}. Cannot convert to int for DB.")
                error_output_data = {"error": error_message, "debug": {"last_error": error_message}}
                return {"error": error_message, "output_data": error_output_data}
        except ValueError:
            error_message = f"Invalid user_profile_id format: {self.user_profile_id}"
            logger.error(f"{error_message}. Cannot convert to int for DB.")
            # Include a fallback wheel structure to maintain the output contract
            error_output_data = {
                "error": error_message,
                "debug": {"last_error": error_message},
                "wheel": {
                    "metadata": {"name": "Error Wheel", "trust_phase": "Foundation", "error": error_message},
                    "items": [],
                    "activities": [],
                    "value_propositions": {}
                },
                "next_agent": "error_handler"
            }
            return {"error": error_message, "output_data": error_output_data}

        current_operation = "starting_run"
        # Start profiling DB start
        self.start_stage('activity_db_start_run')
        # Start a run in the database - WRAPPED
        run = await self.db_service.start_run(
            agent_definition=self.agent_definition, # Now guaranteed loaded
            user_profile_id=user_profile_id_int, # Use int ID
            input_data={ # Pass as input_data kwarg
                "context_packet": context_packet,
                "resource_context": resource_context,
                "strategy_framework": strategy_framework
            },
            state={"workflow_id": workflow_id} # Pass as state kwarg
        )
        self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id'))
        self.stop_stage('activity_db_start_run')

        try:
            current_operation = "querying_catalog"
            # Query activity catalog based on strategy
            self.start_stage('activity_query_catalog')
            candidate_activities = await self._query_activity_catalog(strategy_framework, context_packet)
            self.stop_stage('activity_query_catalog')

            current_operation = "tailoring_activities"
            # Create tailored activities
            self.start_stage('activity_tailor_activities')
            tailored_activities = await self._create_tailored_activities(
                candidate_activities,
                resource_context,
                context_packet,
                strategy_framework
            )
            self.stop_stage('activity_tailor_activities')

            current_operation = "assigning_weights"
            # Assign probability weights
            self.start_stage('activity_assign_weights')
            wheel_items = await self._assign_probability_weights(
                tailored_activities,
                strategy_framework
            )
            self.stop_stage('activity_assign_weights')

            current_operation = "creating_metadata"
            # Create wheel metadata
            self.start_stage('activity_create_metadata')
            wheel_metadata = await self._create_wheel_metadata(
                context_packet,
                strategy_framework
            )
            self.stop_stage('activity_create_metadata')

            current_operation = "creating_value_props"
            # Create value propositions
            self.start_stage('activity_create_value_props')
            value_propositions = await self._create_value_propositions(
                tailored_activities,
                strategy_framework
            )
            self.stop_stage('activity_create_value_props')

            current_operation = "combining_results"
            # Combine into wheel package
            self.start_stage('activity_combine_results')
            wheel = {
                "metadata": wheel_metadata,
                "items": wheel_items,
                "activities": tailored_activities,
                "value_propositions": value_propositions,
                "timestamp": context_packet.get("session_timestamp", ""),
                "user_id": self.user_profile_id
            }
            self.stop_stage('activity_combine_results')

            # Validate wheel data structure before returning
            self.start_stage('activity_validate_wheel')
            wheel = self._validate_wheel_data(wheel)
            self.stop_stage('activity_validate_wheel')

            # CRITICAL FIX: Save wheel to database using generate_wheel tool
            current_operation = "saving_wheel_to_database"
            logger.info(f"🔥 CRITICAL DEBUG: About to save wheel to database for user {user_profile_id_int}")
            self.start_stage('activity_save_wheel_db')
            try:
                # Prepare strategy framework as dictionary (tool expects dict, not string)
                strategy_framework_dict = {
                    "name": strategy_framework if isinstance(strategy_framework, str) else "balanced",
                    "domain_distribution": {
                        "physical": 25,
                        "mental": 25,
                        "social": 25,
                        "creative": 25
                    },
                    "challenge_calibration": {
                        "physical": 0,
                        "mental": 0,
                        "social": 0,
                        "creative": 0
                    }
                }

                # Call generate_wheel tool to persist wheel data to database
                # The generate_wheel tool expects input_data as a single parameter
                wheel_input_data = {
                    "user_profile_id": user_profile_id_int,
                    "strategy_framework": strategy_framework_dict,
                    "activity_count": len(tailored_activities)
                }
                tool_params = {"input_data": wheel_input_data}
                logger.info(f"🔥 CRITICAL DEBUG: Calling generate_wheel with params: {tool_params}")
                wheel_save_result = await self._call_tool(
                    "generate_wheel",
                    tool_params
                )

                # Update wheel with database information if successful
                if wheel_save_result and "wheel" in wheel_save_result:
                    saved_wheel = wheel_save_result["wheel"]
                    wheel["database_id"] = saved_wheel.get("id")
                    wheel["database_saved"] = True
                    logger.info(f"Successfully saved wheel to database with ID: {saved_wheel.get('id')}")
                else:
                    logger.warning("Wheel save to database returned no wheel data, continuing with in-memory wheel")
                    wheel["database_saved"] = False

            except Exception as e:
                logger.error(f"Failed to save wheel to database: {e}", exc_info=True)
                # Continue with in-memory wheel but mark as not saved
                wheel["database_saved"] = False
                wheel["database_error"] = str(e)

            self.stop_stage('activity_save_wheel_db')

            # Output data including wheel and routing
            output_data = {
                "wheel": wheel,
                "next_agent": "ethical"
            }

            # Prepare state updates dictionary
            state_updates = {"output_data": output_data}

            current_operation = "completing_run_success"
            # Start profiling DB complete (success)
            self.start_stage('activity_db_complete_run')
            # Complete the run - WRAPPED
            await self.db_service.complete_run(
                run_id=self.run_id, # Use run_id kwarg
                output_data=output_data, # Use output_data kwarg
                state={"workflow_id": workflow_id}, # Use state kwarg
                status='completed' # Use status kwarg
            )
            self.stop_stage('activity_db_complete_run')

            # Add run_id to state updates before returning
            state_updates["run_id"] = self.run_id
            logger.debug(f"WheelAndActivityAgent returning state updates: {state_updates}")
            return state_updates

        except Exception as e:
            error_type = type(e).__name__
            error_message = f"Error in wheel/activity agent during '{current_operation}': {str(e)}"
            logger.error(f"Exception caught in WheelAndActivityAgent process ({error_type} during {current_operation}): {error_message}", exc_info=True)

            # Prepare output data including the error and routing info for persistence
            # Also include a fallback wheel structure to maintain the output contract
            error_output_data = {
                "error": error_message,
                "debug": {"last_error": error_message, "failed_operation": current_operation},
                "forwardTo": "error_handler", # Keep routing info if needed by graph
                "wheel": {
                    "metadata": {"name": "Error Wheel", "trust_phase": "Foundation", "error": error_message},
                    "items": [],
                    "activities": [],
                    "value_propositions": {}
                },
                "next_agent": "error_handler"
            }

            # Attempt to complete the run as failed - WRAPPED
            try:
                current_operation = "completing_run_failure"
                self.start_stage('activity_db_complete_run_error')
                await self.db_service.complete_run(
                    run_id=self.run_id,
                    output_data=error_output_data, # Pass error details in output
                    state={"error_details": error_message}, # Simple state
                    status='failed',
                    error_message=error_message # Pass explicit error message if arg exists
                )
                self.stop_stage('activity_db_complete_run_error')
            except Exception as db_error:
                 self.stop_stage('activity_db_complete_run_error') # Stop stage even on error
                 logger.error(f"Failed to complete run in DB after agent error: {db_error}", exc_info=True)

            # Return the state update for LangGraph, including output_data
            error_updates = {
                "error": error_message,
                "output_data": error_output_data, # Ensure output_data is included
                "forwardTo": "error_handler" # Keep routing info
            }
            if hasattr(self, 'run_id') and self.run_id:
                 error_updates["run_id"] = self.run_id

            # Validate that wheel is present in the output data
            if "wheel" not in error_output_data:
                logger.warning("Wheel data missing in error output, adding fallback wheel structure")
                error_output_data["wheel"] = {
                    "metadata": {"name": "Error Wheel", "trust_phase": "Foundation", "error": error_message},
                    "items": [],
                    "activities": [],
                    "value_propositions": {}
                }
                error_updates["output_data"] = error_output_data

            return error_updates

    async def _query_activity_catalog(self, strategy_framework, context_packet=None):
        """Query the activity catalog based on strategy framework"""
        try:
            # Extract selection criteria from strategy, ensuring it's a dict
            selection_criteria = strategy_framework.get("selection_criteria") or {}

            # Extract domains from domain_distribution in strategy framework
            domain_distribution = strategy_framework.get("domain_distribution", {})
            domains = []
            if isinstance(domain_distribution, dict) and "domains" in domain_distribution:
                domains = list(domain_distribution["domains"].keys())

            logger.debug(f"Extracted domains for activity selection: {domains}")

            # Call tool to query catalog
            activity_results = await self._call_tool(
                "query_activity_catalog",
                {
                    "user_profile_id": self.user_profile_id,
                    "domains": domains,
                    "trait_requirements": selection_criteria.get("trait_requirements", {}),
                    "duration_range": selection_criteria.get("duration_range", {}),
                    "resource_constraints": selection_criteria.get("resource_constraints", []),
                    "limit": 15,  # Get more than needed for diversity
                    "context_packet": context_packet  # Pass context packet for workflow origin detection
                }
            )
            # Ensure the result is a list, even if the key is missing or None
            return activity_results.get("candidate_activities") or []

        except Exception as e:
            # Return minimal results if tool fails
            return []

    async def _create_tailored_activities(self, candidate_activities, resource_context, context_packet, strategy_framework=None):
        """Create tailored activities from generic activities"""
        tailored_activities = []

        # Ensure candidate_activities is a list
        if not isinstance(candidate_activities, list):
            logger.warning(f"candidate_activities is not a list: {candidate_activities}")
            candidate_activities = []

        # If no candidate activities, create multiple default activities across domains
        if not candidate_activities:
            logger.warning("No candidate activities available, creating diverse default activities")
            # Create activities for each domain from the strategy framework
            strategy_domains = []
            if isinstance(strategy_framework, dict):
                domain_distribution = strategy_framework.get("domain_distribution", {})
                if isinstance(domain_distribution, dict) and "domains" in domain_distribution:
                    strategy_domains = list(domain_distribution["domains"].keys())

            # If no domains from strategy, use default balanced set
            if not strategy_domains:
                strategy_domains = ["creative", "physical", "intellectual", "social", "emotional"]

            logger.debug(f"Creating default activities for domains: {strategy_domains}")

            # Create one activity per domain to ensure variety
            default_activities_templates = {
                "creative": {
                    "name": "Creative Expression",
                    "description": "Express yourself through creative activities",
                    "domain": "creativity"
                },
                "physical": {
                    "name": "Gentle Movement",
                    "description": "Simple physical activities to energize your body",
                    "domain": "physical"
                },
                "intellectual": {
                    "name": "Learning Moment",
                    "description": "Engage your mind with learning activities",
                    "domain": "learning"
                },
                "social": {
                    "name": "Connection Activity",
                    "description": "Activities to connect with others or community",
                    "domain": "social"
                },
                "emotional": {
                    "name": "Emotional Wellness",
                    "description": "Activities to support your emotional well-being",
                    "domain": "wellness"
                }
            }

            candidate_activities = []
            for i, domain in enumerate(strategy_domains[:6]):  # Limit to 6 activities max
                template = default_activities_templates.get(domain, {
                    "name": f"{domain.title()} Activity",
                    "description": f"A {domain} activity to support your growth",
                    "domain": domain
                })

                default_activity = {
                    "id": f"default-activity-{i+1}-{domain}",  # Include domain for uniqueness
                    "name": template["name"],
                    "description": template["description"],
                    "domain": template["domain"],
                    "duration_range": {"min": 15, "max": 30},
                    "challenge_level": 50
                }
                candidate_activities.append(default_activity)

            logger.debug(f"Created {len(candidate_activities)} default activities across domains")

        # Log the number of candidate activities
        logger.debug(f"Creating tailored activities from {len(candidate_activities)} candidates")

        # Process up to 5 activities for the wheel (as specified in requirements)
        for i, activity in enumerate(candidate_activities[:5]):
            try:
                # Ensure activity is a dict
                if not isinstance(activity, dict):
                    logger.warning(f"Activity {i} is not a dict: {activity}")
                    continue

                # Ensure activity has an ID
                activity_id = activity.get("id")
                if not activity_id:
                    activity_id = f"activity-{i+1}"
                    activity["id"] = activity_id

                logger.debug(f"Tailoring activity {activity_id}")

                # Call tool to tailor activity - use the activity_id directly
                # The enhanced activity catalog already provides proper IDs like "creative_1", "wellness_1", etc.
                # The tailor_activity function can handle these directly
                domain = activity.get("domain", "general")

                # Use the activity_id directly - no need to modify it
                # The tailor_activity function now handles enhanced catalog IDs properly
                enhanced_activity_id = activity_id

                tailored = await self._call_tool(
                    "tailor_activity",
                    {
                        "user_profile_id": self.user_profile_id,
                        "generic_activity_id": enhanced_activity_id,
                        "resource_context": resource_context,
                        "context_packet": context_packet
                    }
                )

                # Debug: Log the full result from the tool call
                logger.debug(f"Tool call result type: {type(tailored)}")
                logger.debug(f"Tool call result: {tailored}")

                # Ensure the result is a dict and get the tailored activity
                if not isinstance(tailored, dict):
                    logger.warning(f"Tool call returned non-dict: {type(tailored)} - {tailored}")
                    tailored = {}

                tailored_activity = tailored.get("tailored_activity")
                if not isinstance(tailored_activity, dict):
                    logger.warning(f"Tailored activity is not a dict: {tailored_activity}")
                    logger.debug(f"Full tailored result: {tailored}")
                    logger.debug(f"Available keys in tailored result: {list(tailored.keys()) if isinstance(tailored, dict) else 'N/A'}")
                    # Create a default tailored activity based on the original
                    tailored_activity = {
                        "id": activity_id,
                        "name": activity.get("name", f"Activity {i+1}"),
                        "description": activity.get("description", "A tailored activity"),
                        "instructions": "Follow the activity instructions",
                        "domain": activity.get("domain", "reflective"),
                        "duration": activity.get("duration_range", {}).get("min", 20),
                        "challenge_level": activity.get("challenge_level", 50),
                        "resources_required": []
                    }

                # Map the tailored activity fields to the expected wheel format
                wheel_activity = {
                    "id": tailored_activity.get("id", activity_id),
                    "name": tailored_activity.get("title", activity.get("name", f"Activity {i+1}")),
                    "description": tailored_activity.get("description", activity.get("description", "A tailored activity")),
                    "instructions": tailored_activity.get("instructions", "Follow the activity instructions"),
                    "domain": tailored_activity.get("domain", activity.get("domain", "reflective")),
                    "duration": tailored_activity.get("duration_minutes", self._extract_duration_from_range(activity.get("duration_range", "20 minutes"))),
                    "challenge_level": tailored_activity.get("difficulty_level", activity.get("challenge_level", 50)),
                    "resources_required": tailored_activity.get("required_resources", []),
                    "estimated_completion_time": tailored_activity.get("duration_minutes", 20) + 5,
                    "resource_intensity": "low",
                    "adaptability": {
                        "can_simplify": True,
                        "can_extend": True,
                        "alternative_resources": tailored_activity.get("required_resources", [])
                    }
                }

                # Use the mapped activity instead of the original tailored_activity
                tailored_activity = wheel_activity

                # Add the tailored activity to the list
                tailored_activities.append(tailored_activity)
                logger.debug(f"Successfully tailored activity {tailored_activity.get('id')}")

            except Exception as e:
                logger.error(f"Error tailoring activity {i}: {str(e)}", exc_info=True)
                # Create a fallback activity instead of skipping
                fallback_activity = {
                    "id": activity.get("id", f"fallback-{i+1}"),
                    "name": activity.get("name", f"Fallback Activity {i+1}"),
                    "description": activity.get("description", "A simple activity"),
                    "instructions": "Follow the activity instructions",
                    "domain": activity.get("domain", "reflective"),
                    "duration": self._extract_duration_from_range(activity.get("duration_range", "20 minutes")),
                    "challenge_level": activity.get("challenge_level", 50),
                    "resources_required": [],
                    "estimated_completion_time": self._extract_duration_from_range(activity.get("duration_range", "20 minutes")) + 5,
                    "resource_intensity": "low",
                    "adaptability": {
                        "can_simplify": True,
                        "can_extend": True,
                        "alternative_resources": ["basic household items"]
                    }
                }
                tailored_activities.append(fallback_activity)
                logger.debug(f"Added fallback activity {fallback_activity['id']}")

        # Ensure we have at least one activity with enhanced personalization
        if not tailored_activities:
            logger.warning("No tailored activities created, adding personalized default activity")

            # Extract context for personalized default
            mood = context_packet.get("reported_mood", "neutral")
            environment = context_packet.get("reported_environment", "home")
            time_available = resource_context.get("time", {}).get("reported_duration_minutes", 20)
            energy_level = context_packet.get("reported_energy_level", "medium")

            # Create multiple diverse default activities instead of just one
            logger.info("No candidate activities found, creating diverse default activities")

            # Create at least 4 diverse activities across different domains
            default_domains = ["wellness", "creativity", "personal_growth", "physical", "learning", "social"]
            # Ensure we create at least 4 activities
            min_activities = max(4, len(default_domains))

            for i in range(min_activities):
                domain = default_domains[i % len(default_domains)]

                # Use the enhanced fallback activity creator
                from .tools.activity_tools import _create_enhanced_fallback_activity

                fallback_activity = _create_enhanced_fallback_activity(
                    i + 1, mood, environment, time_available, energy_level
                )

                # Override domain to ensure variety
                fallback_activity["domain"] = domain
                fallback_activity["id"] = f"default-{i+1}-{domain}"  # Include domain for uniqueness

                # Adjust title to reflect domain
                domain_titles = {
                    "wellness": "Mindful Wellness",
                    "creativity": "Creative Expression",
                    "personal_growth": "Personal Reflection",
                    "physical": "Gentle Movement",
                    "learning": "Learning Moment",
                    "social": "Connection Activity"
                }
                fallback_activity["title"] = f"{domain_titles.get(domain, 'Mindful Activity')} for {mood.title()} Mood"
                fallback_activity["name"] = fallback_activity["title"]

                tailored_activities.append(fallback_activity)

        logger.debug(f"Created {len(tailored_activities)} tailored activities")
        return tailored_activities

    async def _assign_probability_weights(self, tailored_activities, strategy_framework):
        """Assign probability weights to wheel items"""
        try:
            # Ensure tailored_activities is a list
            if not isinstance(tailored_activities, list):
                logger.warning(f"tailored_activities is not a list: {tailored_activities}")
                tailored_activities = []

            # If no tailored activities, return an empty list
            if not tailored_activities:
                logger.warning("No tailored activities to assign weights to")
                return []

            # Get domain distribution from strategy, ensuring it's a dict
            domain_distribution = strategy_framework.get("domain_distribution") or {}
            if not isinstance(domain_distribution, dict):
                logger.warning(f"domain_distribution is not a dict: {domain_distribution}")
                domain_distribution = {}

            # Ensure strategy_framework is a dict
            if not isinstance(strategy_framework, dict):
                logger.warning(f"strategy_framework is not a dict: {strategy_framework}")
                strategy_framework = {}

            logger.debug(f"Assigning probability weights to {len(tailored_activities)} activities")

            # Call tool to assign weights
            weight_results = await self._call_tool(
                "assign_wheel_probabilities",
                {
                    "user_profile_id": self.user_profile_id,
                    "activities": tailored_activities,
                    "domain_distribution": domain_distribution,
                    "strategy_framework": strategy_framework
                }
            )

            # Ensure weight_results is a dict
            if not isinstance(weight_results, dict):
                logger.warning(f"weight_results is not a dict: {weight_results}")
                weight_results = {}

            # Get wheel items from results
            wheel_items = weight_results.get("wheel_items", [])

            # Ensure wheel_items is a list
            if not isinstance(wheel_items, list):
                logger.warning(f"wheel_items is not a list: {wheel_items}")
                wheel_items = []

            # If no wheel items returned, create default ones
            if not wheel_items:
                logger.warning("No wheel items returned from tool, creating default ones")
                wheel_items = self._create_default_wheel_items(tailored_activities)

            # Ensure each wheel item has all required fields
            for i, item in enumerate(wheel_items):
                # Ensure item is a dict
                if not isinstance(item, dict):
                    logger.warning(f"Wheel item {i} is not a dict: {item}")
                    # Replace with a default item
                    if i < len(tailored_activities):
                        activity = tailored_activities[i]
                        activity_id = activity.get("id", f"activity-{i+1}")
                        wheel_items[i] = {
                            "id": f"wheel-item-{i+1}",
                            "activity_id": activity_id,
                            "percentage": 100.0 / len(tailored_activities),
                            "position": i,
                            "color": self._get_activity_color("general")  # Default domain for missing activities
                        }
                    continue

                # Ensure item has an ID
                if "id" not in item:
                    item["id"] = f"wheel-item-{i+1}"

                # Ensure item has an activity_id
                if "activity_id" not in item:
                    # Try to find a matching activity
                    if i < len(tailored_activities):
                        item["activity_id"] = tailored_activities[i].get("id", f"activity-{i+1}")
                    else:
                        item["activity_id"] = f"activity-{i+1}"

                # Ensure item has a percentage
                if "percentage" not in item:
                    item["percentage"] = 100.0 / len(tailored_activities)

                # Ensure item has a position
                if "position" not in item:
                    item["position"] = i

                # Ensure item has a color
                if "color" not in item:
                    # Get domain from corresponding activity if available
                    domain = "general"
                    if i < len(tailored_activities):
                        activity = tailored_activities[i]
                        domain = activity.get("domain", "general")
                    item["color"] = self._get_activity_color(domain)

                # Ensure item has a title (use activity name if available)
                if "title" not in item:
                    # Try to find the corresponding activity and use its name
                    if i < len(tailored_activities):
                        activity = tailored_activities[i]
                        item["title"] = activity.get("name", activity.get("title", f"Activity {i+1}"))
                    else:
                        item["title"] = f"Activity {i+1}"

                # Ensure item has a domain
                if "domain" not in item:
                    if i < len(tailored_activities):
                        activity = tailored_activities[i]
                        item["domain"] = activity.get("domain", "general")
                    else:
                        item["domain"] = "general"

                # Ensure item has a probability
                if "probability" not in item:
                    item["probability"] = item.get("percentage", 100.0 / len(tailored_activities)) / 100.0

            logger.debug(f"Created {len(wheel_items)} wheel items")
            return wheel_items

        except Exception as e:
            logger.error(f"Error assigning probability weights: {str(e)}", exc_info=True)
            # Create default wheel items if tool fails
            return self._create_default_wheel_items(tailored_activities)

    def _create_default_wheel_items(self, tailored_activities):
        """Create default wheel items with equal weights"""
        wheel_items = []

        # Ensure tailored_activities is a list
        if not isinstance(tailored_activities, list):
            tailored_activities = []

        # If no tailored activities, return an empty list
        if not tailored_activities:
            return []

        # Calculate equal weight
        weight = 100.0 / len(tailored_activities)

        # Create wheel items
        for i, activity in enumerate(tailored_activities):
            # Ensure activity is a dict
            if not isinstance(activity, dict):
                continue

            # Get activity ID
            activity_id = activity.get("id", f"activity-{i+1}")

            # Create wheel item
            wheel_items.append({
                "id": f"wheel-item-{i+1}",
                "activity_id": activity_id,
                "title": activity.get("name", activity.get("title", f"Activity {i+1}")),
                "percentage": weight,
                "position": i,
                "color": self._get_activity_color(activity.get("domain", "general")),
                "domain": activity.get("domain", "general"),
                "probability": weight / 100.0  # Convert percentage to probability
            })

        return wheel_items

    def _get_default_color(self, index):
        """Get a default color based on index"""
        colors = [
            "#66BB6A",  # Green
            "#42A5F5",  # Blue
            "#FFA726",  # Orange
            "#EF5350",  # Red
            "#AB47BC",  # Purple
            "#26C6DA",  # Cyan
            "#FFC107",  # Amber
            "#78909C"   # Blue Grey
        ]
        return colors[index % len(colors)]

    def _extract_duration_from_range(self, duration_range: str, default: int = 20) -> int:
        """Extract a duration value from a duration range string like '20-40 minutes'."""
        if not duration_range or not isinstance(duration_range, str):
            return default

        try:
            # Extract numbers from the duration range string
            import re
            numbers = re.findall(r'\d+', duration_range)
            if len(numbers) >= 2:
                # Take the minimum of the range
                return int(numbers[0])
            elif len(numbers) == 1:
                return int(numbers[0])
            else:
                return default
        except (ValueError, IndexError):
            return default

    def _validate_wheel_data(self, wheel):
        """
        Validate the wheel data structure and ensure all required fields are present.

        Args:
            wheel (dict): The wheel data structure to validate

        Returns:
            dict: The validated and potentially fixed wheel data structure
        """
        if not wheel:
            logger.warning("Wheel data is empty, creating a default structure")
            wheel = {
                "metadata": {"name": "Default Wheel", "trust_phase": "Foundation"},
                "items": [],
                "activities": [],
                "value_propositions": {},
                "timestamp": "",
                "user_id": self.user_profile_id
            }

        # Ensure metadata exists and has required fields
        if "metadata" not in wheel:
            wheel["metadata"] = {"name": "Default Wheel", "trust_phase": "Foundation"}
        elif not isinstance(wheel["metadata"], dict):
            wheel["metadata"] = {"name": "Default Wheel", "trust_phase": "Foundation"}
        elif "name" not in wheel["metadata"]:
            wheel["metadata"]["name"] = "Default Wheel"

        # Ensure items exists
        if "items" not in wheel:
            wheel["items"] = []
        elif not isinstance(wheel["items"], list):
            wheel["items"] = []

        # Ensure activities exists
        if "activities" not in wheel:
            wheel["activities"] = []
        elif not isinstance(wheel["activities"], list):
            wheel["activities"] = []

        # Validate each activity
        for i, activity in enumerate(wheel["activities"]):
            if not isinstance(activity, dict):
                logger.warning(f"Activity {i} is not a dictionary, skipping validation")
                continue

            # Ensure required fields exist
            if "id" not in activity:
                activity["id"] = f"activity-{i+1}"
            if "name" not in activity:
                activity["name"] = f"Activity {i+1}"
            if "description" not in activity:
                activity["description"] = "A default activity"
            if "domain" not in activity:
                activity["domain"] = "reflective"
            if "duration" not in activity:
                activity["duration"] = 20
            if "challenge_level" not in activity:
                activity["challenge_level"] = 50
            if "resources_required" not in activity:
                activity["resources_required"] = []
            if "estimated_completion_time" not in activity:
                activity["estimated_completion_time"] = activity.get("duration", 20) + 5

        # Ensure value_propositions exists
        if "value_propositions" not in wheel:
            wheel["value_propositions"] = {}
        elif not isinstance(wheel["value_propositions"], dict):
            wheel["value_propositions"] = {}

        # Ensure timestamp and user_id exist
        if "timestamp" not in wheel:
            wheel["timestamp"] = ""
        if "user_id" not in wheel:
            wheel["user_id"] = self.user_profile_id

        return wheel

    async def _create_wheel_metadata(self, context_packet, strategy_framework):
        """Create metadata for the wheel"""
        # Extract trust phase from strategy, ensuring intermediate dict exists
        trust_phase = "Foundation"
        growth_alignment = strategy_framework.get("growth_alignment") or {}
        trust_phase = growth_alignment.get("trust_phase", "Foundation")

        # Generate wheel name
        wheel_name = f"Activity Wheel - {trust_phase} Phase"

        return {
            "name": wheel_name,
            "created_at": context_packet.get("session_timestamp", ""),
            "trust_phase": trust_phase,
            "strategy_id": strategy_framework.get("id", "strategy-unknown")
        }

    async def _create_value_propositions(self, tailored_activities, strategy_framework):
        """Create value propositions for activities"""
        try:
            # Call tool to create value propositions
            value_results = await self._call_tool(
                "create_value_propositions",
                {
                    "user_profile_id": self.user_profile_id,
                    "activities": tailored_activities,
                    "strategy_framework": strategy_framework
                }
            )
            # Ensure the result is a dict, even if key is missing or None
            return value_results.get("value_propositions") or {}

        except Exception as e:
            # Create basic value propositions if tool fails
            value_propositions = {}

            for activity in tailored_activities:
                activity_id = activity.get("id", "unknown")
                value_propositions[activity_id] = {
                    "growth_value": "This activity supports your personal growth.",
                    "connection_to_goals": "This aligns with your stated goals.",
                    "challenge_description": "This provides an appropriate level of challenge."
                }

            return value_propositions

    async def _call_tool(self, tool_code, tool_input):
        """
        Call a tool using the centralized utility and handle potential errors.
        Propagates ToolExecutionError and logs/raises other unexpected errors.
        """
        # Import tool utility here
        from apps.main.agents.tools.tools_util import execute_tool, ToolExecutionError
        logger.debug(f"WheelActivityAgent attempting to call tool '{tool_code}'")
        try:
            result = await execute_tool(
                tool_code=tool_code,
                tool_input=tool_input,
                run_id=self.run_id, # Pass run_id
                user_profile_id=self.user_profile_id # Pass user_id
            )
            logger.debug(f"Tool '{tool_code}' executed successfully by WheelActivityAgent.")
            return result
        except ToolExecutionError as e:
            # Log the specific ToolExecutionError and re-raise it
            logger.error(f"ToolExecutionError in WheelActivityAgent calling '{tool_code}': {e}", exc_info=True)
            # Re-raise to be handled by the main process loop or specific callers
            raise
        except Exception as e:
            # Catch unexpected errors, log them, and wrap in ToolExecutionError
            logger.error(f"Unexpected error in WheelActivityAgent calling tool '{tool_code}': {e}", exc_info=True)
            # Wrap in ToolExecutionError before raising
            raise ToolExecutionError(f"Unexpected error calling tool {tool_code}: {e}") from e
