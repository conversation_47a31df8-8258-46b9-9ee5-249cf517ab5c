import json
import os
from typing import Any, Dict, List, Optional
from mistralai import ChatCompletionResponse, Mistral, UserMessage
import backoff
import logging
import traceback # Import traceback

# Removed top-level import: from apps.main.models import LLMConfig
from apps.main.services.event_service import EventService # Import EventService
from .response import LLMResponse, ResponseType, ToolCall

logger = logging.getLogger(__name__)

class LLMClientConfig:
    """Configuration for LLM client with environment-based settings"""
    API_KEY = os.environ.get("MISTRAL_API_KEY")
    DEFAULT_MODEL = os.environ.get("MISTRAL_MODEL", "mistral-small-latest")
    TIMEOUT = int(os.environ.get("LLM_TIMEOUT", "60"))
    MAX_RETRIES = int(os.environ.get("LLM_MAX_RETRIES", "3"))

class LLMClient:
    """
    Wrapper for Mistral AI API with standardized response handling.
    Uses LLMConfig for model name and temperature settings.
    """
    DEFAULT_TEMPERATURE = 0.7

    def __init__(self, api_key: Optional[str] = None, llm_config: Optional['LLMConfig'] = None): # Use string literal for hint
        # Import LLMConfig here, inside the method
        from apps.main.models import LLMConfig
        self.api_key = api_key or LLMClientConfig.API_KEY
        if not self.api_key:
            raise ValueError("Mistral API key not provided")

        # Check if this is a dummy/test API key
        self.is_test_key = self._is_dummy_api_key(self.api_key)

        resolved_config = llm_config
        if resolved_config is None:
            logger.debug("No LLMConfig provided, attempting to fetch default.")
            resolved_config = LLMConfig.get_default() # Fetch default if none provided

        if resolved_config is None:
            # Fallback or error if no config found
            # Option 1: Raise error
            raise ValueError("LLMClient requires an LLMConfig, and no default is set.")
            # Option 2: Fallback to environment (less ideal if DB config is intended)
            # logger.warning("No LLMConfig provided or found as default. Falling back to environment variables.")
            # self.model = LLMClientConfig.DEFAULT_MODEL
            # self.temperature = self.DEFAULT_TEMPERATURE # Or fetch from env if defined
            # self.llm_config = None # Indicate no DB config is used
        else:
            logger.info(f"Using LLMConfig: {resolved_config.name}")
            self.llm_config = resolved_config
            self.model = self.llm_config.model_name
            # Use config temperature if set, otherwise default
            self.temperature = self.llm_config.temperature if self.llm_config.temperature is not None else self.DEFAULT_TEMPERATURE

        # Only create real Mistral client if not using test key
        if not self.is_test_key:
            self.client = Mistral(api_key=self.api_key)
        else:
            # For test keys, create a mock client that will be handled by the service layer
            logger.info(f"Using dummy API key for testing: {self.api_key[:20]}...")
            self.client = None  # Will be handled by service layer

        self.event_service = EventService() # Instantiate EventService

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with proper cleanup."""
        await self.close()

    async def close(self):
        """Close the client and clean up resources."""
        if self.client and hasattr(self.client, '__aexit__'):
            try:
                await self.client.__aexit__(None, None, None)
            except Exception as e:
                logger.warning(f"Error closing Mistral client: {e}")

    def __del__(self):
        """Destructor to ensure cleanup happens even if close() isn't called."""
        # Only try cleanup if we have a client and we're not in test mode
        if self.client and not self.is_test_key:
            try:
                import asyncio
                # Try to get the current event loop
                try:
                    loop = asyncio.get_running_loop()
                    # Schedule cleanup without waiting
                    loop.create_task(self.close())
                except RuntimeError:
                    # No running event loop, can't do async cleanup
                    pass
            except Exception:
                # If anything fails, just ignore it
                pass

    def _is_dummy_api_key(self, api_key: str) -> bool:
        """
        Check if an API key is a dummy test key.

        Args:
            api_key: The API key to check

        Returns:
            bool: True if it's a dummy test key, False if it's a real API key
        """
        if not api_key:
            return False

        # List of patterns that indicate dummy/test keys
        dummy_patterns = [
            'test_',
            'dummy_',
            'fake_',
            'mock_',
            '_for_testing',
            'testing_only'
        ]

        api_key_lower = api_key.lower()
        return any(pattern in api_key_lower for pattern in dummy_patterns)

    @backoff.on_exception(
        backoff.expo,
        (Exception,),
        max_tries=LLMClientConfig.MAX_RETRIES,
        max_time=LLMClientConfig.TIMEOUT
    )
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None,
        tools: Optional[List[Dict[str, Any]]] = None
    ) -> LLMResponse:
        """
        Perform a chat completion request to the LLM service.
        
        Args:
            messages: List of message objects with role and content
            temperature: Temperature parameter for response generation (0.0-1.0)
            max_tokens: Maximum number of tokens to generate
            tools: Optional list of tools to make available to the model
            
        Returns:
            LLMResponse object containing the response and/or tool calls
        """
        try:
            if not messages:
                raise ValueError("No messages provided for chat completion")

            # If using a test key, return a mock response with realistic token counts
            if self.is_test_key:
                logger.info("Using dummy API key - returning mock LLM response")

                # Estimate token counts based on message content (rough approximation: 1 token ≈ 4 characters)
                total_input_chars = sum(len(msg.get('content', '')) for msg in messages)
                estimated_input_tokens = max(10, total_input_chars // 4)  # Minimum 10 tokens

                # Mock response content
                mock_content = "This is a mock LLM response for testing purposes. The response length varies based on the input to provide more realistic token estimates."
                estimated_output_tokens = max(15, len(mock_content) // 4)  # Minimum 15 tokens

                logger.debug(f"Mock LLM response: {estimated_input_tokens} input tokens, {estimated_output_tokens} output tokens")

                return LLMResponse(
                    response_type=ResponseType.TEXT,
                    content=mock_content,
                    input_tokens=estimated_input_tokens,
                    output_tokens=estimated_output_tokens
                )

            # Format tools for the LLM if provided
            formatted_tools = None
            if tools:
                from apps.main.llm.tools_formatter import convert_tools_for_llm
                formatted_tools = convert_tools_for_llm(tools)
            
            # Prepare parameters
            params = {
                "model": self.model,
                "messages": messages,
                "temperature": self.temperature # Use temperature from resolved config
            }

            # Add optional parameters if provided
            if max_tokens:
                params["max_tokens"] = max_tokens
                
            if formatted_tools:
                params["tools"] = formatted_tools
                # Most LLMs need this parameter to actually use tools
                params["tool_choice"] = "auto"
            
            # Make the API request
            # Make the API call
            response = self.client.chat.complete(**params)
            
            # Parse the response
            return self._parse_completion_response(response)
        
        except Exception as e:
            # Enhance error message propagation for API errors
            error_message = str(e)
            # Attempt to extract detailed API error message if available
            if hasattr(e, 'args') and e.args:
                for arg in e.args:
                    if isinstance(arg, str) and "Invalid model" in arg:
                        error_message = arg
                        break
            
            # Log the error locally
            logging.error(f"Chat completion error: {error_message}", exc_info=True)
            
            # Emit debug event (synchronously) - No user_profile_id available here
            self.event_service.emit_event_sync(
                event_type='debug_info',
                data={
                    'level': 'error',
                    'message': f"LLM chat completion failed for model '{self.model}'",
                    'source': 'LLMClient',
                    'details': {
                        'model': self.model,
                        'error_type': type(e).__name__,
                        'error_message': error_message, # Use potentially extracted message
                        'original_exception': str(e),
                        'traceback': traceback.format_exc()
                    }
                }
                # user_profile_id is None here
            )
            
            # Raise a new exception with detailed message
            raise RuntimeError(f"Chat completion failed: {error_message}") from e

    def _parse_completion_response(self, response_data: ChatCompletionResponse) -> LLMResponse:
        """
        Parse the raw LLM API response into our standardized LLMResponse format.
        
        Args:
            response_data: Raw response data from the LLM API
            
            Returns:
            LLMResponse object with appropriate type and data
        """
        try:
            # Extract token usage if available
            input_tokens = None
            output_tokens = None
            if response_data.usage:
                input_tokens = response_data.usage.prompt_tokens
                output_tokens = response_data.usage.completion_tokens
                logger.debug(f"LLM Usage - Input: {input_tokens}, Output: {output_tokens}")

            # Extract choices from response
            choices = response_data.choices
            if not choices:
                return LLMResponse(
                    response_type=ResponseType.ERROR,
                    content="No choices in LLM response"
                )
            
            # Get the first choice
            choice = choices[0]
            message = choice.message
            
            # Check for tool calls
            tool_calls = message.tool_calls
            if tool_calls:
                # Process tool calls
                from apps.main.llm.tools_formatter import parse_tool_call_from_llm
                
                parsed_tool_calls = []
                for tool_call in tool_calls:
                    parsed = parse_tool_call_from_llm(tool_call)
                    
                    # Create a ToolCall object for each tool call
                    parsed_tool_calls.append(
                        ToolCall(
                            tool_name=parsed['tool_name'],
                            tool_input=parsed['tool_input']
                        )
                    )
                
                return LLMResponse(
                    response_type=ResponseType.TOOL_CALL,
                    content=message.content,
                    tool_calls=parsed_tool_calls,
                    input_tokens=input_tokens, # Add token counts
                    output_tokens=output_tokens # Add token counts
                )

            # Handle regular text responses
            content = message.content
            return LLMResponse(
                response_type=ResponseType.TEXT,
                content=content,
                input_tokens=input_tokens, # Add token counts
                output_tokens=output_tokens # Add token counts
            )

        except Exception as e:
            logging.error(f"Error parsing LLM response: {str(e)}", exc_info=True)
            return LLMResponse(
                response_type=ResponseType.ERROR,
                content=f"Error parsing response: {str(e)}"
            )
