"""
Management command to register missing tools in the database.
"""

from django.core.management.base import BaseCommand
from apps.main.models import AgentTool


class Command(BaseCommand):
    help = 'Register missing tools in the database'

    def handle(self, *args, **options):
        """Register all missing tools."""
        
        # Define the missing tools with their metadata
        missing_tools = [
            # Engagement Tools
            {
                'code': 'get_domain_preferences',
                'name': 'Get Domain Preferences',
                'description': 'Analyzes user\'s historical domain preferences based on activity history and feedback',
                'is_active': True
            },
            {
                'code': 'get_completion_patterns',
                'name': 'Get Completion Patterns',
                'description': 'Analyzes user\'s activity completion patterns and success factors',
                'is_active': True
            },
            {
                'code': 'get_temporal_patterns',
                'name': 'Get Temporal Patterns',
                'description': 'Analyzes user\'s temporal engagement patterns and optimal activity times',
                'is_active': True
            },
            {
                'code': 'get_preference_consistency',
                'name': 'Get Preference Consistency',
                'description': 'Analyzes consistency between stated preferences and actual behaviors',
                'is_active': True
            },
            {
                'code': 'get_feedback_sentiment',
                'name': 'Get Feedback Sentiment',
                'description': 'Analyzes sentiment in user feedback across different domains and time periods',
                'is_active': True
            },
            
            # Psychological Tools
            {
                'code': 'analyze_psychological_state',
                'name': 'Analyze Psychological State',
                'description': 'Analyzes the user\'s current psychological state based on context and mood',
                'is_active': True
            },
            {
                'code': 'get_trust_metrics',
                'name': 'Get Trust Metrics',
                'description': 'Determines user\'s trust phase and metrics based on interaction history',
                'is_active': True
            },
            {
                'code': 'get_trait_analysis',
                'name': 'Get Trait Analysis',
                'description': 'Analyzes user\'s personality traits using HEXACO framework',
                'is_active': True
            },
            {
                'code': 'get_belief_analysis',
                'name': 'Get Belief Analysis',
                'description': 'Analyzes user\'s belief system and core values',
                'is_active': True
            },
            {
                'code': 'identify_growth_opportunities',
                'name': 'Identify Growth Opportunities',
                'description': 'Identifies growth opportunities based on trait analysis, beliefs, and engagement patterns',
                'is_active': True
            },
            {
                'code': 'calculate_challenge_calibration',
                'name': 'Calculate Challenge Calibration',
                'description': 'Calculates optimal challenge level based on user\'s skills, comfort zone, and growth goals',
                'is_active': True
            },
            
            # Activity Tools
            {
                'code': 'tailor_activity',
                'name': 'Tailor Activity',
                'description': 'Tailors a generic activity to user\'s specific context, resources, and preferences',
                'is_active': True
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for tool_data in missing_tools:
            tool, created = AgentTool.objects.get_or_create(
                code=tool_data['code'],
                defaults={
                    'name': tool_data['name'],
                    'description': tool_data['description'],
                    'is_active': tool_data['is_active'],
                    'input_schema': {},  # Empty schema for now
                    'output_schema': {},  # Empty schema for now
                    'function_path': f'apps.main.agents.tools.{tool_data["code"]}'
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created tool: {tool_data["code"]}')
                )
            else:
                # Update existing tool if needed
                updated = False
                if tool.name != tool_data['name']:
                    tool.name = tool_data['name']
                    updated = True
                if tool.description != tool_data['description']:
                    tool.description = tool_data['description']
                    updated = True
                if tool.is_active != tool_data['is_active']:
                    tool.is_active = tool_data['is_active']
                    updated = True
                
                if updated:
                    tool.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(f'Updated tool: {tool_data["code"]}')
                    )
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f'Tool already exists: {tool_data["code"]}')
                    )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nSummary: {created_count} tools created, {updated_count} tools updated'
            )
        )
        
        # Verify all tools are now registered
        self.stdout.write('\n=== Verification ===')
        for tool_data in missing_tools:
            exists = AgentTool.objects.filter(code=tool_data['code'], is_active=True).exists()
            status = 'REGISTERED' if exists else 'MISSING'
            style = self.style.SUCCESS if exists else self.style.ERROR
            self.stdout.write(style(f'{tool_data["code"]}: {status}'))
