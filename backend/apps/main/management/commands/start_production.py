"""
Django management command to start Django with <PERSON><PERSON><PERSON> (ASGI) for WebSocket support.
This command only starts the web server - Celery workers run in separate services.
"""

import os
import sys
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = 'Start Django with <PERSON><PERSON><PERSON> (ASGI) for WebSocket support'

    def add_arguments(self, parser):
        parser.add_argument(
            '--workers',
            type=int,
            default=1,
            help='Number of Uvicorn worker processes (default: 1)'
        )
        parser.add_argument(
            '--host',
            type=str,
            default='0.0.0.0',
            help='Host to bind to (default: 0.0.0.0)'
        )
        parser.add_argument(
            '--port',
            type=int,
            default=8080,
            help='Port to bind to (default: 8080)'
        )

    def handle(self, *args, **options):
        self.stdout.write("🌐 Starting Django with Uvicorn (ASGI) for WebSocket support...")

        uvicorn_cmd = [
            'uvicorn',
            'config.asgi:application',
            '--host', options['host'],
            '--port', str(options['port']),
            f'--workers={options["workers"]}',
            '--log-level', 'info'
        ]

        self.stdout.write(f"🚀 Command: {' '.join(uvicorn_cmd)}")

        # Execute Uvicorn (this replaces the current process)
        try:
            os.execvp('uvicorn', uvicorn_cmd)
        except Exception as e:
            self.stdout.write(f"❌ Failed to start Uvicorn: {e}")
            sys.exit(1)
