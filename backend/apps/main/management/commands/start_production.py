"""
Django management command to start both Django and Celery in production.
This command starts Celery worker in the background and then starts Gunicorn.
"""

import os
import sys
import time
import signal
import subprocess
from django.core.management.base import BaseCommand
from django.conf import settings


class Command(BaseCommand):
    help = 'Start production services (Django + Celery)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--celery-concurrency',
            type=int,
            default=2,
            help='Number of Celery worker processes (default: 2)'
        )
        parser.add_argument(
            '--gunicorn-workers',
            type=int,
            default=1,
            help='Number of Gunicorn worker processes (default: 1)'
        )

    def handle(self, *args, **options):
        self.stdout.write("🚀 Starting production services...")
        
        # Start Celery worker in the background
        self.stdout.write("📋 Starting Celery worker...")
        
        celery_cmd = [
            'celery', '-A', 'config', 'worker',
            '--loglevel=info',
            f'--concurrency={options["celery_concurrency"]}',
            '--detach',
            '--pidfile=/tmp/celery.pid',
            '--logfile=/tmp/celery.log'
        ]
        
        try:
            subprocess.run(celery_cmd, check=True)
            time.sleep(3)  # Give Celery time to start
            
            # Check if Celery started successfully
            if os.path.exists('/tmp/celery.pid'):
                with open('/tmp/celery.pid', 'r') as f:
                    pid = f.read().strip()
                self.stdout.write(f"✅ Celery worker started successfully (PID: {pid})")
            else:
                self.stdout.write("❌ Failed to start Celery worker")
                sys.exit(1)
                
        except subprocess.CalledProcessError as e:
            self.stdout.write(f"❌ Failed to start Celery worker: {e}")
            sys.exit(1)
        
        # Start Gunicorn (Django) in the foreground
        self.stdout.write("🌐 Starting Django with Gunicorn...")
        
        gunicorn_cmd = [
            'gunicorn',
            '--worker-tmp-dir', '/dev/shm',
            '--bind', '0.0.0.0:8080',
            f'--workers={options["gunicorn_workers"]}',
            'config.wsgi:application'
        ]
        
        # Setup signal handlers to cleanup Celery on exit
        def cleanup_celery(signum, frame):
            self.stdout.write("🛑 Shutting down services...")
            if os.path.exists('/tmp/celery.pid'):
                with open('/tmp/celery.pid', 'r') as f:
                    pid = int(f.read().strip())
                try:
                    os.kill(pid, signal.SIGTERM)
                    self.stdout.write("✅ Celery worker stopped")
                except ProcessLookupError:
                    pass
            sys.exit(0)
        
        signal.signal(signal.SIGTERM, cleanup_celery)
        signal.signal(signal.SIGINT, cleanup_celery)
        
        # Execute Gunicorn (this replaces the current process)
        try:
            os.execvp('gunicorn', gunicorn_cmd)
        except Exception as e:
            self.stdout.write(f"❌ Failed to start Gunicorn: {e}")
            cleanup_celery(None, None)
            sys.exit(1)
