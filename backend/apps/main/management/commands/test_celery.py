# backend/apps/main/management/commands/test_celery.py

import time
import json
from django.core.management.base import BaseCommand
from celery.result import AsyncResult
from apps.main.tasks.test_tasks import (
    test_worker_health,
    test_api_call,
    test_ml_simulation,
    test_combined_workload
)


class Command(BaseCommand):
    help = 'Test Celery worker functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-type',
            type=str,
            default='all',
            choices=['health', 'api', 'ml', 'combined', 'all'],
            help='Type of test to run'
        )
        parser.add_argument(
            '--wait',
            action='store_true',
            help='Wait for task completion and show results'
        )
        parser.add_argument(
            '--timeout',
            type=int,
            default=60,
            help='Timeout in seconds for waiting for task completion'
        )

    def handle(self, *args, **options):
        test_type = options['test_type']
        wait_for_result = options['wait']
        timeout = options['timeout']

        self.stdout.write(
            self.style.SUCCESS(f'Starting Celery worker tests - Type: {test_type}')
        )

        if test_type in ['health', 'all']:
            self.run_health_test(wait_for_result, timeout)

        if test_type in ['api', 'all']:
            self.run_api_test(wait_for_result, timeout)

        if test_type in ['ml', 'all']:
            self.run_ml_test(wait_for_result, timeout)

        if test_type in ['combined', 'all']:
            self.run_combined_test(wait_for_result, timeout)

        self.stdout.write(
            self.style.SUCCESS('Celery worker tests completed!')
        )

    def run_health_test(self, wait_for_result, timeout):
        self.stdout.write('\n--- Health Check Test ---')
        
        try:
            # Submit the task
            result = test_worker_health.delay()
            self.stdout.write(f'Task submitted: {result.id}')
            
            if wait_for_result:
                self.wait_and_display_result(result, timeout, 'Health Check')
            else:
                self.stdout.write(f'Task ID: {result.id} (use --wait to see results)')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to submit health check task: {str(e)}')
            )

    def run_api_test(self, wait_for_result, timeout):
        self.stdout.write('\n--- API Call Test ---')
        
        try:
            # Submit the task
            result = test_api_call.delay("https://httpbin.org/json")
            self.stdout.write(f'Task submitted: {result.id}')
            
            if wait_for_result:
                self.wait_and_display_result(result, timeout, 'API Call')
            else:
                self.stdout.write(f'Task ID: {result.id} (use --wait to see results)')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to submit API call task: {str(e)}')
            )

    def run_ml_test(self, wait_for_result, timeout):
        self.stdout.write('\n--- ML Simulation Test ---')
        
        try:
            # Submit the task
            result = test_ml_simulation.delay("medium")
            self.stdout.write(f'Task submitted: {result.id}')
            
            if wait_for_result:
                self.wait_and_display_result(result, timeout, 'ML Simulation')
            else:
                self.stdout.write(f'Task ID: {result.id} (use --wait to see results)')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to submit ML simulation task: {str(e)}')
            )

    def run_combined_test(self, wait_for_result, timeout):
        self.stdout.write('\n--- Combined Workload Test ---')
        
        try:
            # Submit the task
            result = test_combined_workload.delay("https://httpbin.org/delay/2")
            self.stdout.write(f'Task submitted: {result.id}')
            
            if wait_for_result:
                self.wait_and_display_result(result, timeout, 'Combined Workload')
            else:
                self.stdout.write(f'Task ID: {result.id} (use --wait to see results)')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to submit combined workload task: {str(e)}')
            )

    def wait_and_display_result(self, result, timeout, test_name):
        self.stdout.write(f'Waiting for {test_name} task to complete...')
        
        try:
            # Wait for the result
            task_result = result.get(timeout=timeout)
            
            self.stdout.write(
                self.style.SUCCESS(f'{test_name} task completed successfully!')
            )
            self.stdout.write('Result:')
            self.stdout.write(json.dumps(task_result, indent=2))
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'{test_name} task failed or timed out: {str(e)}')
            )
            
            # Check task state
            self.stdout.write(f'Task state: {result.state}')
            if result.state == 'FAILURE':
                self.stdout.write(f'Task error: {result.info}')

    def check_task_status(self, task_id):
        """Check the status of a specific task"""
        try:
            result = AsyncResult(task_id)
            self.stdout.write(f'\nTask {task_id} status: {result.state}')
            
            if result.state == 'SUCCESS':
                self.stdout.write('Result:')
                self.stdout.write(json.dumps(result.result, indent=2))
            elif result.state == 'FAILURE':
                self.stdout.write(f'Error: {result.info}')
            elif result.state == 'PENDING':
                self.stdout.write('Task is still pending...')
            else:
                self.stdout.write(f'Task info: {result.info}')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to check task status: {str(e)}')
            )
