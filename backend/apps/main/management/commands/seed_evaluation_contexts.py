from django.core.management.base import BaseCommand
from apps.main.services.evaluation_context_service import EvaluationContextService


class Command(BaseCommand):
    help = 'Seed evaluation contexts for agent evaluation'
    
    def handle(self, *args, **options):
        service = EvaluationContextService()
        
        contexts_data = [
            # Mentor Agent Contexts
            {
                'name': 'Stressed_Evening_WheelGen_Mentor',
                'description': 'Stressed user in evening, wheel generation result delivery, evaluating mentor agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'result_delivery',
                'agent_role_being_evaluated': 'mentor',
                
                # Context overrides (will override UserProfile values)
                'trust_level_override': 45,  # Force expansion phase
                'mood_valence_override': -0.3,  # Negative mood
                'mood_arousal_override': 0.2,   # Low energy
                'stress_level_override': 75,    # High stress
                'time_pressure_override': 60,   # Time pressure
                'reported_time_availability_override': '15_minutes',
                'reported_focus_override': 'quick stress relief',
                
                'previous_agent_outputs': [
                    'final_wheel: 6_calming_foundation_activities'
                ],
                'expected_next_agents': []  # <PERSON>tor is final
            },
            {
                'name': 'Confident_Morning_Discussion_Mentor',
                'description': 'Confident user seeking guidance, discussion workflow, evaluating mentor agent',
                'current_workflow_type': 'discussion',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'mentor',
                
                'trust_level_override': 78,    # Integration phase
                'mood_valence_override': 0.6,  # Positive mood
                'mood_arousal_override': 0.4,  # Moderate energy
                'stress_level_override': 20,   # Low stress
                'reported_environment_override': 'home',
                'reported_time_availability_override': '60_minutes',
                'reported_focus_override': 'creative and challenging activities',
                
                'previous_agent_outputs': [],
                'expected_next_agents': []
            },
            
            # Psychological Agent Contexts
            {
                'name': 'New_User_WheelGen_Psychological',
                'description': 'New anxious user, wheel generation processing, evaluating psychological agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'psychological',
                
                'trust_level_override': 25,    # Foundation phase
                'mood_valence_override': -0.1, # Slightly negative
                'mood_arousal_override': 0.6,  # Anxious energy
                'stress_level_override': 50,   # Moderate stress
                'reported_environment_override': 'home',
                'reported_focus_override': 'something easy to start with',
                
                'previous_agent_outputs': [
                    'resource_agent: limited_energy_home_environment',
                    'engagement_agent: no_clear_patterns_new_user'
                ],
                'expected_next_agents': ['strategy', 'wheel_activity', 'ethical']
            },
            
            # Strategy Agent Contexts  
            {
                'name': 'ADHD_User_WheelGen_Strategy',
                'description': 'ADHD user, wheel generation processing, evaluating strategy agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'strategy',
                
                'trust_level_override': 82,    # Integration phase
                'mood_valence_override': 0.5,  # Positive
                'mood_arousal_override': 0.7,  # High energy (ADHD)
                'stress_level_override': 30,   # Low stress
                'reported_focus_override': 'creative and novel activities',
                
                'previous_agent_outputs': [
                    'resource_agent: high_energy_creative_materials_available',
                    'engagement_agent: strong_preference_for_novelty_and_creativity',
                    'psychological_agent: integration_phase_ready_for_complex_challenges'
                ],
                'expected_next_agents': ['wheel_activity', 'ethical']
            },
            
            # Activity Feedback Contexts
            {
                'name': 'Post_Success_Feedback_Psychological',
                'description': 'User completed challenging activity successfully, evaluating psychological agent',
                'current_workflow_type': 'activity_feedback',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'psychological',
                
                'trust_level_override': 68,    # Expansion phase
                'mood_valence_override': 0.8,  # Very positive after success
                'mood_arousal_override': 0.5,  # Energized
                'stress_level_override': 15,   # Very low stress
                'reported_focus_override': 'share success and plan next steps',
                
                'previous_agent_outputs': [
                    'activity_completed: creative_writing_exercise_30min',
                    'user_feedback: felt_challenging_but_very_rewarding'
                ],
                'expected_next_agents': ['mentor']
            },
            
            # Orchestrator Agent Contexts
            {
                'name': 'Complex_WheelGen_Orchestrator',
                'description': 'Complex wheel generation scenario, evaluating orchestrator agent coordination',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'orchestrator',
                
                'trust_level_override': 55,    # Expansion phase
                'mood_valence_override': 0.2,  # Slightly positive
                'mood_arousal_override': 0.3,  # Moderate energy
                'stress_level_override': 40,   # Moderate stress
                'reported_focus_override': 'balanced activities',
                
                'previous_agent_outputs': [],
                'expected_next_agents': ['resource', 'engagement', 'psychological', 'strategy', 'wheel_activity', 'ethical']
            },
            
            # Resource Agent Contexts
            {
                'name': 'Limited_Resources_WheelGen_Resource',
                'description': 'User with limited resources, evaluating resource agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'resource',
                
                'trust_level_override': 35,    # Foundation phase
                'mood_valence_override': -0.2, # Slightly negative
                'mood_arousal_override': 0.1,  # Low energy
                'stress_level_override': 60,   # High stress
                'reported_environment_override': 'home',
                'reported_time_availability_override': '15_minutes',
                'reported_focus_override': 'low cost activities',
                
                'previous_agent_outputs': [
                    'orchestrator: assess_available_resources_and_constraints'
                ],
                'expected_next_agents': ['engagement', 'psychological']
            },
            
            # Engagement Agent Contexts
            {
                'name': 'High_Energy_WheelGen_Engagement',
                'description': 'High energy user, evaluating engagement agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'engagement',
                
                'trust_level_override': 75,    # Integration phase
                'mood_valence_override': 0.7,  # Very positive
                'mood_arousal_override': 0.8,  # High energy
                'stress_level_override': 20,   # Low stress
                'reported_focus_override': 'challenging and exciting activities',
                
                'previous_agent_outputs': [
                    'resource_agent: abundant_resources_available',
                    'psychological_agent: ready_for_high_challenge'
                ],
                'expected_next_agents': ['strategy', 'wheel_activity']
            },
            
            # Post-Spin Contexts
            {
                'name': 'Activity_Completion_PostSpin_Mentor',
                'description': 'User completed activity, post-spin workflow, evaluating mentor agent',
                'current_workflow_type': 'post_spin',
                'workflow_stage': 'result_delivery',
                'agent_role_being_evaluated': 'mentor',
                
                'trust_level_override': 60,    # Expansion phase
                'mood_valence_override': 0.6,  # Positive after completion
                'mood_arousal_override': 0.4,  # Moderate energy
                'stress_level_override': 25,   # Low stress
                'reported_focus_override': 'reflection and next steps',
                
                'previous_agent_outputs': [
                    'activity_result: completed_successfully',
                    'user_feedback: enjoyed_the_challenge'
                ],
                'expected_next_agents': []
            }
        ]
        
        for context_data in contexts_data:
            # Check if context already exists by name
            from apps.main.models import EvaluationContext
            existing = EvaluationContext.objects.filter(
                name=context_data['name'],
                is_active=True
            ).first()

            if not existing:
                service.create_context(context_data)
                self.stdout.write(
                    self.style.SUCCESS(f"Created evaluation context: {context_data['name']}")
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f"Context already exists: {context_data['name']}")
                )
        
        self.stdout.write(
            self.style.SUCCESS(f"Successfully processed {len(contexts_data)} evaluation contexts")
        )
