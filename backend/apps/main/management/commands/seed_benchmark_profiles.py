"""
Management command to seed benchmark UserProfiles
Follows Django management command best practices
"""
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory

class Command(BaseCommand):
    help = 'Seed benchmark UserProfiles for quick agent benchmarking'
    
    def add_arguments(self, parser):
        """Add command line arguments following Django conventions."""
        parser.add_argument(
            '--template',
            type=str,
            help='Specific template to create (default: all templates)'
        )
        parser.add_argument(
            '--recreate',
            action='store_true',
            help='Delete existing profiles and recreate them'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating'
        )
    
    def handle(self, *args, **options):
        """Execute command with proper error handling and logging."""
        try:
            if options['dry_run']:
                self._dry_run_mode(options)
                return
            
            if options['recreate']:
                self._recreate_profiles(options)
            else:
                self._create_profiles(options)
                
        except Exception as e:
            raise CommandError(f"Command failed: {e}")
    
    def _dry_run_mode(self, options):
        """Show what would be created in dry run mode."""
        templates = self._get_templates_to_process(options)
        
        self.stdout.write(
            self.style.WARNING('DRY RUN MODE - No profiles will be created')
        )
        
        for template_name in templates:
            template = BenchmarkProfileFactory.PROFILE_TEMPLATES[template_name]
            self.stdout.write(f"Would create: {template['profile_name']}")
            self.stdout.write(f"  Description: {template['description']}")
            self.stdout.write(f"  Goals: {len(template['goals'])}")
            self.stdout.write(f"  Beliefs: {len(template['beliefs'])}")
            self.stdout.write("")
    
    @transaction.atomic
    def _create_profiles(self, options):
        """Create benchmark profiles."""
        templates = self._get_templates_to_process(options)
        created_count = 0
        skipped_count = 0
        
        for template_name in templates:
            try:
                # Use get_or_create to avoid duplicates
                profile = BenchmarkProfileFactory.get_or_create_benchmark_profile(
                    template_name
                )
                
                if profile:
                    self.stdout.write(
                        self.style.SUCCESS(f"✓ Created: {profile.profile_name}")
                    )
                    created_count += 1
                else:
                    self.stdout.write(
                        self.style.WARNING(f"- Skipped (exists): {template_name}")
                    )
                    skipped_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"✗ Failed to create {template_name}: {e}")
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f"\nCompleted: {created_count} created, {skipped_count} skipped"
            )
        )
    
    def _recreate_profiles(self, options):
        """Delete existing profiles and recreate them."""
        from apps.user.models import UserProfile
        
        templates = self._get_templates_to_process(options)
        
        # Delete existing benchmark profiles
        template_objects = BenchmarkProfileFactory.PROFILE_TEMPLATES
        profile_names = [
            template_objects[name]['profile_name'] 
            for name in templates
        ]
        
        deleted_count = UserProfile.objects.filter(
            profile_name__in=profile_names,
            is_real=False
        ).delete()[0]
        
        if deleted_count > 0:
            self.stdout.write(
                self.style.WARNING(f"Deleted {deleted_count} existing profiles")
            )
        
        # Create new profiles
        self._create_profiles(options)
    
    def _get_templates_to_process(self, options):
        """Get list of templates to process based on options."""
        if options['template']:
            template_name = options['template']
            if template_name not in BenchmarkProfileFactory.PROFILE_TEMPLATES:
                available = list(BenchmarkProfileFactory.PROFILE_TEMPLATES.keys())
                raise CommandError(
                    f"Unknown template '{template_name}'. Available: {available}"
                )
            return [template_name]
        else:
            return list(BenchmarkProfileFactory.PROFILE_TEMPLATES.keys())
