from django.core.management.base import BaseCommand
from django.db import transaction
import importlib
import inspect
import sys
import logging

logger = logging.getLogger(__name__)

# Define the default list of modules where tools are expected
DEFAULT_TOOL_MODULES = [
    'apps.main.agents.tools.tools',
    'apps.main.agents.tools.extra_tools',
    'apps.main.agents.tools.get_user_profile_tool',
    'apps.main.agents.tools.dispatcher_tools',
    'apps.main.agents.tools.mentor_tools',
    'apps.main.agents.tools.update_current_mood_tool',
    'apps.main.agents.tools.resource_tools',
    'apps.main.agents.tools.engagement_tools',
    'apps.main.agents.tools.psychological_tools',
    'apps.main.agents.tools.activity_tools',
    'apps.main.agents.tools.ethical_tools',
    'apps.main.agents.tools.create_user_profile_data_tool',
    'apps.main.agents.tools.profile_extraction_tools',  # Phase 3: Profile data extraction tools
    # Add other tool modules here as needed
]

class Command(BaseCommand):
    help = 'Registers/updates tool handlers defined with @register_tool in the database idempotently.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--module',
            type=str,  # Changed to str to prevent splitting
            action='append',  # Allow multiple modules as separate arguments
            help='Specific module(s) to scan for tools (e.g., apps.main.agents.tools.tools apps.main.agents.tools.extra_tools). If omitted, scans default modules.'
        )

    def handle(self, *args, **options):
        modules_input = options.get('module')
        
        # Ensure modules_to_scan is always a list
        if modules_input:
            # Handle the case where modules might be a list of strings or a single string
            modules_to_scan = []
            for module in modules_input:
                # If the module contains spaces, it might be multiple modules in one argument
                if ' ' in module:
                    modules_to_scan.extend(module.split())
                else:
                    modules_to_scan.append(module)
        else:
            modules_to_scan = DEFAULT_TOOL_MODULES
            
        self.stdout.write(self.style.NOTICE(f"Scanning for tools in modules: {', '.join(modules_to_scan)}"))
        
        imported_modules_count = 0
        failed_imports = []

        # Import all specified/default modules to trigger @register_tool decorators
        for module_name in modules_to_scan:
            try:
                # Force reload if already imported to ensure decorators run in dev environments
                if module_name in sys.modules:
                    importlib.reload(sys.modules[module_name])
                    logger.debug(f"Reloaded module: {module_name}")
                else:
                    importlib.import_module(module_name)
                    logger.debug(f"Imported module: {module_name}")
                imported_modules_count += 1
            except ImportError as e:
                # Log specific import errors but continue
                self.stdout.write(self.style.WARNING(f"Could not import module {module_name}: {str(e)}"))
                logger.warning(f"Could not import module {module_name}: {str(e)}")
                failed_imports.append(module_name)
            except Exception as e:
                # Log unexpected errors but continue
                self.stdout.write(self.style.ERROR(f"Unexpected error importing module {module_name}: {str(e)}"))
                logger.error(f"Unexpected error importing module {module_name}: {str(e)}", exc_info=True)
                failed_imports.append(module_name)

        if imported_modules_count > 0:
            self.stdout.write(self.style.SUCCESS(f"Successfully processed {imported_modules_count} potential tool module(s)."))
        else:
            self.stdout.write(self.style.ERROR("No tool modules could be successfully processed."))
            # Decide if we should exit. If sync can handle an empty registry, maybe continue?
            # For now, let's continue to allow sync to potentially deactivate old tools.
            # return 

        if failed_imports:
             self.stdout.write(self.style.WARNING(f"Failed to import modules: {', '.join(failed_imports)}"))
       
        # Synchronize the in-memory registry with the database
        self.stdout.write(self.style.NOTICE("Synchronizing tool registry with database..."))
        try:
            from apps.main.agents.tools.tools_util import sync_tool_registry_with_database
            sync_tool_registry_with_database()
            self.stdout.write(self.style.SUCCESS("Successfully synchronized tools with database"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error synchronizing tools: {str(e)}"))