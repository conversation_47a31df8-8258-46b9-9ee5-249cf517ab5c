"""
Management command to seed archetypal scenarios for post-spin workflow benchmarking.

This command creates comprehensive test scenarios that cover the full spectrum of 
post-spin workflow behaviors, from simple activity acceptance to complex emergency 
refusal situations. These scenarios leverage the context accumulated from real user 
interactions and testing to provide meaningful benchmarking data.

Usage:
    python manage.py seed_post_spin_benchmark_scenarios
    python manage.py seed_post_spin_benchmark_scenarios --clear-existing
"""

import json
from django.core.management.base import BaseCommand
from apps.main.models import (
    BenchmarkScenario,
    LLMConfig
)
from apps.user.models import UserProfile


class Command(BaseCommand):
    help = 'Seed archetypal scenarios for post-spin workflow benchmarking'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing post-spin benchmark scenarios before seeding',
        )

    def handle(self, *args, **options):
        if options['clear_existing']:
            self.stdout.write('🧹 Clearing existing post-spin benchmark scenarios...')
            BenchmarkScenario.objects.filter(workflow_type='post_spin').delete()
            self.stdout.write(self.style.SUCCESS('✅ Cleared existing scenarios'))

        self.stdout.write('🌱 Seeding post-spin benchmark scenarios...')
        
        # Create scenarios
        scenarios_created = 0
        
        # Get test users and LLM configs
        test_users = list(UserProfile.objects.filter(is_real=False))
        all_users = list(UserProfile.objects.all())
        llm_configs = list(LLMConfig.objects.all()[:2])

        if not test_users:
            self.stdout.write(self.style.WARNING('⚠️  No test users found. Using all available users.'))
            test_users = all_users

        if not test_users:
            self.stdout.write(self.style.ERROR('❌ No users found at all. Please run user seeding commands first.'))
            return

        if not llm_configs:
            self.stdout.write(self.style.ERROR('❌ No LLM configs found. Please seed LLM configs first.'))
            return

        # Ensure we have enough users by cycling through available ones
        if len(test_users) < 3:
            # Duplicate users to have at least 3 for variety
            while len(test_users) < 3:
                test_users.extend(test_users[:min(len(test_users), 3 - len(test_users))])

        self.stdout.write(f'📊 Using {len(test_users)} users and {len(llm_configs)} LLM configs')

        # Scenario 1: Simple Activity Acceptance - Physical Activity
        scenario_1 = self.create_scenario(
            name="Post-Spin: Physical Activity Acceptance",
            description="User spins wheel and gets a physical activity, accepts it happily",
            user_profile=test_users[0],
            llm_config=llm_configs[0],
            activity_data={
                "activity_tailored_id": "physical_running_001",
                "name": "30-Minute Energizing Run",
                "description": "A moderate-pace run to boost energy and clear your mind. Perfect for when you're feeling restless and need physical movement.",
                "domain": "physical",
                "duration_minutes": 30,
                "challenge_rating": 45,
                "resources_required": ["running shoes", "outdoor space or treadmill"]
            },
            expected_response_type="acceptance",
            evaluation_criteria={
                "mentor_tone": "encouraging and supportive",
                "activity_instructions_quality": "clear and actionable",
                "goal_connection": "relates activity to user's restless energy",
                "post_activity_guidance": "mentions follow-up and reflection",
                "estimated_duration_accuracy": "matches activity duration",
                "resource_awareness": "acknowledges required resources"
            }
        )
        scenarios_created += 1

        # Scenario 2: Creative Activity Acceptance - Artistic Expression
        scenario_2 = self.create_scenario(
            name="Post-Spin: Creative Activity Acceptance",
            description="User spins wheel and gets a creative activity, shows enthusiasm",
            user_profile=test_users[1],
            llm_config=llm_configs[0],
            activity_data={
                "activity_tailored_id": "creative_sketching_002",
                "name": "Mindful Nature Sketching",
                "description": "Spend time observing and sketching natural elements around you. Focus on details and let your creativity flow without judgment.",
                "domain": "creative",
                "duration_minutes": 45,
                "challenge_rating": 25,
                "resources_required": ["paper", "pencil or pen", "natural setting"]
            },
            expected_response_type="acceptance",
            evaluation_criteria={
                "mentor_tone": "inspiring and creative",
                "activity_instructions_quality": "detailed and encouraging",
                "creativity_emphasis": "encourages artistic expression",
                "mindfulness_integration": "connects to present-moment awareness",
                "beginner_friendliness": "accessible to all skill levels"
            }
        )
        scenarios_created += 1

        # Scenario 3: Emergency Refusal - Time Constraint
        scenario_3 = self.create_scenario(
            name="Post-Spin: Emergency Refusal - Time Constraint",
            description="User spins wheel but suddenly has an emergency time constraint",
            user_profile=test_users[2],
            llm_config=llm_configs[1],
            activity_data={
                "activity_tailored_id": "learning_language_003",
                "name": "60-Minute Language Learning Session",
                "description": "Immersive language practice session with conversation exercises and vocabulary building.",
                "domain": "learning",
                "duration_minutes": 60,
                "challenge_rating": 55,
                "resources_required": ["language app", "quiet space", "notebook"]
            },
            expected_response_type="emergency_refusal",
            emergency_context={
                "user_response": "Oh no, I just got an urgent call from work. I can't do a 60-minute session right now. I only have 10 minutes before I need to leave.",
                "constraint_type": "time_emergency",
                "available_time": 10,
                "stress_level": "high"
            },
            evaluation_criteria={
                "empathy_demonstration": "acknowledges emergency with understanding",
                "alternative_generation": "provides quick 10-minute alternative",
                "stress_reduction": "offers calming activity option",
                "flexibility_showcase": "adapts to changed circumstances",
                "trust_maintenance": "maintains supportive relationship",
                "emergency_validation": "validates that emergencies happen"
            }
        )
        scenarios_created += 1

        # Scenario 4: Emergency Refusal - Physical Limitation
        scenario_4 = self.create_scenario(
            name="Post-Spin: Emergency Refusal - Physical Limitation",
            description="User spins wheel but discovers physical limitation prevents activity",
            user_profile=test_users[0],
            llm_config=llm_configs[1],
            activity_data={
                "activity_tailored_id": "physical_hiking_004",
                "name": "Mountain Trail Hiking Adventure",
                "description": "Challenging 2-hour hike through mountain trails with elevation gain and scenic views.",
                "domain": "physical",
                "duration_minutes": 120,
                "challenge_rating": 75,
                "resources_required": ["hiking boots", "water bottle", "trail access"]
            },
            expected_response_type="emergency_refusal",
            emergency_context={
                "user_response": "I'm really sorry, but I just realized I twisted my ankle yesterday and it's still sore. I can't do hiking right now. I need something gentler.",
                "constraint_type": "physical_limitation",
                "limitation_details": "ankle injury",
                "mobility_level": "limited"
            },
            evaluation_criteria={
                "injury_sensitivity": "responds with care about physical limitation",
                "alternative_appropriateness": "suggests gentle, low-impact activity",
                "safety_prioritization": "emphasizes healing and safety",
                "encouragement_balance": "maintains motivation while being realistic",
                "adaptation_quality": "shows understanding of physical constraints"
            }
        )
        scenarios_created += 1

        # Scenario 5: Complex Acceptance - Social Activity
        scenario_5 = self.create_scenario(
            name="Post-Spin: Social Activity with Coordination Needs",
            description="User gets social activity requiring coordination with others",
            user_profile=test_users[1],
            llm_config=llm_configs[0],
            activity_data={
                "activity_tailored_id": "social_game_night_005",
                "name": "Board Game Night with Friends",
                "description": "Organize a fun board game evening with 3-4 friends. Choose games that encourage laughter and connection.",
                "domain": "social",
                "duration_minutes": 180,
                "challenge_rating": 35,
                "resources_required": ["board games", "friends availability", "snacks", "comfortable space"]
            },
            expected_response_type="acceptance",
            evaluation_criteria={
                "coordination_guidance": "provides tips for organizing with others",
                "social_benefits_emphasis": "highlights connection and fun aspects",
                "practical_suggestions": "offers specific game recommendations",
                "flexibility_options": "suggests alternatives if friends unavailable",
                "preparation_support": "helps with planning and setup"
            }
        )
        scenarios_created += 1

        # Scenario 6: Emergency Refusal - Emotional State
        scenario_6 = self.create_scenario(
            name="Post-Spin: Emergency Refusal - Emotional Overwhelm",
            description="User spins wheel but is emotionally overwhelmed and needs gentler approach",
            user_profile=test_users[2],
            llm_config=llm_configs[1],
            activity_data={
                "activity_tailored_id": "social_networking_006",
                "name": "Professional Networking Event",
                "description": "Attend a local professional networking event to meet new contacts and practice conversation skills.",
                "domain": "social",
                "duration_minutes": 90,
                "challenge_rating": 65,
                "resources_required": ["business cards", "professional attire", "event registration"]
            },
            expected_response_type="emergency_refusal",
            emergency_context={
                "user_response": "I'm feeling really anxious today and overwhelmed. The thought of networking and meeting new people feels too much right now. I need something calming.",
                "constraint_type": "emotional_state",
                "emotional_state": "anxious and overwhelmed",
                "energy_level": "low"
            },
            evaluation_criteria={
                "emotional_validation": "acknowledges and validates emotional state",
                "calming_alternative": "suggests soothing, low-pressure activity",
                "anxiety_sensitivity": "shows understanding of social anxiety",
                "self_care_emphasis": "prioritizes emotional well-being",
                "gentle_encouragement": "maintains hope without pressure"
            }
        )
        scenarios_created += 1

        # Scenario 7: Learning Activity with Resource Constraints
        scenario_7 = self.create_scenario(
            name="Post-Spin: Learning Activity - Resource Adaptation",
            description="User gets learning activity but lacks some required resources",
            user_profile=test_users[0],
            llm_config=llm_configs[0],
            activity_data={
                "activity_tailored_id": "learning_coding_007",
                "name": "Python Programming Tutorial",
                "description": "Complete a 90-minute Python programming tutorial focusing on data structures and algorithms.",
                "domain": "learning",
                "duration_minutes": 90,
                "challenge_rating": 60,
                "resources_required": ["computer", "Python IDE", "stable internet", "quiet workspace"]
            },
            expected_response_type="acceptance",
            evaluation_criteria={
                "resource_flexibility": "suggests alternatives for missing resources",
                "learning_encouragement": "motivates continued skill development",
                "difficulty_calibration": "acknowledges challenge level appropriately",
                "progress_tracking": "mentions ways to measure learning progress",
                "practical_application": "connects learning to real-world use"
            }
        )
        scenarios_created += 1

        # Scenario 8: Mindfulness Activity - Perfect Match
        scenario_8 = self.create_scenario(
            name="Post-Spin: Mindfulness Activity - Ideal Alignment",
            description="User gets mindfulness activity that perfectly matches their current state",
            user_profile=test_users[1],
            llm_config=llm_configs[1],
            activity_data={
                "activity_tailored_id": "mindfulness_meditation_008",
                "name": "20-Minute Guided Meditation",
                "description": "A calming guided meditation session focusing on breath awareness and present-moment mindfulness.",
                "domain": "mindfulness",
                "duration_minutes": 20,
                "challenge_rating": 15,
                "resources_required": ["quiet space", "comfortable seating", "meditation app or audio"]
            },
            expected_response_type="acceptance",
            evaluation_criteria={
                "mindfulness_benefits_explanation": "clearly explains meditation benefits",
                "guidance_quality": "provides helpful meditation tips",
                "environment_setup": "suggests optimal meditation environment",
                "beginner_support": "accessible to meditation newcomers",
                "post_meditation_integration": "suggests how to carry mindfulness forward"
            }
        )
        scenarios_created += 1

        # Scenario 9: Emergency Refusal - Equipment Failure
        scenario_9 = self.create_scenario(
            name="Post-Spin: Emergency Refusal - Equipment Unavailable",
            description="User spins wheel but discovers required equipment is broken/unavailable",
            user_profile=test_users[2],
            llm_config=llm_configs[0],
            activity_data={
                "activity_tailored_id": "creative_music_009",
                "name": "Guitar Practice Session",
                "description": "Practice guitar for 45 minutes, working on chord progressions and a new song.",
                "domain": "creative",
                "duration_minutes": 45,
                "challenge_rating": 40,
                "resources_required": ["guitar", "tuner", "music sheets or tabs", "quiet practice space"]
            },
            expected_response_type="emergency_refusal",
            emergency_context={
                "user_response": "Oh no! I just remembered my guitar is at my friend's place and I won't get it back until next week. I can't do guitar practice without it.",
                "constraint_type": "equipment_unavailable",
                "missing_resource": "guitar",
                "alternative_interest": "still interested in music/creative activities"
            },
            evaluation_criteria={
                "equipment_understanding": "acknowledges equipment limitation",
                "creative_alternative": "suggests music-related activity without guitar",
                "interest_preservation": "maintains focus on creative/musical growth",
                "resourcefulness": "shows creative problem-solving",
                "future_planning": "mentions resuming guitar when available"
            }
        )
        scenarios_created += 1

        # Scenario 10: Complex Social Activity - Group Coordination
        scenario_10 = self.create_scenario(
            name="Post-Spin: Complex Social Activity - Event Planning",
            description="User gets complex social activity requiring significant coordination",
            user_profile=test_users[0],
            llm_config=llm_configs[1],
            activity_data={
                "activity_tailored_id": "social_dinner_party_010",
                "name": "Host Dinner Party for 6 Friends",
                "description": "Plan and host a dinner party for 6 friends, including menu planning, cooking, and creating a welcoming atmosphere.",
                "domain": "social",
                "duration_minutes": 240,
                "challenge_rating": 70,
                "resources_required": ["kitchen access", "dining space", "groceries", "cookware", "friends availability"]
            },
            expected_response_type="acceptance",
            evaluation_criteria={
                "planning_support": "provides structured planning approach",
                "time_management": "helps break down the 4-hour commitment",
                "stress_reduction": "offers tips to make hosting enjoyable",
                "menu_suggestions": "provides practical menu ideas",
                "social_benefits": "emphasizes connection and community building",
                "flexibility_options": "suggests simpler alternatives if needed"
            }
        )
        scenarios_created += 1

        self.stdout.write(
            self.style.SUCCESS(f'✅ Successfully created {scenarios_created} post-spin benchmark scenarios')
        )
        
        # Display summary
        self.stdout.write('\n📊 Scenario Summary:')
        self.stdout.write('  • 3 Acceptance scenarios (physical, creative, social)')
        self.stdout.write('  • 3 Emergency refusal scenarios (time, physical, emotional)')
        self.stdout.write('  • Coverage of all major activity domains')
        self.stdout.write('  • Multiple user profiles and LLM configs')
        self.stdout.write('\n🎯 These scenarios can now be used with the benchmarking system to evaluate post-spin workflow quality.')

    def create_scenario(self, name, description, user_profile, llm_config, activity_data, 
                       expected_response_type, evaluation_criteria, emergency_context=None):
        """Create a benchmark scenario for post-spin workflow testing."""
        
        # Create the scenario input that mimics a real spin result
        scenario_input = {
            "user_profile_id": str(user_profile.id),
            "activity_selection": activity_data,
            "context_packet": {
                "user_id": str(user_profile.id),
                "user_profile_id": str(user_profile.id),
                "session_timestamp": "2025-06-14T12:00:00Z",
                "workflow_metadata": {
                    "activity_id": activity_data["activity_tailored_id"],
                    "activity_name": activity_data["name"]
                },
                "original_content": {
                    "activity_tailored_id": activity_data["activity_tailored_id"],
                    "name": activity_data["name"],
                    "description": activity_data["description"],
                    "user_profile_id": str(user_profile.id)
                },
                "user_ws_session_name": f"client_session_{user_profile.id}"
            }
        }
        
        # Add emergency context if provided
        if emergency_context:
            scenario_input["emergency_simulation"] = emergency_context
        
        # Create evaluation criteria specific to post-spin workflow
        post_spin_criteria = {
            "workflow_completion": "workflow completes successfully without errors",
            "response_appropriateness": "response matches expected type (acceptance/emergency_refusal)",
            "mentor_communication_quality": "mentor response is clear, supportive, and well-structured",
            "activity_instruction_clarity": "activity instructions are detailed and actionable",
            "user_context_integration": "response incorporates user profile and preferences",
            **evaluation_criteria
        }
        
        # Create the scenario without tags first
        scenario = BenchmarkScenario.objects.create(
            name=name,
            description=description,
            workflow_type='post_spin',
            agent_role='mentor',  # Post-spin scenarios test the mentor agent
            input_data=scenario_input,
            expected_quality_criteria=post_spin_criteria,
            user_profile_context={
                "user_profile_id": str(user_profile.id),
                "profile_name": user_profile.profile_name,
                "is_real": user_profile.is_real
            },
            activity_context=activity_data,
            metadata={
                "expected_response_type": expected_response_type,
                "emergency_context": emergency_context,
                "expected_output_structure": {
                    "workflow_completed": True,
                    "user_response": "string",
                    "response_type": expected_response_type,
                    "activity_presentation": "object",
                    "workflow_metadata": "object"
                }
            }
        )

        # Note: Tags would need to be created as BenchmarkTag objects first
        # For now, we'll store tag information in metadata
        
        self.stdout.write(f'  ✅ Created: {name}')
        return scenario
