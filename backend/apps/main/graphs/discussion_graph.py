# backend/apps/main/graphs/discussion_graph.py

from typing import Dict, Any, List, Optional, Tuple
import uuid
import logging
# Removed BaseModel, Field, Literal imports as they are now in state_models
from langgraph.graph import END, StateGraph
from apps.main.services.event_service import EventService # Import EventService

# Import state model from the new file
from .state_models import DiscussionState, WorkflowTransitionRequest

from apps.main.agents.mentor_agent import MentorAgent
from apps.main.services.conversation_dispatcher import ConversationDispatcher

# Configure logging
logger = logging.getLogger(__name__)
# Removed basicConfig call to avoid potential conflicts if logging is configured elsewhere
# logging.basicConfig(level=logging.DEBUG)

# --- REMOVED Model Definitions (Moved to state_models.py) ---
# DiscussionStage, WorkflowTransitionRequest, DiscussionState definitions are removed from here.
# --- End Removal ---


def create_discussion_graph(user_profile_id: str, llm_config_id: Optional[str] = None) -> StateGraph:
    """
    Create a LangGraph workflow for discussion/reflection interactions.

    This workflow is designed for:
    1. Deep conversational exploration
    2. User reflection and guidance
    3. Potential profile or goal refinement
    4. Seamless transitions to other workflows when appropriate

    Args:
        user_profile_id: The ID of the user profile
        llm_config_id: Optional LLM config ID for debug mode

    Returns:
        StateGraph: The configured discussion workflow graph
    """
    # Initialize the graph with the state model
    workflow = StateGraph(DiscussionState)

    # Get LLM config if provided
    llm_config = None
    if llm_config_id:
        try:
            from apps.main.models import LLMConfig
            llm_config = LLMConfig.objects.get(id=llm_config_id)
            logger.info(f"Using LLM config: {llm_config.name} (ID: {llm_config_id}) for discussion workflow")
        except Exception as e:
            logger.warning(f"Failed to load LLM config {llm_config_id}: {e}, using default")

    # Add the Mentor Agent as the primary (and only) node with LLM config
    workflow.add_node("mentor", MentorAgent(user_profile_id, llm_config=llm_config))
    
    # Define routing logic for the Mentor Agent
    def route_from_mentor(state: DiscussionState):
        # Safety mechanisms: Check iteration limits (inspired by successful onboarding workflow)
        state.iteration_count = getattr(state, 'iteration_count', 0) + 1
        if state.iteration_count >= state.max_iterations:
            logger.warning(f"🚨 Discussion workflow {state.workflow_id} reached maximum iterations ({state.iteration_count}), forcing completion.")
            state.completed = True
            state.current_stage = "workflow_complete"
            state.output_data["user_response"] = "Thank you for our conversation. I'm here whenever you'd like to continue exploring your goals and activities!"
            return END

        # Track conversation depth for intelligent completion
        state.conversation_depth = len([msg for msg in state.conversation_history if msg.get('role') == 'user'])

        # Intelligent completion logic: End if conversation has reached natural conclusion
        if (state.conversation_depth >= 3 and
            state.current_stage in ["guidance_preparation", "reflection"] and
            state.iteration_count >= 3):
            logger.info(f"Discussion workflow {state.workflow_id} reached natural conclusion after {state.conversation_depth} exchanges.")
            state.completed = True
            state.current_stage = "workflow_complete"
            return END

        # Check for errors first
        if state.error:
            logger.error(f"Workflow {state.workflow_id} ending due to error: {state.error}")
            return END

        # Check if a specific transition to another workflow was requested by the agent
        if state.transition_request is not None:
            logger.info(f"Workflow {state.workflow_id} ending to transition to {state.transition_request.target_workflow}.")
            return END # End this graph to allow transition

        # Check the stage determined by the agent in the previous step
        # If the agent set the stage to 'workflow_complete', end the graph.
        if state.current_stage == "workflow_complete":
            logger.info(f"Workflow {state.workflow_id} ending as stage is 'workflow_complete'.")
            return END
        else:
            # Otherwise, continue the loop by calling the mentor agent again
            logger.debug(f"Workflow {state.workflow_id} continuing to stage {state.current_stage}, routing back to mentor (iteration {state.iteration_count}).")
            return "mentor"
    
    # Add conditional edges for mentor node
    workflow.add_conditional_edges(
        "mentor",
        route_from_mentor,
        {
            "mentor": "mentor",
            END: END
        }
    )
    
    # Set the entry point to the Mentor Agent
    workflow.set_entry_point("mentor")
    
    return workflow

async def run_discussion_workflow(
    user_profile_id: str,
    context_packet: Dict[str, Any],
    workflow_id: Optional[str] = None,
    workflow_input: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Run the discussion workflow for a specific user.

    This function supports both the legacy interface (individual parameters) and the new
    benchmarking interface (workflow_input dictionary) for maximum compatibility.

    Args:
        user_profile_id: The ID of the user profile (legacy interface)
        context_packet: Initial context information from the user (legacy interface)
        workflow_id: Optional workflow ID (will generate a new one if not provided)
        workflow_input: Dictionary containing all workflow parameters (new benchmarking interface)
                       Expected keys: user_profile_id, context_packet, workflow_id,
                       use_real_llm, use_real_tools, use_real_db, mock_tools

    Returns:
        Dict[str, Any]: Final state and output from the workflow
    """
    # Handle both legacy and new interfaces
    if workflow_input is not None:
        # New benchmarking interface - extract parameters from workflow_input
        user_profile_id = workflow_input.get("user_profile_id", user_profile_id)
        context_packet = workflow_input.get("context_packet", context_packet)
        workflow_id = workflow_input.get("workflow_id", workflow_id)
        use_real_llm = workflow_input.get("use_real_llm", False)
        use_real_tools = workflow_input.get("use_real_tools", False)
        use_real_db = workflow_input.get("use_real_db", False)
        mock_tools = workflow_input.get("mock_tools")

        logger.info(f"Running discussion workflow with benchmarking interface: real_llm={use_real_llm}, "
                   f"real_tools={use_real_tools}, real_db={use_real_db}")
    else:
        # Legacy interface - use provided parameters
        use_real_llm = False
        use_real_tools = False
        use_real_db = False
        mock_tools = None

        logger.info("Running discussion workflow with legacy interface (all operations mocked)")

    # Validate required parameters
    if not user_profile_id:
        raise ValueError("user_profile_id is required")
    if not context_packet:
        raise ValueError("context_packet is required")

    # Extract LLM config ID from context packet if provided (debug mode)
    llm_config_id = context_packet.get('llm_config_id')
    if llm_config_id:
        logger.info(f"Discussion workflow using LLM config ID: {llm_config_id}")

    # Create the workflow with LLM config
    workflow = create_discussion_graph(user_profile_id, llm_config_id)
    
    # Compile the workflow
    app = workflow.compile()
    
    # Set up the initial state
    if not workflow_id:
        workflow_id = str(uuid.uuid4())
        
    # Create initial state with user details and context
    initial_state = DiscussionState(
        workflow_id=workflow_id,
        user_profile_id=user_profile_id,
        initial_context_packet=context_packet,
        user_ws_session_name=context_packet.get('user_ws_session_name')
    )
    
    # Log workflow initiation
    logger.info(f"Starting discussion workflow {workflow_id} for user {user_profile_id}")
    
    try:
        # Execute the workflow
        result = await app.ainvoke(initial_state)
        logger.info(f"Completed discussion workflow {workflow_id}")
        try:
            result = DiscussionState(**result)
        except Exception as e:
            # --- BEGIN CHANGE: Log the problematic result for debugging ---
            logger.error(f"Failed to construct DiscussionState from result: {result}. Error: {e}", exc_info=True)
            # Re-raise or handle appropriately, maybe return an error dict
            raise ValueError(f"Cannot construct state from result: {e}") # Re-raise for clarity
            # --- END CHANGE ---

        # Process the result first
        processed_result = _process_discussion_result(result)

        # --- REMOVED: Sending final agent response via EventService ---
        # This responsibility is now handled centrally by the calling Celery task
        # (execute_graph_workflow in agent_tasks.py) based on the workflow result.
        # This prevents duplicate messages from being sent.
        # --- END REMOVAL ---

        # Check if there's a transition request to another workflow
        if result.transition_request is not None:
            logger.info(f"Detected workflow transition request to: {result.transition_request.target_workflow}")
            # Process the transition
            # Convert the Pydantic model back to a dict for the handler if needed,
            # or update the handler to accept the model. Assuming handler needs dict for now.
            transition_request_dict = result.transition_request.model_dump(exclude_none=True)
            transition_result = await handle_workflow_transition(
                user_profile_id,
                transition_request_dict,
                result.user_ws_session_name
            )

            # Blend results
            # processed_result = _process_discussion_result(result) # Already done above
            processed_result["transitioned_to"] = result.transition_request.target_workflow
            processed_result["transition_result"] = transition_result

            return processed_result # Return after handling transition

        # Return standard processed result (even if message was sent)
        # The return value of the Celery task might still be useful for logging/history
        return processed_result
    
    except Exception as e:
        logger.error(f"Error in discussion workflow {workflow_id}: {str(e)}", exc_info=True)
        # Return error state
        return {
            "error": str(e),
            "workflow_id": workflow_id,
            "completed": False,
            "user_profile_id": user_profile_id
        }

async def handle_workflow_transition(
    user_profile_id: str,
    transition_request_dict: Dict[str, Any], # Expecting a dict now
    user_ws_session_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Handle the transition from discussion workflow to another workflow type.
    
    Args:
        user_profile_id: The user's profile ID
        transition_request: Details about the requested workflow transition
        user_ws_session_name: Optional WebSocket session for result routing
        
    Returns:
        Dict with transition results
    """
    try:
        # Create a conversation dispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=user_profile_id,
            user_ws_session_name=user_ws_session_name
        )
        
        # Prepare the message for the dispatcher using the dict
        # Important: Include the requested_workflow in metadata
        user_message = {
            "text": transition_request_dict.get("message", ""),
            "metadata": {
                "requested_workflow": transition_request_dict.get("target_workflow"), # Use correct key
                "transition_context": transition_request_dict.get("context", {}),
                "from_workflow": "discussion"
            }
        }
        
        # Process the message through the dispatcher
        # This will initiate the new workflow
        result = await dispatcher.process_message(user_message)
        
        return {
            "status": "transition_initiated",
            "to_workflow": transition_request_dict.get("target_workflow"), # Use correct key
            "dispatcher_result": result
        }
        
    except Exception as e:
        logger.error(f"Error handling workflow transition: {str(e)}", exc_info=True)
        return {
            "status": "transition_failed",
            "error": str(e)
        }

def _process_discussion_result(result: DiscussionState) -> Dict[str, Any]:
    """
    Process the final result of the discussion workflow.

    Args:
        result: The final workflow state

    Returns:
        Dict containing processed workflow results
    """
    # Extract key information
    profile_updates = result.profile_updates if hasattr(result, 'profile_updates') else {}
    output_data = result.output_data if hasattr(result, 'output_data') else {}

    # Prepare the result dictionary
    processed_result = {
        "workflow_id": result.workflow_id,
        "user_profile_id": result.user_profile_id,
        "completed": result.completed,
        "conversation_history": result.conversation_history,
        "profile_updates": profile_updates,
        "output_data": output_data,
        # --- BEGIN CHANGE: Add user_ws_session_name ---
        "user_ws_session_name": result.user_ws_session_name
        # --- END CHANGE ---
    }

    # Add conversation state updates based on workflow outcome
    if result.transition_request:
        processed_result["conversation_state_updates"] = {
            'phase': 'transitioning',
            'last_workflow': 'discussion',
            'awaiting_response_type': None,
            'context': {
                'transition_to': result.transition_request.target_workflow
            }
        }
    elif result.current_stage == "guidance_preparation":
        processed_result["conversation_state_updates"] = {
            'phase': 'awaiting_reflection',
            'awaiting_response_type': 'reflection_answer',
            'context': {
                'guidance_provided': True,
                'last_question': result.output_data.get('question')
            }
        }
    else:
        processed_result["conversation_state_updates"] = {
            'phase': 'discussion_active',
            'last_workflow': 'discussion',
            'awaiting_response_type': None
        }

    # Add error information if present
    if result.error:
        processed_result["error"] = result.error
        
    # Add transition request information if present
    if hasattr(result, 'transition_request') and result.transition_request:
        processed_result["transition_requested"] = True
        processed_result["requested_workflow"] = result.transition_request.target_workflow # Access attribute
    
    return processed_result
