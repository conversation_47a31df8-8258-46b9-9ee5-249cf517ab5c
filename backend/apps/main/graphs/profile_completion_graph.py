# backend/apps/main/graphs/profile_completion_graph.py

"""
Unified Profile Completion Workflow Graph

This module implements a comprehensive LangGraph workflow for user profile completion
that combines both conversational engagement and data processing capabilities.

Key Features:
- Conversational profile completion using Mentor agent
- Natural dialogue to gather user information
- Contextual questions based on profile gaps
- Data extraction, validation, and storage
- Seamless integration with existing agent infrastructure

Architecture:
- Unified workflow combining conversation and data processing
- Uses Mentor agent for conversational flow
- Leverages existing agent tools and capabilities
- Provides human-friendly responses
- Maintains conversation context and history

The workflow engages users through natural conversation to:
1. Understand their goals and aspirations
2. Learn about their preferences and constraints
3. Build comprehensive user profiles
4. Extract and store profile information
5. Provide personalized guidance and support

This approach ensures users have a welcoming, conversational experience
while efficiently gathering and processing the information needed for personalization.
"""

import logging
from typing import Any, Dict, Optional, List
import uuid
from pydantic import BaseModel, Field
from langgraph.graph import END, StateGraph

from apps.main.agents.tools.tools_util import execute_tool
from apps.main.agents.mentor_agent import MentorAgent
from apps.main.graphs.state_models import OnboardingStage

logger = logging.getLogger(__name__)


class ProfileCompletionState(BaseModel):
    """
    Unified state model for the profile completion workflow.
    Combines conversational engagement and data processing capabilities.
    """
    # Workflow identification
    workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_profile_id: str
    user_ws_session_name: Optional[str] = None

    # Conversational context (from onboarding_graph.py)
    initial_context_packet: Dict[str, Any] = Field(default_factory=dict)
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list)

    # Data processing context (existing)
    profile_data_packet: Dict[str, Any] = Field(default_factory=dict)
    extracted_information: Dict[str, Any] = Field(default_factory=dict)

    # Agent outputs and state tracking
    output_data: Dict[str, Any] = Field(default_factory=dict)
    processing_results: Dict[str, Any] = Field(default_factory=dict)

    # Workflow state tracking
    completion_stage: str = "conversational"  # conversational -> data_processing -> completed
    onboarding_stage: OnboardingStage = "initial_greeting"
    last_agent: Optional[str] = "mentor"
    error: Optional[str] = None
    completed: bool = False

    # Profile completion tracking
    profile_updates: Dict[str, Any] = Field(default_factory=dict)
    initial_profile_completion: float = 0.0
    current_profile_completion: float = 0.0

    # Safety mechanisms
    iteration_count: int = 0
    max_iterations: int = 8  # Reasonable limit for profile completion conversation
    conversation_depth: int = 0

    # Agent Run ID tracking
    run_id: Optional[uuid.UUID] = Field(None, description="The UUID of the last agent run associated with this state.")


# Conversational profile completion functions (moved from onboarding_graph.py)
async def mentor_profile_completion_node(state: ProfileCompletionState, config: Optional[Dict[str, Any]] = None) -> ProfileCompletionState:
    """
    Mentor agent node for conversational profile completion.

    Handles the conversational flow to gather user information and complete their profile
    through natural, engaging dialogue.

    Moved from onboarding_graph.py to consolidate all profile completion logic.
    """
    try:
        logger.info(f"🤖 Mentor agent starting profile completion conversation for user {state.user_profile_id}")

        # Initialize the Mentor agent with profile completion context
        mentor_agent = MentorAgent(
            user_profile_id=state.user_profile_id
        )

        # Set onboarding stage in the agent
        mentor_agent.onboarding_stage = state.onboarding_stage

        # Process the conversation with profile completion context
        result = await mentor_agent.process(state)

        # Update state with agent results
        state.output_data.update(result.get('output_data', {}))
        state.run_id = result.get('run_id')

        # Check if conversational phase should be completed
        # The MentorAgent sets onboarding_stage to "completed" when it's done
        if (result.get('onboarding_completed', False) or
            result.get('state_updates', {}).get('onboarding_stage') == "completed"):
            state.completion_stage = "data_processing"
            state.onboarding_stage = "completed"
            logger.info(f"✅ Conversational profile completion finished for user {state.user_profile_id}")
        else:
            # Update onboarding stage based on conversation progress
            state.onboarding_stage = result.get('next_onboarding_stage', state.onboarding_stage)
            # Also check state_updates for onboarding_stage
            if 'state_updates' in result and 'onboarding_stage' in result['state_updates']:
                state.onboarding_stage = result['state_updates']['onboarding_stage']

        # Update conversation tracking and iteration count
        # CRITICAL FIX: Handle both Pydantic model and AddableValuesDict state objects
        if hasattr(state, 'conversation_depth'):
            state.conversation_depth += 1
            state.last_agent = "mentor"
            # Update iteration count for routing
            current_iteration = getattr(state, 'iteration_count', 0)
            state.iteration_count = current_iteration + 1
        else:
            # For AddableValuesDict, return updates
            current_iteration = state.get('iteration_count', 0)
            return {
                'conversation_depth': state.get('conversation_depth', 0) + 1,
                'last_agent': "mentor",
                'iteration_count': current_iteration + 1,
                'output_data': state.get('output_data', {}),
                'run_id': result.get('run_id'),
                'completed': False,  # Ensure we don't accidentally complete here
                'completion_stage': "awaiting_user_response"  # Set appropriate stage
            }

        logger.info(f"✅ Mentor agent completed profile completion step for user {state.user_profile_id}")

        return state

    except Exception as e:
        logger.error(f"❌ Error in mentor profile completion node: {e}")
        state.error = str(e)
        state.output_data["user_response"] = "I apologize, but I'm having some technical difficulties. Let me try to help you in a different way."
        return state


# Data processing functions for the unified architecture
async def _extract_profile_information(state: ProfileCompletionState, config: Optional[Dict[str, Any]] = None) -> ProfileCompletionState:
    """
    Extract profile information from conversation data.

    Unified Architecture: Processes conversation data from the mentor agent
    to extract structured profile information for storage.
    """
    try:
        logger.info(f"Extracting profile information for workflow {state.workflow_id}")

        # Extract information from conversation output or profile data packet
        conversation_data = state.output_data.get('user_response', '')
        profile_data = state.profile_data_packet
        extracted_info = {}

        # Combine conversation data and profile data for extraction
        text_to_process = conversation_data
        if profile_data.get('text'):
            text_to_process += " " + profile_data['text']

        # Use LLM-based extraction tools to process the data
        if text_to_process.strip():
            # Extract demographics information
            demographics_result = await execute_tool(
                tool_code="extract_demographics_from_text",
                tool_input={"input_data": {"text": text_to_process}},
                user_profile_id=state.user_profile_id,
                session_id=state.user_ws_session_name
            )
            extracted_info['demographics'] = demographics_result.get('demographics', {})

            # Extract goals information
            goals_result = await execute_tool(
                tool_code="extract_goals_from_text",
                tool_input={"input_data": {"text": text_to_process}},
                user_profile_id=state.user_profile_id,
                session_id=state.user_ws_session_name
            )
            extracted_info['goals'] = goals_result.get('goals', [])

            # Extract preferences information
            preferences_result = await execute_tool(
                tool_code="extract_preferences_from_text",
                tool_input={"input_data": {"text": text_to_process}},
                user_profile_id=state.user_profile_id,
                session_id=state.user_ws_session_name
            )
            extracted_info['preferences'] = preferences_result.get('preferences', [])

        # Store extracted information in state
        state.extracted_information = extracted_info
        state.completion_stage = "validation"

        logger.info(f"Successfully extracted profile information: {len(extracted_info)} categories")
        return state

    except Exception as e:
        logger.error(f"Error extracting profile information: {str(e)}")
        state.error = f"Information extraction failed: {str(e)}"
        return state


async def _validate_extracted_data(state: ProfileCompletionState, config: Optional[Dict[str, Any]] = None) -> ProfileCompletionState:
    """
    Validate the extracted profile data for completeness and accuracy.

    Unified Architecture: Data validation for profile completion workflow.
    """
    try:
        logger.info(f"Validating extracted data for workflow {state.workflow_id}")

        extracted_info = state.extracted_information
        validation_results = {
            'valid': True,
            'issues': [],
            'processed_data': {}
        }

        # Validate demographics data
        demographics = extracted_info.get('demographics', {})
        if demographics:
            # Basic validation - ensure required fields are present
            required_fields = ['age', 'location']
            for field in required_fields:
                if not demographics.get(field):
                    validation_results['issues'].append(f"Missing demographics field: {field}")

            validation_results['processed_data']['demographics'] = demographics

        # Validate goals data
        goals = extracted_info.get('goals', [])
        if goals:
            # Ensure goals have required structure
            processed_goals = []
            for goal in goals:
                if isinstance(goal, dict) and goal.get('title'):
                    processed_goals.append(goal)
                else:
                    validation_results['issues'].append(f"Invalid goal structure: {goal}")

            validation_results['processed_data']['goals'] = processed_goals

        # Validate preferences data
        preferences = extracted_info.get('preferences', [])
        if preferences:
            # Ensure preferences have required structure
            processed_preferences = []
            for pref in preferences:
                if isinstance(pref, dict) and pref.get('pref_name'):
                    processed_preferences.append(pref)
                else:
                    validation_results['issues'].append(f"Invalid preference structure: {pref}")

            validation_results['processed_data']['preferences'] = processed_preferences

        # Store validation results
        state.processing_results = validation_results

        if validation_results['issues']:
            logger.warning(f"Data validation found {len(validation_results['issues'])} issues")
            # Continue to storage anyway - partial data is better than no data

        state.completion_stage = "storage"

        logger.info(f"Data validation completed with {len(validation_results['issues'])} issues")
        return state

    except Exception as e:
        logger.error(f"Error validating extracted data: {str(e)}")
        state.error = f"Data validation failed: {str(e)}"
        return state


async def _store_profile_data(state: ProfileCompletionState, config: Optional[Dict[str, Any]] = None) -> ProfileCompletionState:
    """
    Store the validated profile data in the database.

    Unified Architecture: Database storage for profile completion workflow.
    """
    try:
        logger.info(f"Storing profile data for workflow {state.workflow_id}")

        validation_results = state.processing_results
        processed_data = validation_results.get('processed_data', {})
        storage_results = {
            'stored_items': [],
            'errors': []
        }

        # Store demographics data
        demographics = processed_data.get('demographics')
        if demographics:
            try:
                # Add user_profile_id to the input_data as required by the tool
                demographics_with_user_id = {**demographics, 'user_profile_id': state.user_profile_id}
                await execute_tool(
                    tool_code="create_user_demographics",
                    tool_input={"input_data": demographics_with_user_id},
                    user_profile_id=state.user_profile_id,
                    session_id=state.user_ws_session_name
                )
                storage_results['stored_items'].append('demographics')
                logger.info("Successfully stored demographics data")
            except Exception as e:
                storage_results['errors'].append(f"Demographics storage failed: {str(e)}")

        # Store goals data
        goals = processed_data.get('goals', [])
        for goal in goals:
            try:
                # Add user_profile_id to the input_data as required by the tool
                goal_with_user_id = {**goal, 'user_profile_id': state.user_profile_id}
                await execute_tool(
                    tool_code="create_user_goal",
                    tool_input={"input_data": goal_with_user_id},
                    user_profile_id=state.user_profile_id,
                    session_id=state.user_ws_session_name
                )
                storage_results['stored_items'].append(f"goal: {goal.get('title', 'unnamed')}")
            except Exception as e:
                storage_results['errors'].append(f"Goal storage failed: {str(e)}")

        # Store preferences data
        preferences = processed_data.get('preferences', [])
        for pref in preferences:
            try:
                # Add user_profile_id to the input_data as required by the tool
                pref_with_user_id = {**pref, 'user_profile_id': state.user_profile_id}
                await execute_tool(
                    tool_code="create_user_preference",
                    tool_input={"input_data": pref_with_user_id},
                    user_profile_id=state.user_profile_id,
                    session_id=state.user_ws_session_name
                )
                storage_results['stored_items'].append(f"preference: {pref.get('pref_name', 'unnamed')}")
            except Exception as e:
                storage_results['errors'].append(f"Preference storage failed: {str(e)}")

        # Update output data with storage results
        state.output_data.update({
            'storage_results': storage_results,
            'items_stored': len(storage_results['stored_items']),
            'errors_count': len(storage_results['errors'])
        })

        # Mark as completed
        state.completion_stage = "completed"
        state.completed = True

        logger.info(f"Profile data storage completed: {len(storage_results['stored_items'])} items stored, {len(storage_results['errors'])} errors")
        return state

    except Exception as e:
        logger.error(f"Error storing profile data: {str(e)}")
        state.error = f"Data storage failed: {str(e)}"
        return state


def route_profile_completion_flow(state):
    """
    Route the unified profile completion workflow based on current state.

    Determines whether to continue conversation, process data, complete workflow,
    or handle error conditions.

    CRITICAL FIX: This function must work with LangGraph's AddableValuesDict state objects.
    We cannot modify state directly - we can only return the next node name or END.
    State updates must happen in the node functions themselves.
    """
    # CRITICAL FIX: Handle both Pydantic model and AddableValuesDict state objects
    # Use safe access methods that work with both types
    def safe_get(obj, key, default=None):
        if hasattr(obj, key):
            return getattr(obj, key, default)
        elif hasattr(obj, 'get'):
            return obj.get(key, default)
        else:
            return default

    current_iteration = safe_get(state, 'iteration_count', 0) + 1
    workflow_id = safe_get(state, 'workflow_id', 'unknown')
    max_iterations = safe_get(state, 'max_iterations', 8)
    completed = safe_get(state, 'completed', False)
    completion_stage = safe_get(state, 'completion_stage', 'conversational')
    error = safe_get(state, 'error')

    # Safety check: prevent infinite loops
    if current_iteration >= max_iterations:
        logger.warning(f"🚨 Profile completion workflow {workflow_id} reached maximum iterations ({current_iteration}), forcing completion.")
        # We cannot modify state here - the node function will handle completion
        return END

    # Check for errors
    if error:
        logger.error(f"❌ Profile completion workflow {workflow_id} has error: {error}")
        return END

    # Check if workflow is completed
    if completed or completion_stage == "completed":
        logger.info(f"✅ Profile completion workflow {workflow_id} completed successfully")
        return END

    # CRITICAL FIX: Prevent infinite loops with proper state management
    # The mentor agent should only be called once per workflow to ask questions

    # Check if this is the first iteration
    if current_iteration == 1:
        # First iteration - mentor should ask questions
        logger.debug(f"🔄 First iteration - mentor asking questions for workflow {workflow_id}")
        return "mentor"

    # Route based on completion stage and iteration count
    if completion_stage == "conversational":
        if current_iteration == 2:
            # Mentor has asked questions - complete the workflow for now
            # The user will respond in a separate conversation, not in this workflow
            logger.debug(f"🔄 Mentor asked questions, completing workflow {workflow_id} - awaiting user response")
            return END
        else:
            # Prevent infinite loops - complete if we've already asked questions
            logger.warning(f"⚠️ Preventing infinite loop - completing workflow {workflow_id}")
            return END
    elif completion_stage == "data_processing":
        # Start data extraction
        logger.debug(f"🔄 Starting data processing for workflow {workflow_id}")
        return "extract_information"
    elif completion_stage == "validation":
        # Validate extracted data
        return "validate_data"
    elif completion_stage == "storage":
        # Store validated data
        return "store_data"
    else:
        logger.error(f"Unknown completion stage: {completion_stage}")
        # We cannot modify state here in routing function
        return END


def create_profile_completion_graph(user_profile_id: str) -> StateGraph:
    """
    Create a unified LangGraph workflow for profile completion.

    Unified Architecture: This workflow combines conversational engagement
    and data processing into a single, cohesive profile completion experience.

    Args:
        user_profile_id: The ID of the user profile this workflow is for

    Returns:
        StateGraph: The configured unified profile completion workflow graph
    """
    # Create the unified workflow graph
    workflow = StateGraph(ProfileCompletionState)

    # Add conversational profile completion node (moved from onboarding_graph.py)
    workflow.add_node("mentor", mentor_profile_completion_node)

    # Add data processing nodes
    workflow.add_node("extract_information", _extract_profile_information)
    workflow.add_node("validate_data", _validate_extracted_data)
    workflow.add_node("store_data", _store_profile_data)

    # Add conditional edges for unified workflow routing
    workflow.add_conditional_edges(
        "mentor",
        route_profile_completion_flow,
        {
            "mentor": "mentor",  # Continue conversation
            "extract_information": "extract_information",  # Start data processing
            END: END  # Complete workflow
        }
    )

    workflow.add_conditional_edges(
        "extract_information",
        route_profile_completion_flow,
        {
            "validate_data": "validate_data",
            END: END
        }
    )

    workflow.add_conditional_edges(
        "validate_data",
        route_profile_completion_flow,
        {
            "store_data": "store_data",
            END: END
        }
    )

    workflow.add_conditional_edges(
        "store_data",
        route_profile_completion_flow,
        {
            END: END
        }
    )

    # Set the entry point to conversational profile completion
    workflow.set_entry_point("mentor")

    return workflow


async def run_profile_completion_workflow(
    user_profile_id: str,
    context_packet: Dict[str, Any],
    workflow_id: Optional[str] = None,
    workflow_input: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Execute the unified profile completion workflow.

    Unified Architecture: This function runs a comprehensive profile completion workflow
    that combines conversational engagement and data processing for optimal user experience.

    This function supports both the legacy interface (individual parameters) and the new
    benchmarking interface (workflow_input dictionary) for maximum compatibility.

    Args:
        user_profile_id: The ID of the user profile (legacy interface)
        context_packet: Initial context information from user/system (legacy interface)
        workflow_id: Optional workflow ID (will generate new one if not provided)
        workflow_input: Dictionary containing all workflow parameters (new benchmarking interface)
                       Expected keys: user_profile_id, context_packet, workflow_id,
                       use_real_llm, use_real_tools, use_real_db, mock_tools

    Returns:
        dict: Profile completion results including conversation and data processing
    """
    # Handle both legacy and new interfaces
    if workflow_input is not None:
        # New benchmarking interface - extract parameters from workflow_input
        user_profile_id = workflow_input.get("user_profile_id", user_profile_id)
        context_packet = workflow_input.get("context_packet", context_packet)
        workflow_id = workflow_input.get("workflow_id", workflow_id)
        # Note: LLM/tool/DB settings are handled by the agent infrastructure

        logger.info(f"Running profile completion workflow with benchmarking interface")
    else:
        # Legacy interface - use provided parameters
        logger.info("Running profile completion workflow with legacy interface")

    # Validate required parameters
    if not user_profile_id:
        raise ValueError("user_profile_id is required")
    if not context_packet:
        raise ValueError("context_packet is required")
    logger.info(f"Starting unified profile completion workflow for user {user_profile_id}")

    # Create the unified workflow
    workflow = create_profile_completion_graph(user_profile_id)

    # Compile the workflow with increased recursion limit
    app = workflow.compile(
        checkpointer=None,  # No checkpointing needed for this workflow
        interrupt_before=None,
        interrupt_after=None,
        debug=False
    )
    logger.info("here is the profile_completion_graph")
    logger.info(app.get_graph().draw_mermaid())
    # Set up the initial state
    if not workflow_id:
        workflow_id = str(uuid.uuid4())

    # Create initial state with user details and context
    initial_state = ProfileCompletionState(
        workflow_id=workflow_id,
        user_profile_id=user_profile_id,
        initial_context_packet=context_packet,
        user_ws_session_name=context_packet.get('user_ws_session_name'),
        profile_data_packet=context_packet,
        completion_stage="conversational"  # Start with conversation
    )

    # Log workflow initiation
    logger.info(f"Starting unified profile completion workflow {workflow_id} for user {user_profile_id}")

    try:
        # Execute the unified workflow with proper LangGraph configuration
        config = {
            "recursion_limit": 50,  # Increase from default 25 to 50
            "configurable": {
                "user_profile_id": user_profile_id,
                "workflow_id": workflow_id,
                "session_name": context_packet.get('user_ws_session_name', 'default')
            }
        }

        final_state = await app.ainvoke(initial_state, config=config)

        # Prepare the result
        # CRITICAL FIX: Handle LangGraph AddableValuesDict state object properly
        # LangGraph returns an AddableValuesDict, not our Pydantic model, so access as dictionary
        user_ws_session_name = final_state.get('user_ws_session_name')
        if not user_ws_session_name:
            # Fallback to initial context packet if available
            initial_context = final_state.get('initial_context_packet', {})
            user_ws_session_name = initial_context.get('user_ws_session_name') if initial_context else None
        if not user_ws_session_name:
            # Final fallback to context_packet
            user_ws_session_name = context_packet.get('user_ws_session_name')

        result = {
            'workflow_id': workflow_id,
            'user_profile_id': user_profile_id,
            'user_ws_session_name': user_ws_session_name,
            'completed': final_state.get('completed', False),
            'output_data': final_state.get('output_data', {}),
            'profile_updates': final_state.get('profile_updates', {}),
            'conversation_depth': final_state.get('conversation_depth', 0),
            'completion_stage': final_state.get('completion_stage', 'conversational'),
            'onboarding_stage': final_state.get('onboarding_stage', 'initial_greeting')
        }

        logger.info(f"✅ Unified profile completion workflow {workflow_id} completed successfully")
        return result

    except Exception as e:
        logger.error(f"❌ Error in unified profile completion workflow {workflow_id}: {e}")

        # CRITICAL FIX: Preserve WebSocket session information in error results
        # Extract user_ws_session_name from context_packet to ensure WebSocket communication works
        user_ws_session_name = context_packet.get('user_ws_session_name')

        return {
            'workflow_id': workflow_id,
            'user_profile_id': user_profile_id,
            'user_ws_session_name': user_ws_session_name,  # CRITICAL: Include WebSocket session for error responses
            'completed': False,
            'error': str(e),
            'output_data': {
                'user_response': "I apologize for the technical difficulty. Let me help you get started in a different way."
            }
        }


# Compatibility alias for backward compatibility with existing code
# This allows existing code that imports from onboarding_graph to continue working
async def run_onboarding_workflow(
    user_profile_id: str,
    context_packet: Dict[str, Any],
    workflow_id: Optional[str] = None,
    workflow_input: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Compatibility alias for the unified profile completion workflow.

    This function maintains backward compatibility with existing code that
    expects the onboarding workflow interface.

    Args:
        user_profile_id: The ID of the user profile
        context_packet: Initial context information from user/system
        workflow_id: Optional workflow ID
        workflow_input: Dictionary containing all workflow parameters (benchmarking interface)

    Returns:
        dict: Profile completion results (same as run_profile_completion_workflow)
    """
    logger.info(f"🔄 Redirecting onboarding workflow call to unified profile completion workflow")
    return await run_profile_completion_workflow(
        user_profile_id=user_profile_id,
        context_packet=context_packet,
        workflow_id=workflow_id,
        workflow_input=workflow_input
    )
