"""
Post-Spin Workflow Graph - COMPREHENSIVE IMPLEMENTATION

Implements the complete post-spin workflow according to post_spin_FLOW.md specification.
Handles user activity selection after spinning the wheel with full agent orchestration
including emergency refusal handling, pattern analysis, and psychological monitoring.

Agents included:
- Mentor Agent: Activity presentation and emergency response collection
- Orchestrator Agent: Path determination and workflow coordination
- Engagement & Pattern Analytics Agent: Emergency pattern analysis
- Psychological Monitoring Agent: Emergency alternative analysis
- Wheel/Activity Agent: Emergency alternative generation
- Ethical Oversight Agent: Final validation

Supports both acceptance and emergency refusal paths with comprehensive
data tracking, trust management, and user experience optimization.

Flow paths:
1. Acceptance Path: Activity presentation → Activity preparation → Post-activity handoff
2. Emergency Refusal Path: Emergency response → Pattern analysis → Alternative generation → Validation

Last Updated: 2025-06-13 - Aligned with official specifications
"""

import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field

from langgraph.graph import StateGraph, END
from apps.main.agents.mentor_agent import MentorAgent
from apps.main.services.event_service import EventService

logger = logging.getLogger(__name__)


class PostSpinState(BaseModel):
    """
    Enhanced state model for the comprehensive post-spin workflow.

    Supports both acceptance and emergency refusal paths with complete
    agent orchestration according to post_spin_FLOW.md specification.
    Enhanced with safety mechanisms from successful onboarding workflow.
    """
    # Workflow identification
    workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_profile_id: str
    user_ws_session_name: Optional[str] = None

    # Input/context data
    context_packet: Dict[str, Any] = Field(default_factory=dict)
    activity_info: Dict[str, Any] = Field(default_factory=dict)

    # Workflow path determination
    response_type: Optional[str] = None  # "acceptance" or "emergency_refusal"
    emergency_context: Optional[Dict[str, Any]] = None

    # Agent outputs
    mentor_output: Dict[str, Any] = Field(default_factory=dict)
    orchestrator_output: Dict[str, Any] = Field(default_factory=dict)
    pattern_analytics_output: Dict[str, Any] = Field(default_factory=dict)
    psychological_output: Dict[str, Any] = Field(default_factory=dict)
    activity_generation_output: Dict[str, Any] = Field(default_factory=dict)
    ethical_oversight_output: Dict[str, Any] = Field(default_factory=dict)

    # Final output data
    output_data: Dict[str, Any] = Field(default_factory=dict)

    # Workflow state tracking
    current_agent: Optional[str] = None
    next_agent: Optional[str] = None
    error: Optional[str] = None
    completed: bool = False

    # Trust and pattern tracking
    trust_impact: Optional[Dict[str, Any]] = None
    pattern_insights: Optional[Dict[str, Any]] = None

    # Safety mechanisms (inspired by successful onboarding workflow)
    iteration_count: int = 0  # Track iterations to prevent infinite loops
    agent_execution_count: Dict[str, int] = Field(default_factory=dict)  # Track per-agent executions
    max_iterations: int = 8  # Maximum total iterations before forced completion
    max_agent_executions: int = 2  # Maximum executions per agent before escalation
    emergency_escalation_count: int = 0  # Track emergency escalations


async def run_post_spin_workflow(
    user_profile_id: str,
    context_packet: Dict[str, Any],
    workflow_id: Optional[str] = None,
    workflow_input: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Execute the comprehensive post-spin workflow for activity selection.

    Implements the complete post_spin_FLOW.md specification with multi-agent
    orchestration supporting both acceptance and emergency refusal paths.

    Args:
        user_profile_id: ID of the user profile
        context_packet: Context information from ConversationDispatcher
        workflow_id: Optional workflow ID
        workflow_input: Optional workflow input for benchmarking

    Returns:
        Dict containing comprehensive workflow results with agent outputs
    """
    if not workflow_id:
        workflow_id = str(uuid.uuid4())

    logger.info(f"🎯 Starting comprehensive post-spin workflow {workflow_id} for user {user_profile_id}")

    try:
        # Extract activity information from context
        activity_info = _extract_activity_info(context_packet)
        if not activity_info:
            raise ValueError("No activity information found in context packet")

        logger.info(f"📋 Activity extracted: {activity_info.get('activity_name', 'Unknown')}")

        # Initialize comprehensive workflow state
        initial_state = PostSpinState(
            workflow_id=workflow_id,
            user_profile_id=user_profile_id,
            context_packet=context_packet,
            activity_info=activity_info,
            current_agent="mentor",
            output_data={
                "activity_info": activity_info,
                "workflow_type": "post_spin",
                "workflow_version": "comprehensive_v1.0"
            }
        )

        # Create and compile the comprehensive workflow graph
        workflow = _create_comprehensive_post_spin_graph()
        compiled_app = workflow.compile()

        # Execute the workflow
        logger.info(f"🔄 Executing comprehensive post-spin workflow {workflow_id}")
        result = await compiled_app.ainvoke(initial_state)

        # Extract final results
        final_output = result.get("output_data", {})

        # Add workflow metadata
        final_output.update({
            "workflow_metadata": {
                "agents_executed": _get_executed_agents(result),
                "response_type": result.get("response_type", "acceptance"),
                "emergency_handled": result.get("response_type") == "emergency_refusal",
                "trust_impact": result.get("trust_impact"),
                "pattern_insights": result.get("pattern_insights")
            }
        })

        # Format the comprehensive result
        formatted_result = {
            "workflow_id": workflow_id,
            "user_profile_id": user_profile_id,
            "completed": result.get("completed", True),
            "output_data": final_output,
            "user_ws_session_name": context_packet.get("user_ws_session_name"),
            "workflow_type": "post_spin"
        }

        logger.info(f"✅ Completed comprehensive post-spin workflow {workflow_id}")
        return formatted_result

    except Exception as e:
        logger.error(f"❌ Error in comprehensive post-spin workflow {workflow_id}: {str(e)}", exc_info=True)

        # Return comprehensive error result
        return {
            "workflow_id": workflow_id,
            "user_profile_id": user_profile_id,
            "completed": False,
            "error": str(e),
            "output_data": {
                "error": str(e),
                "workflow_type": "post_spin",
                "workflow_version": "comprehensive_v1.0",
                "error_context": "Comprehensive workflow execution failed"
            },
            "user_ws_session_name": context_packet.get("user_ws_session_name")
        }


def _extract_activity_info(context_packet: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Extract activity information from the context packet."""
    try:
        logger.info(f"🔍 DEBUG: Starting activity info extraction")
        logger.info(f"🔍 DEBUG: Full context_packet keys: {list(context_packet.keys())}")

        # Check for activity information in metadata
        metadata = context_packet.get("metadata", {})

        # Try to get activity_tailored_id and name from the original message content
        # This would come from the spin_result message
        original_content = context_packet.get("original_content", {})

        # Also check workflow_metadata for activity information
        workflow_metadata = context_packet.get("workflow_metadata", {})

        logger.info(f"🔍 DEBUG: metadata: {metadata}")
        logger.info(f"🔍 DEBUG: original_content: {original_content}")
        logger.info(f"🔍 DEBUG: workflow_metadata: {workflow_metadata}")

        activity_info = {
            "activity_id": metadata.get("activity_id"),
            "activity_name": metadata.get("activity_name"),
            "activity_tailored_id": None
        }

        # Extract from workflow_metadata (classification result)
        if isinstance(workflow_metadata, dict):
            if not activity_info["activity_id"]:
                activity_info["activity_id"] = workflow_metadata.get("activity_id")
            if not activity_info["activity_name"]:
                activity_info["activity_name"] = workflow_metadata.get("activity_name")

        # Extract from original_content (spin_result message)
        if isinstance(original_content, dict):
            activity_info["activity_tailored_id"] = original_content.get("activity_tailored_id")
            # Frontend sends 'name' in spin_result, use it if activity_name is not set
            if not activity_info["activity_name"]:
                activity_info["activity_name"] = original_content.get("name")

        # Also check if activity_name is directly in metadata as activity_name
        if not activity_info["activity_name"]:
            activity_info["activity_name"] = metadata.get("activity_name")

        logger.info(f"🔍 DEBUG: Final extracted activity info: {activity_info}")

        # Validate we have the minimum required information
        if activity_info["activity_name"]:
            return activity_info

        logger.warning(f"Insufficient activity information in context packet. metadata: {metadata}, original_content: {original_content}, workflow_metadata: {workflow_metadata}")
        return None

    except Exception as e:
        logger.error(f"Error extracting activity info: {str(e)}")
        return None


def _create_comprehensive_post_spin_graph() -> StateGraph:
    """
    Create the comprehensive post-spin workflow graph.

    Implements the complete post_spin_FLOW.md specification with all agents:
    - Mentor Agent: Activity presentation and emergency response
    - Orchestrator Agent: Path determination and coordination
    - Pattern Analytics Agent: Emergency pattern analysis
    - Psychological Monitoring Agent: Emergency alternatives
    - Activity Generation Agent: Emergency activity creation
    - Ethical Oversight Agent: Final validation
    """
    workflow = StateGraph(PostSpinState)

    # Add all agent nodes
    workflow.add_node("mentor", _mentor_agent_node)
    workflow.add_node("orchestrator", _orchestrator_agent_node)
    workflow.add_node("pattern_analytics", _pattern_analytics_agent_node)
    workflow.add_node("psychological_monitoring", _psychological_monitoring_agent_node)
    workflow.add_node("activity_generation", _activity_generation_agent_node)
    workflow.add_node("ethical_oversight", _ethical_oversight_agent_node)
    workflow.add_node("finalize", _finalize_node)

    # Define the comprehensive flow
    workflow.set_entry_point("mentor")

    # Mentor → Orchestrator (always)
    workflow.add_edge("mentor", "orchestrator")

    # Orchestrator → Path determination
    workflow.add_conditional_edges(
        "orchestrator",
        _determine_workflow_path,
        {
            "acceptance": "finalize",
            "emergency_refusal": "pattern_analytics"
        }
    )

    # Emergency refusal path
    workflow.add_edge("pattern_analytics", "psychological_monitoring")
    workflow.add_edge("psychological_monitoring", "activity_generation")
    workflow.add_edge("activity_generation", "ethical_oversight")
    workflow.add_edge("ethical_oversight", "finalize")

    # Finalize → END
    workflow.add_edge("finalize", END)

    return workflow


def _get_executed_agents(result: Dict[str, Any]) -> List[str]:
    """Extract list of agents that were executed in the workflow."""
    executed_agents = ["mentor"]  # Always starts with mentor

    if result.get("orchestrator_output"):
        executed_agents.append("orchestrator")

    if result.get("response_type") == "emergency_refusal":
        if result.get("pattern_analytics_output"):
            executed_agents.append("pattern_analytics")
        if result.get("psychological_output"):
            executed_agents.append("psychological_monitoring")
        if result.get("activity_generation_output"):
            executed_agents.append("activity_generation")
        if result.get("ethical_oversight_output"):
            executed_agents.append("ethical_oversight")

    executed_agents.append("finalize")
    return executed_agents


def _determine_workflow_path(state: PostSpinState) -> str:
    """
    Determine the workflow path based on orchestrator analysis.

    Returns:
        "acceptance" for normal activity acceptance
        "emergency_refusal" for emergency situations requiring alternatives
    """
    response_type = state.response_type or "acceptance"
    logger.info(f"🔀 Workflow path determined: {response_type}")
    return response_type


async def _mentor_agent_node(state: PostSpinState) -> Dict[str, Any]:
    """
    Mentor Agent Node - Activity presentation and emergency response collection.

    Implements the Mentor Agent responsibilities from post_spin_FLOW.md:
    - Deliver initial response to wheel spin outcome
    - Present selected activity with appropriate tone and enthusiasm
    - Frame activity in relation to user's goals and growth areas
    - Provide detailed activity instructions
    - Process emergency refusals when they occur
    """
    logger.info(f"🧠 Mentor Agent processing activity selection for workflow {state.workflow_id}")

    try:
        # Get activity information
        activity_info = state.activity_info
        activity_name = activity_info.get("activity_name", "Unknown Activity")
        activity_tailored_id = activity_info.get("activity_tailored_id")

        logger.info(f"📋 Processing activity: {activity_name} (ID: {activity_tailored_id})")

        # Initialize mentor agent with proper context
        mentor = MentorAgent(user_profile_id=state.user_profile_id)

        # Create comprehensive context for mentor
        mentor_context = {
            "user_profile_id": state.user_profile_id,
            "workflow_id": state.workflow_id,
            "activity_name": activity_name,
            "activity_info": activity_info,
            "context_packet": state.context_packet,
            "task_type": "post_spin_activity_presentation",
            "workflow_stage": "initial_response"
        }

        # Process with mentor agent using the state directly
        # The MentorAgent.process() method expects a state parameter, not separate arguments
        mentor_result = await mentor.process(state)

        # Extract mentor response and analyze for emergency signals
        mentor_output = {}
        user_response = None
        emergency_detected = False

        if mentor_result and "output_data" in mentor_result:
            output_data = mentor_result["output_data"]
            mentor_output = {
                "mentor_response": output_data.get("mentor_response"),
                "user_response": output_data.get("user_response"),
                "communication_tone": output_data.get("communication_tone", "encouraging"),
                "trust_level": output_data.get("trust_level", 0.5)
            }
            user_response = output_data.get("user_response")

            # Check for emergency refusal indicators (this would be enhanced with real detection)
            emergency_keywords = ["emergency", "can't", "unable", "impossible", "crisis", "urgent"]
            if user_response and any(keyword in user_response.lower() for keyword in emergency_keywords):
                emergency_detected = True
                logger.info(f"🚨 Emergency refusal detected in response: {user_response}")

        # Create comprehensive activity presentation
        activity_presentation = {
            "congratulatory_message": f"Excellent choice! '{activity_name}' is a wonderful activity that aligns perfectly with your growth journey.",
            "activity_instructions": _generate_activity_instructions(activity_info),
            "goal_connection": f"This activity will help you develop in areas that matter to you.",
            "post_activity_expectations": "After completing this activity, I'll be here to help you reflect on the experience and capture insights for your continued growth.",
            "estimated_duration": activity_info.get("duration_minutes", 20),
            "resources_required": activity_info.get("resources_required", [])
        }

        # Update state with mentor results
        updated_state = {
            "mentor_output": mentor_output,
            "output_data": {
                **state.output_data,
                "activity_presentation": activity_presentation,
                "mentor_response": mentor_output.get("mentor_response"),
                "user_response": user_response,
                "emergency_detected": emergency_detected
            },
            "current_agent": "orchestrator",
            "next_agent": "orchestrator"
        }

        # Set response type for orchestrator
        if emergency_detected:
            updated_state["response_type"] = "emergency_refusal"
            updated_state["emergency_context"] = {
                "user_response": user_response,
                "detected_at": datetime.now(timezone.utc).isoformat(),
                "activity_context": activity_info
            }
        else:
            updated_state["response_type"] = "acceptance"

        logger.info(f"✅ Mentor Agent completed processing for {state.workflow_id} (Response: {updated_state['response_type']})")

        return updated_state

    except Exception as e:
        logger.error(f"❌ Error in Mentor Agent node: {str(e)}", exc_info=True)

        # Provide fallback response
        activity_name = state.activity_info.get("activity_name", "your selected activity")
        fallback_response = f"Great choice! '{activity_name}' is an excellent activity to try. I'm here to support you through this journey!"

        return {
            "mentor_output": {"error": str(e)},
            "output_data": {
                **state.output_data,
                "user_response": fallback_response,
                "activity_instructions": fallback_response,
                "error": str(e)
            },
            "response_type": "acceptance",  # Default to acceptance on error
            "current_agent": "orchestrator",
            "next_agent": "orchestrator"
        }


def _generate_activity_instructions(activity_info: Dict[str, Any]) -> str:
    """Generate detailed activity instructions based on activity information."""
    activity_name = activity_info.get("activity_name", "the activity")
    description = activity_info.get("description", "")

    # Create basic instructions (this would be enhanced with real activity data)
    instructions = f"Here's how to approach '{activity_name}':\n\n"

    if description:
        instructions += f"Overview: {description}\n\n"

    instructions += "Steps to get started:\n"
    instructions += "1. Find a comfortable space where you can focus\n"
    instructions += "2. Set aside the recommended time for this activity\n"
    instructions += "3. Approach this with an open mind and curiosity\n"
    instructions += "4. Remember that the goal is growth and exploration\n\n"
    instructions += "Take your time and enjoy the process!"

    return instructions


async def _orchestrator_agent_node(state: PostSpinState) -> Dict[str, Any]:
    """
    Orchestrator Agent Node - Path determination and workflow coordination.

    Implements the Orchestrator Agent responsibilities from post_spin_FLOW.md:
    - Determine appropriate workflow path (acceptance/emergency refusal)
    - Route acceptances to activity preparation
    - Direct emergency refusals to pattern analysis
    - Prepare activity context for Post-Activity workflow
    - Coordinate emergency response planning
    """
    logger.info(f"🎯 Orchestrator Agent determining workflow path for {state.workflow_id}")

    try:
        response_type = state.response_type or "acceptance"
        activity_info = state.activity_info
        mentor_output = state.mentor_output

        logger.info(f"📊 Analyzing response type: {response_type}")

        orchestrator_output = {
            "path_determination": response_type,
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
            "activity_context": activity_info,
            "mentor_assessment": mentor_output
        }

        if response_type == "acceptance":
            # Acceptance Path: Prepare activity context for execution
            logger.info(f"✅ Processing acceptance path for activity: {activity_info.get('activity_name')}")

            # Prepare activity preparation package
            activity_preparation = {
                "activity_instructions": state.output_data.get("activity_presentation", {}).get("activity_instructions"),
                "user_context": {
                    "user_profile_id": state.user_profile_id,
                    "trust_level": mentor_output.get("trust_level", 0.5),
                    "communication_preferences": mentor_output.get("communication_tone", "encouraging")
                },
                "commitment_reference": {
                    "activity_selected": activity_info.get("activity_name"),
                    "selection_timestamp": datetime.now(timezone.utc).isoformat(),
                    "workflow_id": state.workflow_id
                },
                "expected_completion_parameters": {
                    "estimated_duration": activity_info.get("duration_minutes", 20),
                    "difficulty_level": activity_info.get("base_challenge_rating", 50),
                    "resources_required": activity_info.get("resources_required", [])
                }
            }

            orchestrator_output.update({
                "activity_preparation": activity_preparation,
                "next_workflow": "post_activity",
                "handoff_ready": True
            })

            # Update output data for final response
            updated_output = state.output_data.copy()
            updated_output.update({
                "orchestrator_analysis": orchestrator_output,
                "workflow_path": "acceptance",
                "activity_ready": True,
                "post_activity_handoff": activity_preparation
            })

        else:
            # Emergency Refusal Path: Prepare for pattern analysis
            logger.info(f"🚨 Processing emergency refusal path")

            emergency_context = state.emergency_context or {}

            # Prepare structured feedback package for pattern analysis
            feedback_package = {
                "emergency_context": emergency_context,
                "user_response_data": emergency_context.get("user_response"),
                "historical_references": {
                    "user_profile_id": state.user_profile_id,
                    "activity_context": activity_info
                },
                "pre_spin_commitment_context": {
                    "original_activity": activity_info.get("activity_name"),
                    "selection_timestamp": emergency_context.get("detected_at")
                }
            }

            orchestrator_output.update({
                "emergency_feedback_package": feedback_package,
                "next_agent": "pattern_analytics",
                "emergency_protocol_activated": True
            })

            # Update output data for emergency processing
            updated_output = state.output_data.copy()
            updated_output.update({
                "orchestrator_analysis": orchestrator_output,
                "workflow_path": "emergency_refusal",
                "emergency_package": feedback_package
            })

        logger.info(f"✅ Orchestrator Agent completed path determination: {response_type}")

        return {
            "orchestrator_output": orchestrator_output,
            "output_data": updated_output,
            "current_agent": "orchestrator",
            "next_agent": "pattern_analytics" if response_type == "emergency_refusal" else "finalize"
        }

    except Exception as e:
        logger.error(f"❌ Error in Orchestrator Agent node: {str(e)}", exc_info=True)

        # Default to acceptance path on error
        return {
            "orchestrator_output": {"error": str(e), "default_path": "acceptance"},
            "output_data": {
                **state.output_data,
                "orchestrator_error": str(e),
                "workflow_path": "acceptance"
            },
            "response_type": "acceptance",
            "current_agent": "orchestrator",
            "next_agent": "finalize"
        }


async def _pattern_analytics_agent_node(state: PostSpinState) -> Dict[str, Any]:
    """
    Engagement & Pattern Analytics Agent Node - Emergency pattern analysis.

    Implements pattern analysis responsibilities from post_spin_FLOW.md:
    - Analyze historical patterns related to emergencies
    - Classify emergency type based on evidence
    - Assess trust impact implications
    - Provide pattern-based recommendations
    """
    logger.info(f"📊 Pattern Analytics Agent analyzing emergency for {state.workflow_id}")

    try:
        emergency_package = state.output_data.get("emergency_package", {})
        user_response = emergency_package.get("user_response_data", "")

        # Simulate pattern analysis (would be enhanced with real historical data)
        pattern_analysis = {
            "emergency_classification": "genuinely_unpredictable",  # or "potentially_foreseeable", "preference_masked"
            "confidence_score": 0.8,
            "historical_context": "No similar emergency patterns found in recent history",
            "consistency_metrics": {
                "previous_emergencies": 0,
                "completion_rate": 0.85,
                "trust_impact_low": True
            },
            "supporting_evidence": [
                "First emergency refusal in recent activity history",
                "User response indicates genuine constraint",
                "No pattern of avoidance detected"
            ]
        }

        # Trust impact assessment
        trust_impact = {
            "metric_impact_projection": "minimal",
            "current_trust_level": state.mentor_output.get("trust_level", 0.5),
            "projected_trust_level": max(0.0, state.mentor_output.get("trust_level", 0.5) - 0.05),
            "recovery_opportunities": [
                "Provide appropriate alternative activity",
                "Acknowledge genuine emergency with understanding",
                "Maintain commitment principle appropriately"
            ],
            "recommended_approach": "supportive_alternative"
        }

        pattern_output = {
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
            "pattern_analysis": pattern_analysis,
            "trust_impact_assessment": trust_impact,
            "recommended_handling": "provide_alternative_activity"
        }

        logger.info(f"✅ Pattern Analytics completed: {pattern_analysis['emergency_classification']}")

        return {
            "pattern_analytics_output": pattern_output,
            "trust_impact": trust_impact,
            "pattern_insights": pattern_analysis,
            "output_data": {
                **state.output_data,
                "pattern_analysis": pattern_output
            },
            "current_agent": "pattern_analytics",
            "next_agent": "psychological_monitoring"
        }

    except Exception as e:
        logger.error(f"❌ Error in Pattern Analytics Agent: {str(e)}", exc_info=True)

        return {
            "pattern_analytics_output": {"error": str(e)},
            "output_data": {
                **state.output_data,
                "pattern_analysis_error": str(e)
            },
            "current_agent": "pattern_analytics",
            "next_agent": "psychological_monitoring"
        }


async def _psychological_monitoring_agent_node(state: PostSpinState) -> Dict[str, Any]:
    """
    Psychological Monitoring Agent Node - Emergency alternative analysis.

    Implements psychological analysis from post_spin_FLOW.md:
    - Assess user's current psychological state
    - Determine appropriate alternative activity parameters
    - Develop trust recovery communication strategy
    """
    logger.info(f"🧠 Psychological Monitoring Agent analyzing state for {state.workflow_id}")

    try:
        pattern_analysis = state.pattern_analytics_output
        emergency_context = state.emergency_context or {}

        # Psychological state assessment
        psychological_assessment = {
            "emotional_state": "stressed_but_cooperative",
            "cognitive_state": "clear_thinking",
            "emergency_impact": "moderate",
            "receptiveness_to_alternatives": "high",
            "support_approach_needed": "understanding_and_practical"
        }

        # Alternative activity parameters
        alternative_parameters = {
            "constraint_accommodations": [
                "Reduced time requirement (10-15 minutes)",
                "Minimal resource requirements",
                "Can be done in current environment"
            ],
            "psychological_appropriateness": "calming_and_achievable",
            "connection_to_original_objectives": "maintains_growth_focus",
            "challenge_calibration": "gentle_to_moderate"
        }

        # Trust recovery strategy
        trust_recovery_strategy = {
            "key_messaging": [
                "Acknowledge the genuine nature of the emergency",
                "Emphasize understanding without judgment",
                "Maintain growth focus with flexibility"
            ],
            "framing_recommendations": "supportive_and_adaptive",
            "language_suggestions": "empathetic_and_solution_focused",
            "pitfalls_to_avoid": [
                "Expressing disappointment",
                "Questioning the emergency validity",
                "Abandoning growth objectives"
            ]
        }

        psychological_output = {
            "assessment_timestamp": datetime.now(timezone.utc).isoformat(),
            "psychological_assessment": psychological_assessment,
            "alternative_parameters": alternative_parameters,
            "trust_recovery_strategy": trust_recovery_strategy
        }

        logger.info(f"✅ Psychological Monitoring completed assessment")

        return {
            "psychological_output": psychological_output,
            "output_data": {
                **state.output_data,
                "psychological_analysis": psychological_output
            },
            "current_agent": "psychological_monitoring",
            "next_agent": "activity_generation"
        }

    except Exception as e:
        logger.error(f"❌ Error in Psychological Monitoring Agent: {str(e)}", exc_info=True)

        return {
            "psychological_output": {"error": str(e)},
            "output_data": {
                **state.output_data,
                "psychological_analysis_error": str(e)
            },
            "current_agent": "psychological_monitoring",
            "next_agent": "activity_generation"
        }


async def _activity_generation_agent_node(state: PostSpinState) -> Dict[str, Any]:
    """
    Wheel/Activity Agent Node - Emergency alternative generation.

    Implements activity generation from post_spin_FLOW.md:
    - Generate appropriate alternative activities
    - Customize selected alternatives
    - Ensure quality and appropriateness
    """
    logger.info(f"🎨 Activity Generation Agent creating alternatives for {state.workflow_id}")

    try:
        psychological_analysis = state.psychological_output
        alternative_params = psychological_analysis.get("alternative_parameters", {})

        # Generate emergency alternative activity (simplified for MVP)
        alternative_activity = {
            "activity_tailored_id": f"emergency_alt_{uuid.uuid4().hex[:8]}",
            "name": "Mindful Breathing Exercise",
            "description": "A quick, calming breathing exercise that can be done anywhere",
            "instructions": "Take 5 deep breaths, focusing on the sensation of air entering and leaving your body. Count slowly: in for 4, hold for 4, out for 6.",
            "duration_minutes": 5,
            "resources_required": [],
            "environment": "any_quiet_space",
            "challenge_level": 20,
            "domain": "mindfulness",
            "constraint_accommodations": alternative_params.get("constraint_accommodations", []),
            "psychological_appropriateness": alternative_params.get("psychological_appropriateness"),
            "connection_to_original": "Maintains focus on personal growth and well-being"
        }

        # Validation checks
        validation_results = {
            "constraint_compliance": True,
            "psychological_suitability": True,
            "growth_objective_connection": True,
            "feasibility_under_emergency": True,
            "instruction_clarity": True
        }

        activity_output = {
            "generation_timestamp": datetime.now(timezone.utc).isoformat(),
            "emergency_alternative": alternative_activity,
            "validation_results": validation_results,
            "generation_method": "emergency_template_based"
        }

        logger.info(f"✅ Activity Generation completed: {alternative_activity['name']}")

        return {
            "activity_generation_output": activity_output,
            "output_data": {
                **state.output_data,
                "emergency_alternative": alternative_activity,
                "activity_generation": activity_output
            },
            "current_agent": "activity_generation",
            "next_agent": "ethical_oversight"
        }

    except Exception as e:
        logger.error(f"❌ Error in Activity Generation Agent: {str(e)}", exc_info=True)

        return {
            "activity_generation_output": {"error": str(e)},
            "output_data": {
                **state.output_data,
                "activity_generation_error": str(e)
            },
            "current_agent": "activity_generation",
            "next_agent": "ethical_oversight"
        }


async def _ethical_oversight_agent_node(state: PostSpinState) -> Dict[str, Any]:
    """
    Ethical Oversight Agent Node - Final validation.

    Implements ethical validation from post_spin_FLOW.md:
    - Validate ethical appropriateness of alternative
    - Review communication strategy
    - Provide final validation
    """
    logger.info(f"⚖️ Ethical Oversight Agent validating for {state.workflow_id}")

    try:
        alternative_activity = state.output_data.get("emergency_alternative", {})
        trust_recovery = state.psychological_output.get("trust_recovery_strategy", {})

        # Ethical assessment
        ethical_assessment = {
            "user_wellbeing_alignment": True,
            "respects_current_limitations": True,
            "maintains_growth_focus": True,
            "psychological_safety": True,
            "appropriateness_score": 0.95
        }

        # Communication review
        communication_review = {
            "judgment_free_verification": True,
            "commitment_principle_balance": True,
            "psychological_safety_support": True,
            "framing_appropriateness": True
        }

        # Final validation
        validation_status = "approved"
        implementation_guidance = [
            "Present alternative with understanding and empathy",
            "Emphasize that emergencies are part of life",
            "Maintain focus on growth while being flexible",
            "Follow up after completion to maintain connection"
        ]

        ethical_output = {
            "validation_timestamp": datetime.now(timezone.utc).isoformat(),
            "ethical_assessment": ethical_assessment,
            "communication_review": communication_review,
            "validation_status": validation_status,
            "implementation_guidance": implementation_guidance
        }

        logger.info(f"✅ Ethical Oversight completed: {validation_status}")

        return {
            "ethical_oversight_output": ethical_output,
            "output_data": {
                **state.output_data,
                "ethical_validation": ethical_output,
                "final_alternative_approved": True
            },
            "current_agent": "ethical_oversight",
            "next_agent": "finalize"
        }

    except Exception as e:
        logger.error(f"❌ Error in Ethical Oversight Agent: {str(e)}", exc_info=True)

        return {
            "ethical_oversight_output": {"error": str(e)},
            "output_data": {
                **state.output_data,
                "ethical_validation_error": str(e)
            },
            "current_agent": "ethical_oversight",
            "next_agent": "finalize"
        }


async def _finalize_node(state: PostSpinState) -> Dict[str, Any]:
    """
    Finalization Node - Prepares the comprehensive final response.

    Consolidates all agent outputs and creates the final user response
    based on the workflow path taken (acceptance or emergency refusal).
    """
    logger.info(f"🏁 Finalizing comprehensive post-spin workflow {state.workflow_id}")

    try:
        response_type = state.response_type or "acceptance"
        activity_info = state.activity_info

        # Create final user response based on path
        if response_type == "acceptance":
            # Acceptance path - provide activity instructions and encouragement
            activity_name = activity_info.get("activity_name", "your selected activity")
            user_response = f"Great choice! '{activity_name}' is an excellent activity to try."

            # Add detailed instructions if available
            activity_presentation = state.output_data.get("activity_presentation", {})
            if activity_presentation.get("activity_instructions"):
                user_response += f"\n\nHere's what to do: {activity_presentation['activity_instructions']}"
            else:
                user_response += f"\n\nHere's what to do: {_generate_activity_instructions(activity_info)}"

        else:
            # Emergency refusal path - provide alternative activity
            alternative_activity = state.output_data.get("emergency_alternative", {})
            trust_recovery = state.psychological_output.get("trust_recovery_strategy", {})

            if alternative_activity:
                alt_name = alternative_activity.get("name", "a calming activity")
                alt_instructions = alternative_activity.get("instructions", "Follow the activity guidelines")

                user_response = f"I completely understand that emergencies happen, and that's perfectly okay! "
                user_response += f"Instead, let's try '{alt_name}' - {alt_instructions}"
                user_response += f"\n\nThis will only take about {alternative_activity.get('duration_minutes', 5)} minutes and can help you feel more centered."
            else:
                user_response = "I understand that emergencies happen. Let's try a simple breathing exercise instead - take 5 deep breaths and focus on the present moment."

        # Prepare comprehensive final output
        final_output = {
            **state.output_data,
            "user_response": user_response,
            "workflow_completed": True,
            "completion_timestamp": datetime.now(timezone.utc).isoformat(),
            "final_summary": {
                "workflow_path": response_type,
                "activity_processed": activity_info.get("activity_name"),
                "agents_executed": _get_executed_agents(state.__dict__),
                "emergency_handled": response_type == "emergency_refusal",
                "user_support_provided": True
            }
        }

        # Send the consolidated mentor response directly to WebSocket
        try:
            from channels.layers import get_channel_layer
            channel_layer = get_channel_layer()

            if channel_layer and state.user_ws_session_name:
                # Send single consolidated chat message
                await channel_layer.group_send(
                    state.user_ws_session_name,
                    {
                        'type': 'chat_message',
                        'content': user_response,
                        'is_user': False
                    }
                )

                # Send processing completion status
                await channel_layer.group_send(
                    state.user_ws_session_name,
                    {
                        'type': 'processing_status',
                        'status': 'completed'
                    }
                )

                logger.info(f"✅ Sent consolidated post-spin response to {state.user_ws_session_name}")

        except Exception as event_error:
            logger.warning(f"Failed to send post-spin response: {event_error}")

        # Emit completion event for monitoring
        try:
            await EventService.emit_event(
                event_type="workflow_completed",
                data={
                    "workflow_id": state.workflow_id,
                    "workflow_type": "post_spin_comprehensive",
                    "user_profile_id": state.user_profile_id,
                    "activity_name": activity_info.get("activity_name"),
                    "response_type": response_type,
                    "completed_at": datetime.now(timezone.utc).isoformat()
                }
            )
        except Exception as event_error:
            logger.warning(f"Event emission failed: {event_error}")

        logger.info(f"✅ Comprehensive post-spin workflow {state.workflow_id} finalized successfully")

        return {
            "output_data": final_output,
            "completed": True,
            "response_type": response_type,
            "trust_impact": state.trust_impact,
            "pattern_insights": state.pattern_insights
        }

    except Exception as e:
        logger.error(f"❌ Error in finalize node: {str(e)}", exc_info=True)

        # Provide fallback response
        activity_name = state.activity_info.get("activity_name", "your selected activity")
        fallback_response = f"Great choice! '{activity_name}' is an excellent activity to try. I'm here to support you through this journey!"

        return {
            "output_data": {
                **state.output_data,
                "user_response": fallback_response,
                "finalize_error": str(e),
                "workflow_completed": True
            },
            "completed": True,
            "error": str(e)
        }
