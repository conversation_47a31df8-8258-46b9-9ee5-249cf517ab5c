# backend/apps/main/graphs/onboarding_graph.py
# this graph is obsolete and should not be used !
#everything related to profile completion should be managed by profile_completion_graph.py
"""
Conversational Onboarding Workflow Graph

This module implements a LangGraph workflow for user onboarding using conversational agents.
The workflow is designed to engage users in meaningful conversations to complete their profiles
while providing excellent user experience.

Key Features:
- Conversational onboarding using Mentor agent
- Profile completion through natural dialogue
- Contextual questions based on user responses
- Seamless integration with existing agent infrastructure

Architecture:
- Uses Mentor agent for conversational flow
- Leverages existing agent tools and capabilities
- Provides human-friendly responses
- Maintains conversation context and history

The workflow engages users through natural conversation to:
1. Understand their goals and aspirations
2. Learn about their preferences and constraints
3. Build comprehensive user profiles
4. Provide personalized guidance and support

This approach ensures users have a welcoming, conversational experience
while efficiently gathering the information needed for personalization.
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from langgraph.graph import StateGraph, END

from apps.main.agents.mentor_agent import MentorAgent
from apps.main.graphs.state_models import OnboardingStage

logger = logging.getLogger(__name__)


class OnboardingState(BaseModel):
    """
    State model for the conversational onboarding workflow.
    Tracks user interaction and profile completion progress.
    """
    # Workflow identification
    workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_profile_id: str
    user_ws_session_name: Optional[str] = None

    # Conversational context
    initial_context_packet: Dict[str, Any] = Field(default_factory=dict)
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list)

    # Agent outputs and state tracking
    output_data: Dict[str, Any] = Field(default_factory=dict)

    # Workflow state tracking
    onboarding_stage: OnboardingStage = "initial_greeting"
    last_agent: Optional[str] = "mentor"
    error: Optional[str] = None
    completed: bool = False

    # Profile completion tracking
    profile_updates: Dict[str, Any] = Field(default_factory=dict)
    initial_profile_completion: float = 0.0
    current_profile_completion: float = 0.0

    # Safety mechanisms
    iteration_count: int = 0
    max_iterations: int = 8  # Reasonable limit for onboarding conversation
    conversation_depth: int = 0

    # Agent Run ID tracking
    run_id: Optional[uuid.UUID] = Field(None, description="The UUID of the last agent run associated with this state.")


async def mentor_onboarding_node(state: OnboardingState) -> OnboardingState:
    """
    Mentor agent node for conversational onboarding.
    
    Handles the conversational flow to gather user information and complete their profile
    through natural, engaging dialogue.
    """
    try:
        logger.info(f"🤖 Mentor agent starting onboarding conversation for user {state.user_profile_id}")
        
        # Initialize the Mentor agent with onboarding context
        mentor_agent = MentorAgent(
            user_profile_id=state.user_profile_id
        )

        # Set onboarding stage in the agent
        mentor_agent.onboarding_stage = state.onboarding_stage
        
        # Process the conversation with onboarding context
        result = await mentor_agent.process(state)
        
        # Update state with agent results
        state.output_data.update(result.get('output_data', {}))
        state.run_id = result.get('run_id')
        
        # Check if onboarding should be completed
        if result.get('onboarding_completed', False):
            state.completed = True
            state.onboarding_stage = "completed"
            logger.info(f"✅ Onboarding completed for user {state.user_profile_id}")
        else:
            # Update onboarding stage based on conversation progress
            state.onboarding_stage = result.get('next_onboarding_stage', state.onboarding_stage)
        
        # Update conversation tracking
        state.conversation_depth += 1
        state.last_agent = "mentor"
        
        logger.info(f"✅ Mentor agent completed onboarding step for user {state.user_profile_id}")
        
        return state
        
    except Exception as e:
        logger.error(f"❌ Error in mentor onboarding node: {e}")
        state.error = str(e)
        state.output_data["user_response"] = "I apologize, but I'm having some technical difficulties. Let me try to help you in a different way."
        return state


def route_onboarding_flow(state: OnboardingState):
    """
    Route the onboarding workflow based on current state.
    
    Determines whether to continue the conversation, complete onboarding,
    or handle error conditions.
    """
    # Safety check: prevent infinite loops
    state.iteration_count = getattr(state, 'iteration_count', 0) + 1
    if state.iteration_count >= state.max_iterations:
        logger.warning(f"🚨 Onboarding workflow {state.workflow_id} reached maximum iterations ({state.iteration_count}), forcing completion.")
        state.completed = True
        state.onboarding_stage = "completed"
        state.output_data["user_response"] = "Thank you for sharing! I have enough information to get started. I'm here whenever you'd like to explore activities or continue our conversation."
        return END
    
    # Check for errors
    if state.error:
        logger.error(f"❌ Onboarding workflow {state.workflow_id} has error: {state.error}")
        return END
    
    # Check if onboarding is completed
    if state.completed or state.onboarding_stage == "completed":
        logger.info(f"✅ Onboarding workflow {state.workflow_id} completed successfully")
        return END
    
    # Continue with mentor conversation
    logger.debug(f"🔄 Continuing onboarding conversation for workflow {state.workflow_id}")
    return "mentor"


def create_onboarding_graph(user_profile_id: str) -> StateGraph:
    """
    Create a LangGraph workflow for conversational user onboarding.
    
    This creates a simple but effective onboarding flow that uses the Mentor agent
    to engage users in natural conversation while gathering profile information.
    
    Args:
        user_profile_id: The ID of the user profile this workflow is for
        
    Returns:
        StateGraph: The configured onboarding workflow graph
    """
    # Create the graph
    workflow = StateGraph(OnboardingState)
    
    # Add the mentor agent node
    workflow.add_node("mentor", mentor_onboarding_node)
    
    # Add conditional edges for routing
    workflow.add_conditional_edges(
        "mentor",
        route_onboarding_flow,
        {
            "mentor": "mentor",  # Continue conversation
            END: END  # Complete onboarding
        }
    )
    
    # Set the entry point to the mentor agent
    workflow.set_entry_point("mentor")
    
    return workflow


async def run_onboarding_workflow(
    user_profile_id: str,
    context_packet: Dict[str, Any],
    workflow_id: Optional[str] = None,
    workflow_input: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Execute the conversational onboarding workflow.

    This function supports both the legacy interface (individual parameters) and the new
    benchmarking interface (workflow_input dictionary) for maximum compatibility.

    Args:
        user_profile_id: The ID of the user profile (legacy interface)
        context_packet: Initial context information from user/system (legacy interface)
        workflow_id: Optional workflow ID (will generate new one if not provided)
        workflow_input: Dictionary containing all workflow parameters (new benchmarking interface)
                       Expected keys: user_profile_id, context_packet, workflow_id,
                       use_real_llm, use_real_tools, use_real_db, mock_tools

    Returns:
        dict: Onboarding results including conversation and profile updates
    """
    # Handle both legacy and new interfaces
    if workflow_input is not None:
        # New benchmarking interface - extract parameters from workflow_input
        user_profile_id = workflow_input.get("user_profile_id", user_profile_id)
        context_packet = workflow_input.get("context_packet", context_packet)
        workflow_id = workflow_input.get("workflow_id", workflow_id)
        # Note: LLM/tool/DB settings are handled by the agent infrastructure

        logger.info(f"Running onboarding workflow with benchmarking interface")
    else:
        # Legacy interface - use provided parameters
        logger.info("Running onboarding workflow with legacy interface")

    # Validate required parameters
    if not user_profile_id:
        raise ValueError("user_profile_id is required")
    if not context_packet:
        raise ValueError("context_packet is required")
    logger.info(f"Starting conversational onboarding workflow for user {user_profile_id}")
    
    # Create the workflow
    workflow = create_onboarding_graph(user_profile_id)
    
    # Compile the workflow
    app = workflow.compile()
    
    # Set up the initial state
    if not workflow_id:
        workflow_id = str(uuid.uuid4())
    
    # Create initial state with user details and context
    initial_state = OnboardingState(
        workflow_id=workflow_id,
        user_profile_id=user_profile_id,
        initial_context_packet=context_packet,
        user_ws_session_name=context_packet.get('user_ws_session_name')
    )
    
    # Log workflow initiation
    logger.info(f"Starting onboarding workflow {workflow_id} for user {user_profile_id}")
    
    try:
        # Execute the workflow
        final_state = await app.ainvoke(initial_state)
        
        # Prepare the result
        result = {
            'workflow_id': workflow_id,
            'user_profile_id': user_profile_id,
            'user_ws_session_name': getattr(final_state, 'user_ws_session_name', None) or final_state.initial_context_packet.get('user_ws_session_name'),
            'completed': final_state.completed,
            'output_data': final_state.output_data,
            'profile_updates': final_state.profile_updates,
            'conversation_depth': final_state.conversation_depth,
            'onboarding_stage': final_state.onboarding_stage
        }
        
        logger.info(f"✅ Onboarding workflow {workflow_id} completed successfully")
        return result
        
    except Exception as e:
        logger.error(f"❌ Error in onboarding workflow {workflow_id}: {e}")
        return {
            'workflow_id': workflow_id,
            'user_profile_id': user_profile_id,
            'completed': False,
            'error': str(e),
            'output_data': {
                'user_response': "I apologize for the technical difficulty. Let me help you get started in a different way."
            }
        }
