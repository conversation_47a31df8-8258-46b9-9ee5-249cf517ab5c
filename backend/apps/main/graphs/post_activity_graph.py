# backend/apps/main/graphs/post_activity_graph.py

import logging
from typing import Any, Dict, Optional, List
import uuid
from pydantic import BaseModel, Field
from langgraph.graph import END, StateGraph

from apps.main.agents.mentor_agent import MentorAgent

logger = logging.getLogger(__name__)


def create_post_activity_graph(user_profile_id):
    """
    Create a LangGraph workflow for the post-activity feedback process.
    
    This workflow follows the post_activity_FLOW.md specification with a simplified
    implementation focusing on the Mentor agent collecting feedback and creating
    a structured feedback report.
    
    Args:
        user_profile_id: The ID of the user profile this workflow is for
        
    Returns:
        StateGraph: The configured post-activity workflow graph
    """
    # Define the workflow state structure
    class PostActivityState(BaseModel):
        workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
        user_profile_id: str
        user_ws_session_name: Optional[str] = None

        # Activity context
        activity_id: Optional[str] = None
        activity_name: Optional[str] = None
        activity_completion_time: Optional[str] = None

        # Context and conversation tracking
        initial_context_packet: Dict[str, Any] = Field(default_factory=dict)
        conversation_history: List[Dict[str, Any]] = Field(default_factory=list)

        # Legacy fields for compatibility
        user_input: Optional[Dict[str, Any]] = None
        current_context: Dict[str, Any] = Field(default_factory=dict)
        processed_responses: Dict[str, Any] = Field(default_factory=dict)

        # Agent outputs and state tracking
        output_data: Dict[str, Any] = Field(default_factory=dict)
        last_agent: Optional[str] = None
        error: Optional[str] = None
        completed: bool = False
        feedback_stage: str = "initial"  # Tracks which part of feedback collection we're in
        iteration_count: int = 0  # Track iterations to prevent infinite loops
        
        # Feedback collection data
        feedback_report: Dict[str, Any] = Field(default_factory=dict)
    
    # Create the graph
    workflow = StateGraph(PostActivityState)
    
    # Add agent nodes (simplified - only mentor for MVP, following the specification)
    workflow.add_node("mentor", MentorAgent(user_profile_id))
    
    # Define routing logic
    def route_next_agent(state):
        """Determine the next agent based on current state."""
        try:
            # Check if we have an error
            if state.error:
                logger.error(f"Error in post-activity workflow: {state.error}")
                return END
            
            # Check if workflow is completed
            if state.completed:
                logger.info(f"Post-activity workflow {state.workflow_id} completed successfully")
                return END
            
            # Check for infinite loop protection
            if state.iteration_count > 5:
                logger.warning(f"Post-activity workflow {state.workflow_id} exceeded max iterations")
                return END
            
            # For MVP, we only have the mentor agent
            # In the full implementation, this would route through multiple agents
            current_agent = getattr(state, 'last_agent', None)
            
            if current_agent is None:
                # Start with mentor agent
                return "mentor"
            elif current_agent == "mentor":
                # Check if mentor has completed feedback collection
                feedback_stage = getattr(state, 'feedback_stage', 'initial')
                if feedback_stage == "completed":
                    return END
                else:
                    # Continue with mentor for more feedback collection
                    return "mentor"
            else:
                # Default to end
                return END
                
        except Exception as e:
            logger.error(f"Error in post-activity routing: {str(e)}")
            return END
    
    # Set up the workflow routing
    workflow.add_conditional_edges(
        "mentor",
        route_next_agent,
        {
            "mentor": "mentor",
            END: END
        }
    )
    
    # Set entry point
    workflow.set_entry_point("mentor")
    
    return workflow


async def run_post_activity_workflow(user_profile_id, initial_input, workflow_id=None, workflow_input=None):
    """
    Run the post-activity workflow with the provided user input.

    This function supports both the legacy interface (individual parameters) and the new
    benchmarking interface (workflow_input dictionary) for maximum compatibility.

    Args:
        user_profile_id: The ID of the user profile (legacy interface)
        initial_input: The initial input from the user (legacy interface)
        workflow_id: Optional workflow ID (will generate a new one if not provided)
        workflow_input: Dictionary containing all workflow parameters (new benchmarking interface)
                       Expected keys: user_profile_id, context_packet, workflow_id,
                       use_real_llm, use_real_tools, use_real_db, mock_tools

    Returns:
        dict: Post-activity workflow results including feedback report
    """
    # Handle both legacy and new interfaces
    if workflow_input:
        # New benchmarking interface
        user_profile_id = workflow_input.get('user_profile_id', user_profile_id)
        initial_input = workflow_input.get('context_packet', initial_input)
        workflow_id = workflow_input.get('workflow_id', workflow_id)
    
    # Generate workflow ID if not provided
    if not workflow_id:
        workflow_id = str(uuid.uuid4())
    
    logger.info(f"Starting post-activity workflow for user {user_profile_id}, workflow_id: {workflow_id}")

    # Create the workflow
    workflow = create_post_activity_graph(user_profile_id)

    # Compile the workflow
    app = workflow.compile()

    # Create initial state with proper context packet
    initial_state = {
        "workflow_id": workflow_id,
        "user_profile_id": user_profile_id,
        "initial_context_packet": initial_input if isinstance(initial_input, dict) else {"text": str(initial_input)},
        "user_ws_session_name": initial_input.get('user_ws_session_name') if isinstance(initial_input, dict) else None,
        "feedback_stage": "initial",
        "conversation_history": [],
        "output_data": {},
        "iteration_count": 0,
        "feedback_report": {}
    }
    
    # Extract activity context if available
    if isinstance(initial_input, dict):
        initial_state["activity_id"] = initial_input.get('activity_id')
        initial_state["activity_name"] = initial_input.get('activity_name')
        initial_state["activity_completion_time"] = initial_input.get('activity_completion_time')

    # Run the workflow
    result = await app.ainvoke(initial_state)

    return result
