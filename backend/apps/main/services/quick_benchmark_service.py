"""
Quick Benchmark Service
Implements Facade Pattern to orchestrate existing benchmarking infrastructure
"""
import logging
from typing import Dict, Any, Optional
from django.db import transaction
from django.core.exceptions import ValidationError
from asgiref.sync import sync_to_async, async_to_sync

from apps.user.models import UserProfile
from apps.main.models import BenchmarkRun, BenchmarkScenario, GenericAgent, EvaluationContext
from apps.main.services.benchmark_manager import AgentBenchmarker
from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
from apps.main.services.simple_evaluation_adapter import SimpleEvaluationAdapter
from apps.main.services.evaluation_context_service import EvaluationContextService

logger = logging.getLogger(__name__)

class QuickBenchmarkService:
    """
    Facade service for quick agent benchmarking.
    Orchestrates existing services without duplicating functionality.
    """
    
    def __init__(self):
        """Initialize service with dependency injection of existing services."""
        self.profile_factory = BenchmarkProfileFactory()
        self.evaluation_adapter = SimpleEvaluationAdapter()
        self.agent_benchmarker = AgentBenchmarker()
        self.evaluation_context_service = EvaluationContextService()
    
    def run_quick_benchmark_sync(
        self,
        agent_name: str,  # This will be the agent role
        user_profile_id: str,  # Changed from profile_template to user_profile_id
        evaluation_template: str,
        scenario_context: Optional[Dict[str, Any]] = None,
        use_real_tools: bool = True,  # Default to real tools for real profiles
        use_real_db: bool = True      # Default to real DB for real profiles
    ) -> BenchmarkRun:
        """
        Run a quick benchmark using existing infrastructure (synchronous version).

        Args:
            agent_name: Name of the agent to benchmark (actually the role)
            user_profile_id: ID of the real user profile to use
            evaluation_template: Template for evaluation criteria
            scenario_context: Optional context for scenario customization
            use_real_tools: Whether to use real tools (default True for real profiles)
            use_real_db: Whether to use real database (default True for real profiles)

        Returns:
            BenchmarkRun: Completed benchmark run with results

        Raises:
            ValidationError: If agent not found or benchmark fails
        """
        try:
            logger.info(f"Starting quick benchmark: agent={agent_name}, profile_id={user_profile_id}, eval={evaluation_template}")

            # 1. Get real UserProfile (synchronous)
            from apps.user.models import UserProfile
            try:
                user_profile = UserProfile.objects.get(id=user_profile_id)
                logger.debug(f"Retrieved user profile: {user_profile.profile_name} (real={user_profile.is_real})")
            except UserProfile.DoesNotExist:
                raise ValidationError(f"User profile with ID {user_profile_id} not found")

            # 2. Get agent (synchronous)
            agent = self._get_agent(agent_name)
            logger.debug(f"Retrieved agent: {agent.role}")

            # 3. Create scenario (synchronous)
            scenario = self._create_quick_scenario(
                agent, user_profile, scenario_context
            )
            logger.debug(f"Created scenario: {scenario.name}")

            # 4. Run benchmark using existing AgentBenchmarker (async to sync)
            benchmark_run = self._run_benchmark_sync(
                scenario=scenario,
                user_profile=user_profile,
                agent=agent,
                use_real_tools=use_real_tools,
                use_real_db=use_real_db
            )
            logger.debug(f"Benchmark run completed: {benchmark_run.id}")

            # 5. Enhanced evaluation (synchronous)
            agent_response_text = self._extract_agent_response(benchmark_run)
            if agent_response_text:
                try:
                    evaluation_result = self.evaluation_adapter.evaluate_with_simple_prompt_sync(
                        agent_response=agent_response_text,
                        evaluation_template=evaluation_template,
                        context={
                            'user_profile': user_profile,
                            'user_profile_id': str(user_profile.id),
                            'scenario': scenario,
                            'scenario_context': scenario.description,
                            'trust_level': getattr(user_profile, 'trust_level', None)
                        },
                        benchmark_run=benchmark_run
                    )

                    # 6. Update benchmark run with evaluation
                    benchmark_run.semantic_evaluation = evaluation_result
                    benchmark_run.save()
                    logger.debug(f"Evaluation completed and saved")
                except Exception as eval_error:
                    logger.warning(f"Evaluation failed, continuing without it: {eval_error}")
            else:
                logger.warning("No agent response found for evaluation")

            logger.info(f"Quick benchmark completed successfully: {benchmark_run.id}")
            return benchmark_run

        except Exception as e:
            logger.error(f"Quick benchmark failed: {e}", exc_info=True)
            raise ValidationError(f"Benchmark execution failed: {e}")
    
    def _get_agent(self, agent_role: str) -> GenericAgent:
        """Get agent by role with proper error handling."""
        try:
            return GenericAgent.objects.get(role=agent_role)
        except GenericAgent.DoesNotExist:
            available_agents = list(GenericAgent.objects.values_list('role', flat=True))
            raise ValidationError(
                f"Agent '{agent_role}' not found. Available: {available_agents}"
            )
    
    def _create_quick_scenario(
        self,
        agent: GenericAgent,
        user_profile: UserProfile,
        context: Optional[Dict[str, Any]]
    ) -> BenchmarkScenario:
        """
        Create a quick benchmark scenario.
        Leverages existing BenchmarkScenario model and patterns.
        """
        scenario_data = {
            'name': f"Quick_{agent.role}_{user_profile.profile_name}",
            'description': f"Quick benchmark for {agent.role} with {user_profile.profile_name}",
            'agent_role': agent.role,
            'workflow_type': context.get('workflow_type', 'discussion') if context else 'discussion',
            'input_data': {
                'user_input': context.get('user_input', 'Hello, I need help') if context else 'Hello, I need help',
                'user_profile_id': str(user_profile.id),
                'scenario_type': 'quick_benchmark'
            },
            'metadata': {
                'expected_behavior': f"Agent should respond appropriately to {user_profile.profile_name} characteristics",
                'evaluation_focus': 'response_quality',
                'scenario_type': 'quick_benchmark',
                'evaluator_models': ['mistral-small-latest'],  # Default evaluator model
                'expected_quality_criteria': {
                    'Helpfulness': [
                        'Response addresses the user\'s request appropriately',
                        'Provides useful and relevant information',
                        'Shows understanding of user context'
                    ],
                    'Clarity': [
                        'Response is clear and easy to understand',
                        'Uses appropriate language for the user',
                        'Well-structured and coherent'
                    ],
                    'Appropriateness': [
                        'Tone matches user profile characteristics',
                        'Response length is suitable for the context',
                        'Maintains professional and supportive demeanor'
                    ]
                }
            },
            'is_active': True,
            'version': 1,
            'is_latest': True
        }
        
        # Create scenario (leverages existing model) - use get_or_create for idempotency
        scenario, created = BenchmarkScenario.objects.get_or_create(
            name=scenario_data['name'],
            defaults=scenario_data
        )
        if created:
            logger.debug(f"Created new scenario: {scenario.name}")
        else:
            # Update existing scenario with new metadata (including evaluation criteria)
            scenario.metadata = scenario_data['metadata']
            scenario.save()
            logger.debug(f"Updated existing scenario: {scenario.name}")
        return scenario
    
    def _run_benchmark_sync(
        self,
        scenario: BenchmarkScenario,
        user_profile: UserProfile,
        agent: GenericAgent,
        use_real_tools: bool = True,
        use_real_db: bool = True
    ) -> BenchmarkRun:
        """
        Run agent benchmark using existing AgentBenchmarker (synchronous wrapper).
        """
        try:
            # Use existing AgentBenchmarker.run_benchmark method with async_to_sync
            params = {
                'runs': 1,
                'warmup_runs': 0,
                'use_real_llm': True,
                'use_real_tools': use_real_tools,  # Use real tools for real profiles
                'use_real_db': use_real_db,       # Use real DB for real profiles
                'semantic_evaluation': True,
                'evaluation_llm_config_id': None  # Let it use default evaluation config
            }

            # The AgentBenchmarker.run_benchmark method expects scenario_id
            benchmark_run = async_to_sync(self.agent_benchmarker.run_benchmark)(
                scenario_id=scenario.id,
                params=params,
                user_profile_id=str(user_profile.id)
            )

            return benchmark_run
        except Exception as e:
            logger.error(f"Agent benchmark execution failed: {e}", exc_info=True)
            raise ValidationError(f"Failed to run agent benchmark: {e}")
    
    def get_available_options(self) -> Dict[str, Any]:
        """
        Get available options for quick benchmarking.
        Returns real user profiles, agents, evaluation options, workflows, and evaluation contexts.
        """
        from apps.user.models import UserProfile

        # Get real user profiles (both real and test profiles)
        user_profiles = []
        for profile in UserProfile.objects.select_related('user').all()[:50]:  # Limit to 50 profiles
            user_profiles.append({
                'id': str(profile.id),
                'profile_name': profile.profile_name or f"User {profile.id}",
                'is_real': profile.is_real,
                'description': f"{'Real user' if profile.is_real else 'Test profile'} - {profile.profile_name}"
            })

        # Get evaluation templates from database
        from apps.main.models import EvaluationCriteriaTemplate
        evaluation_templates = list(
            EvaluationCriteriaTemplate.objects.filter(is_active=True).values_list('name', flat=True)
        )

        return {
            'user_profiles': user_profiles,  # Changed from profile_templates to user_profiles
            'evaluation_templates': evaluation_templates,  # Now from database
            'available_agents': list(
                GenericAgent.objects.values('role', 'description').filter(is_active=True)
            ),
            'available_workflows': self.evaluation_context_service.get_available_workflows(),
            'available_agent_roles': self.evaluation_context_service.get_available_agent_roles()
        }
    
    def _extract_agent_response(self, benchmark_run: BenchmarkRun) -> Optional[str]:
        """Extract agent response text from benchmark run results."""
        try:
            # Check raw_results for agent response
            if benchmark_run.raw_results and isinstance(benchmark_run.raw_results, dict):
                # Check last_output structure (current format)
                last_output = benchmark_run.raw_results.get('last_output')
                if isinstance(last_output, dict):
                    response_text = last_output.get('user_response')
                    if response_text:
                        return str(response_text)

                # Check last_output_data structure (alternative format)
                last_output_data = benchmark_run.raw_results.get('last_output_data')
                if isinstance(last_output_data, dict):
                    response_text = (
                        last_output_data.get('user_response') or
                        last_output_data.get('response_text') or
                        last_output_data.get('output') or
                        last_output_data.get('content')
                    )
                    if response_text:
                        return str(response_text)

                # Fallback: check if raw_results has direct response
                response_text = (
                    benchmark_run.raw_results.get('user_response') or
                    benchmark_run.raw_results.get('response_text') or
                    benchmark_run.raw_results.get('output') or
                    benchmark_run.raw_results.get('content')
                )
                if response_text:
                    return str(response_text)

            logger.debug(f"No agent response found in benchmark run {benchmark_run.id}")
            return None

        except Exception as e:
            logger.warning(f"Error extracting agent response: {e}")
            return None

    def get_benchmark_results_url(self, benchmark_run: BenchmarkRun) -> str:
        """Get URL for viewing benchmark results in existing admin interface."""
        from django.urls import reverse
        try:
            return reverse('admin:main_benchmarkrun_change', args=[benchmark_run.pk])
        except:
            # Fallback to benchmark history if direct admin link fails
            return f"/admin/benchmarks/history/?run_id={benchmark_run.pk}"

    def run_agent_in_workflow_context_sync(
        self,
        agent_role: str,
        workflow_type: str,
        user_profile_id: str,
        evaluation_context_id: Optional[str] = None,
        evaluation_template: str = 'agent_accuracy',
        use_real_tools: bool = True,
        use_real_db: bool = True
    ) -> BenchmarkRun:
        """
        Run an agent benchmark within a specific workflow context.
        This evaluates how well an individual agent performs within a workflow scenario.

        Args:
            agent_role: Role of the agent to evaluate
            workflow_type: Type of workflow to simulate
            user_profile_id: ID of the user profile to use
            evaluation_context_id: Optional specific evaluation context to use
            evaluation_template: Template for evaluation criteria
            use_real_tools: Whether to use real tools
            use_real_db: Whether to use real database

        Returns:
            BenchmarkRun: Completed benchmark run with workflow context
        """
        try:
            logger.info(f"Starting workflow-aware benchmark: agent={agent_role}, workflow={workflow_type}")

            # 1. Get user profile
            user_profile = UserProfile.objects.get(id=user_profile_id)

            # 2. Get or create evaluation context
            if evaluation_context_id:
                evaluation_context = EvaluationContext.objects.get(id=evaluation_context_id)
            else:
                # Find a suitable context or create a default one
                evaluation_context = self._get_or_create_default_context(
                    agent_role, workflow_type, user_profile
                )

            # 3. Get agent
            agent = self._get_agent(agent_role)

            # 4. Simulate workflow graph state
            graph_state = self.evaluation_context_service.simulate_agent_graph_state(
                evaluation_context, user_profile
            )

            # 5. Create workflow-aware scenario
            scenario = self._create_workflow_scenario(
                agent, user_profile, evaluation_context, graph_state
            )

            # 6. Run benchmark with workflow context
            benchmark_run = self._run_benchmark_sync(
                scenario=scenario,
                user_profile=user_profile,
                agent=agent,
                use_real_tools=use_real_tools,
                use_real_db=use_real_db
            )

            # 7. Enhanced evaluation with workflow context
            agent_response_text = self._extract_agent_response(benchmark_run)
            if agent_response_text:
                try:
                    combined_context = self.evaluation_context_service.get_combined_context(
                        str(evaluation_context.id), user_profile
                    )

                    evaluation_result = self.evaluation_adapter.evaluate_with_simple_prompt_sync(
                        agent_response=agent_response_text,
                        evaluation_template=evaluation_template,
                        context={
                            **combined_context,
                            'scenario': scenario,
                            'workflow_context': graph_state['workflow_context'],
                            'agent_coordination': graph_state['agent_coordination']
                        },
                        benchmark_run=benchmark_run
                    )

                    benchmark_run.semantic_evaluation = evaluation_result
                    benchmark_run.save()

                except Exception as eval_error:
                    logger.warning(f"Workflow evaluation failed: {eval_error}")

            logger.info(f"Workflow-aware benchmark completed: {benchmark_run.id}")
            return benchmark_run

        except Exception as e:
            logger.error(f"Workflow-aware benchmark failed: {e}", exc_info=True)
            raise ValidationError(f"Workflow benchmark execution failed: {e}")

    def get_evaluation_contexts_for_workflow(self, workflow_type: str) -> list:
        """Get available evaluation contexts for a specific workflow type."""
        return self.evaluation_context_service.list_contexts_for_workflow(workflow_type)

    def get_evaluation_contexts_for_agent(self, agent_role: str) -> list:
        """Get available evaluation contexts for a specific agent role."""
        return self.evaluation_context_service.list_contexts_for_agent(agent_role)

    def _get_or_create_default_context(
        self,
        agent_role: str,
        workflow_type: str,
        user_profile: UserProfile
    ) -> EvaluationContext:
        """Get or create a default evaluation context for the given parameters."""
        context_name = f"Default_{workflow_type}_{agent_role}"

        try:
            return EvaluationContext.objects.get(
                name=context_name,
                current_workflow_type=workflow_type,
                agent_role_being_evaluated=agent_role,
                is_active=True
            )
        except EvaluationContext.DoesNotExist:
            # Create a default context
            context_data = {
                'name': context_name,
                'description': f"Default evaluation context for {agent_role} in {workflow_type} workflow",
                'current_workflow_type': workflow_type,
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': agent_role,
                'previous_agent_outputs': [],
                'expected_next_agents': []
            }
            return self.evaluation_context_service.create_context(context_data)

    def _create_workflow_scenario(
        self,
        agent: GenericAgent,
        user_profile: UserProfile,
        evaluation_context: EvaluationContext,
        graph_state: Dict[str, Any]
    ) -> BenchmarkScenario:
        """Create a workflow-aware benchmark scenario."""
        scenario_data = {
            'name': f"Workflow_{evaluation_context.current_workflow_type}_{agent.role}_{user_profile.profile_name}",
            'description': f"Workflow-aware benchmark for {agent.role} in {evaluation_context.current_workflow_type} with {user_profile.profile_name}",
            'agent_role': agent.role,
            'workflow_type': evaluation_context.current_workflow_type,
            'input_data': {
                'user_input': 'Hello, I need help',
                'user_profile_id': str(user_profile.id),
                'scenario_type': 'workflow_benchmark',
                'workflow_context': graph_state['workflow_context'],
                'agent_coordination': graph_state['agent_coordination'],
                'evaluation_context_id': str(evaluation_context.id)
            },
            'metadata': {
                'expected_behavior': f"Agent should respond appropriately within {evaluation_context.current_workflow_type} workflow context",
                'evaluation_focus': 'workflow_integration',
                'scenario_type': 'workflow_benchmark',
                'workflow_stage': evaluation_context.workflow_stage,
                'previous_agent_outputs': evaluation_context.previous_agent_outputs,
                'expected_next_agents': evaluation_context.expected_next_agents,
                'evaluator_models': ['mistral-small-latest'],
                'expected_quality_criteria': {
                    'Workflow Integration': [
                        'Agent responds appropriately to workflow context',
                        'Uses information from previous agents effectively',
                        'Prepares output suitable for next agents in workflow'
                    ],
                    'Context Awareness': [
                        'Shows understanding of current workflow stage',
                        'Responds appropriately to user profile characteristics',
                        'Considers environmental and mood factors'
                    ],
                    'Agent Coordination': [
                        'Output format suitable for workflow continuation',
                        'Maintains consistency with previous agent outputs',
                        'Provides necessary information for downstream agents'
                    ]
                }
            },
            'is_active': True,
            'version': 1,
            'is_latest': True
        }

        # Create scenario with workflow context
        scenario, created = BenchmarkScenario.objects.get_or_create(
            name=scenario_data['name'],
            defaults=scenario_data
        )
        if created:
            logger.debug(f"Created new workflow scenario: {scenario.name}")
        else:
            # Update existing scenario with new metadata
            scenario.metadata = scenario_data['metadata']
            scenario.input_data = scenario_data['input_data']
            scenario.save()
            logger.debug(f"Updated existing workflow scenario: {scenario.name}")

        return scenario
