"""
Benchmark Profile Factory Service
Follows Factory Pattern and Django service layer best practices
"""
import logging
from typing import Dict, Optional
from django.db import transaction
from django.core.exceptions import ValidationError

from apps.user.models import UserProfile, UserTraitInclination, UserGoal, Belief, GenericTrait

logger = logging.getLogger(__name__)

class BenchmarkProfileFactory:
    """
    Factory service for creating benchmark-ready UserProfiles.
    Implements Factory Pattern with proper error handling and validation.
    """
    
    # Profile templates following established psychology frameworks
    PROFILE_TEMPLATES = {
        'anxious_new_user': {
            'profile_name': 'Benchmark_Anxious_New_User',
            'description': 'High neuroticism, low confidence, new to system',
            'personality_traits': {
                'openness': 0.4,
                'conscientiousness': 0.7,
                'extraversion': 0.3,
                'agreeableness': 0.8,
                'neuroticism': 0.8,
                'honesty_humility': 0.7
            },
            'goals': [
                'Reduce social anxiety in group settings',
                'Build confidence through small achievements',
                'Improve social skills gradually'
            ],
            'beliefs': [
                'I am not good enough for others',
                'People will judge me harshly if I make mistakes'
            ],
            'limitations': ['Social interaction anxiety', 'Low self-confidence'],
            'stress_level': 7,
            'mood_valence': 3.0,
            'mood_arousal': 6.0
        },
        'confident_adhd_user': {
            'profile_name': 'Benchmark_Confident_ADHD_User', 
            'description': 'High openness, low conscientiousness, creative energy',
            'personality_traits': {
                'openness': 0.9,
                'conscientiousness': 0.4,
                'extraversion': 0.7,
                'agreeableness': 0.6,
                'neuroticism': 0.3,
                'honesty_humility': 0.6
            },
            'goals': [
                'Channel creative energy into projects',
                'Improve focus and attention management',
                'Complete long-term creative projects'
            ],
            'beliefs': [
                'I can figure out creative solutions to problems',
                'Challenges are opportunities for growth'
            ],
            'limitations': ['Attention regulation difficulties', 'Task completion challenges'],
            'stress_level': 4,
            'mood_valence': 7.0,
            'mood_arousal': 8.0
        },
        'stressed_professional': {
            'profile_name': 'Benchmark_Stressed_Professional',
            'description': 'High conscientiousness, work-life balance issues',
            'personality_traits': {
                'openness': 0.6,
                'conscientiousness': 0.9,
                'extraversion': 0.5,
                'agreeableness': 0.5,
                'neuroticism': 0.6,
                'honesty_humility': 0.8
            },
            'goals': [
                'Manage work-related stress effectively',
                'Achieve better work-life balance',
                'Increase productivity and efficiency'
            ],
            'beliefs': [
                'Hard work always pays off in the end',
                'I must do everything perfectly to be valuable'
            ],
            'limitations': ['Perfectionism', 'Work-life boundary issues'],
            'stress_level': 8,
            'mood_valence': 5.0,
            'mood_arousal': 7.0
        }
    }
    
    @classmethod
    @transaction.atomic
    def create_benchmark_profile(cls, template_name: str) -> UserProfile:
        """
        Create a benchmark UserProfile from template.
        
        Args:
            template_name: Key from PROFILE_TEMPLATES
            
        Returns:
            UserProfile: Created profile with is_real=False
            
        Raises:
            ValidationError: If template not found or creation fails
        """
        if template_name not in cls.PROFILE_TEMPLATES:
            raise ValidationError(f"Unknown template: {template_name}")
            
        template = cls.PROFILE_TEMPLATES[template_name]
        
        try:
            # Create a Django User for the benchmark profile
            from django.contrib.auth import get_user_model
            User = get_user_model()

            # Create or get benchmark user
            username = template['profile_name'].lower().replace(' ', '_')
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': f'{username}@benchmark.local',
                    'first_name': template['profile_name'],
                    'is_active': False,  # Benchmark users are inactive
                    'is_staff': False
                }
            )

            # Create UserProfile with is_real=False (your key insight)
            # Note: UserProfile model only has basic fields: user, profile_name, current_environment, is_real
            profile = UserProfile.objects.create(
                user=user,
                profile_name=template['profile_name'],
                is_real=False  # Critical: marks as benchmark profile
            )
            
            # Create personality traits using existing relationship
            cls._create_personality_traits(profile, template['personality_traits'])
            
            # Create goals using existing relationship  
            cls._create_user_goals(profile, template['goals'])
            
            # Create beliefs using existing relationship
            cls._create_user_beliefs(profile, template['beliefs'])
            
            # Create limitations if provided
            if 'limitations' in template:
                cls._create_user_limitations(profile, template['limitations'])
            
            logger.info(f"Created benchmark profile: {profile.profile_name}")
            return profile
            
        except Exception as e:
            logger.error(f"Failed to create benchmark profile {template_name}: {e}")
            raise ValidationError(f"Profile creation failed: {e}")
    
    @classmethod
    def get_or_create_benchmark_profile(cls, template_name: str) -> UserProfile:
        """
        Get existing benchmark profile or create new one.
        Implements idempotent profile creation.
        """
        if template_name not in cls.PROFILE_TEMPLATES:
            raise ValidationError(f"Unknown template: {template_name}")
            
        template = cls.PROFILE_TEMPLATES[template_name]
        profile_name = template['profile_name']
        
        # Try to get existing profile
        profile = UserProfile.objects.filter(
            profile_name=profile_name,
            is_real=False
        ).first()
        
        if profile:
            logger.info(f"Using existing benchmark profile: {profile_name}")
            return profile
        
        # Create new profile
        return cls.create_benchmark_profile(template_name)
    
    @classmethod
    def list_available_templates(cls) -> list[dict[str, str]]:
        """Return list of available profile templates with descriptions."""
        return [
            {
                'template_name': name,
                'profile_name': template['profile_name'],
                'description': template['description']
            }
            for name, template in cls.PROFILE_TEMPLATES.items()
        ]
    
    @classmethod  
    def _create_personality_traits(cls, profile: UserProfile, traits: Dict[str, float]):
        """Create UserTraitInclination objects from traits dictionary."""
        for trait_name, strength in traits.items():
            try:
                # Get or create personality trait
                trait, _ = GenericTrait.objects.get_or_create(
                    code=trait_name.lower(),
                    defaults={
                        'name': trait_name.title(),
                        'description': f'{trait_name.title()} personality dimension',
                        'trait_type': 'PERSONALITY'
                    }
                )
                
                # Create trait inclination
                UserTraitInclination.objects.create(
                    user_profile=profile,
                    generic_trait=trait,
                    strength=strength,
                    awareness=80  # Default awareness level
                )
            except Exception as e:
                logger.warning(f"Failed to create trait {trait_name}: {e}")
    
    @classmethod
    def _create_user_goals(cls, profile: UserProfile, goals: list[str]):
        """Create UserGoal objects from goals list."""
        for i, goal_description in enumerate(goals):
            try:
                UserGoal.objects.create(
                    user_profile=profile,
                    title=f"Goal {i+1}",  # Required field
                    description=goal_description,  # Required field
                    importance_according_user=70,  # Default importance
                    importance_according_system=70,  # Default importance
                    strength=60  # Default strength
                )
            except Exception as e:
                logger.warning(f"Failed to create goal '{goal_description}': {e}")
    
    @classmethod
    def _create_user_beliefs(cls, profile: UserProfile, beliefs: list[str]):
        """Create Belief objects from beliefs list."""
        from datetime import date

        for belief_text in beliefs:
            try:
                Belief.objects.create(
                    user_profile=profile,
                    content=belief_text,  # Correct field name
                    last_updated=date.today(),  # Required field
                    user_confidence=80,  # Default confidence
                    system_confidence=75,  # Default system confidence
                    emotionality=10,  # Default emotional charge
                    stability=70,  # Default stability
                    user_awareness=60  # Default awareness
                )
            except Exception as e:
                logger.warning(f"Failed to create belief '{belief_text}': {e}")
    
    @classmethod
    def _create_user_limitations(cls, profile: UserProfile, limitations: list[str]):
        """Create UserLimitation objects from limitations list."""
        from apps.user.models import UserLimitation, GenericUserLimitation
        from datetime import date, timedelta

        for limitation_text in limitations:
            try:
                # Get or create generic limitation
                limitation, _ = GenericUserLimitation.objects.get_or_create(
                    code=limitation_text.lower().replace(' ', '_'),
                    defaults={
                        'description': limitation_text,
                        'limitation_type': 'PSYCHOLOGICAL'
                    }
                )

                # Create user limitation
                UserLimitation.objects.create(
                    user_profile=profile,
                    generic_limitation=limitation,
                    severity=70,  # Moderate severity
                    valid_until=date.today() + timedelta(days=365),  # Valid for 1 year
                    effective_start=date.today(),
                    effective_end=date.today() + timedelta(days=365),
                    duration_estimate='365 days',
                    user_awareness=60  # Moderate awareness
                )
            except Exception as e:
                logger.warning(f"Failed to create limitation '{limitation_text}': {e}")
