"""
Simple Evaluation Adapter Service
Implements <PERSON>pt<PERSON> <PERSON><PERSON> to bridge natural language prompts to existing evaluation criteria
"""
import logging
from typing import Dict, Any, Optional
from django.core.exceptions import ValidationError
from asgiref.sync import async_to_sync

from apps.main.services.semantic_evaluator import SemanticEvaluator
from apps.main.models import BenchmarkRun

logger = logging.getLogger(__name__)

class SimpleEvaluationAdapter:
    """
    Adapter to convert simple natural language evaluation prompts 
    to existing SemanticEvaluator criteria format.
    """
    
    # Pre-defined evaluation templates for common scenarios
    EVALUATION_TEMPLATES = {
        'mentor_helpfulness': {
            'evaluation_prompt': """
            Evaluate the mentor agent's response for helpfulness and appropriateness:
            
            1. Helpfulness (0-10): How well does the response address the user's needs?
            2. Tone Appropriateness (0-10): Is the tone supportive and empathetic?
            3. Actionability (0-10): Does the response provide clear next steps?
            4. Trust Building (0-10): Does the response build trust with the user?
            
            Provide scores and brief justifications for each dimension.
            """,
            'criteria_mapping': {
                'helpfulness': 0.3,
                'tone_appropriateness': 0.25,
                'actionability': 0.25,
                'trust_building': 0.2
            }
        },
        'agent_accuracy': {
            'evaluation_prompt': """
            Evaluate the agent's response for accuracy and relevance:
            
            1. Factual Accuracy (0-10): Are the facts and information correct?
            2. Relevance (0-10): How relevant is the response to the user's situation?
            3. Completeness (0-10): Does the response address all aspects of the request?
            4. Clarity (0-10): Is the response clear and understandable?
            
            Provide scores and justifications.
            """,
            'criteria_mapping': {
                'factual_accuracy': 0.3,
                'relevance': 0.3,
                'completeness': 0.2,
                'clarity': 0.2
            }
        },
        'wheel_generation_quality': {
            'evaluation_prompt': """
            Evaluate the wheel generation workflow output for quality and relevance:
            
            1. Activity Relevance (0-10): How relevant are the activities to the user's goals?
            2. Activity Diversity (0-10): How diverse and well-balanced are the activities?
            3. Personalization (0-10): How well are activities tailored to the user profile?
            4. Actionability (0-10): How actionable and specific are the activities?
            
            Provide scores and justifications for each dimension.
            """,
            'criteria_mapping': {
                'activity_relevance': 0.3,
                'activity_diversity': 0.25,
                'personalization': 0.25,
                'actionability': 0.2
            }
        }
    }
    
    def __init__(self, semantic_evaluator: Optional[SemanticEvaluator] = None):
        """Initialize adapter with optional semantic evaluator dependency injection."""
        self.semantic_evaluator = semantic_evaluator or SemanticEvaluator()
    
    async def evaluate_with_simple_prompt(
        self, 
        agent_response: str,
        evaluation_template: str,
        context: Dict[str, Any],
        benchmark_run: Optional[BenchmarkRun] = None
    ) -> Dict[str, Any]:
        """
        Evaluate agent response using simple template.
        
        Args:
            agent_response: The agent's response to evaluate
            evaluation_template: Key from EVALUATION_TEMPLATES  
            context: Additional context for evaluation
            benchmark_run: Associated benchmark run (optional)
            
        Returns:
            Dict containing evaluation results
            
        Raises:
            ValidationError: If template not found or evaluation fails
        """
        if evaluation_template not in self.EVALUATION_TEMPLATES:
            raise ValidationError(f"Unknown evaluation template: {evaluation_template}")
        
        template = self.EVALUATION_TEMPLATES[evaluation_template]
        
        try:
            # Convert simple prompt to existing criteria format
            evaluation_criteria = self._convert_to_criteria_format(template)
            
            # Extract scenario context from context dict
            scenario_context = context.get('scenario_context', 'Generic benchmark scenario')
            if 'scenario' in context and hasattr(context['scenario'], 'description'):
                scenario_context = context['scenario'].description
            
            # Use existing SemanticEvaluator with converted criteria
            evaluation_result = await self.semantic_evaluator.evaluate_response(
                scenario_context=scenario_context,
                agent_response=agent_response,
                criteria=evaluation_criteria,
                user_profile_id=context.get('user_profile_id'),
                trust_level=context.get('trust_level'),
                evaluation_context='simple_benchmark'
            )
            
            logger.info(f"Completed simple evaluation with template: {evaluation_template}")
            return evaluation_result
            
        except Exception as e:
            logger.error(f"Simple evaluation failed: {e}")
            raise ValidationError(f"Evaluation failed: {e}")
    
    def _convert_to_criteria_format(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert simple template to existing SemanticEvaluator criteria format.
        Maintains compatibility with existing evaluation infrastructure.
        """
        # Convert criteria mapping to the format expected by SemanticEvaluator
        criteria_dict = {}
        for criterion, weight in template['criteria_mapping'].items():
            criteria_dict[criterion.title().replace('_', ' ')] = [
                f"Evaluate {criterion.replace('_', ' ')} (weight: {weight})"
            ]
        
        return {
            'template_name': 'simple_evaluation',
            'description': 'Simple evaluation using natural language prompt',
            'evaluation_prompt': template['evaluation_prompt'],
            'criteria': criteria_dict,
            'dimension_weights': template['criteria_mapping']
        }
    
    def create_custom_evaluation(
        self,
        prompt: str,
        criteria_weights: Dict[str, float]
    ) -> str:
        """
        Create custom evaluation template from prompt and weights.
        Validates that weights sum to 1.0.
        """
        # Validate weights
        total_weight = sum(criteria_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            raise ValidationError(f"Criteria weights must sum to 1.0, got {total_weight}")
        
        template_key = f"custom_{hash(prompt) % 10000}"
        
        self.EVALUATION_TEMPLATES[template_key] = {
            'evaluation_prompt': prompt,
            'criteria_mapping': criteria_weights
        }
        
        return template_key
    
    def get_available_templates(self) -> list[dict[str, str]]:
        """Return list of available evaluation templates."""
        return [
            {
                'template_name': name,
                'description': template.get('description', 'Custom evaluation template')
            }
            for name, template in self.EVALUATION_TEMPLATES.items()
        ]

    def evaluate_with_simple_prompt_sync(
        self,
        agent_response: str,
        evaluation_template: str,
        context: Dict[str, Any],
        benchmark_run: Optional[BenchmarkRun] = None
    ) -> Dict[str, Any]:
        """
        Synchronous wrapper for evaluate_with_simple_prompt.

        Args:
            agent_response: The agent's response to evaluate
            evaluation_template: Template name for evaluation criteria
            context: Context information for evaluation
            benchmark_run: Optional benchmark run for additional context

        Returns:
            Dict containing evaluation results
        """
        try:
            # Use async_to_sync with a lambda to avoid parameter conflicts
            return async_to_sync(
                lambda: self.evaluate_with_simple_prompt(
                    agent_response=agent_response,
                    evaluation_template=evaluation_template,
                    context=context,
                    benchmark_run=benchmark_run
                )
            )()
        except Exception as e:
            logger.error(f"Synchronous evaluation failed: {e}", exc_info=True)
            # Return a basic evaluation result on failure
            return {
                'overall_score': 0.5,
                'overall_reasoning': f'Evaluation failed: {e}',
                'dimensions': {},
                'error': True
            }
