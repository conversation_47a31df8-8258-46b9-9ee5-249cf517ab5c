import logging
from typing import Dict, Any, List, Optional
from apps.main.models import EvaluationContext
from apps.user.models import UserProfile

logger = logging.getLogger(__name__)


class EvaluationContextService:
    """
    Service for managing evaluation contexts and combining them with UserProfiles.
    
    This service enables workflow-aware benchmarking by providing context simulation
    and agent state mocking capabilities for testing agents within specific workflow scenarios.
    """
    
    def create_context(self, context_data: Dict[str, Any]) -> EvaluationContext:
        """Create a new evaluation context"""
        context = EvaluationContext.objects.create(**context_data)
        logger.info(f"Created evaluation context: {context.name}")
        return context
    
    def get_combined_context(
        self, 
        context_id: str, 
        user_profile: UserProfile
    ) -> Dict[str, Any]:
        """
        Get combined context for agent evaluation.
        This merges UserProfile data with EvaluationContext overrides.
        """
        try:
            evaluation_context = EvaluationContext.objects.get(id=context_id, is_active=True)
            
            # This is the key method - combines real user data with evaluation context
            combined_context = evaluation_context.get_effective_context(user_profile)
            
            # Add evaluation metadata
            combined_context.update({
                'evaluation_context_id': str(evaluation_context.id),
                'evaluation_context_name': evaluation_context.name,
                'evaluation_context_description': evaluation_context.description,
                'evaluation_timestamp': evaluation_context.created_at.isoformat()
            })
            
            return combined_context
            
        except EvaluationContext.DoesNotExist:
            logger.error(f"Evaluation context {context_id} not found")
            return {}
    
    def list_contexts_for_agent(self, agent_role: str) -> List[Dict[str, Any]]:
        """List all contexts suitable for evaluating a specific agent"""
        contexts = EvaluationContext.objects.filter(
            agent_role_being_evaluated=agent_role,
            is_active=True
        ).order_by('name')
        
        return [
            {
                'id': str(ctx.id),
                'name': ctx.name,
                'description': ctx.description,
                'workflow_type': ctx.current_workflow_type,
                'workflow_stage': ctx.workflow_stage,
                'trust_level_override': ctx.trust_level_override,
                'trust_phase': ctx.trust_phase,
                'stress_level_override': ctx.stress_level_override,
                'mood_summary': self._format_mood_summary(ctx),
                'has_overrides': self._has_context_overrides(ctx)
            }
            for ctx in contexts
        ]
    
    def list_contexts_for_workflow(self, workflow_type: str) -> List[Dict[str, Any]]:
        """List all contexts for a specific workflow type"""
        contexts = EvaluationContext.objects.filter(
            current_workflow_type=workflow_type,
            is_active=True
        ).order_by('agent_role_being_evaluated', 'name')
        
        return [
            {
                'id': str(ctx.id),
                'name': ctx.name,
                'description': ctx.description,
                'agent_role': ctx.agent_role_being_evaluated,
                'workflow_stage': ctx.workflow_stage,
                'trust_level_override': ctx.trust_level_override,
                'trust_phase': ctx.trust_phase,
                'stress_level_override': ctx.stress_level_override,
                'mood_summary': self._format_mood_summary(ctx),
                'has_overrides': self._has_context_overrides(ctx)
            }
            for ctx in contexts
        ]
    
    def get_available_workflows(self) -> List[Dict[str, str]]:
        """Get list of available workflow types"""
        return [
            {'value': choice[0], 'label': choice[1]}
            for choice in EvaluationContext._meta.get_field('current_workflow_type').choices
        ]
    
    def get_available_agent_roles(self) -> List[Dict[str, str]]:
        """Get list of available agent roles"""
        return [
            {'value': choice[0], 'label': choice[1]}
            for choice in EvaluationContext._meta.get_field('agent_role_being_evaluated').choices
        ]
    
    def _format_mood_summary(self, ctx: EvaluationContext) -> str:
        """Format mood override summary"""
        if ctx.mood_valence_override is not None and ctx.mood_arousal_override is not None:
            return f"valence={ctx.mood_valence_override:.1f}, arousal={ctx.mood_arousal_override:.1f}"
        elif ctx.mood_valence_override is not None:
            return f"valence={ctx.mood_valence_override:.1f}, arousal=default"
        elif ctx.mood_arousal_override is not None:
            return f"valence=default, arousal={ctx.mood_arousal_override:.1f}"
        else:
            return "default mood"
    
    def _has_context_overrides(self, ctx: EvaluationContext) -> bool:
        """Check if context has any variable overrides"""
        override_fields = [
            ctx.trust_level_override,
            ctx.mood_valence_override, 
            ctx.mood_arousal_override,
            ctx.stress_level_override,
            ctx.time_pressure_override,
            ctx.reported_environment_override,
            ctx.reported_time_availability_override,
            ctx.reported_focus_override
        ]
        return any(field is not None for field in override_fields)
    
    def get_contexts_by_workflow(self, workflow_type: str) -> List[EvaluationContext]:
        """Get all contexts for a specific workflow type"""
        return EvaluationContext.objects.filter(
            current_workflow_type=workflow_type,
            is_active=True
        ).order_by('agent_role_being_evaluated', 'name')
    
    def create_context_for_userprofile_combination(
        self,
        base_context_id: str,
        user_profile: UserProfile,
        context_name_suffix: str = ""
    ) -> Dict[str, Any]:
        """
        Create a preview of how a context would work with a specific UserProfile.
        Useful for testing context-user combinations.
        """
        try:
            base_context = EvaluationContext.objects.get(id=base_context_id)
            combined_context = base_context.get_effective_context(user_profile)
            
            # Add combination metadata
            combined_context.update({
                'combination_name': f"{base_context.name} + {user_profile.profile_name}{context_name_suffix}",
                'base_context_name': base_context.name,
                'user_profile_name': user_profile.profile_name,
                'is_fake_profile': not user_profile.is_real,
                'context_overrides_applied': self._has_context_overrides(base_context)
            })
            
            return combined_context
            
        except EvaluationContext.DoesNotExist:
            logger.error(f"Base context {base_context_id} not found")
            return {}
    
    def simulate_agent_graph_state(
        self,
        evaluation_context: EvaluationContext,
        user_profile: UserProfile
    ) -> Dict[str, Any]:
        """
        Simulate the graph state an agent would receive within a workflow.
        This creates the context that would be passed to an agent during workflow execution.
        """
        effective_context = evaluation_context.get_effective_context(user_profile)
        
        # Simulate LangGraph state structure
        graph_state = {
            'user_id': str(user_profile.id),
            'user_profile': {
                'id': str(user_profile.id),
                'profile_name': user_profile.profile_name,
                'is_real': user_profile.is_real,
                'trust_level': effective_context['trust_level'],
                'mood': effective_context['mood'],
                'environment': effective_context['environment'],
                'time_availability': effective_context['time_availability'],
                'reported_focus': effective_context['reported_focus'],
                'personality_traits': effective_context['personality_traits'],
                'user_goals': effective_context['user_goals'],
                'user_beliefs': effective_context['user_beliefs'],
                'user_limitations': effective_context['user_limitations']
            },
            'workflow_context': {
                'workflow_type': evaluation_context.current_workflow_type,
                'workflow_stage': evaluation_context.workflow_stage,
                'current_agent': evaluation_context.agent_role_being_evaluated
            },
            'agent_coordination': {
                'previous_agent_outputs': evaluation_context.previous_agent_outputs,
                'expected_next_agents': evaluation_context.expected_next_agents
            },
            'evaluation_metadata': {
                'context_id': str(evaluation_context.id),
                'context_name': evaluation_context.name,
                'is_evaluation_run': True
            }
        }
        
        return graph_state
    
    def mock_tool_calls_for_context(
        self,
        evaluation_context: EvaluationContext,
        agent_role: str
    ) -> Dict[str, Any]:
        """
        Generate mock tool call responses relevant to the workflow context.
        This simulates the tools an agent would call in a specific workflow scenario.
        """
        mock_responses = {}
        
        # Mock tool responses based on workflow type and agent role
        if evaluation_context.current_workflow_type == 'wheel_generation':
            if agent_role == 'mentor':
                mock_responses['get_user_profile'] = {
                    'trust_level': evaluation_context.trust_level_override or 50,
                    'mood_valence': evaluation_context.mood_valence_override or 0.0,
                    'mood_arousal': evaluation_context.mood_arousal_override or 0.0
                }
                mock_responses['get_recent_activities'] = [
                    {'name': 'Mock Activity 1', 'feedback': 'positive'},
                    {'name': 'Mock Activity 2', 'feedback': 'neutral'}
                ]
            elif agent_role == 'psychological':
                mock_responses['assess_user_state'] = {
                    'stress_level': evaluation_context.stress_level_override or 30,
                    'readiness_for_challenge': 'moderate'
                }
        
        elif evaluation_context.current_workflow_type == 'discussion':
            if agent_role == 'mentor':
                mock_responses['get_conversation_history'] = [
                    {'message': 'Previous user message', 'timestamp': '2025-06-15T10:00:00Z'}
                ]
        
        return mock_responses
