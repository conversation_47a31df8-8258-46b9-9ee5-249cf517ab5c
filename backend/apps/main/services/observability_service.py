"""
Advanced Observability Service for LangGraph + Celery + Django

A lightweight, high-performance observability system specifically designed for our stack:
- LangGraph workflow execution tracking
- Celery task monitoring with minimal overhead
- Django request/response correlation
- Real-time performance metrics
- Distributed tracing across components
- Smart sampling to reduce overhead
- Structured logging with context propagation

Key Features:
1. Zero-copy context propagation
2. Async-first design for minimal blocking
3. Smart sampling based on performance impact
4. LangGraph node-level instrumentation
5. Celery task lifecycle tracking
6. WebSocket real-time streaming
7. Performance anomaly detection
8. Cost tracking for LLM calls
"""

import asyncio
import time
import uuid
import json
import threading
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Callable, Union, Set
from dataclasses import dataclass, field, asdict
from enum import Enum
from collections import defaultdict, deque
import logging
from contextlib import contextmanager
import weakref

from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.conf import settings

logger = logging.getLogger(__name__)


class EventType(Enum):
    """Event types for observability tracking"""
    WORKFLOW_START = "workflow_start"
    WORKFLOW_END = "workflow_end"
    NODE_START = "node_start"
    NODE_END = "node_end"
    TASK_START = "task_start"
    TASK_END = "task_end"
    LLM_CALL = "llm_call"
    TOOL_CALL = "tool_call"
    ERROR = "error"
    PERFORMANCE_ALERT = "performance_alert"
    COST_ALERT = "cost_alert"


class Severity(Enum):
    """Event severity levels"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ObservabilityEvent:
    """Lightweight event structure for observability"""
    event_id: str
    event_type: EventType
    timestamp: float
    trace_id: str
    span_id: str
    parent_span_id: Optional[str]
    component: str  # 'langgraph', 'celery', 'django', 'websocket'
    operation: str  # specific operation name
    duration_ms: Optional[float] = None
    severity: Severity = Severity.INFO
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: Dict[str, str] = field(default_factory=dict)
    metrics: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'timestamp': self.timestamp,
            'trace_id': self.trace_id,
            'span_id': self.span_id,
            'parent_span_id': self.parent_span_id,
            'component': self.component,
            'operation': self.operation,
            'duration_ms': self.duration_ms,
            'severity': self.severity.value,
            'metadata': self.metadata,
            'tags': self.tags,
            'metrics': self.metrics
        }


class TraceContext:
    """Thread-local trace context for correlation"""
    _local = threading.local()
    
    @classmethod
    def get_trace_id(cls) -> Optional[str]:
        return getattr(cls._local, 'trace_id', None)
    
    @classmethod
    def get_span_id(cls) -> Optional[str]:
        return getattr(cls._local, 'span_id', None)
    
    @classmethod
    def set_context(cls, trace_id: str, span_id: str):
        cls._local.trace_id = trace_id
        cls._local.span_id = span_id
    
    @classmethod
    def clear_context(cls):
        cls._local.trace_id = None
        cls._local.span_id = None


class PerformanceTracker:
    """Lightweight performance tracking with smart sampling"""
    
    def __init__(self, max_samples: int = 1000):
        self.max_samples = max_samples
        self.samples = deque(maxlen=max_samples)
        self.operation_stats = defaultdict(lambda: {'count': 0, 'total_time': 0, 'avg_time': 0})
        self._lock = threading.Lock()
    
    def record_operation(self, operation: str, duration_ms: float):
        """Record operation performance with minimal overhead"""
        with self._lock:
            stats = self.operation_stats[operation]
            stats['count'] += 1
            stats['total_time'] += duration_ms
            stats['avg_time'] = stats['total_time'] / stats['count']
            
            # Smart sampling: keep recent samples and outliers
            if len(self.samples) < self.max_samples or duration_ms > stats['avg_time'] * 2:
                self.samples.append({
                    'operation': operation,
                    'duration_ms': duration_ms,
                    'timestamp': time.time()
                })
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary with minimal computation"""
        with self._lock:
            return {
                'operation_stats': dict(self.operation_stats),
                'recent_samples': list(self.samples)[-50:],  # Last 50 samples
                'total_operations': sum(stats['count'] for stats in self.operation_stats.values())
            }


class ObservabilityService:
    """
    High-performance observability service for our LangGraph + Celery + Django stack.
    
    Design principles:
    1. Minimal overhead - async processing, smart sampling
    2. Context propagation - trace requests across components
    3. Real-time streaming - WebSocket updates for live monitoring
    4. Smart alerting - detect anomalies and performance issues
    5. Cost tracking - monitor LLM usage and costs
    """
    
    _instance: Optional['ObservabilityService'] = None
    _lock = threading.Lock()
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
        self.performance_tracker = PerformanceTracker()
        self.active_traces: Dict[str, Dict[str, Any]] = {}
        self.event_buffer: deque = deque(maxlen=10000)  # Circular buffer for events
        self.subscribers: Set[Callable] = set()
        self.sampling_rate = 1.0  # Start with 100% sampling
        self.cost_tracker = defaultdict(float)
        self.anomaly_thresholds = {
            'slow_operation_multiplier': 3.0,
            'high_cost_threshold': 1.0,  # $1.00
            'error_rate_threshold': 0.05  # 5%
        }
        
        # Background processing
        self._processing_queue = None  # Will be initialized when needed
        self._processing_task = None
        self._shutdown = False
        self._sync_mode = False  # For testing and simple scenarios
    
    @classmethod
    def get_instance(cls) -> 'ObservabilityService':
        """Thread-safe singleton"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    async def start_background_processing(self):
        """Start background event processing"""
        if self._processing_task is None:
            # Initialize queue if not already done
            if self._processing_queue is None:
                self._processing_queue = asyncio.Queue()
            self._processing_task = asyncio.create_task(self._process_events())

    def enable_sync_mode(self):
        """Enable synchronous mode for testing and simple scenarios"""
        self._sync_mode = True
    
    async def _process_events(self):
        """Background event processing with batching"""
        batch = []
        batch_size = 50
        batch_timeout = 1.0  # 1 second
        last_flush = time.time()
        
        while not self._shutdown:
            try:
                # Wait for event or timeout
                try:
                    event = await asyncio.wait_for(
                        self._processing_queue.get(), 
                        timeout=batch_timeout
                    )
                    batch.append(event)
                except asyncio.TimeoutError:
                    pass
                
                # Flush batch if full or timeout reached
                current_time = time.time()
                if (len(batch) >= batch_size or 
                    (batch and current_time - last_flush >= batch_timeout)):
                    
                    if batch:
                        await self._flush_batch(batch)
                        batch.clear()
                        last_flush = current_time
                
            except Exception as e:
                logger.error(f"Error in observability event processing: {e}")
                await asyncio.sleep(0.1)
    
    async def _flush_batch(self, events: List[ObservabilityEvent]):
        """Flush batch of events to subscribers and storage"""
        try:
            # Add to buffer
            self.event_buffer.extend(events)
            
            # Notify subscribers
            for subscriber in self.subscribers.copy():
                try:
                    if asyncio.iscoroutinefunction(subscriber):
                        await subscriber(events)
                    else:
                        subscriber(events)
                except Exception as e:
                    logger.warning(f"Error in observability subscriber: {e}")
                    self.subscribers.discard(subscriber)
            
            # Send to WebSocket if available
            if self.channel_layer:
                await self._send_to_websocket(events)
                
        except Exception as e:
            logger.error(f"Error flushing observability batch: {e}")
    
    async def _send_to_websocket(self, events: List[ObservabilityEvent]):
        """Send events to WebSocket subscribers"""
        try:
            # Send to monitoring dashboard
            await self.channel_layer.group_send(
                "observability_monitoring",
                {
                    'type': 'observability_events',
                    'events': [event.to_dict() for event in events]
                }
            )
        except Exception as e:
            logger.warning(f"Error sending observability events to WebSocket: {e}")
    
    def emit_event(self, 
                   event_type: EventType,
                   component: str,
                   operation: str,
                   duration_ms: Optional[float] = None,
                   severity: Severity = Severity.INFO,
                   metadata: Optional[Dict[str, Any]] = None,
                   tags: Optional[Dict[str, str]] = None,
                   metrics: Optional[Dict[str, float]] = None) -> str:
        """
        Emit observability event with minimal overhead.
        Returns event_id for correlation.
        """
        # Smart sampling to reduce overhead
        if self.sampling_rate < 1.0 and time.time() % 1 > self.sampling_rate:
            return ""
        
        # Get or create trace context
        trace_id = TraceContext.get_trace_id() or str(uuid.uuid4())
        span_id = str(uuid.uuid4())
        parent_span_id = TraceContext.get_span_id()
        
        event = ObservabilityEvent(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            timestamp=time.time(),
            trace_id=trace_id,
            span_id=span_id,
            parent_span_id=parent_span_id,
            component=component,
            operation=operation,
            duration_ms=duration_ms,
            severity=severity,
            metadata=metadata or {},
            tags=tags or {},
            metrics=metrics or {}
        )
        
        # Record performance
        if duration_ms is not None:
            self.performance_tracker.record_operation(operation, duration_ms)

        # Handle event processing based on mode
        if self._sync_mode:
            # Synchronous mode: directly add to buffer
            self.event_buffer.append(event)
        else:
            # Async mode: queue for background processing
            try:
                if self._processing_queue:
                    self._processing_queue.put_nowait(event)
                else:
                    # Fallback to sync mode if queue not initialized
                    self.event_buffer.append(event)
            except asyncio.QueueFull:
                # Drop event if queue is full (backpressure)
                logger.warning("Observability queue full, dropping event")
            except RuntimeError:
                # No event loop running, fallback to sync mode
                self.event_buffer.append(event)

        return event.event_id
    
    @contextmanager
    def trace_operation(self, 
                       component: str, 
                       operation: str,
                       metadata: Optional[Dict[str, Any]] = None,
                       tags: Optional[Dict[str, str]] = None):
        """Context manager for tracing operations"""
        start_time = time.time()
        trace_id = TraceContext.get_trace_id() or str(uuid.uuid4())
        span_id = str(uuid.uuid4())
        
        # Set context
        old_trace_id = TraceContext.get_trace_id()
        old_span_id = TraceContext.get_span_id()
        TraceContext.set_context(trace_id, span_id)
        
        # Emit start event
        start_event_id = self.emit_event(
            EventType.NODE_START if component == 'langgraph' else EventType.TASK_START,
            component,
            operation,
            metadata=metadata,
            tags=tags
        )
        
        try:
            yield {
                'trace_id': trace_id,
                'span_id': span_id,
                'start_event_id': start_event_id
            }
        except Exception as e:
            # Emit error event
            self.emit_event(
                EventType.ERROR,
                component,
                operation,
                duration_ms=(time.time() - start_time) * 1000,
                severity=Severity.ERROR,
                metadata={
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    **(metadata or {})
                },
                tags=tags
            )
            raise
        finally:
            # Emit end event
            duration_ms = (time.time() - start_time) * 1000
            self.emit_event(
                EventType.NODE_END if component == 'langgraph' else EventType.TASK_END,
                component,
                operation,
                duration_ms=duration_ms,
                metadata=metadata,
                tags=tags
            )
            
            # Restore context
            if old_trace_id and old_span_id:
                TraceContext.set_context(old_trace_id, old_span_id)
            else:
                TraceContext.clear_context()
    
    def track_llm_call(self, 
                      model: str, 
                      tokens_used: int, 
                      cost: float,
                      duration_ms: float,
                      metadata: Optional[Dict[str, Any]] = None):
        """Track LLM call with cost and performance metrics"""
        self.cost_tracker[model] += cost
        
        self.emit_event(
            EventType.LLM_CALL,
            'llm',
            f'call_{model}',
            duration_ms=duration_ms,
            metadata={
                'model': model,
                'tokens_used': tokens_used,
                'cost': cost,
                'tokens_per_second': tokens_used / (duration_ms / 1000) if duration_ms > 0 else 0,
                **(metadata or {})
            },
            metrics={
                'tokens_used': float(tokens_used),
                'cost': cost,
                'duration_ms': duration_ms
            }
        )
        
        # Check for cost alerts
        if cost > self.anomaly_thresholds['high_cost_threshold']:
            self.emit_event(
                EventType.COST_ALERT,
                'llm',
                f'high_cost_{model}',
                severity=Severity.WARNING,
                metadata={
                    'model': model,
                    'cost': cost,
                    'threshold': self.anomaly_thresholds['high_cost_threshold']
                }
            )
    
    def get_performance_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive performance data for dashboard"""
        return {
            'performance_summary': self.performance_tracker.get_performance_summary(),
            'active_traces': len(self.active_traces),
            'total_events': len(self.event_buffer),
            'cost_summary': dict(self.cost_tracker),
            'sampling_rate': self.sampling_rate,
            'recent_events': [event.to_dict() for event in list(self.event_buffer)[-100:]]
        }
    
    def subscribe(self, callback: Callable):
        """Subscribe to observability events"""
        self.subscribers.add(callback)
    
    def unsubscribe(self, callback: Callable):
        """Unsubscribe from observability events"""
        self.subscribers.discard(callback)
    
    async def shutdown(self):
        """Graceful shutdown"""
        self._shutdown = True
        if self._processing_task:
            await self._processing_task


# Global instance
observability = ObservabilityService.get_instance()


# Decorators for easy instrumentation
def observe_langgraph_node(node_name: str):
    """Decorator for LangGraph node instrumentation"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            with observability.trace_operation('langgraph', f'node_{node_name}'):
                return await func(*args, **kwargs)
        return wrapper
    return decorator


def observe_celery_task(task_name: str):
    """Decorator for Celery task instrumentation"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with observability.trace_operation('celery', f'task_{task_name}'):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def observe_django_view(view_name: str):
    """Decorator for Django view instrumentation"""
    def decorator(func):
        def wrapper(request, *args, **kwargs):
            with observability.trace_operation('django', f'view_{view_name}'):
                return func(request, *args, **kwargs)
        return wrapper
    return decorator


# LangGraph Integration Helpers
class LangGraphObserver:
    """LangGraph-specific observability integration"""

    @staticmethod
    def instrument_workflow(workflow_name: str):
        """Instrument entire LangGraph workflow"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                with observability.trace_operation('langgraph', f'workflow_{workflow_name}'):
                    return await func(*args, **kwargs)
            return wrapper
        return decorator

    @staticmethod
    def track_node_execution(node_name: str, node_type: str = 'agent'):
        """Track individual node execution with detailed metrics"""
        def decorator(func):
            async def wrapper(state, config=None, **kwargs):
                start_time = time.time()

                # Extract useful metadata from state
                metadata = {
                    'node_type': node_type,
                    'state_keys': list(state.keys()) if isinstance(state, dict) else [],
                    'config_keys': list(config.keys()) if config and isinstance(config, dict) else []
                }

                with observability.trace_operation(
                    'langgraph',
                    f'node_{node_name}',
                    metadata=metadata,
                    tags={'node_type': node_type, 'workflow': 'wheel_generation'}
                ):
                    result = await func(state, config, **kwargs)

                    # Track output metrics
                    duration_ms = (time.time() - start_time) * 1000
                    observability.emit_event(
                        EventType.NODE_END,
                        'langgraph',
                        f'node_{node_name}',
                        duration_ms=duration_ms,
                        metadata={
                            **metadata,
                            'output_keys': list(result.keys()) if isinstance(result, dict) else [],
                            'success': True
                        }
                    )

                    return result
            return wrapper
        return decorator


# Celery Integration
class CeleryObserver:
    """Celery-specific observability integration"""

    @staticmethod
    def instrument_task(task_name: str):
        """Instrument Celery task with full lifecycle tracking"""
        def decorator(func):
            def wrapper(self, *args, **kwargs):
                # Extract task metadata
                metadata = {
                    'task_id': self.request.id if hasattr(self, 'request') else 'unknown',
                    'task_name': task_name,
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys())
                }

                with observability.trace_operation(
                    'celery',
                    f'task_{task_name}',
                    metadata=metadata,
                    tags={'task_type': 'workflow', 'priority': 'high'}
                ):
                    return func(self, *args, **kwargs)
            return wrapper
        return decorator
