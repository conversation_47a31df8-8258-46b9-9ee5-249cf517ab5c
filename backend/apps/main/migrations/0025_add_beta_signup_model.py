# Generated by Django 5.2.1 on 2025-06-21 00:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0024_fix_wheelitem_activitytailored_relationship'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BetaSignup',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('email', models.EmailField(help_text='Email address of the interested user', max_length=255)),
                ('message', models.TextField(blank=True, help_text='Optional message from the user about their interest', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this signup request was submitted')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address of the user who submitted the request', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='User agent string from the browser', null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('contacted', 'Contacted'), ('invited', 'Invited to Beta'), ('registered', 'Registered as User'), ('declined', 'Declined')], default='pending', help_text='Current status of this beta signup request', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Internal notes about this signup request', null=True)),
                ('processed_at', models.DateTimeField(blank=True, help_text='When this request was processed by staff', null=True)),
                ('processed_by', models.ForeignKey(blank=True, help_text='Staff member who processed this request', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_beta_signups', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Beta Signup',
                'verbose_name_plural': 'Beta Signups',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['email'], name='main_betasi_email_38d350_idx'), models.Index(fields=['status'], name='main_betasi_status_05112d_idx'), models.Index(fields=['created_at'], name='main_betasi_created_359e14_idx')],
                'constraints': [models.UniqueConstraint(fields=('email',), name='unique_beta_signup_email')],
            },
        ),
    ]
