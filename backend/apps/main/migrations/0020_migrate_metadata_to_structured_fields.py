# Generated manually for migrating metadata to structured fields

from django.db import migrations


def migrate_metadata_to_structured_fields(apps, schema_editor):
    """
    Migrate data from metadata JSON field to new structured fields.
    """
    BenchmarkScenario = apps.get_model('main', 'BenchmarkScenario')
    
    for scenario in BenchmarkScenario.objects.all():
        if not scenario.metadata:
            continue
            
        updated = False
        
        # Migrate workflow_type
        if 'workflow_type' in scenario.metadata and not scenario.workflow_type:
            scenario.workflow_type = scenario.metadata['workflow_type']
            updated = True
            
        # Migrate warmup_runs
        if 'warmup_runs' in scenario.metadata:
            try:
                scenario.warmup_runs = int(scenario.metadata['warmup_runs'])
                updated = True
            except (ValueError, TypeError):
                pass
                
        # Migrate benchmark_runs
        if 'benchmark_runs' in scenario.metadata:
            try:
                scenario.benchmark_runs = int(scenario.metadata['benchmark_runs'])
                updated = True
            except (ValueError, TypeError):
                pass
                
        # Migrate timeout_seconds
        if 'timeout_seconds' in scenario.metadata:
            try:
                scenario.timeout_seconds = int(scenario.metadata['timeout_seconds'])
                updated = True
            except (ValueError, TypeError):
                pass
                
        # Migrate evaluation_template_id
        if 'evaluation_template_id' in scenario.metadata:
            try:
                scenario.evaluation_template_id = int(scenario.metadata['evaluation_template_id'])
                updated = True
            except (ValueError, TypeError):
                pass
                
        # Migrate evaluation_template_name
        if 'evaluation_template_name' in scenario.metadata and not scenario.evaluation_template_name:
            scenario.evaluation_template_name = scenario.metadata['evaluation_template_name']
            updated = True
            
        # Migrate expected_quality_criteria
        if 'expected_quality_criteria' in scenario.metadata:
            scenario.expected_quality_criteria = scenario.metadata['expected_quality_criteria']
            updated = True
            
        # Migrate mock_tool_responses
        if 'mock_tool_responses' in scenario.metadata:
            scenario.mock_tool_responses = scenario.metadata['mock_tool_responses']
            updated = True
            
        # Migrate user_profile_context
        if 'user_profile_context' in scenario.metadata:
            scenario.user_profile_context = scenario.metadata['user_profile_context']
            updated = True
            
        # Migrate activity_context
        if 'activity_context' in scenario.metadata:
            scenario.activity_context = scenario.metadata['activity_context']
            updated = True
            
        if updated:
            scenario.save()


def reverse_migrate_structured_fields_to_metadata(apps, schema_editor):
    """
    Reverse migration: move data back from structured fields to metadata.
    """
    BenchmarkScenario = apps.get_model('main', 'BenchmarkScenario')
    
    for scenario in BenchmarkScenario.objects.all():
        if not scenario.metadata:
            scenario.metadata = {}
            
        updated = False
        
        # Move data back to metadata
        if scenario.workflow_type:
            scenario.metadata['workflow_type'] = scenario.workflow_type
            updated = True
            
        if scenario.warmup_runs != 1:  # Default value
            scenario.metadata['warmup_runs'] = scenario.warmup_runs
            updated = True
            
        if scenario.benchmark_runs != 3:  # Default value
            scenario.metadata['benchmark_runs'] = scenario.benchmark_runs
            updated = True
            
        if scenario.timeout_seconds:
            scenario.metadata['timeout_seconds'] = scenario.timeout_seconds
            updated = True
            
        if scenario.evaluation_template_id:
            scenario.metadata['evaluation_template_id'] = scenario.evaluation_template_id
            updated = True
            
        if scenario.evaluation_template_name:
            scenario.metadata['evaluation_template_name'] = scenario.evaluation_template_name
            updated = True
            
        if scenario.expected_quality_criteria:
            scenario.metadata['expected_quality_criteria'] = scenario.expected_quality_criteria
            updated = True
            
        if scenario.mock_tool_responses:
            scenario.metadata['mock_tool_responses'] = scenario.mock_tool_responses
            updated = True
            
        if scenario.user_profile_context:
            scenario.metadata['user_profile_context'] = scenario.user_profile_context
            updated = True
            
        if scenario.activity_context:
            scenario.metadata['activity_context'] = scenario.activity_context
            updated = True
            
        if updated:
            scenario.save()


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0019_add_scenario_structured_fields'),
    ]

    operations = [
        migrations.RunPython(
            migrate_metadata_to_structured_fields,
            reverse_migrate_structured_fields_to_metadata
        ),
    ]
