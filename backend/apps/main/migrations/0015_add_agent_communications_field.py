# Generated by Django 5.2.1 on 2025-06-01 09:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0014_fix_mood_valence_extraction'),
    ]

    operations = [
        migrations.AddField(
            model_name='benchmarkrun',
            name='agent_communications',
            field=models.JSONField(default=dict, help_text="Detailed capture of agent-to-agent communications during workflow execution. Structure: {'workflow_id': str, 'agents': [{'agent': str, 'stage': str, 'input': dict, 'output': dict, 'timestamp': str, 'duration_ms': float}], 'state_transitions': [{'from_state': dict, 'to_state': dict, 'agent': str, 'timestamp': str}]}"),
        ),
    ]
