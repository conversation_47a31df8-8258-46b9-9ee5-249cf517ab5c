# Generated by Django 5.2.1 on 2025-06-08 15:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0018_alter_agentmemory_agent_role_alter_genericagent_role'),
    ]

    operations = [
        migrations.AddField(
            model_name='benchmarkscenario',
            name='activity_context',
            field=models.JSONField(blank=True, default=dict, help_text='Activity-specific context for the scenario'),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='benchmark_runs',
            field=models.PositiveIntegerField(default=3, help_text='Number of benchmark runs to perform'),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='evaluation_template_id',
            field=models.PositiveIntegerField(blank=True, help_text='ID of the evaluation criteria template to use', null=True),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='evaluation_template_name',
            field=models.CharField(blank=True, help_text='Name of the evaluation criteria template to use', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='expected_quality_criteria',
            field=models.JSONField(blank=True, default=dict, help_text='Expected quality criteria for evaluation'),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='mock_tool_responses',
            field=models.JSONField(blank=True, default=dict, help_text='Mock responses for tool calls during testing'),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='timeout_seconds',
            field=models.PositiveIntegerField(blank=True, help_text='Timeout in seconds for scenario execution', null=True),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='user_profile_context',
            field=models.JSONField(blank=True, default=dict, help_text='User profile context for the scenario'),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='warmup_runs',
            field=models.PositiveIntegerField(default=1, help_text='Number of warmup runs before actual benchmarking'),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='workflow_type',
            field=models.CharField(blank=True, help_text="Type of workflow this scenario tests (e.g., 'wheel_generation', 'discussion')", max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='benchmarkscenario',
            name='metadata',
            field=models.JSONField(default=dict, help_text='Additional metadata and legacy fields'),
        ),
    ]
