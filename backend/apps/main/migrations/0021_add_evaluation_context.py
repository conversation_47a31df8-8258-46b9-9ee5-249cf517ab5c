# Generated by Django 5.2.1 on 2025-06-15 12:35

import django.core.validators
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0020_migrate_metadata_to_structured_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='EvaluationContext',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text="e.g., 'Stressed_Evening_WheelGen_Mentor'", max_length=100)),
                ('description', models.TextField(help_text='Description of this evaluation context scenario')),
                ('current_workflow_type', models.CharField(choices=[('wheel_generation', 'Wheel Generation'), ('activity_feedback', 'Activity Feedback'), ('discussion', 'Discussion'), ('onboarding', 'Onboarding'), ('post_spin', 'Post Spin'), ('pre_spin_feedback', 'Pre Spin Feedback')], help_text='Current workflow being executed', max_length=50)),
                ('workflow_stage', models.CharField(choices=[('initiation', 'Workflow Initiation'), ('agent_processing', 'Multi-Agent Processing'), ('result_delivery', 'Result Delivery'), ('user_response', 'Awaiting User Response'), ('error_recovery', 'Error Recovery')], default='agent_processing', help_text='Current stage in workflow', max_length=50)),
                ('agent_role_being_evaluated', models.CharField(choices=[('mentor', 'Mentor Agent'), ('orchestrator', 'Orchestrator Agent'), ('resource', 'Resource Agent'), ('engagement', 'Engagement Agent'), ('psychological', 'Psychological Agent'), ('strategy', 'Strategy Agent'), ('wheel_activity', 'Wheel/Activity Agent'), ('ethical', 'Ethical Agent')], help_text='Which agent is being evaluated', max_length=50)),
                ('trust_level_override', models.IntegerField(blank=True, help_text='Override trust level for evaluation (null = use UserProfile value)', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('mood_valence_override', models.FloatField(blank=True, help_text='Override mood valence for evaluation (null = use UserProfile value)', null=True, validators=[django.core.validators.MinValueValidator(-1.0), django.core.validators.MaxValueValidator(1.0)])),
                ('mood_arousal_override', models.FloatField(blank=True, help_text='Override mood arousal for evaluation (null = use UserProfile value)', null=True, validators=[django.core.validators.MinValueValidator(-1.0), django.core.validators.MaxValueValidator(1.0)])),
                ('reported_environment_override', models.CharField(blank=True, choices=[('home', 'Home'), ('work', 'Work'), ('public', 'Public Space'), ('transit', 'In Transit')], help_text='Override environment for evaluation', max_length=50, null=True)),
                ('stress_level_override', models.IntegerField(blank=True, help_text='Override stress level for evaluation', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('time_pressure_override', models.IntegerField(blank=True, help_text='Override time pressure for evaluation', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('reported_time_availability_override', models.CharField(blank=True, choices=[('5_minutes', '5 minutes'), ('15_minutes', '15 minutes'), ('30_minutes', '30 minutes'), ('60_minutes', '60+ minutes')], help_text='Override time availability for evaluation', max_length=50, null=True)),
                ('reported_focus_override', models.CharField(blank=True, help_text="Override focus area for evaluation (e.g., 'creative', 'physical')", max_length=100, null=True)),
                ('previous_agent_outputs', models.JSONField(default=list, help_text='Simulated outputs from previous agents in workflow')),
                ('expected_next_agents', models.JSONField(default=list, help_text='Which agents should be called after this one')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Evaluation Context',
                'verbose_name_plural': 'Evaluation Contexts',
            },
        ),
    ]
