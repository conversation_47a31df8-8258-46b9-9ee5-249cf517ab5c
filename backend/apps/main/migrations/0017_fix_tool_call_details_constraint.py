# Generated migration to fix tool_call_details constraint

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0016_merge_20250601_1708'),
    ]

    operations = [
        # First, update any existing NULL values to empty dict
        migrations.RunSQL(
            sql="UPDATE main_benchmarkrun SET tool_call_details = '{}' WHERE tool_call_details IS NULL;",
            reverse_sql="-- No reverse operation needed"
        ),
        # Then alter the column to have a proper default and allow NULL temporarily
        migrations.RunSQL(
            sql="ALTER TABLE main_benchmarkrun ALTER COLUMN tool_call_details SET DEFAULT '{}';",
            reverse_sql="ALTER TABLE main_benchmarkrun ALTER COLUMN tool_call_details DROP DEFAULT;"
        ),
        # Finally, make it NOT NULL with the default
        migrations.RunSQL(
            sql="ALTER TABLE main_benchmarkrun ALTER COLUMN tool_call_details SET NOT NULL;",
            reverse_sql="ALTER TABLE main_benchmarkrun ALTER COLUMN tool_call_details DROP NOT NULL;"
        ),
    ]
