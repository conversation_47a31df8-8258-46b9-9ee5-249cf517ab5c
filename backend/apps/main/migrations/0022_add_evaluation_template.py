# Generated by Django 5.2.1 on 2025-06-15 12:52

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0021_add_evaluation_context'),
    ]

    operations = [
        migrations.CreateModel(
            name='EvaluationTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Unique template name', max_length=100, unique=True)),
                ('display_name', models.<PERSON><PERSON><PERSON><PERSON>(help_text='Human-readable display name', max_length=150)),
                ('description', models.TextField(help_text='Description of what this template evaluates')),
                ('category', models.CharField(choices=[('agent_specific', 'Agent-Specific Evaluation'), ('workflow_quality', 'Workflow Quality Assessment'), ('general_purpose', 'General Purpose Evaluation'), ('custom', 'Custom Evaluation')], default='agent_specific', help_text='Category of evaluation template', max_length=50)),
                ('applicable_workflows', models.<PERSON><PERSON><PERSON><PERSON>(default=list, help_text='List of workflow types this template applies to (empty = all workflows)')),
                ('applicable_agent_roles', models.J<PERSON><PERSON>ield(default=list, help_text='List of agent roles this template applies to (empty = all agents)')),
                ('evaluation_prompt', models.TextField(help_text='The evaluation prompt that will be sent to the LLM evaluator')),
                ('criteria_mapping', models.JSONField(help_text='Dictionary mapping criteria names to their weights (must sum to 1.0)')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.CharField(default='system', help_text='Who created this template', max_length=100)),
            ],
            options={
                'verbose_name': 'Evaluation Template',
                'verbose_name_plural': 'Evaluation Templates',
                'ordering': ['category', 'display_name'],
            },
        ),
    ]
