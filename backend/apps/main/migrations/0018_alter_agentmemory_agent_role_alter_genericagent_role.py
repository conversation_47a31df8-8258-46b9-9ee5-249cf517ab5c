# Generated by Django 5.2.1 on 2025-06-03 21:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0017_fix_tool_call_details_constraint'),
    ]

    operations = [
        migrations.AlterField(
            model_name='agentmemory',
            name='agent_role',
            field=models.Char<PERSON><PERSON>(choices=[('mentor', 'Mentor Agent'), ('orchestrator', 'Orchestrator Agent'), ('resource', 'Resource & Capacity Management Agent'), ('engagement', 'Engagement & Pattern Analytics Agent'), ('psychological', 'Psychological Monitoring Agent'), ('strategy', 'Strategy Agent'), ('activity', 'Wheel/Activity Agent'), ('ethical', 'Ethical Oversight Agent'), ('dispatcher', 'Dispatcher Agent'), ('error_handler', 'Error Handler Agent')], help_text='The agent role this memory belongs to', max_length=20),
        ),
        migrations.AlterField(
            model_name='genericagent',
            name='role',
            field=models.CharField(choices=[('mentor', 'Mentor Agent'), ('orchestrator', 'Orchestrator Agent'), ('resource', 'Resource & Capacity Management Agent'), ('engagement', 'Engagement & Pattern Analytics Agent'), ('psychological', 'Psychological Monitoring Agent'), ('strategy', 'Strategy Agent'), ('activity', 'Wheel/Activity Agent'), ('ethical', 'Ethical Oversight Agent'), ('dispatcher', 'Dispatcher Agent'), ('error_handler', 'Error Handler Agent')], help_text='The specific role this agent fulfills in the system', max_length=20, unique=True),
        ),
    ]
