# backend/apps/main/tasks/test_tasks.py

import time
import logging
import requests
from celery import shared_task
from typing import Dict, Any

logger = logging.getLogger(__name__)

@shared_task(bind=True, name="test_worker_health")
def test_worker_health(self):
    """
    Simple health check task to verify Celery worker is functioning.
    
    Returns:
        dict: Status information about the worker
    """
    try:
        task_id = self.request.id
        logger.info(f"Health check task started: {task_id}")
        
        # Simulate some work
        time.sleep(2)
        
        result = {
            "status": "healthy",
            "task_id": task_id,
            "worker_name": self.request.hostname,
            "timestamp": time.time(),
            "message": "Celery worker is functioning properly"
        }
        
        logger.info(f"Health check task completed: {task_id}")
        return result
        
    except Exception as e:
        logger.error(f"Health check task failed: {str(e)}")
        raise


@shared_task(bind=True, name="test_api_call")
def test_api_call(self, url: str = "https://httpbin.org/json") -> Dict[str, Any]:
    """
    Test task that makes an external API call.
    
    Args:
        url: URL to make the API call to
        
    Returns:
        dict: API response and task metadata
    """
    try:
        task_id = self.request.id
        logger.info(f"API call test task started: {task_id} - URL: {url}")
        
        # Make the API call
        start_time = time.time()
        response = requests.get(url, timeout=30)
        end_time = time.time()
        
        result = {
            "status": "success",
            "task_id": task_id,
            "worker_name": self.request.hostname,
            "url": url,
            "status_code": response.status_code,
            "response_time_seconds": round(end_time - start_time, 2),
            "response_size_bytes": len(response.content),
            "timestamp": time.time()
        }
        
        # Include response data if it's JSON
        try:
            if response.headers.get('content-type', '').startswith('application/json'):
                result["response_data"] = response.json()
        except:
            result["response_data"] = "Non-JSON response"
        
        logger.info(f"API call test task completed: {task_id} - Status: {response.status_code}")
        return result
        
    except requests.RequestException as e:
        logger.error(f"API call test task failed: {task_id} - Error: {str(e)}")
        return {
            "status": "error",
            "task_id": task_id,
            "worker_name": self.request.hostname,
            "url": url,
            "error": str(e),
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"API call test task failed with unexpected error: {task_id} - Error: {str(e)}")
        raise


@shared_task(bind=True, name="test_ml_simulation")
def test_ml_simulation(self, complexity: str = "simple") -> Dict[str, Any]:
    """
    Simulate ML inference workload to test worker performance.
    
    Args:
        complexity: "simple", "medium", or "complex" to simulate different workloads
        
    Returns:
        dict: Simulation results and performance metrics
    """
    try:
        task_id = self.request.id
        logger.info(f"ML simulation task started: {task_id} - Complexity: {complexity}")
        
        # Define workload based on complexity
        workload_config = {
            "simple": {"iterations": 1000, "sleep_time": 0.001},
            "medium": {"iterations": 5000, "sleep_time": 0.002},
            "complex": {"iterations": 10000, "sleep_time": 0.005}
        }
        
        config = workload_config.get(complexity, workload_config["simple"])
        
        # Simulate ML workload
        start_time = time.time()
        
        # Simulate computation
        total = 0
        for i in range(config["iterations"]):
            total += i ** 2
            if i % 1000 == 0:
                time.sleep(config["sleep_time"])
        
        end_time = time.time()
        
        result = {
            "status": "success",
            "task_id": task_id,
            "worker_name": self.request.hostname,
            "complexity": complexity,
            "iterations": config["iterations"],
            "computation_result": total,
            "execution_time_seconds": round(end_time - start_time, 3),
            "timestamp": time.time()
        }
        
        logger.info(f"ML simulation task completed: {task_id} - Time: {result['execution_time_seconds']}s")
        return result
        
    except Exception as e:
        logger.error(f"ML simulation task failed: {task_id} - Error: {str(e)}")
        raise


@shared_task(bind=True, name="test_combined_workload")
def test_combined_workload(self, api_url: str = "https://httpbin.org/delay/1") -> Dict[str, Any]:
    """
    Test task that combines API calls and ML simulation to test realistic workload.
    
    Args:
        api_url: URL for API call test
        
    Returns:
        dict: Combined test results
    """
    try:
        task_id = self.request.id
        logger.info(f"Combined workload test started: {task_id}")
        
        start_time = time.time()
        
        # Step 1: Make API call
        api_start = time.time()
        response = requests.get(api_url, timeout=30)
        api_end = time.time()
        
        # Step 2: Simulate ML processing
        ml_start = time.time()
        total = sum(i ** 2 for i in range(2000))
        time.sleep(0.5)  # Simulate processing time
        ml_end = time.time()
        
        end_time = time.time()
        
        result = {
            "status": "success",
            "task_id": task_id,
            "worker_name": self.request.hostname,
            "total_time_seconds": round(end_time - start_time, 3),
            "api_call": {
                "url": api_url,
                "status_code": response.status_code,
                "time_seconds": round(api_end - api_start, 3)
            },
            "ml_simulation": {
                "computation_result": total,
                "time_seconds": round(ml_end - ml_start, 3)
            },
            "timestamp": time.time()
        }
        
        logger.info(f"Combined workload test completed: {task_id} - Total time: {result['total_time_seconds']}s")
        return result
        
    except Exception as e:
        logger.error(f"Combined workload test failed: {task_id} - Error: {str(e)}")
        raise
