"""
WSGI config for config project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/wsgi/
"""

import os
import subprocess
import time
import threading
from django.core.wsgi import get_wsgi_application

# Use production settings by default, can be overridden by environment variable
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.prod")

def start_celery_worker():
    """Start Celery worker in a separate thread"""
    try:
        print("🚀 Starting Celery worker from WSGI...")

        # Give Django time to fully initialize
        time.sleep(5)

        celery_cmd = [
            'celery', '-A', 'config', 'worker',
            '--loglevel=info',
            '--concurrency=2',
            '--detach',
            '--pidfile=/tmp/celery.pid',
            '--logfile=/tmp/celery.log'
        ]

        result = subprocess.run(celery_cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ Celery worker started successfully from WSGI")
        else:
            print(f"❌ Failed to start Celery worker: {result.stderr}")

    except Exception as e:
        print(f"❌ Exception starting Celery worker: {e}")

# Start Celery worker in background thread when WSGI loads
if os.environ.get('DJANGO_SETTINGS_MODULE') == 'config.settings.prod':
    print("📋 Scheduling Celery worker startup...")
    celery_thread = threading.Thread(target=start_celery_worker, daemon=True)
    celery_thread.start()

application = get_wsgi_application()
