#!/usr/bin/env python3
"""
Test script to verify contextualized activity tailoring is working.
"""

import os
import sys
import django
import asyncio
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

async def test_contextualized_activity_tailoring():
    """Test that contextualized instructions are being used in activity tailoring."""
    print("🧪 Testing Contextualized Activity Tailoring")
    print("=" * 60)
    
    try:
        from apps.main.agents.tools.activity_tools import tailor_activity
        
        # Test parameters
        user_profile_id = "2"  # PhiPhi
        generic_activity_id = 35  # An activity that should trigger LLM tailoring
        
        # Context packet with user state
        context_packet = {
            "reported_mood": "excited",
            "reported_energy_level": "high", 
            "reported_environment": "home office",
            "system_metadata": {
                "workflow_origin": "frontend"  # This should trigger LLM tailoring
            }
        }
        
        # Resource context
        resource_context = {
            "time": {
                "reported_duration_minutes": 30
            },
            "environment": {
                "environment_description": "comfortable home office",
                "privacy_level": "private",
                "space_size": "medium"
            }
        }
        
        print(f"📋 Testing activity tailoring for user {user_profile_id}")
        print(f"🎯 Activity ID: {generic_activity_id}")
        print(f"🔧 Context: {context_packet}")
        print(f"🏠 Resources: {resource_context}")
        print("\n" + "=" * 60)
        
        # Call the tailor_activity function
        print("🚀 Calling tailor_activity...")
        result = await tailor_activity(
            user_profile_id=user_profile_id,
            generic_activity_id=generic_activity_id,
            resource_context=resource_context,
            context_packet=context_packet
        )
        
        print("✅ Activity tailoring completed!")
        print(f"\n📊 Result keys: {list(result.keys())}")
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return False
        
        if "tailored_activity" in result:
            activity = result["tailored_activity"]
            print(f"\n🎯 Tailored Activity:")
            print(f"  📝 Title: {activity.get('title', 'N/A')}")
            print(f"  📄 Description: {activity.get('description', 'N/A')[:100]}...")
            print(f"  ⏱️  Duration: {activity.get('duration_minutes', 'N/A')} minutes")
            print(f"  🎚️  Difficulty: {activity.get('difficulty_level', 'N/A')}/5")
            print(f"  🔧 Adaptations: {activity.get('adaptations', [])}")
            
        if "customization_notes" in result:
            print(f"\n📝 Customization Notes: {result['customization_notes']}")
            
        if "confidence" in result:
            print(f"🎯 Confidence: {result['confidence']}")
            
        print("\n" + "=" * 60)
        print("🎉 Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_placeholder_injection_in_tool():
    """Test that placeholder injection is working in the tool."""
    print("\n🔧 Testing Placeholder Injection in Tool")
    print("=" * 60)
    
    try:
        from apps.main.agents.utils.placeholder_injector import placeholder_injector
        
        # Build context for user 2
        context_packet = {
            "reported_mood": "excited",
            "reported_energy_level": "high"
        }
        
        resource_context = {
            "environment": {
                "environment_description": "comfortable home office"
            }
        }
        
        print("🔧 Building context...")
        context = placeholder_injector.build_context(
            user_profile_id=2,
            context_packet=context_packet,
            resource_context=resource_context
        )
        
        print(f"✅ Context built with {len(context)} placeholders")
        print(f"🔍 Sample placeholders:")
        for key, value in list(context.items())[:10]:
            print(f"  {key}: {value}")
        
        # Test getting agent instructions
        from apps.main.models import GenericAgent, AgentRole
        agent = GenericAgent.objects.get(role=AgentRole.ACTIVITY)
        
        print(f"\n📋 Agent instructions length: {len(agent.system_instructions)} chars")
        print(f"🎯 Has placeholders: {'{{' in agent.system_instructions}")
        
        # Test placeholder injection
        contextualized = placeholder_injector.inject_placeholders(
            agent.system_instructions, context
        )
        
        print(f"✅ Contextualized instructions length: {len(contextualized)} chars")
        print(f"📝 First 500 chars:")
        print(contextualized[:500] + "...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Contextualized Activity Tailoring Tests")
    print("=" * 80)
    
    async def run_tests():
        success = True
        
        # Test placeholder injection
        if not await test_placeholder_injection_in_tool():
            success = False
        
        # Test activity tailoring
        if not await test_contextualized_activity_tailoring():
            success = False
        
        print("\n" + "=" * 80)
        if success:
            print("🎉 ALL TESTS PASSED! Contextualized activity tailoring is working.")
        else:
            print("❌ SOME TESTS FAILED! Check the errors above.")
        
        return success
    
    # Run the async tests
    success = asyncio.run(run_tests())
    sys.exit(0 if success else 1)
