# Example production environment variables for DigitalOcean App Platform
# Copy this file to .env.production and fill in the actual values

# Django Configuration
DJANGO_SETTINGS_MODULE=config.settings.prod
DEBUG=False
DJANGO_SECRET_KEY=your-secret-key-here
DJANGO_ALLOWED_HOSTS=your-app-domain.ondigitalocean.app

# App Platform Domain
APP_DOMAIN=your-app-domain.ondigitalocean.app

# Database (automatically provided by App Platform managed database)
DATABASE_URL=postgres://username:password@host:port/database

# Optional: Redis for Channels (if using managed Redis)
# REDIS_URL=redis://host:port/db

# Optional: AI Features
# MISTRAL_API_KEY=your-mistral-api-key

# Optional: Development mode (for local testing only)
# DEVELOPMENT_MODE=False
