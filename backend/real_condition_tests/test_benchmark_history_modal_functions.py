#!/usr/bin/env python3
"""
Benchmark History Modal Functions Test

This test specifically checks if the renderAgentDetails function is available
on the benchmark history page and identifies why it's not working.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_benchmark_history_modal_functions.py
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import Client
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)

class BenchmarkHistoryModalFunctionsTest:
    """Test modal functions availability on benchmark history page."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Benchmark History Modal Functions Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
        self.client = Client()
        
    def log_test_result(self, test_name: str, success: bool, details: dict = None, error: str = None):
        """Log the result of a test."""
        self.results['tests'][test_name] = {
            'success': success,
            'details': details or {},
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results['summary']['total_tests'] += 1
        if success:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: {error}")
            if error:
                self.results['summary']['errors'].append(f"{test_name}: {error}")
    
    def test_benchmark_history_page_modal_functions(self):
        """Test that modal functions are available on benchmark history page."""
        try:
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_history_modal_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark history page
            response = self.client.get('/admin/benchmarks/history/')
            
            details = {
                'status_code': response.status_code
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for modal inclusion
                modal_inclusion_checks = {
                    'agent_evaluation_modal_included': "{% include 'admin_tools/modals/agent_evaluation_modal.html' %}" in content,
                    'wheel_generation_modal_included': "{% include 'admin_tools/modals/wheel_generation_evaluation_modal.html' %}" in content,
                    'agent_execution_modal_included': "{% include 'admin_tools/modals/agent_execution_detail_modal.html' %}" in content
                }
                
                # Check for function definitions
                function_definitions = {
                    'renderAgentDetails_global': 'window.renderAgentDetails = ' in content,
                    'renderLLMInteractions_global': 'window.renderLLMInteractions = ' in content,
                    'setupCopyRunDataButton_global': 'window.setupCopyRunDataButton = ' in content,
                    'renderEnhancedToolCalls_global': 'window.renderEnhancedToolCalls = ' in content,
                    'handleViewDetailsClick_defined': 'function handleViewDetailsClick' in content,
                    'renderSemanticEvaluationDetails_global': 'window.renderSemanticEvaluationDetails = ' in content,
                    'renderContextPackageDetails_global': 'window.renderContextPackageDetails = ' in content
                }
                
                # Check for function calls
                function_calls = {
                    'renderAgentDetails_called': 'await renderAgentDetails(' in content,
                    'renderWorkflowDetails_called': 'await renderWorkflowDetails(' in content,
                    'handleViewDetailsClick_called': 'handleViewDetailsClick(' in content
                }
                
                # Check for modal elements
                modal_elements = {
                    'agent_details_modal': 'id="agent-details-modal"' in content,
                    'agent_modal_body': 'id="agent-modal-body"' in content,
                    'workflow_details_modal': 'id="workflow-details-modal"' in content,
                    'workflow_modal_body': 'id="workflow-modal-body"' in content
                }
                
                # Check for script errors
                script_error_indicators = {
                    'unclosed_script_tags': content.count('<script>') != content.count('</script>'),
                    'syntax_error_indicators': any(indicator in content for indicator in [
                        'SyntaxError', 'ReferenceError', 'TypeError', 'Uncaught'
                    ]),
                    'missing_semicolons': content.count('function ') > content.count('};') + content.count('} '),
                    'unmatched_braces': abs(content.count('{') - content.count('}')) > 5
                }
                
                details.update({
                    'modal_inclusion': modal_inclusion_checks,
                    'function_definitions': function_definitions,
                    'function_calls': function_calls,
                    'modal_elements': modal_elements,
                    'script_errors': script_error_indicators,
                    'all_modals_included': all(modal_inclusion_checks.values()),
                    'all_functions_defined': all(function_definitions.values()),
                    'all_elements_present': all(modal_elements.values()),
                    'no_script_errors': not any(script_error_indicators.values())
                })
                
                success = (details['all_modals_included'] and 
                          details['all_functions_defined'] and 
                          details['all_elements_present'] and 
                          details['no_script_errors'])
                
                if not success:
                    error_parts = []
                    if not details['all_modals_included']:
                        missing_modals = [k for k, v in modal_inclusion_checks.items() if not v]
                        error_parts.append(f"Missing modals: {missing_modals}")
                    if not details['all_functions_defined']:
                        missing_functions = [k for k, v in function_definitions.items() if not v]
                        error_parts.append(f"Missing functions: {missing_functions}")
                    if not details['all_elements_present']:
                        missing_elements = [k for k, v in modal_elements.items() if not v]
                        error_parts.append(f"Missing elements: {missing_elements}")
                    if not details['no_script_errors']:
                        script_issues = [k for k, v in script_error_indicators.items() if v]
                        error_parts.append(f"Script issues: {script_issues}")
                    
                    error = "; ".join(error_parts)
                else:
                    error = None
                
            else:
                success = False
                error = f"Could not access benchmark history page: HTTP {response.status_code}"
            
            self.log_test_result('Benchmark History Page Modal Functions', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Benchmark History Page Modal Functions', False, error=str(e))
            return False
    
    def test_function_scope_and_timing(self):
        """Test function scope and timing issues."""
        try:
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_scope_timing_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark history page
            response = self.client.get('/admin/benchmarks/history/')
            
            details = {
                'status_code': response.status_code
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for timing issues
                timing_checks = {
                    'dom_content_loaded_used': 'DOMContentLoaded' in content,
                    'window_load_used': 'window.onload' in content,
                    'function_defined_before_call': self._check_function_definition_order(content),
                    'async_function_handling': 'async function' in content and 'await' in content
                }
                
                # Check for scope issues
                scope_checks = {
                    'window_object_used': 'window.' in content,
                    'global_function_assignment': 'window.renderAgentDetails' in content,
                    'function_hoisting_issues': self._check_function_hoisting(content),
                    'variable_scope_issues': self._check_variable_scope(content)
                }
                
                # Check for error handling
                error_handling_checks = {
                    'try_catch_blocks': 'try {' in content and 'catch' in content,
                    'error_logging': 'console.error' in content,
                    'function_existence_checks': 'typeof' in content and 'function' in content,
                    'graceful_degradation': 'if (' in content and 'function' in content
                }
                
                details.update({
                    'timing_checks': timing_checks,
                    'scope_checks': scope_checks,
                    'error_handling': error_handling_checks,
                    'timing_ok': all(timing_checks.values()),
                    'scope_ok': all(scope_checks.values()),
                    'error_handling_ok': all(error_handling_checks.values())
                })
                
                success = (details['timing_ok'] and 
                          details['scope_ok'] and 
                          details['error_handling_ok'])
                
                error = None if success else "Function scope or timing issues detected"
                
            else:
                success = False
                error = f"Could not access benchmark history page: HTTP {response.status_code}"
            
            self.log_test_result('Function Scope and Timing', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Function Scope and Timing', False, error=str(e))
            return False
    
    def _check_function_definition_order(self, content):
        """Check if functions are defined before they are called."""
        # Simple check: renderAgentDetails should be defined before it's called
        render_agent_def_pos = content.find('window.renderAgentDetails = ')
        render_agent_call_pos = content.find('await renderAgentDetails(')
        
        if render_agent_def_pos == -1 or render_agent_call_pos == -1:
            return False
        
        return render_agent_def_pos < render_agent_call_pos
    
    def _check_function_hoisting(self, content):
        """Check for function hoisting issues."""
        # Function declarations are hoisted, but function expressions are not
        function_expressions = content.count('= function')
        function_declarations = content.count('function ')
        
        # If there are more expressions than declarations, there might be hoisting issues
        return function_expressions <= function_declarations
    
    def _check_variable_scope(self, content):
        """Check for variable scope issues."""
        # Simple check: look for var vs let/const usage
        var_count = content.count(' var ')
        let_const_count = content.count(' let ') + content.count(' const ')
        
        # Modern code should prefer let/const over var
        return let_const_count >= var_count
    
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'benchmark_history_modal_functions_{timestamp}.json'
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Results saved to: {filepath}")
        return filepath
    
    def run_all_tests(self):
        """Run all tests in sequence."""
        print("🚀 Starting Benchmark History Modal Functions Test")
        print("=" * 60)
        
        # Test 1: Modal Functions Availability
        self.test_benchmark_history_page_modal_functions()
        
        # Test 2: Function Scope and Timing
        self.test_function_scope_and_timing()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.results['summary']['total_tests']}")
        print(f"Passed: {self.results['summary']['passed']}")
        print(f"Failed: {self.results['summary']['failed']}")
        
        if self.results['summary']['errors']:
            print("\n❌ ERRORS:")
            for error in self.results['summary']['errors']:
                print(f"  - {error}")
        
        success_rate = (self.results['summary']['passed'] / self.results['summary']['total_tests']) * 100
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("✅ Benchmark History Modal Functions are working correctly!")
        elif success_rate >= 70:
            print("⚠️ Benchmark History Modal Functions have some issues")
        else:
            print("❌ Benchmark History Modal Functions have significant problems")
        
        return self.save_results()

def main():
    """Main test execution."""
    test = BenchmarkHistoryModalFunctionsTest()
    results_file = test.run_all_tests()
    
    print(f"\n🔗 View detailed results: {results_file}")
    print("\n🎯 Next Steps:")
    print("1. If functions missing: Check modal inclusion and script execution order")
    print("2. If scope issues: Verify global function assignment")
    print("3. Manual testing: Open browser dev tools and check console")

if __name__ == '__main__':
    main()
