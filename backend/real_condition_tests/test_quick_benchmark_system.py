#!/usr/bin/env python3
"""
Quick Benchmark System Real Condition Test
Tests the new lean benchmark implementation under real operational conditions.

This test validates:
1. Benchmark profile creation and management
2. Quick benchmark service functionality  
3. Simple evaluation adapter integration
4. API endpoint functionality
5. Admin interface integration
6. End-to-end benchmark execution

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_quick_benchmark_system.py
"""

import os
import sys
import json
import asyncio
import time
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from django.db import transaction

from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
from apps.main.services.simple_evaluation_adapter import SimpleEvaluationAdapter
from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from apps.user.models import UserProfile
from apps.main.models import GenericAgent, BenchmarkRun

class QuickBenchmarkSystemTester:
    """Comprehensive tester for the quick benchmark system."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Quick Benchmark System Real Condition Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {},
            'recommendations': []
        }
        self.client = Client()
        self.User = get_user_model()
        
    def log(self, message, level='INFO'):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        filename = f"quick_benchmark_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        self.log(f"Results saved to: {filepath}")
        return filepath

    def test_benchmark_profile_factory(self):
        """Test benchmark profile creation and management."""
        test_name = 'benchmark_profile_factory'
        self.log("Testing Benchmark Profile Factory...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Test 1: List available templates
            templates = BenchmarkProfileFactory.list_available_templates()
            test_result['details']['available_templates'] = len(templates)
            self.log(f"Found {len(templates)} profile templates")
            
            # Test 2: Create benchmark profile
            profile = BenchmarkProfileFactory.create_benchmark_profile('anxious_new_user')
            test_result['details']['profile_created'] = {
                'id': str(profile.id),
                'name': profile.profile_name,
                'is_real': profile.is_real,
                'has_traits': profile.trait_inclinations.exists(),
                'has_goals': profile.user_goals.exists(),
                'has_beliefs': profile.beliefs.exists()
            }
            
            # Test 3: Idempotent creation
            profile2 = BenchmarkProfileFactory.get_or_create_benchmark_profile('anxious_new_user')
            test_result['details']['idempotent_creation'] = profile.id == profile2.id
            
            # Test 4: Validate profile structure
            trait_count = profile.trait_inclinations.count()
            goal_count = profile.user_goals.count()
            belief_count = profile.beliefs.count()
            
            test_result['details']['profile_structure'] = {
                'trait_count': trait_count,
                'goal_count': goal_count,
                'belief_count': belief_count
            }
            
            self.log(f"Profile created with {trait_count} traits, {goal_count} goals, {belief_count} beliefs")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"Profile factory test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['tests'][test_name] = test_result
        
    def test_simple_evaluation_adapter(self):
        """Test simple evaluation adapter functionality."""
        test_name = 'simple_evaluation_adapter'
        self.log("Testing Simple Evaluation Adapter...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            adapter = SimpleEvaluationAdapter()
            
            # Test 1: List available templates
            templates = adapter.get_available_templates()
            test_result['details']['evaluation_templates'] = len(templates)
            self.log(f"Found {len(templates)} evaluation templates")
            
            # Test 2: Create custom evaluation
            custom_weights = {
                'quality': 0.5,
                'relevance': 0.3,
                'clarity': 0.2
            }
            custom_template = adapter.create_custom_evaluation(
                "Test evaluation prompt",
                custom_weights
            )
            test_result['details']['custom_template_created'] = custom_template
            
            # Test 3: Validate template structure
            template_data = adapter.EVALUATION_TEMPLATES['mentor_helpfulness']
            test_result['details']['template_structure'] = {
                'has_prompt': 'evaluation_prompt' in template_data,
                'has_criteria': 'criteria_mapping' in template_data,
                'criteria_count': len(template_data.get('criteria_mapping', {}))
            }
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"Evaluation adapter test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['tests'][test_name] = test_result

    def test_quick_benchmark_service(self):
        """Test quick benchmark service functionality."""
        test_name = 'quick_benchmark_service'
        self.log("Testing Quick Benchmark Service...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            service = QuickBenchmarkService()
            
            # Test 1: Get available options
            options = service.get_available_options()
            test_result['details']['available_options'] = {
                'profile_templates': len(options.get('profile_templates', [])),
                'evaluation_templates': len(options.get('evaluation_templates', [])),
                'available_agents': len(options.get('available_agents', []))
            }
            
            # Test 2: Validate agent availability
            agents = options.get('available_agents', [])
            if agents:
                test_result['details']['sample_agent'] = agents[0]
                self.log(f"Found {len(agents)} available agents")
            else:
                test_result['errors'].append("No agents available for benchmarking")
                self.log("WARNING: No agents found", 'WARN')
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"Quick benchmark service test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['tests'][test_name] = test_result

    def test_api_endpoints(self):
        """Test API endpoint functionality."""
        test_name = 'api_endpoints'
        self.log("Testing API Endpoints...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Create admin user for testing
            admin_user, created = self.User.objects.get_or_create(
                username='test_admin',
                defaults={
                    'email': '<EMAIL>',
                    'is_staff': True,
                    'is_superuser': True
                }
            )
            
            # Login as admin
            self.client.force_login(admin_user)
            
            # Test 1: Quick benchmark options endpoint
            response = self.client.get('/admin/benchmarks/api/quick-benchmark/')
            test_result['details']['options_endpoint'] = {
                'status_code': response.status_code,
                'has_data': 'options' in response.json() if response.status_code == 200 else False
            }
            
            if response.status_code == 200:
                data = response.json()
                test_result['details']['options_data'] = {
                    'profile_templates': len(data.get('options', {}).get('profile_templates', [])),
                    'evaluation_templates': len(data.get('options', {}).get('evaluation_templates', [])),
                    'available_agents': len(data.get('options', {}).get('available_agents', []))
                }
                self.log("Options endpoint working correctly")
            else:
                test_result['errors'].append(f"Options endpoint failed: {response.status_code}")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"API endpoint test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['tests'][test_name] = test_result

    def test_admin_interface_integration(self):
        """Test admin interface integration."""
        test_name = 'admin_interface_integration'
        self.log("Testing Admin Interface Integration...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Test benchmark management page
            response = self.client.get('/admin/benchmarks/manage/')
            test_result['details']['management_page'] = {
                'status_code': response.status_code,
                'has_quick_benchmark_tab': 'quick-benchmark' in response.content.decode() if response.status_code == 200 else False
            }
            
            if response.status_code == 200:
                content = response.content.decode()
                test_result['details']['page_content'] = {
                    'has_quick_benchmark_form': 'quick-benchmark-form' in content,
                    'has_api_url': 'QUICK_BENCHMARK_API_URL' in content,
                    'has_profile_templates': 'quick-profile-template' in content,
                    'has_evaluation_templates': 'quick-evaluation-template' in content
                }
                self.log("Admin interface integration successful")
            else:
                test_result['errors'].append(f"Management page failed: {response.status_code}")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"Admin interface test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['tests'][test_name] = test_result

    def run_all_tests(self):
        """Run all tests and generate summary."""
        self.log("Starting Quick Benchmark System Real Condition Test")
        self.log("=" * 60)
        
        # Run all tests
        self.test_benchmark_profile_factory()
        self.test_simple_evaluation_adapter()
        self.test_quick_benchmark_service()
        self.test_api_endpoints()
        self.test_admin_interface_integration()
        
        # Generate summary
        total_tests = len(self.results['tests'])
        passed_tests = sum(1 for test in self.results['tests'].values() if test['status'] == 'PASS')
        failed_tests = total_tests - passed_tests
        
        self.results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': f"{(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
            'total_execution_time': sum(test['execution_time'] for test in self.results['tests'].values())
        }
        
        # Generate recommendations
        if failed_tests > 0:
            self.results['recommendations'].append("Some tests failed - check error details and fix issues before proceeding")
        if self.results['summary']['total_execution_time'] > 30:
            self.results['recommendations'].append("Tests taking longer than expected - consider performance optimization")
        
        # Log summary
        self.log("=" * 60)
        self.log(f"Test Summary: {passed_tests}/{total_tests} tests passed ({self.results['summary']['success_rate']})")
        self.log(f"Total execution time: {self.results['summary']['total_execution_time']:.2f}s")
        
        if failed_tests == 0:
            self.log("✅ All tests passed! Quick benchmark system is ready for use.", 'SUCCESS')
        else:
            self.log(f"❌ {failed_tests} tests failed. Check results for details.", 'ERROR')
        
        return self.save_results()

def main():
    """Main test execution."""
    tester = QuickBenchmarkSystemTester()
    results_file = tester.run_all_tests()
    
    print(f"\n📊 Detailed results saved to: {results_file}")
    print("\n🚀 Next steps:")
    print("1. Review test results for any failures")
    print("2. Test the UI functionality in the admin interface")
    print("3. Run a complete end-to-end benchmark test")
    print("\n💡 UI Test Command:")
    print("   Open: http://localhost:8000/admin/benchmarks/management/")
    print("   Navigate to 'Quick Benchmark' tab and test functionality")

if __name__ == '__main__':
    main()
