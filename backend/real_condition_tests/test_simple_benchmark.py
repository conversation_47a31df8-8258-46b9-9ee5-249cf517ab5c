#!/usr/bin/env python3
"""
Simple Benchmark Test

Test the actual benchmarking system to see tool call tracking.
"""

import os
import sys
import django
import asyncio
import json
from datetime import datetime

# Setup Django
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.agents.benchmarking import AgentBenchmarkImproved
from apps.main.agents.resource_agent import ResourceAgent

async def test_simple_benchmark():
    """Test simple agent benchmark"""
    print("🔍 Testing simple agent benchmark...")

    try:
        # Create benchmark instance with correct constructor
        benchmark = AgentBenchmarkImproved(
            agent_class=ResourceAgent,
            user_profile_id='2'  # PhiPhi
        )

        print("📊 Running benchmark...")
        results = await benchmark.run_benchmark(
            scenario_name='test_resource_agent',
            input_data={'user_input': 'Hello, I need help with my resources'},
            runs=1,
            warmup_runs=0
        )
        
        print(f"✅ Benchmark completed successfully")
        print(f"📈 Results summary:")
        print(f"   - Agent role: {results.agent_role}")
        print(f"   - Scenario: {results.scenario_name}")
        print(f"   - Success rate: {results.success_rate}")
        print(f"   - Mean duration: {results.mean_duration:.3f}s")
        print(f"   - Tool call counts: {results.tool_call_counts}")
        print(f"   - Last output data: {bool(results.last_output_data)}")

        # Check if there are any errors
        if results.errors:
            print(f"   - Errors: {results.errors}")

        # Convert to dict for JSON serialization
        results_dict = {
            'agent_role': results.agent_role,
            'scenario_name': results.scenario_name,
            'success_rate': results.success_rate,
            'mean_duration': results.mean_duration,
            'tool_call_counts': results.tool_call_counts,
            'last_output_data': results.last_output_data,
            'errors': results.errors,
            'stage_timings': dict(results.stage_timings) if results.stage_timings else {}
        }
        
        # Save results
        results_file = '/usr/src/app/real_condition_tests/results/simple_benchmark_test.json'
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w') as f:
            json.dump(results_dict, f, indent=2, default=str)
            
        print(f"📄 Results saved to {results_file}")
        
        return results_dict
        
    except Exception as e:
        print(f"❌ Benchmark test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_simple_benchmark())
