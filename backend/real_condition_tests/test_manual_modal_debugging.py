#!/usr/bin/env python3
"""
Manual Modal Debugging Test

This test provides step-by-step instructions for manually testing the modal
functionality and debugging the renderAgentDetails issue.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_manual_modal_debugging.py
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.main.models import BenchmarkRun

logger = logging.getLogger(__name__)

class ManualModalDebuggingTest:
    """Provide manual testing instructions and automated checks."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Manual Modal Debugging Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
        self.client = Client()
        
    def log_test_result(self, test_name: str, success: bool, details: dict = None, error: str = None):
        """Log the result of a test."""
        self.results['tests'][test_name] = {
            'success': success,
            'details': details or {},
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results['summary']['total_tests'] += 1
        if success:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: {error}")
            if error:
                self.results['summary']['errors'].append(f"{test_name}: {error}")
    
    def test_benchmark_runs_availability(self):
        """Test that there are benchmark runs available for testing."""
        try:
            benchmark_runs = BenchmarkRun.objects.all()[:5]
            
            details = {
                'total_runs': BenchmarkRun.objects.count(),
                'recent_runs': [
                    {
                        'id': run.id,
                        'agent_role': run.agent_definition.role if run.agent_definition else 'Unknown',
                        'scenario': str(run.scenario),
                        'execution_date': run.execution_date.isoformat() if run.execution_date else None
                    }
                    for run in benchmark_runs
                ]
            }
            
            success = len(benchmark_runs) > 0
            error = None if success else "No benchmark runs found for testing"
            
            self.log_test_result('Benchmark Runs Availability', success, details, error)
            return success, details['recent_runs'] if success else []
            
        except Exception as e:
            self.log_test_result('Benchmark Runs Availability', False, error=str(e))
            return False, []
    
    def test_benchmark_history_page_access(self):
        """Test that the benchmark history page is accessible."""
        try:
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_manual_debug_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark history page
            response = self.client.get('/admin/benchmarks/history/')
            
            details = {
                'status_code': response.status_code,
                'content_length': len(response.content),
                'has_view_details_links': 'view-details' in response.content.decode('utf-8'),
                'has_modal_elements': 'agent-details-modal' in response.content.decode('utf-8'),
                'has_debug_functions': 'checkModalFunctions' in response.content.decode('utf-8')
            }
            
            success = response.status_code == 200
            error = None if success else f"HTTP {response.status_code}"
            
            self.log_test_result('Benchmark History Page Access', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Benchmark History Page Access', False, error=str(e))
            return False
    
    def generate_manual_testing_instructions(self, recent_runs):
        """Generate step-by-step manual testing instructions."""
        instructions = [
            "🔧 MANUAL TESTING INSTRUCTIONS",
            "=" * 50,
            "",
            "1. Open your browser and navigate to:",
            "   http://localhost:8000/admin/benchmarks/history/",
            "",
            "2. Open Browser Developer Tools:",
            "   - Chrome/Edge: F12 or Ctrl+Shift+I",
            "   - Firefox: F12 or Ctrl+Shift+I",
            "   - Safari: Cmd+Option+I",
            "",
            "3. Go to the Console tab in Developer Tools",
            "",
            "4. Look for debug messages:",
            "   - Should see: 'Checking modal function availability:'",
            "   - Should see: '✅ renderAgentDetails function is available'",
            "   - If you see: '❌ renderAgentDetails function not available!' - this is the problem",
            "",
            "5. Test the modal functionality:",
        ]
        
        if recent_runs:
            instructions.extend([
                f"   - Find a benchmark run (try ID: {recent_runs[0]['id']})",
                "   - Click the 'View Details' link",
                "   - Check what happens in the console",
                "",
                "6. If the modal opens but shows an error:",
                "   - Look for the error message in the modal",
                "   - Check the console for JavaScript errors",
                "   - Look for 'Function Not Available' message",
                "",
                "7. Common issues to check:",
                "   - JavaScript errors preventing script execution",
                "   - Function not being defined globally",
                "   - Script loading order issues",
                "   - Modal include not working properly",
            ])
        else:
            instructions.extend([
                "   - No benchmark runs available for testing",
                "   - Create a benchmark run first using the quick benchmark feature",
            ])
        
        instructions.extend([
            "",
            "8. Debug commands to run in browser console:",
            "   typeof window.renderAgentDetails",
            "   Object.keys(window).filter(key => key.includes('render'))",
            "   document.getElementById('agent-details-modal')",
            "   document.getElementById('agent-modal-body')",
            "",
            "9. Expected results:",
            "   - typeof window.renderAgentDetails should be 'function'",
            "   - Modal elements should exist",
            "   - No JavaScript errors in console",
            "",
            "10. If problems persist:",
            "    - Check if agent_evaluation_modal.html is being included",
            "    - Look for script tag errors",
            "    - Verify function definition syntax",
            "",
            "📊 DEBUGGING DATA:",
            f"- Total benchmark runs: {len(recent_runs)}",
        ])
        
        if recent_runs:
            instructions.append("- Recent runs for testing:")
            for run in recent_runs[:3]:
                instructions.append(f"  * ID {run['id']}: {run['agent_role']} - {run['scenario']}")
        
        return "\n".join(instructions)
    
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'manual_modal_debugging_{timestamp}.json'
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Results saved to: {filepath}")
        return filepath
    
    def run_all_tests(self):
        """Run all tests and provide manual testing instructions."""
        print("🚀 Starting Manual Modal Debugging Test")
        print("=" * 60)
        
        # Test 1: Check benchmark runs availability
        runs_available, recent_runs = self.test_benchmark_runs_availability()
        
        # Test 2: Check page access
        page_accessible = self.test_benchmark_history_page_access()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 AUTOMATED TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.results['summary']['total_tests']}")
        print(f"Passed: {self.results['summary']['passed']}")
        print(f"Failed: {self.results['summary']['failed']}")
        
        if self.results['summary']['errors']:
            print("\n❌ ERRORS:")
            for error in self.results['summary']['errors']:
                print(f"  - {error}")
        
        # Generate manual testing instructions
        print("\n" + "=" * 60)
        instructions = self.generate_manual_testing_instructions(recent_runs)
        print(instructions)
        
        return self.save_results()

def main():
    """Main test execution."""
    test = ManualModalDebuggingTest()
    results_file = test.run_all_tests()
    
    print(f"\n🔗 View detailed results: {results_file}")
    print("\n🎯 Next Steps:")
    print("1. Follow the manual testing instructions above")
    print("2. Check browser console for JavaScript errors")
    print("3. Verify modal function availability")
    print("4. Report findings for further debugging")

if __name__ == '__main__':
    main()
