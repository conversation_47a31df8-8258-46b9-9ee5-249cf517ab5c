#!/usr/bin/env python3
"""
Test WebSocket conversation state flow to debug the missing Celery task issue.

This test simulates the exact WebSocket flow that happens when a user:
1. Sends "make me a wheel" 
2. Gets a direct response asking for info
3. Sends profile information
4. Should trigger a Celery task (but doesn't)
"""

import asyncio
import json
import websockets
import logging
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.user.models import UserProfile
from apps.main.models import AgentTool
from django.contrib.auth import get_user_model
from asgiref.sync import sync_to_async

User = get_user_model()

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class WebSocketConversationStateTester:
    def __init__(self):
        self.websocket = None
        self.user_id = None
        self.session_id = f"test_conversation_state_{int(asyncio.get_event_loop().time())}"
        self.conversation_state = {}
        
    async def setup_test_user(self):
        """Create a test user for the conversation."""
        try:
            # Create Django user first (sync)
            django_user = await sync_to_async(User.objects.create_user)(
                username=f"test_user_{self.session_id}",
                email=f"test_{self.session_id}@example.com"
            )

            # Create UserProfile (sync)
            user_profile = await sync_to_async(UserProfile.objects.create)(
                user=django_user,
                profile_name=f"Test User {self.session_id}",
                is_real=False  # Mark as test profile
            )
            self.user_id = str(user_profile.id)
            logger.info(f"✅ Created test user: ID={self.user_id}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to create test user: {e}")
            return False
    
    async def connect_websocket(self):
        """Connect to the WebSocket server."""
        try:
            # Connect to WebSocket
            uri = "ws://localhost:8000/ws/user-session/"
            self.websocket = await websockets.connect(uri)
            logger.info(f"✅ Connected to WebSocket: {uri}")
            
            # Send authentication message
            auth_message = {
                "type": "authenticate",
                "content": {
                    "user_profile_id": self.user_id,
                    "session_id": self.session_id
                }
            }
            await self.websocket.send(json.dumps(auth_message))
            logger.info(f"📤 Sent authentication: user_id={self.user_id}")
            
            # Wait for authentication response
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            logger.info(f"📥 Auth response: {response}")
            
            return True
        except Exception as e:
            logger.error(f"❌ Failed to connect to WebSocket: {e}")
            return False
    
    async def send_message(self, message_text, include_conversation_state=True):
        """Send a chat message with optional conversation state."""
        try:
            # Build message
            message = {
                "type": "chat_message",
                "content": {
                    "message": message_text,
                    "user_profile_id": self.user_id,
                    "timestamp": "2025-06-18T20:00:00.000Z",
                    "metadata": {}
                }
            }
            
            # Include conversation state if requested
            if include_conversation_state and self.conversation_state:
                metadata = message["content"]["metadata"]
                metadata["conversation_phase"] = self.conversation_state.get("phase", "initial")
                metadata["awaiting_response_type"] = self.conversation_state.get("awaiting_response_type")
                metadata["last_workflow"] = self.conversation_state.get("last_workflow")
                metadata["session_context"] = self.conversation_state.get("context", {})
                
                logger.info(f"📋 Including conversation state: {self.conversation_state}")
            
            # Send message
            await self.websocket.send(json.dumps(message))
            logger.info(f"📤 Sent message: '{message_text}'")
            
            # Listen for responses
            responses = []
            timeout_count = 0
            max_timeout = 3  # Allow up to 3 timeouts before stopping
            
            while timeout_count < max_timeout:
                try:
                    response = await asyncio.wait_for(self.websocket.recv(), timeout=2.0)
                    response_data = json.loads(response)
                    responses.append(response_data)
                    
                    # Check for conversation state updates
                    if response_data.get("type") == "conversation_state_update":
                        updates = response_data.get("updates", {})
                        self.conversation_state.update(updates)
                        logger.info(f"🔄 Updated conversation state: {self.conversation_state}")
                    
                    logger.info(f"📥 Response: {response_data.get('type', 'unknown')} - {str(response_data)[:100]}...")
                    
                except asyncio.TimeoutError:
                    timeout_count += 1
                    logger.debug(f"⏱️ Timeout {timeout_count}/{max_timeout} waiting for response")
                    
            return responses
            
        except Exception as e:
            logger.error(f"❌ Failed to send message: {e}")
            return []
    
    async def run_test(self):
        """Run the complete conversation state test."""
        logger.info("🚀 Starting WebSocket Conversation State Test")
        logger.info("=" * 60)
        
        # Step 1: Setup
        if not await self.setup_test_user():
            return False
            
        if not await self.connect_websocket():
            return False
        
        # Step 2: Send initial wheel request
        logger.info("\n1️⃣ Testing initial wheel request...")
        responses1 = await self.send_message("make me a wheel", include_conversation_state=False)
        
        # Analyze responses
        has_direct_response = any(r.get("type") == "chat_message" and not r.get("is_user") for r in responses1)
        has_state_update = any(r.get("type") == "conversation_state_update" for r in responses1)
        
        logger.info(f"   📊 Responses: {len(responses1)} total")
        logger.info(f"   💬 Direct response: {'✅' if has_direct_response else '❌'}")
        logger.info(f"   🔄 State update: {'✅' if has_state_update else '❌'}")
        logger.info(f"   📋 Current state: {self.conversation_state}")
        
        # Step 3: Send profile information with conversation state
        logger.info("\n2️⃣ Testing profile information with conversation state...")
        profile_info = "I'm a 25-year-old software developer from Berlin. I love hiking, reading sci-fi books, and learning new programming languages. My main goal is to improve my work-life balance."
        responses2 = await self.send_message(profile_info, include_conversation_state=True)
        
        # Analyze responses
        has_workflow_launch = any("workflow" in str(r).lower() for r in responses2)
        has_celery_activity = len(responses2) > 2  # More responses usually indicate workflow activity
        
        logger.info(f"   📊 Responses: {len(responses2)} total")
        logger.info(f"   🔄 Workflow activity: {'✅' if has_workflow_launch else '❌'}")
        logger.info(f"   ⚙️ Celery activity: {'✅' if has_celery_activity else '❌'}")
        
        # Step 4: Analysis
        logger.info("\n3️⃣ Analysis and Diagnosis")
        logger.info("=" * 40)
        
        if not has_state_update:
            logger.error("❌ ISSUE: No conversation state update received in step 1")
        elif not self.conversation_state.get("awaiting_response_type"):
            logger.error("❌ ISSUE: Conversation state missing awaiting_response_type")
        elif not has_workflow_launch:
            logger.error("❌ ISSUE: No workflow launched in step 2 despite conversation state")
            logger.error("   This suggests the backend is not detecting the conversation state properly")
        else:
            logger.info("✅ Conversation state flow appears to be working correctly")
        
        # Close connection
        if self.websocket:
            await self.websocket.close()
            
        return True

async def main():
    """Main test function."""
    tester = WebSocketConversationStateTester()
    try:
        await tester.run_test()
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
