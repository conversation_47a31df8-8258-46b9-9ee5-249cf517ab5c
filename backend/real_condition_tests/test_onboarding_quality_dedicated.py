#!/usr/bin/env python3
"""
Dedicated Onboarding Quality Test
Tests the onboarding workflow quality with focus on Preference model usage and profile enrichment.
Validates that the workflow properly creates Preference records when users express preferences.
"""

import os
import sys
import django
import asyncio
import time
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Configure Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile, Demographics, Preference, UserGoal, TrustLevel
from django.contrib.auth.models import User
from asgiref.sync import sync_to_async

class OnboardingQualityTest:
    """
    Dedicated test for onboarding workflow quality with focus on:
    - Preference model usage when users express preferences
    - Profile enrichment through conversation
    - Quality of mentor responses
    - Database changes validation
    """

    def __init__(self):
        self.test_id = str(uuid.uuid4())[:8]
        self.start_time = time.time()
        self.user_profile_id = None
        self.session_name = f"onboarding_quality_{self.test_id}"
        self.test_results = {}
        self.database_snapshots = {}

        # Test persona - student with clear preferences
        self.persona = {
            "name": "Emma Test",
            "age": 21,
            "gender": "female", 
            "location": "Berlin, Germany",
            "occupation": "Student",
            "characteristics": ["ADHD", "upcoming_exams", "stress_management"],
            "preferences": [
                "I love creative activities like drawing and writing",
                "I prefer morning activities when I'm most focused",
                "I need short activities because of my ADHD",
                "I enjoy outdoor activities when the weather is nice",
                "I dislike long, structured tasks"
            ]
        }

    async def run_onboarding_quality_test(self):
        """Run the dedicated onboarding quality test."""
        print("🎯 Dedicated Onboarding Quality Test")
        print(f"   Test ID: {self.test_id}")
        print(f"   Session: {self.session_name}")
        print(f"   Persona: {self.persona['name']} - {self.persona['age']}-year-old {self.persona['occupation']}")
        print(f"   Focus: Preference model usage and profile enrichment")
        print()

        try:
            # Phase 1: Setup test environment
            await self._setup_test_environment()

            # Phase 2: Test onboarding with preference expressions
            await self._test_onboarding_with_preferences()

            # Phase 3: Validate preference creation
            await self._validate_preference_creation()

            # Phase 4: Test follow-up conversation
            await self._test_follow_up_conversation()

            # Phase 5: Generate quality assessment
            await self._generate_quality_assessment()

            print("\n✅ Onboarding Quality Test Completed Successfully")
            self._print_summary()

        except Exception as e:
            print(f"\n❌ Onboarding Quality Test Failed: {e}")
            import traceback
            traceback.print_exc()
            await self._handle_test_failure(e)

    async def _setup_test_environment(self):
        """Setup test environment with minimal user profile."""
        print("📋 Phase 1: Setting up test environment")
        phase_start = time.time()

        try:
            # Create test user profile
            user_profile = await self._create_test_user_profile()
            self.user_profile_id = str(user_profile.id)

            # Take initial database snapshot
            await self._take_database_snapshot("initial")

            print(f"   ✓ Test user created: ID {self.user_profile_id}")
            print(f"   ✓ Initial database snapshot taken")

        except Exception as e:
            print(f"   ❌ Setup failed: {e}")
            raise

        duration = time.time() - phase_start
        self.test_results["setup_duration"] = duration
        print(f"   ✓ Setup completed in {duration:.2f}s")
        print()

    async def _create_test_user_profile(self):
        """Create a minimal test user profile."""
        @sync_to_async
        def create_user_and_profile():
            # Create Django user
            username = f"test_onboarding_{self.test_id}"
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': f'{username}@test.com',
                    'first_name': self.persona['name'].split()[0],
                    'last_name': self.persona['name'].split()[1]
                }
            )

            # Create minimal UserProfile
            user_profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'profile_name': self.persona['name'],
                    'is_real': False,  # Mark as test profile
                }
            )

            return user_profile

        return await create_user_and_profile()

    async def _test_onboarding_with_preferences(self):
        """Test onboarding workflow with clear preference expressions."""
        print("🎯 Phase 2: Testing onboarding with preference expressions")
        phase_start = time.time()

        # Take pre-onboarding snapshot
        await self._take_database_snapshot("pre_onboarding")

        # Create conversation dispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=self.user_profile_id,
            user_ws_session_name=self.session_name,
            fail_fast_on_errors=True
        )

        # Check initial profile completion
        initial_completion = await self._check_profile_completion()
        print(f"   Initial profile completion: {initial_completion:.1%}")

        # Onboarding message with clear preferences
        onboarding_message = {
            "text": f"Hi! I'm {self.persona['name']}, a {self.persona['age']}-year-old {self.persona['occupation']} in {self.persona['location']}. I have ADHD and upcoming exams, so I'm quite stressed. I love creative activities like drawing and writing, and I prefer morning activities when I'm most focused. I need short activities because of my ADHD - I dislike long, structured tasks. I also enjoy outdoor activities when the weather is nice. Can you help me?",
            "metadata": {
                "test_context": "onboarding_preferences",
                "persona": self.persona,
                "preference_expressions": len(self.persona["preferences"])
            }
        }

        print(f"   Sending preference-rich onboarding message...")
        print(f"   Message contains {len(self.persona['preferences'])} preference expressions")
        step_start = time.time()

        try:
            response = await dispatcher.process_message(onboarding_message)
            step_duration = time.time() - step_start

            workflow_type = response.get('workflow_type')
            confidence = response.get('confidence', 0.0)

            # Check profile completion after onboarding
            final_completion = await self._check_profile_completion()
            completion_increase = final_completion - initial_completion

            # Take post-onboarding snapshot
            await self._take_database_snapshot("post_onboarding")

            print(f"   ✓ Workflow: {workflow_type} (confidence: {confidence:.2f})")
            print(f"   ✓ Duration: {step_duration:.2f}s")
            print(f"   ✓ Profile completion: {initial_completion:.1%} → {final_completion:.1%} (+{completion_increase:.1%})")

            self.test_results["onboarding"] = {
                'workflow_type': workflow_type,
                'confidence': confidence,
                'duration': step_duration,
                'profile_completion_before': initial_completion,
                'profile_completion_after': final_completion,
                'completion_increase': completion_increase,
                'success': workflow_type == 'onboarding' and completion_increase > 0,
                'response': response
            }

        except Exception as e:
            print(f"   ❌ Onboarding failed: {e}")
            self.test_results["onboarding"] = {
                'error': str(e),
                'duration': time.time() - step_start,
                'success': False
            }
            raise

        total_duration = time.time() - phase_start
        print(f"   ✓ Onboarding phase completed in {total_duration:.2f}s")

        # Wait for profile enrichment to complete
        print(f"   ⏳ Waiting for profile enrichment to complete...")
        await asyncio.sleep(5)
        print()

    async def _validate_preference_creation(self):
        """Validate that Preference records were created from user expressions."""
        print("✅ Phase 3: Validating Preference record creation")
        
        @sync_to_async
        def get_preferences():
            user_profile = UserProfile.objects.get(id=self.user_profile_id)
            return list(user_profile.preferences.all().values(
                'pref_name', 'pref_description', 'pref_strength', 'user_awareness'
            ))

        preferences = await get_preferences()
        
        print(f"   Found {len(preferences)} Preference records:")
        
        preference_analysis = {
            'total_preferences': len(preferences),
            'preferences_created': preferences,
            'expected_categories': ['creative', 'temporal', 'duration', 'outdoor', 'structure'],
            'categories_found': []
        }

        for pref in preferences:
            print(f"     - {pref['pref_name']}: {pref['pref_description'][:60]}...")
            print(f"       Strength: {pref['pref_strength']}, Awareness: {pref['user_awareness']}")
            
            # Categorize preferences
            name_lower = pref['pref_name'].lower()
            desc_lower = pref['pref_description'].lower()
            
            if any(word in name_lower + desc_lower for word in ['creative', 'drawing', 'writing']):
                preference_analysis['categories_found'].append('creative')
            elif any(word in name_lower + desc_lower for word in ['morning', 'time', 'temporal']):
                preference_analysis['categories_found'].append('temporal')
            elif any(word in name_lower + desc_lower for word in ['short', 'duration', 'adhd']):
                preference_analysis['categories_found'].append('duration')
            elif any(word in name_lower + desc_lower for word in ['outdoor', 'weather']):
                preference_analysis['categories_found'].append('outdoor')

        # Quality assessment
        quality_score = min(10, len(preferences) * 2)  # 2 points per preference, max 10
        categories_score = len(set(preference_analysis['categories_found'])) * 2  # 2 points per category
        
        preference_analysis['quality_score'] = (quality_score + categories_score) / 2
        preference_analysis['success'] = len(preferences) >= 3  # At least 3 preferences should be created

        print(f"   ✓ Quality score: {preference_analysis['quality_score']:.1f}/10")
        print(f"   ✓ Categories found: {set(preference_analysis['categories_found'])}")
        print(f"   ✓ Success: {preference_analysis['success']}")

        self.test_results["preference_validation"] = preference_analysis
        print()

    async def _check_profile_completion(self):
        """Check current profile completion percentage."""
        @sync_to_async
        def get_completion():
            try:
                user_profile = UserProfile.objects.get(id=self.user_profile_id)
                # Simple completion calculation based on related objects
                completion = 0.0
                
                # Demographics (20%)
                if hasattr(user_profile, 'demographics'):
                    completion += 0.2
                
                # Preferences (30%)
                pref_count = user_profile.preferences.count()
                completion += min(0.3, pref_count * 0.1)  # 10% per preference, max 30%
                
                # Goals (25%)
                goal_count = user_profile.user_goals.count()
                completion += min(0.25, goal_count * 0.125)  # 12.5% per goal, max 25%
                
                # Trust level (25%)
                if hasattr(user_profile, 'trust_level'):
                    completion += 0.25
                
                return completion
            except Exception:
                return 0.0

        return await get_completion()

    async def _take_database_snapshot(self, snapshot_name: str):
        """Take a snapshot of relevant database state."""
        @sync_to_async
        def get_snapshot():
            try:
                user_profile = UserProfile.objects.get(id=self.user_profile_id)
                return {
                    'timestamp': datetime.now().isoformat(),
                    'demographics_exists': hasattr(user_profile, 'demographics'),
                    'preferences_count': user_profile.preferences.count(),
                    'goals_count': user_profile.user_goals.count(),
                    'trust_level_exists': hasattr(user_profile, 'trust_level')
                }
            except Exception as e:
                return {'error': str(e)}

        snapshot = await get_snapshot()
        # Add profile completion separately since it's async
        snapshot['profile_completion'] = await self._check_profile_completion()
        self.database_snapshots[snapshot_name] = snapshot
        return snapshot

    async def _test_follow_up_conversation(self):
        """Test follow-up conversation to validate mentor quality."""
        print("💬 Phase 4: Testing follow-up conversation quality")

        dispatcher = ConversationDispatcher(
            user_profile_id=self.user_profile_id,
            user_ws_session_name=f"{self.session_name}_followup",
            fail_fast_on_errors=True
        )

        # Follow-up message to test personalization
        followup_message = {
            "text": "Thanks for the introduction! Can you remind me what you learned about my preferences? And what would you suggest for me right now?",
            "metadata": {
                "test_context": "followup_personalization",
                "persona": self.persona
            }
        }

        print(f"   Testing personalized response...")
        step_start = time.time()

        try:
            response = await dispatcher.process_message(followup_message)
            step_duration = time.time() - step_start

            # Analyze response quality
            response_text = response.get('response', {}).get('text', '')

            quality_analysis = {
                'duration': step_duration,
                'response_length': len(response_text),
                'mentions_preferences': any(word in response_text.lower() for word in ['creative', 'morning', 'short', 'outdoor']),
                'mentions_adhd': 'adhd' in response_text.lower(),
                'mentions_name': self.persona['name'].lower() in response_text.lower(),
                'provides_suggestions': any(word in response_text.lower() for word in ['suggest', 'recommend', 'try', 'activity']),
                'response_text': response_text
            }

            quality_score = sum([
                quality_analysis['mentions_preferences'] * 3,
                quality_analysis['mentions_adhd'] * 2,
                quality_analysis['mentions_name'] * 2,
                quality_analysis['provides_suggestions'] * 3
            ])

            quality_analysis['quality_score'] = quality_score
            quality_analysis['success'] = quality_score >= 6

            print(f"   ✓ Duration: {step_duration:.2f}s")
            print(f"   ✓ Response length: {quality_analysis['response_length']} chars")
            print(f"   ✓ Mentions preferences: {quality_analysis['mentions_preferences']}")
            print(f"   ✓ Mentions ADHD: {quality_analysis['mentions_adhd']}")
            print(f"   ✓ Mentions name: {quality_analysis['mentions_name']}")
            print(f"   ✓ Provides suggestions: {quality_analysis['provides_suggestions']}")
            print(f"   ✓ Quality score: {quality_score}/10")

            self.test_results["followup"] = quality_analysis

        except Exception as e:
            print(f"   ❌ Follow-up conversation failed: {e}")
            self.test_results["followup"] = {
                'error': str(e),
                'duration': time.time() - step_start,
                'success': False
            }

        print()

    async def _generate_quality_assessment(self):
        """Generate overall quality assessment."""
        print("📊 Phase 5: Generating quality assessment")

        # Calculate overall scores
        onboarding_success = self.test_results.get("onboarding", {}).get("success", False)
        preference_success = self.test_results.get("preference_validation", {}).get("success", False)
        followup_success = self.test_results.get("followup", {}).get("success", False)

        preference_score = self.test_results.get("preference_validation", {}).get("quality_score", 0)
        followup_score = self.test_results.get("followup", {}).get("quality_score", 0)

        overall_score = (
            (onboarding_success * 3) +
            (preference_success * 4) +
            (followup_success * 3) +
            (preference_score * 0.5) +
            (followup_score * 0.5)
        ) / 2

        assessment = {
            'overall_score': overall_score,
            'grade': 'A' if overall_score >= 9 else 'B' if overall_score >= 7 else 'C' if overall_score >= 5 else 'D',
            'onboarding_success': onboarding_success,
            'preference_creation_success': preference_success,
            'followup_quality_success': followup_success,
            'preferences_created': self.test_results.get("preference_validation", {}).get("total_preferences", 0),
            'completion_increase': self.test_results.get("onboarding", {}).get("completion_increase", 0),
            'total_duration': time.time() - self.start_time,
            'recommendations': []
        }

        # Generate recommendations
        if not preference_success:
            assessment['recommendations'].append("Improve preference extraction from user messages")
        if not followup_success:
            assessment['recommendations'].append("Enhance personalization in mentor responses")
        if assessment['preferences_created'] < 3:
            assessment['recommendations'].append("Increase sensitivity to preference expressions")

        self.test_results["assessment"] = assessment

        print(f"   ✓ Overall score: {overall_score:.1f}/10 (Grade: {assessment['grade']})")
        print(f"   ✓ Onboarding success: {onboarding_success}")
        print(f"   ✓ Preference creation: {preference_success} ({assessment['preferences_created']} preferences)")
        print(f"   ✓ Follow-up quality: {followup_success}")
        print(f"   ✓ Total duration: {assessment['total_duration']:.2f}s")

        if assessment['recommendations']:
            print(f"   📋 Recommendations:")
            for rec in assessment['recommendations']:
                print(f"     - {rec}")

        print()

    def _print_summary(self):
        """Print test summary."""
        assessment = self.test_results.get("assessment", {})
        print("📋 Test Summary:")
        print(f"   Test ID: {self.test_id}")
        print(f"   Overall Grade: {assessment.get('grade', 'F')}")
        print(f"   Overall Score: {assessment.get('overall_score', 0):.1f}/10")
        print(f"   Preferences Created: {assessment.get('preferences_created', 0)}")
        print(f"   Profile Completion Increase: {assessment.get('completion_increase', 0):.1%}")
        print(f"   Total Duration: {assessment.get('total_duration', 0):.2f}s")

    async def _handle_test_failure(self, error):
        """Handle test failure."""
        self.test_results["error"] = {
            'message': str(error),
            'timestamp': datetime.now().isoformat(),
            'total_duration': time.time() - self.start_time
        }


async def main():
    """Run the onboarding quality test."""
    test = OnboardingQualityTest()
    await test.run_onboarding_quality_test()


if __name__ == "__main__":
    asyncio.run(main())
