#!/usr/bin/env python3
"""
Tool Call and LLM Interaction Validation Test
Specifically tests whether agents are making real LLM calls and tool calls
vs using cached/mocked responses.
"""

import os
import sys
import json
import time
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.user.models import UserProfile
from apps.main.models import GenericAgent
from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from apps.main.services.agent_communication_tracker import Agent<PERSON>ommunicationTracker
from apps.main.agents.resource_agent import ResourceAgent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ToolCallLLMValidator:
    """Validates real vs mocked tool calls and LLM interactions."""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'analysis': {},
            'recommendations': []
        }
    
    def log(self, message: str, level: str = 'INFO'):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        if level == 'ERROR':
            logger.error(message)
        else:
            logger.info(message)
    
    def run_validation(self):
        """Run comprehensive validation of tool calls and LLM interactions."""
        self.log("🔍 Starting tool call and LLM validation...")
        
        try:
            # Test 1: Direct agent execution with tracking
            self.test_direct_agent_execution()
            
            # Test 2: Benchmark execution analysis
            self.test_benchmark_execution_analysis()
            
            # Test 3: Tool call interception
            self.test_tool_call_interception()
            
            # Test 4: LLM interaction capture
            self.test_llm_interaction_capture()
            
            # Test 5: ResourceAgent behavior analysis
            self.test_resource_agent_behavior()
            
            # Generate analysis and recommendations
            self.generate_analysis()
            
        except Exception as e:
            self.log(f"Critical error in validation: {e}", 'ERROR')
            self.results['critical_error'] = str(e)
            return False
        
        return True
    
    def test_direct_agent_execution(self):
        """Test direct agent execution with communication tracking."""
        self.log("Testing direct agent execution...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            # Get user profile
            user_profile = UserProfile.objects.filter(is_real=True).first()
            if not user_profile:
                user_profile = UserProfile.objects.first()
            
            # Set up communication tracker
            tracker = AgentCommunicationTracker(enabled=True)
            
            # Create ResourceAgent instance
            agent = ResourceAgent(
                user_profile_id=str(user_profile.id),
                llm_config=None  # This should trigger real LLM usage
            )
            
            # Execute agent with tracking
            start_time = time.time()
            
            # Mock state for testing
            class MockState:
                def __init__(self):
                    self.user_input = "Hello, I need help with my resources"
                    self.user_profile_id = str(user_profile.id)
                    self.workflow_type = "wheel_generation"
                    self.current_agent = "resource"
            
            state = MockState()
            
            # This should trigger tool calls and potentially LLM calls
            try:
                result = asyncio.run(agent.process(state))
                execution_time = time.time() - start_time
                
                # Export tracking data
                tracking_data = tracker.export_data()
                
                test_result['details'] = {
                    'execution_time': execution_time,
                    'agent_result': result,
                    'llm_interactions': len(tracking_data.get('llm_interactions', [])),
                    'tool_calls': len(tracking_data.get('tool_calls', [])),
                    'tracking_enabled': tracker.enabled,
                    'has_result': result is not None
                }
                
                # Analyze results
                if tracking_data.get('llm_interactions'):
                    self.log(f"✅ Found {len(tracking_data['llm_interactions'])} LLM interactions")
                else:
                    test_result['issues'].append('No LLM interactions captured')
                    self.log("⚠️ No LLM interactions captured", 'WARN')
                
                if tracking_data.get('tool_calls'):
                    self.log(f"✅ Found {len(tracking_data['tool_calls'])} tool calls")
                else:
                    test_result['issues'].append('No tool calls captured')
                    self.log("⚠️ No tool calls captured", 'WARN')
                
            except Exception as agent_error:
                test_result['issues'].append(f'Agent execution failed: {agent_error}')
                self.log(f"Agent execution failed: {agent_error}", 'ERROR')
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"Direct agent execution test failed: {e}", 'ERROR')
        
        self.results['tests']['direct_agent_execution'] = test_result
    
    def test_benchmark_execution_analysis(self):
        """Analyze benchmark execution for real vs mocked behavior."""
        self.log("Testing benchmark execution analysis...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            # Get test data
            user_profile = UserProfile.objects.filter(is_real=True).first()
            if not user_profile:
                user_profile = UserProfile.objects.first()
            
            agent = GenericAgent.objects.filter(is_active=True, role='resource').first()
            if not agent:
                agent = GenericAgent.objects.filter(is_active=True).first()
            
            # Run benchmark with real tools and DB
            service = QuickBenchmarkService()
            
            start_time = time.time()
            benchmark_run = service.run_quick_benchmark_sync(
                agent_name=agent.role,
                user_profile_id=str(user_profile.id),
                evaluation_template='mentor_helpfulness',
                scenario_context={'user_input': 'I need help analyzing my resources'},
                use_real_tools=True,
                use_real_db=True
            )
            execution_time = time.time() - start_time
            
            # Analyze benchmark results
            test_result['details'] = {
                'benchmark_run_id': benchmark_run.id,
                'execution_time': execution_time,
                'llm_calls': benchmark_run.llm_calls,
                'tool_calls': benchmark_run.tool_calls,
                'token_usage': benchmark_run.token_usage,
                'total_input_tokens': benchmark_run.total_input_tokens,
                'total_output_tokens': benchmark_run.total_output_tokens,
                'estimated_cost': float(benchmark_run.estimated_cost) if benchmark_run.estimated_cost else 0,
                'semantic_score': benchmark_run.semantic_score,
                'success_rate': benchmark_run.success_rate,
                'has_raw_results': bool(benchmark_run.raw_results),
                'raw_results_keys': list(benchmark_run.raw_results.keys()) if benchmark_run.raw_results else []
            }
            
            # Check for signs of mocked behavior
            if benchmark_run.llm_calls == 0:
                test_result['issues'].append('Zero LLM calls suggests mocked/cached responses')
            
            if benchmark_run.tool_calls == 0:
                test_result['issues'].append('Zero tool calls suggests mocked tool execution')
            
            if benchmark_run.token_usage == 0:
                test_result['issues'].append('Zero token usage suggests no real LLM calls')
            
            if execution_time < 1.0:
                test_result['issues'].append('Very fast execution suggests cached responses')
            
            # Check raw results structure
            if benchmark_run.raw_results:
                last_output = benchmark_run.raw_results.get('last_output', {})
                if isinstance(last_output, dict):
                    # Check if this looks like hardcoded ResourceAgent output
                    if 'resource_context' in last_output and 'next_agent' in last_output:
                        if last_output.get('next_agent') == 'engagement':
                            test_result['issues'].append('Output structure suggests hardcoded ResourceAgent response')
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"Benchmark execution analysis failed: {e}", 'ERROR')
        
        self.results['tests']['benchmark_execution_analysis'] = test_result
    
    def test_tool_call_interception(self):
        """Test tool call interception setup."""
        self.log("Testing tool call interception...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            from apps.main.services.tool_call_interceptor import ToolCallInterceptor, setup_comprehensive_tool_interception
            from apps.main.agents.tools.tools_util import execute_tool
            
            # Test interceptor creation
            tracker = AgentCommunicationTracker(enabled=True)
            interceptor = ToolCallInterceptor(tracker)
            
            test_result['details'] = {
                'interceptor_created': bool(interceptor),
                'tracker_enabled': tracker.enabled,
                'execute_tool_available': callable(execute_tool)
            }
            
            # Test tool execution with tracking
            try:
                # This should be intercepted if working correctly
                tool_result = asyncio.run(execute_tool(
                    'get_environment_context',
                    {
                        'input_data': {
                            'user_profile_id': '1',
                            'reported_environment': 'home'
                        }
                    },
                    'test-run-id'
                ))
                
                tracking_data = tracker.export_data()
                
                test_result['details'].update({
                    'tool_execution_successful': tool_result is not None,
                    'tool_calls_tracked': len(tracking_data.get('tool_calls', [])),
                    'tool_result_type': type(tool_result).__name__
                })
                
                if not tracking_data.get('tool_calls'):
                    test_result['issues'].append('Tool calls not being intercepted/tracked')
                
            except Exception as tool_error:
                test_result['issues'].append(f'Tool execution failed: {tool_error}')
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"Tool call interception test failed: {e}", 'ERROR')
        
        self.results['tests']['tool_call_interception'] = test_result
    
    def test_llm_interaction_capture(self):
        """Test LLM interaction capture functionality."""
        self.log("Testing LLM interaction capture...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            from apps.main.services.llm_interaction_interceptor import LLMInteractionInterceptor
            
            tracker = AgentCommunicationTracker(enabled=True)
            interceptor = LLMInteractionInterceptor(tracker)
            
            # Test manual LLM interaction recording
            tracker.record_llm_interaction(
                agent_name="test_agent",
                model_name="mistral-small-latest",
                prompt="Test prompt for validation",
                response="Test response from LLM",
                token_usage={'input': 15, 'output': 25},
                duration_ms=150.0
            )
            
            tracking_data = tracker.export_data()
            
            test_result['details'] = {
                'interceptor_created': bool(interceptor),
                'llm_interactions_recorded': len(tracking_data.get('llm_interactions', [])),
                'interaction_structure': list(tracking_data.get('llm_interactions', [{}])[0].keys()) if tracking_data.get('llm_interactions') else []
            }
            
            if not tracking_data.get('llm_interactions'):
                test_result['issues'].append('LLM interactions not being recorded')
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"LLM interaction capture test failed: {e}", 'ERROR')
        
        self.results['tests']['llm_interaction_capture'] = test_result
    
    def test_resource_agent_behavior(self):
        """Analyze ResourceAgent behavior for hardcoded vs dynamic responses."""
        self.log("Testing ResourceAgent behavior...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            # Examine ResourceAgent source code for hardcoded behavior
            agent_file_path = '/usr/src/app/apps/main/agents/resource_agent.py'
            
            if os.path.exists(agent_file_path):
                with open(agent_file_path, 'r') as f:
                    agent_code = f.read()
                
                # Look for signs of hardcoded responses
                hardcoded_indicators = [
                    'return {' in agent_code and 'next_agent' in agent_code,
                    'engagement' in agent_code,  # Hardcoded next agent
                    'resource_context' in agent_code,  # Hardcoded structure
                    'await self._call_tool' in agent_code,  # Tool calls present
                    'llm_client' in agent_code or 'llm_service' in agent_code  # LLM usage
                ]
                
                test_result['details'] = {
                    'agent_file_exists': True,
                    'has_hardcoded_structure': hardcoded_indicators[0],
                    'has_hardcoded_next_agent': hardcoded_indicators[1],
                    'has_hardcoded_context': hardcoded_indicators[2],
                    'has_tool_calls': hardcoded_indicators[3],
                    'has_llm_usage': hardcoded_indicators[4],
                    'code_length': len(agent_code)
                }
                
                # Analyze behavior
                if hardcoded_indicators[0] and hardcoded_indicators[1]:
                    test_result['issues'].append('ResourceAgent appears to use hardcoded response structure')
                
                if not hardcoded_indicators[4]:
                    test_result['issues'].append('ResourceAgent does not appear to use LLM services')
                
            else:
                test_result['issues'].append('ResourceAgent source file not found')
                test_result['details'] = {'agent_file_exists': False}
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"ResourceAgent behavior test failed: {e}", 'ERROR')
        
        self.results['tests']['resource_agent_behavior'] = test_result
    
    def generate_analysis(self):
        """Generate analysis and recommendations."""
        self.log("Generating analysis and recommendations...")
        
        # Analyze test results
        issues_by_category = {
            'llm_interactions': [],
            'tool_calls': [],
            'agent_behavior': [],
            'tracking': []
        }
        
        for test_name, test_result in self.results['tests'].items():
            for issue in test_result.get('issues', []):
                if 'LLM' in issue or 'llm' in issue:
                    issues_by_category['llm_interactions'].append(issue)
                elif 'tool' in issue or 'Tool' in issue:
                    issues_by_category['tool_calls'].append(issue)
                elif 'hardcoded' in issue or 'cached' in issue:
                    issues_by_category['agent_behavior'].append(issue)
                else:
                    issues_by_category['tracking'].append(issue)
        
        # Generate recommendations
        recommendations = []
        
        if issues_by_category['llm_interactions']:
            recommendations.append({
                'category': 'LLM Interactions',
                'priority': 'HIGH',
                'action': 'Modify ResourceAgent to make real LLM calls instead of using hardcoded logic',
                'details': 'The agent should use its llm_client to generate dynamic responses based on user context'
            })
        
        if issues_by_category['tool_calls']:
            recommendations.append({
                'category': 'Tool Call Tracking',
                'priority': 'MEDIUM',
                'action': 'Ensure tool call interceptor is properly set up during benchmark execution',
                'details': 'Tool calls are happening but not being tracked/displayed in the UI'
            })
        
        if issues_by_category['agent_behavior']:
            recommendations.append({
                'category': 'Agent Behavior',
                'priority': 'HIGH',
                'action': 'Replace hardcoded agent responses with dynamic LLM-generated content',
                'details': 'Agents should use LLM services to generate contextual responses'
            })
        
        self.results['analysis'] = {
            'issues_by_category': issues_by_category,
            'total_issues': sum(len(issues) for issues in issues_by_category.values()),
            'critical_findings': [
                issue for issues in issues_by_category.values() for issue in issues
                if 'Zero' in issue or 'hardcoded' in issue
            ]
        }
        
        self.results['recommendations'] = recommendations
        
        # Save results
        results_dir = '/usr/src/app/real_condition_tests/results'
        os.makedirs(results_dir, exist_ok=True)
        
        results_file = os.path.join(results_dir, 'tool_call_llm_validation.json')
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        self.log(f"Results saved to: {results_file}")
        
        # Print summary
        print("\n" + "="*80)
        print("🔍 TOOL CALL & LLM VALIDATION REPORT")
        print("="*80)
        print(f"📊 Total Issues Found: {self.results['analysis']['total_issues']}")
        
        for category, issues in issues_by_category.items():
            if issues:
                print(f"\n🔸 {category.upper()}:")
                for issue in issues:
                    print(f"   • {issue}")
        
        if recommendations:
            print("\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. [{rec['priority']}] {rec['action']}")
                print(f"      {rec['details']}")
        
        print("\n📁 Detailed results saved to:", results_file)
        print("="*80)

def main():
    """Main execution function."""
    validator = ToolCallLLMValidator()
    success = validator.run_validation()
    
    if success:
        print("✅ Tool call and LLM validation completed successfully")
        return 0
    else:
        print("❌ Tool call and LLM validation failed")
        return 1

if __name__ == "__main__":
    exit(main())
