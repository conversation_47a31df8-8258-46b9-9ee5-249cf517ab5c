#!/usr/bin/env python3
"""
JavaScript Console Errors Test

This test checks for JavaScript console errors that might be preventing
the agent evaluation modal functions from working properly.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_javascript_console_errors.py
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import Client
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)

class JavaScriptConsoleErrorsTest:
    """Test for JavaScript console errors that might break functionality."""
    
    def __init__(self):
        self.results = {
            'test_name': 'JavaScript Console Errors Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
        self.client = Client()
        
    def log_test_result(self, test_name: str, success: bool, details: dict = None, error: str = None):
        """Log the result of a test."""
        self.results['tests'][test_name] = {
            'success': success,
            'details': details or {},
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results['summary']['total_tests'] += 1
        if success:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: {error}")
            if error:
                self.results['summary']['errors'].append(f"{test_name}: {error}")
    
    def test_javascript_syntax_errors(self):
        """Test for JavaScript syntax errors in the page content."""
        try:
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_js_errors_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark management page
            response = self.client.get('/admin/benchmarks/manage/')
            
            details = {
                'status_code': response.status_code
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for common JavaScript syntax errors
                js_error_patterns = [
                    'SyntaxError',
                    'ReferenceError',
                    'TypeError',
                    'Uncaught',
                    'undefined is not a function',
                    'Cannot read property',
                    'Cannot read properties',
                    'is not defined'
                ]
                
                # Check for potential syntax issues in inline scripts
                syntax_issues = []
                
                # Look for unclosed script tags
                script_count = content.count('<script>')
                script_close_count = content.count('</script>')
                if script_count != script_close_count:
                    syntax_issues.append(f"Mismatched script tags: {script_count} open, {script_close_count} close")
                
                # Look for unclosed function definitions
                function_count = content.count('function ')
                brace_open = content.count('{')
                brace_close = content.count('}')
                if abs(brace_open - brace_close) > 10:  # Allow some tolerance
                    syntax_issues.append(f"Potential brace mismatch: {brace_open} open, {brace_close} close")
                
                # Check for specific function definitions
                function_checks = {
                    'renderAgentDetails_defined': 'window.renderAgentDetails = ' in content or 'function renderAgentDetails' in content,
                    'renderLLMInteractions_defined': 'window.renderLLMInteractions = ' in content or 'function renderLLMInteractions' in content,
                    'setupCopyRunDataButton_defined': 'window.setupCopyRunDataButton = ' in content or 'function setupCopyRunDataButton' in content,
                    'openAgentEvaluationModal_defined': 'function openAgentEvaluationModal' in content
                }
                
                # Check for script loading
                script_loading_checks = {
                    'quick_benchmark_js_loaded': 'quick_benchmark.js' in content,
                    'benchmark_management_js_loaded': 'benchmark_management.js' in content,
                    'jquery_loaded': 'jquery' in content.lower(),
                    'bootstrap_loaded': 'bootstrap' in content.lower()
                }
                
                details.update({
                    'syntax_issues': syntax_issues,
                    'function_checks': function_checks,
                    'script_loading_checks': script_loading_checks,
                    'all_functions_defined': all(function_checks.values()),
                    'all_scripts_loaded': all(script_loading_checks.values()),
                    'no_syntax_issues': len(syntax_issues) == 0
                })
                
                success = (details['all_functions_defined'] and 
                          details['all_scripts_loaded'] and 
                          details['no_syntax_issues'])
                
                if not success:
                    error_parts = []
                    if not details['all_functions_defined']:
                        missing_functions = [k for k, v in function_checks.items() if not v]
                        error_parts.append(f"Missing functions: {missing_functions}")
                    if not details['all_scripts_loaded']:
                        missing_scripts = [k for k, v in script_loading_checks.items() if not v]
                        error_parts.append(f"Missing scripts: {missing_scripts}")
                    if not details['no_syntax_issues']:
                        error_parts.append(f"Syntax issues: {syntax_issues}")
                    
                    error = "; ".join(error_parts)
                else:
                    error = None
                
            else:
                success = False
                error = f"Could not access benchmark management page: HTTP {response.status_code}"
            
            self.log_test_result('JavaScript Syntax Errors', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('JavaScript Syntax Errors', False, error=str(e))
            return False
    
    def test_modal_html_structure(self):
        """Test that the modal HTML structure is complete and valid."""
        try:
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_modal_structure_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark management page
            response = self.client.get('/admin/benchmarks/manage/')
            
            details = {
                'status_code': response.status_code
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for modal structure
                modal_structure_checks = {
                    'modal_container': 'id="agent-details-modal"' in content,
                    'modal_body': 'id="agent-modal-body"' in content,
                    'modal_close_button': 'class="close"' in content,
                    'copy_button': 'id="copy-run-data-btn"' in content,
                    'refresh_button': 'id="refresh-modal-btn"' in content,
                    'llm_interactions_section': 'id="llm-interactions-content"' in content,
                    'tool_calls_section': 'id="enhanced-tool-calls-section"' in content
                }
                
                # Check for CSS classes
                css_checks = {
                    'modal_css': '.modal' in content,
                    'modal_content_css': '.modal-content' in content,
                    'modal_header_css': '.modal-header' in content,
                    'btn_css': '.btn' in content
                }
                
                # Check for JavaScript event handlers
                event_handler_checks = {
                    'modal_close_handler': 'onclick' in content or 'addEventListener' in content,
                    'copy_button_handler': 'copy-run-data-btn' in content,
                    'modal_backdrop_close': 'modal.onclick' in content or 'backdrop' in content
                }
                
                details.update({
                    'modal_structure': modal_structure_checks,
                    'css_checks': css_checks,
                    'event_handlers': event_handler_checks,
                    'modal_structure_complete': all(modal_structure_checks.values()),
                    'css_available': all(css_checks.values()),
                    'event_handlers_present': any(event_handler_checks.values())
                })
                
                success = (details['modal_structure_complete'] and 
                          details['css_available'])
                
                error = None if success else "Modal structure or CSS incomplete"
                
            else:
                success = False
                error = f"Could not access benchmark management page: HTTP {response.status_code}"
            
            self.log_test_result('Modal HTML Structure', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Modal HTML Structure', False, error=str(e))
            return False
    
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'javascript_console_errors_{timestamp}.json'
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Results saved to: {filepath}")
        return filepath
    
    def run_all_tests(self):
        """Run all tests in sequence."""
        print("🚀 Starting JavaScript Console Errors Test")
        print("=" * 60)
        
        # Test 1: JavaScript Syntax Errors
        self.test_javascript_syntax_errors()
        
        # Test 2: Modal HTML Structure
        self.test_modal_html_structure()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.results['summary']['total_tests']}")
        print(f"Passed: {self.results['summary']['passed']}")
        print(f"Failed: {self.results['summary']['failed']}")
        
        if self.results['summary']['errors']:
            print("\n❌ ERRORS:")
            for error in self.results['summary']['errors']:
                print(f"  - {error}")
        
        success_rate = (self.results['summary']['passed'] / self.results['summary']['total_tests']) * 100
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("✅ JavaScript Console Errors Test passed!")
        elif success_rate >= 70:
            print("⚠️ JavaScript Console Errors Test has some issues")
        else:
            print("❌ JavaScript Console Errors Test found significant problems")
        
        return self.save_results()

def main():
    """Main test execution."""
    test = JavaScriptConsoleErrorsTest()
    results_file = test.run_all_tests()
    
    print(f"\n🔗 View detailed results: {results_file}")
    print("\n🎯 Next Steps:")
    print("1. If JavaScript errors found: Check browser console for runtime errors")
    print("2. If functions missing: Verify script loading order")
    print("3. Manual testing: Open browser dev tools and check console")

if __name__ == '__main__':
    main()
