#!/usr/bin/env python3
"""
Activity Relevance Measurement System

This test system measures how well the wheel generation adapts to different
time available and energy level combinations. It analyzes:

1. Activity duration appropriateness for time constraints
2. Activity energy requirements vs user energy level
3. Activity type distribution across different contexts
4. Consistency and quality of recommendations

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_activity_relevance_measurement.py
"""

import os
import sys
import django
import logging
import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Tuple

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.user.models import UserProfile
from apps.main.agents.tools.tools import generate_wheel
from asgiref.sync import sync_to_async

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ActivityRelevanceMeasurement:
    """Comprehensive system for measuring activity relevance across different contexts."""
    
    def __init__(self):
        self.test_scenarios = []
        self.results = []
        self.analysis_metrics = {
            'duration_appropriateness': [],
            'energy_alignment': [],
            'activity_diversity': [],
            'context_adaptation': []
        }
    
    def setup_test_scenarios(self) -> List[Dict]:
        """Define test scenarios with different time/energy combinations."""
        scenarios = [
            # Low energy scenarios
            {"time_minutes": 10, "energy_level": 20, "context": "Low energy, quick break"},
            {"time_minutes": 30, "energy_level": 25, "context": "Low energy, short session"},
            {"time_minutes": 60, "energy_level": 30, "context": "Low energy, medium time"},
            {"time_minutes": 120, "energy_level": 35, "context": "Low energy, long session"},
            
            # Medium energy scenarios
            {"time_minutes": 15, "energy_level": 50, "context": "Medium energy, quick activity"},
            {"time_minutes": 45, "energy_level": 55, "context": "Medium energy, standard session"},
            {"time_minutes": 90, "energy_level": 60, "context": "Medium energy, extended time"},
            {"time_minutes": 180, "energy_level": 65, "context": "Medium energy, long period"},
            
            # High energy scenarios
            {"time_minutes": 20, "energy_level": 80, "context": "High energy, focused burst"},
            {"time_minutes": 60, "energy_level": 85, "context": "High energy, workout time"},
            {"time_minutes": 120, "energy_level": 90, "context": "High energy, project time"},
            {"time_minutes": 240, "energy_level": 95, "context": "High energy, full session"},
            
            # Edge cases
            {"time_minutes": 5, "energy_level": 10, "context": "Minimal time and energy"},
            {"time_minutes": 5, "energy_level": 100, "context": "Minimal time, max energy"},
            {"time_minutes": 240, "energy_level": 10, "context": "Max time, minimal energy"},
            {"time_minutes": 240, "energy_level": 100, "context": "Max time and energy"},
        ]
        
        self.test_scenarios = scenarios
        logger.info(f"✅ Setup {len(scenarios)} test scenarios")
        return scenarios
    
    async def run_wheel_generation_test(self, scenario: Dict, user_profile_id: int) -> Dict:
        """Run wheel generation for a specific scenario and collect results."""
        logger.info(f"🎡 Testing scenario: {scenario['context']} (Time: {scenario['time_minutes']}min, Energy: {scenario['energy_level']}%)")
        
        try:
            # Create strategy framework
            strategy_framework = {
                "domains": {
                    "mindfulness": {"name": "Mindfulness", "weight": 0.3},
                    "physical": {"name": "Physical", "weight": 0.3},
                    "creative": {"name": "Creative", "weight": 0.2},
                    "practical": {"name": "Practical", "weight": 0.2}
                },
                "challenge_calibration": {
                    "base_level": scenario['energy_level'],
                    "adjustment_factors": {"trust_phase": "foundation"}
                },
                "time_constraints": {
                    "available_minutes": scenario['time_minutes'],
                    "energy_level": scenario['energy_level']
                }
            }
            
            # Generate wheel (this will fail due to the constraint issue, but we can analyze the error)
            try:
                result = await generate_wheel({
                    "user_profile_id": user_profile_id,
                    "strategy_framework": strategy_framework,
                    "activity_count": 4
                })
                
                if "error" in result:
                    logger.warning(f"⚠️ Wheel generation returned error: {result['error']}")
                    return {
                        "scenario": scenario,
                        "status": "error",
                        "error": result["error"],
                        "activities": []
                    }
                
                activities = result.get("wheel", {}).get("items", [])
                logger.info(f"✅ Generated {len(activities)} activities")
                
                return {
                    "scenario": scenario,
                    "status": "success",
                    "activities": activities,
                    "wheel_data": result.get("wheel", {})
                }
                
            except Exception as e:
                error_msg = str(e)
                logger.warning(f"⚠️ Wheel generation failed: {error_msg}")
                
                # If it's the constraint violation we expect, we can still analyze the process
                if "duplicate key value violates unique constraint" in error_msg:
                    return {
                        "scenario": scenario,
                        "status": "constraint_violation",
                        "error": error_msg,
                        "activities": [],
                        "note": "Expected constraint violation - demonstrates the database issue we're fixing"
                    }
                else:
                    return {
                        "scenario": scenario,
                        "status": "unexpected_error",
                        "error": error_msg,
                        "activities": []
                    }
                    
        except Exception as e:
            logger.error(f"❌ Test scenario failed: {str(e)}")
            return {
                "scenario": scenario,
                "status": "test_failure",
                "error": str(e),
                "activities": []
            }
    
    def analyze_duration_appropriateness(self, activities: List[Dict], time_available: int) -> float:
        """Analyze how well activity durations match available time."""
        if not activities:
            return 0.0
        
        total_score = 0.0
        for activity in activities:
            duration_range = activity.get('duration_range', '15-30 minutes')
            
            # Parse duration range (simplified)
            try:
                if '-' in duration_range:
                    min_dur, max_dur = duration_range.split('-')
                    min_minutes = int(min_dur.strip().split()[0])
                    max_minutes = int(max_dur.strip().split()[0])
                    avg_duration = (min_minutes + max_minutes) / 2
                else:
                    # Single duration
                    avg_duration = int(duration_range.split()[0])
                
                # Score based on how well duration fits available time
                if avg_duration <= time_available:
                    if avg_duration >= time_available * 0.5:  # Good fit
                        score = 1.0
                    elif avg_duration >= time_available * 0.25:  # Acceptable
                        score = 0.7
                    else:  # Too short
                        score = 0.4
                else:
                    # Activity too long
                    score = max(0.0, 1.0 - (avg_duration - time_available) / time_available)
                
                total_score += score
                
            except (ValueError, IndexError):
                # Couldn't parse duration, neutral score
                total_score += 0.5
        
        return total_score / len(activities)
    
    def analyze_energy_alignment(self, activities: List[Dict], energy_level: int) -> float:
        """Analyze how well activity energy requirements match user energy."""
        if not activities:
            return 0.0
        
        total_score = 0.0
        for activity in activities:
            challenge_rating = activity.get('base_challenge_rating', 50)
            
            # Score based on energy alignment
            energy_diff = abs(challenge_rating - energy_level)
            if energy_diff <= 10:  # Very good match
                score = 1.0
            elif energy_diff <= 20:  # Good match
                score = 0.8
            elif energy_diff <= 30:  # Acceptable
                score = 0.6
            elif energy_diff <= 40:  # Poor match
                score = 0.4
            else:  # Very poor match
                score = 0.2
            
            total_score += score
        
        return total_score / len(activities)
    
    def analyze_activity_diversity(self, activities: List[Dict]) -> float:
        """Analyze diversity of activity types and domains."""
        if not activities:
            return 0.0
        
        domains = set()
        activity_types = set()
        
        for activity in activities:
            # Extract domain information
            domain = activity.get('domain', 'unknown')
            domains.add(domain)
            
            # Extract activity type from name/description
            name = activity.get('activity_name', activity.get('name', '')).lower()
            if any(word in name for word in ['walk', 'run', 'exercise', 'yoga']):
                activity_types.add('physical')
            elif any(word in name for word in ['read', 'write', 'learn', 'study']):
                activity_types.add('mental')
            elif any(word in name for word in ['create', 'draw', 'music', 'art']):
                activity_types.add('creative')
            elif any(word in name for word in ['meditate', 'breathe', 'relax']):
                activity_types.add('mindfulness')
            else:
                activity_types.add('other')
        
        # Score based on diversity
        domain_diversity = len(domains) / max(1, len(activities))
        type_diversity = len(activity_types) / max(1, len(activities))
        
        return (domain_diversity + type_diversity) / 2
    
    def generate_comprehensive_report(self) -> Dict:
        """Generate a comprehensive analysis report."""
        if not self.results:
            return {"error": "No results to analyze"}
        
        report = {
            "test_summary": {
                "total_scenarios": len(self.results),
                "successful_generations": len([r for r in self.results if r["status"] == "success"]),
                "constraint_violations": len([r for r in self.results if r["status"] == "constraint_violation"]),
                "other_errors": len([r for r in self.results if r["status"] not in ["success", "constraint_violation"]])
            },
            "scenario_analysis": [],
            "overall_metrics": {},
            "recommendations": []
        }
        
        # Analyze each scenario
        for result in self.results:
            scenario = result["scenario"]
            activities = result.get("activities", [])
            
            analysis = {
                "scenario": scenario,
                "status": result["status"],
                "activity_count": len(activities),
                "metrics": {}
            }
            
            if activities:
                analysis["metrics"] = {
                    "duration_appropriateness": self.analyze_duration_appropriateness(activities, scenario["time_minutes"]),
                    "energy_alignment": self.analyze_energy_alignment(activities, scenario["energy_level"]),
                    "activity_diversity": self.analyze_activity_diversity(activities)
                }
            
            report["scenario_analysis"].append(analysis)
        
        # Calculate overall metrics
        successful_results = [r for r in report["scenario_analysis"] if r["metrics"]]
        if successful_results:
            report["overall_metrics"] = {
                "avg_duration_appropriateness": sum(r["metrics"]["duration_appropriateness"] for r in successful_results) / len(successful_results),
                "avg_energy_alignment": sum(r["metrics"]["energy_alignment"] for r in successful_results) / len(successful_results),
                "avg_activity_diversity": sum(r["metrics"]["activity_diversity"] for r in successful_results) / len(successful_results)
            }
        
        # Generate recommendations
        constraint_violations = report["test_summary"]["constraint_violations"]
        if constraint_violations > 0:
            report["recommendations"].append(
                f"🔧 Database Constraint Issue: {constraint_violations} scenarios failed due to ActivityTailored reuse constraints. "
                "Implement the ActivityTailored-UserEnvironment relationship and change WheelItem to ForeignKey to resolve this."
            )
        
        if report["overall_metrics"]:
            metrics = report["overall_metrics"]
            if metrics["avg_duration_appropriateness"] < 0.7:
                report["recommendations"].append(
                    "⏱️ Duration Matching: Improve activity duration matching to available time constraints."
                )
            if metrics["avg_energy_alignment"] < 0.7:
                report["recommendations"].append(
                    "⚡ Energy Alignment: Enhance energy level matching between activities and user state."
                )
            if metrics["avg_activity_diversity"] < 0.6:
                report["recommendations"].append(
                    "🎨 Activity Diversity: Increase variety in activity types and domains for better user engagement."
                )
        
        return report
    
    async def run_comprehensive_test(self, user_profile_id: int = 2) -> Dict:
        """Run the complete activity relevance measurement test."""
        logger.info("🚀 Starting Comprehensive Activity Relevance Measurement")
        logger.info("=" * 80)
        
        # Setup scenarios
        scenarios = self.setup_test_scenarios()
        
        # Get user profile
        user_profile = await sync_to_async(UserProfile.objects.filter(id=user_profile_id).first)()
        if not user_profile:
            logger.error(f"❌ User profile {user_profile_id} not found")
            return {"error": f"User profile {user_profile_id} not found"}
        
        logger.info(f"✅ Testing with user: {user_profile.profile_name} (ID: {user_profile.id})")
        
        # Run tests for each scenario
        for i, scenario in enumerate(scenarios, 1):
            logger.info(f"\n📊 Running scenario {i}/{len(scenarios)}")
            result = await self.run_wheel_generation_test(scenario, user_profile_id)
            self.results.append(result)
            
            # Brief pause between tests
            await asyncio.sleep(0.5)
        
        # Generate comprehensive report
        logger.info("\n📈 Generating comprehensive analysis report...")
        report = self.generate_comprehensive_report()
        
        # Log summary
        logger.info("=" * 80)
        logger.info("📊 TEST RESULTS SUMMARY")
        logger.info("=" * 80)
        
        summary = report["test_summary"]
        logger.info(f"Total Scenarios: {summary['total_scenarios']}")
        logger.info(f"Successful Generations: {summary['successful_generations']}")
        logger.info(f"Constraint Violations: {summary['constraint_violations']}")
        logger.info(f"Other Errors: {summary['other_errors']}")
        
        if report.get("overall_metrics"):
            metrics = report["overall_metrics"]
            logger.info(f"\n📈 OVERALL METRICS:")
            logger.info(f"Duration Appropriateness: {metrics['avg_duration_appropriateness']:.2f}")
            logger.info(f"Energy Alignment: {metrics['avg_energy_alignment']:.2f}")
            logger.info(f"Activity Diversity: {metrics['avg_activity_diversity']:.2f}")
        
        if report.get("recommendations"):
            logger.info(f"\n💡 RECOMMENDATIONS:")
            for rec in report["recommendations"]:
                logger.info(f"  {rec}")
        
        logger.info("=" * 80)
        
        return report

async def main():
    """Main function to run the activity relevance measurement."""
    measurement_system = ActivityRelevanceMeasurement()
    
    try:
        report = await measurement_system.run_comprehensive_test()
        
        # Save detailed report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"/usr/src/app/real_condition_tests/activity_relevance_report_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📄 Detailed report saved to: {report_file}")
        
        # Determine exit code based on results
        if "error" in report:
            sys.exit(1)
        
        constraint_violations = report["test_summary"]["constraint_violations"]
        if constraint_violations > 0:
            logger.info("🔧 Test completed with expected constraint violations (database issue to be fixed)")
            sys.exit(0)  # Expected issue, not a test failure
        
        sys.exit(0)
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
