#!/usr/bin/env python3
"""
Workflow Quality Improvements Test

This script validates the quality improvements applied to wheel_generation, 
discussion, and post_spin workflows based on successful onboarding patterns.

Key improvements tested:
1. Safety mechanisms (iteration counting, agent execution limits)
2. Enhanced completion logic
3. Profile enrichment capabilities
4. Error recovery improvements
5. Real LLM integration patterns
"""

import os
import sys
import django
import asyncio
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.contrib.auth.models import User
from apps.user.models import UserProfile, Demographics, Preference
from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow
from apps.main.graphs.discussion_graph import run_discussion_workflow
from apps.main.graphs.post_spin_graph import run_post_spin_workflow
from asgiref.sync import sync_to_async

class WorkflowQualityImprovementsTest:
    """Test workflow quality improvements across all workflows."""
    
    def __init__(self):
        self.test_id = str(uuid.uuid4())[:8]
        self.test_user_id = None
        self.test_profile_id = None
        self.results = {
            'test_id': self.test_id,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'test_scenario': 'Workflow quality improvements validation',
            'workflows_tested': {},
            'safety_mechanisms': {},
            'quality_assessment': {},
            'recommendations': []
        }
        
    async def run_complete_test(self):
        """Execute the complete workflow quality improvements test."""
        print(f"🎯 Starting Workflow Quality Improvements Test - ID: {self.test_id}")
        print("=" * 70)
        
        try:
            # Phase 1: Setup test user profile
            await self._phase_1_setup_test_user()
            
            # Phase 2: Test wheel generation workflow improvements
            await self._phase_2_test_wheel_generation()
            
            # Phase 3: Test discussion workflow improvements
            await self._phase_3_test_discussion_workflow()
            
            # Phase 4: Test post-spin workflow improvements
            await self._phase_4_test_post_spin_workflow()
            
            # Phase 5: Validate safety mechanisms
            await self._phase_5_validate_safety_mechanisms()
            
            # Phase 6: Generate quality assessment
            await self._phase_6_quality_assessment()
            
            # Save results
            await self._save_results()
            
            print("\n🎉 Test completed successfully!")
            print(f"📊 Results saved to: results/workflow_quality_improvements_{self.test_id}.json")
            
        except Exception as e:
            print(f"❌ Test failed: {str(e)}")
            self.results['error'] = str(e)
            await self._save_results()
            raise
            
    async def _phase_1_setup_test_user(self):
        """Phase 1: Create test user profile."""
        print("\n📋 Phase 1: Setting up test user profile")
        
        phase_start = datetime.now()
        
        try:
            # Create Django user
            username = f"test_quality_user_{self.test_id}"
            user, created = await sync_to_async(User.objects.get_or_create)(
                username=username,
                defaults={
                    'email': f"{username}@test.goali.com",
                    'first_name': 'Quality',
                    'last_name': 'Test'
                }
            )
            self.test_user_id = user.id

            # Create UserProfile
            profile, created = await sync_to_async(UserProfile.objects.get_or_create)(
                user=user,
                defaults={
                    'profile_name': 'Quality Test User',
                    'is_real': False,  # Test profile
                }
            )
            self.test_profile_id = str(profile.id)

            # Create Demographics
            demographics, created = await sync_to_async(Demographics.objects.get_or_create)(
                user_profile=profile,
                defaults={
                    'full_name': 'Quality Test User',
                    'age': 25,
                    'gender': 'non-binary',
                    'location': 'Test City',
                    'language': 'English',
                    'occupation': 'Quality Tester'
                }
            )

            # Create preferences as separate objects (replacing personal_prefs_json)
            if created:
                from datetime import date, timedelta
                preferences_data = [
                    ("Quality Focus", "Focused on systematic quality testing", 90),
                    ("Systematic Testing", "Prefers methodical testing approaches", 85)
                ]

                for pref_name, description, strength in preferences_data:
                    await sync_to_async(Preference.objects.create)(
                        user_profile=profile,
                        pref_name=pref_name,
                        pref_description=description,
                        pref_strength=strength,
                        user_awareness=85,
                        effective_start=date.today(),
                        duration_estimate="12 months",
                        effective_end=date.today() + timedelta(days=365)
                    )
            
            self.results['workflows_tested']['setup'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'user_id': self.test_user_id,
                'profile_id': self.test_profile_id
            }
            
            print(f"✅ Test user created: {username} (Profile ID: {self.test_profile_id})")
            
        except Exception as e:
            self.results['workflows_tested']['setup'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            raise
            
    async def _phase_2_test_wheel_generation(self):
        """Phase 2: Test wheel generation workflow improvements."""
        print("\n🎡 Phase 2: Testing wheel generation workflow improvements")
        
        phase_start = datetime.now()
        
        try:
            # Test wheel generation with safety mechanisms
            context_packet = {
                'text': 'I need help creating an activity wheel for stress management',
                'metadata': {
                    'test_scenario': 'quality_improvements',
                    'focus_area': 'stress_management'
                },
                'user_ws_session_name': f"test_session_{self.test_id}"
            }
            
            print("🚀 Running wheel generation workflow...")
            workflow_start = datetime.now()
            
            result = await run_wheel_generation_workflow(
                user_profile_id=self.test_profile_id,
                context_packet=context_packet,
                workflow_id=str(uuid.uuid4())  # Use proper UUID
            )
            
            workflow_duration = (datetime.now() - workflow_start).total_seconds()
            
            # Analyze results for safety mechanisms
            safety_analysis = {
                'completed_successfully': result.get('completed', False),
                'execution_time': workflow_duration,
                'has_wheel_output': bool(result.get('output_data', {}).get('wheel')),
                'error_handling': result.get('error') is None,
                'iteration_tracking': 'iteration_count' in str(result),  # Check if iteration tracking is present
                'agent_execution_tracking': 'agent_execution_count' in str(result)
            }
            
            self.results['workflows_tested']['wheel_generation'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'workflow_duration': workflow_duration,
                'safety_analysis': safety_analysis,
                'result_summary': {
                    'completed': result.get('completed'),
                    'has_wheel': safety_analysis['has_wheel_output'],
                    'error': result.get('error')
                }
            }
            
            print(f"✅ Wheel generation completed in {workflow_duration:.2f}s")
            print(f"📊 Safety mechanisms: {sum(safety_analysis.values())}/6 checks passed")
            
        except Exception as e:
            self.results['workflows_tested']['wheel_generation'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            print(f"❌ Wheel generation test failed: {str(e)}")
            
    async def _phase_3_test_discussion_workflow(self):
        """Phase 3: Test discussion workflow improvements."""
        print("\n💬 Phase 3: Testing discussion workflow improvements")
        
        phase_start = datetime.now()
        
        try:
            # Test discussion workflow with safety mechanisms
            context_packet = {
                'text': 'I want to discuss my goals and how to achieve them',
                'metadata': {
                    'test_scenario': 'quality_improvements',
                    'conversation_type': 'goal_discussion'
                },
                'user_ws_session_name': f"test_session_{self.test_id}"
            }
            
            print("🚀 Running discussion workflow...")
            workflow_start = datetime.now()
            
            result = await run_discussion_workflow(
                user_profile_id=self.test_profile_id,
                context_packet=context_packet,
                workflow_id=str(uuid.uuid4())  # Use proper UUID
            )
            
            workflow_duration = (datetime.now() - workflow_start).total_seconds()
            
            # Analyze results for safety mechanisms
            safety_analysis = {
                'completed_successfully': result.get('completed', False),
                'execution_time': workflow_duration,
                'has_conversation_history': bool(result.get('conversation_history')),
                'error_handling': result.get('error') is None,
                'iteration_tracking': 'iteration_count' in str(result),
                'conversation_depth_tracking': 'conversation_depth' in str(result)
            }
            
            self.results['workflows_tested']['discussion'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'workflow_duration': workflow_duration,
                'safety_analysis': safety_analysis,
                'result_summary': {
                    'completed': result.get('completed'),
                    'has_conversation': safety_analysis['has_conversation_history'],
                    'error': result.get('error')
                }
            }
            
            print(f"✅ Discussion workflow completed in {workflow_duration:.2f}s")
            print(f"📊 Safety mechanisms: {sum(safety_analysis.values())}/6 checks passed")
            
        except Exception as e:
            self.results['workflows_tested']['discussion'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            print(f"❌ Discussion workflow test failed: {str(e)}")
            
    async def _phase_4_test_post_spin_workflow(self):
        """Phase 4: Test post-spin workflow improvements."""
        print("\n🎯 Phase 4: Testing post-spin workflow improvements")
        
        phase_start = datetime.now()
        
        try:
            # Test post-spin workflow with safety mechanisms
            context_packet = {
                'text': 'I selected the meditation activity',
                'metadata': {
                    'activity_id': 'test_activity_123',
                    'activity_name': 'Mindful Meditation',
                    'test_scenario': 'quality_improvements'
                },
                'original_content': {
                    'activity_tailored_id': 'tailored_123',
                    'name': 'Mindful Meditation'
                },
                'user_ws_session_name': f"test_session_{self.test_id}"
            }
            
            print("🚀 Running post-spin workflow...")
            workflow_start = datetime.now()
            
            result = await run_post_spin_workflow(
                user_profile_id=self.test_profile_id,
                context_packet=context_packet,
                workflow_id=str(uuid.uuid4())  # Use proper UUID
            )
            
            workflow_duration = (datetime.now() - workflow_start).total_seconds()
            
            # Analyze results for safety mechanisms
            safety_analysis = {
                'completed_successfully': result.get('completed', False),
                'execution_time': workflow_duration,
                'has_activity_info': bool(result.get('output_data', {}).get('activity_info')),
                'error_handling': result.get('error') is None,
                'iteration_tracking': 'iteration_count' in str(result),
                'emergency_handling': 'emergency_escalation_count' in str(result)
            }
            
            self.results['workflows_tested']['post_spin'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'workflow_duration': workflow_duration,
                'safety_analysis': safety_analysis,
                'result_summary': {
                    'completed': result.get('completed'),
                    'has_activity': safety_analysis['has_activity_info'],
                    'error': result.get('error')
                }
            }
            
            print(f"✅ Post-spin workflow completed in {workflow_duration:.2f}s")
            print(f"📊 Safety mechanisms: {sum(safety_analysis.values())}/6 checks passed")
            
        except Exception as e:
            self.results['workflows_tested']['post_spin'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            print(f"❌ Post-spin workflow test failed: {str(e)}")

    async def _phase_5_validate_safety_mechanisms(self):
        """Phase 5: Validate safety mechanisms across all workflows."""
        print("\n🛡️ Phase 5: Validating safety mechanisms")

        phase_start = datetime.now()

        try:
            # Analyze safety mechanisms from all workflow tests
            safety_summary = {
                'wheel_generation': self.results['workflows_tested'].get('wheel_generation', {}).get('safety_analysis', {}),
                'discussion': self.results['workflows_tested'].get('discussion', {}).get('safety_analysis', {}),
                'post_spin': self.results['workflows_tested'].get('post_spin', {}).get('safety_analysis', {})
            }

            # Calculate overall safety score
            total_checks = 0
            passed_checks = 0

            for workflow, analysis in safety_summary.items():
                if analysis:
                    workflow_checks = len(analysis)
                    workflow_passed = sum(analysis.values())
                    total_checks += workflow_checks
                    passed_checks += workflow_passed

                    print(f"📊 {workflow}: {workflow_passed}/{workflow_checks} safety checks passed")

            overall_safety_score = (passed_checks / total_checks * 100) if total_checks > 0 else 0

            self.results['safety_mechanisms'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'overall_safety_score': overall_safety_score,
                'total_checks': total_checks,
                'passed_checks': passed_checks,
                'workflow_analysis': safety_summary,
                'safety_grade': 'A' if overall_safety_score >= 90 else 'B' if overall_safety_score >= 80 else 'C' if overall_safety_score >= 70 else 'D'
            }

            print(f"✅ Overall safety score: {overall_safety_score:.1f}% (Grade: {self.results['safety_mechanisms']['safety_grade']})")

        except Exception as e:
            self.results['safety_mechanisms'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            print(f"❌ Safety mechanisms validation failed: {str(e)}")

    async def _phase_6_quality_assessment(self):
        """Phase 6: Generate quality assessment and recommendations."""
        print("\n🎯 Phase 6: Quality assessment and recommendations")

        phase_start = datetime.now()

        try:
            recommendations = []

            # Analyze each workflow's performance
            for workflow_name, workflow_data in self.results['workflows_tested'].items():
                if workflow_name == 'setup':
                    continue

                if workflow_data.get('status') == 'error':
                    recommendations.append({
                        'category': 'workflow_stability',
                        'priority': 'high',
                        'workflow': workflow_name,
                        'issue': f"{workflow_name} workflow failed during testing",
                        'recommendation': f"Debug and fix {workflow_name} workflow execution issues",
                        'implementation': f"Review error logs and fix underlying issues in {workflow_name}_graph.py"
                    })

                safety_analysis = workflow_data.get('safety_analysis', {})
                failed_checks = [check for check, passed in safety_analysis.items() if not passed]

                if failed_checks:
                    recommendations.append({
                        'category': 'safety_mechanisms',
                        'priority': 'medium',
                        'workflow': workflow_name,
                        'issue': f"Safety checks failed: {', '.join(failed_checks)}",
                        'recommendation': f"Implement missing safety mechanisms in {workflow_name} workflow",
                        'implementation': f"Add {', '.join(failed_checks)} to {workflow_name}_graph.py"
                    })

                # Performance analysis
                workflow_duration = workflow_data.get('workflow_duration', 0)
                if workflow_duration > 60:  # More than 1 minute
                    recommendations.append({
                        'category': 'performance',
                        'priority': 'low',
                        'workflow': workflow_name,
                        'issue': f"Slow execution time: {workflow_duration:.2f}s",
                        'recommendation': f"Optimize {workflow_name} workflow performance",
                        'implementation': f"Review agent instructions and tool call efficiency in {workflow_name}"
                    })

            # Overall safety assessment
            safety_score = self.results['safety_mechanisms'].get('overall_safety_score', 0)
            if safety_score < 90:
                recommendations.append({
                    'category': 'overall_safety',
                    'priority': 'high' if safety_score < 70 else 'medium',
                    'workflow': 'all',
                    'issue': f"Overall safety score below target: {safety_score:.1f}%",
                    'recommendation': "Implement comprehensive safety mechanisms across all workflows",
                    'implementation': "Apply onboarding workflow patterns to all workflow graphs"
                })

            # Success assessment
            if not recommendations:
                recommendations.append({
                    'category': 'success',
                    'priority': 'info',
                    'workflow': 'all',
                    'issue': 'No significant issues detected',
                    'recommendation': 'Continue monitoring workflow quality and performance',
                    'implementation': 'Regular quality testing and performance monitoring'
                })

            self.results['quality_assessment'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'recommendations': recommendations,
                'overall_grade': self.results['safety_mechanisms'].get('safety_grade', 'F'),
                'improvement_areas': len([r for r in recommendations if r['priority'] in ['high', 'medium']]),
                'success_indicators': len([r for r in recommendations if r['category'] == 'success'])
            }

            print(f"✅ Quality assessment completed")
            print(f"📋 Generated {len(recommendations)} recommendations")
            print(f"🎯 Overall grade: {self.results['quality_assessment']['overall_grade']}")

        except Exception as e:
            self.results['quality_assessment'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            print(f"❌ Quality assessment failed: {str(e)}")

    async def _save_results(self):
        """Save test results to file."""
        try:
            # Ensure results directory exists
            results_dir = "/usr/src/app/real_condition_tests/results"
            os.makedirs(results_dir, exist_ok=True)

            # Save results
            filename = f"workflow_quality_improvements_{self.test_id}.json"
            filepath = os.path.join(results_dir, filename)

            with open(filepath, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)

            print(f"💾 Results saved to: {filepath}")

        except Exception as e:
            print(f"❌ Failed to save results: {str(e)}")


async def main():
    """Run the workflow quality improvements test."""
    test = WorkflowQualityImprovementsTest()
    await test.run_complete_test()


if __name__ == "__main__":
    asyncio.run(main())
