#!/usr/bin/env python3
"""
Profile Completion Architecture Baseline Test

This test captures the current behavior of the profile completion system
before architectural refactoring to ensure no regression during changes.
"""

import os
import sys
import django
import asyncio
import logging
from typing import Dict, Any

# Add the backend directory to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.services.mentor_service import MentorService
from apps.main.graphs.profile_completion_graph import run_profile_completion_workflow
from apps.user.models import UserProfile
from apps.main.models import GenericAgent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProfileCompletionArchitectureBaseline:
    """Test suite to capture current profile completion architecture behavior."""
    
    def __init__(self):
        self.test_user_id = None
        self.results = {
            'mentor_agent_tools': [],
            'mentor_agent_instructions': '',
            'conversation_dispatcher_capabilities': {},
            'mentor_service_state': {},
            'profile_completion_workflow_behavior': {},
            'end_to_end_flow': {}
        }
    
    async def run_all_tests(self):
        """Run all baseline tests."""
        logger.info("🚀 Starting Profile Completion Architecture Baseline Tests")
        
        try:
            # Setup test user
            await self.setup_test_user()
            
            # Test 1: Analyze Mentor Agent Configuration
            await self.test_mentor_agent_configuration()
            
            # Test 2: Test ConversationDispatcher Capabilities
            await self.test_conversation_dispatcher_capabilities()
            
            # Test 3: Test MentorService State Management
            await self.test_mentor_service_state()
            
            # Test 4: Test Profile Completion Workflow
            await self.test_profile_completion_workflow()
            
            # Test 5: Test End-to-End Flow
            await self.test_end_to_end_flow()
            
            # Generate report
            self.generate_baseline_report()
            
        except Exception as e:
            logger.error(f"❌ Baseline test failed: {str(e)}", exc_info=True)
            return False
        
        logger.info("✅ Profile Completion Architecture Baseline Tests Completed")
        return True
    
    async def setup_test_user(self):
        """Create a test user for baseline testing."""
        from django.contrib.auth.models import User
        from apps.user.models import UserProfile
        from channels.db import database_sync_to_async

        @database_sync_to_async
        def create_test_user():
            # Create test user
            test_user, created = User.objects.get_or_create(
                username='baseline_test_user',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Baseline',
                    'last_name': 'Test'
                }
            )

            # Create user profile
            user_profile, created = UserProfile.objects.get_or_create(
                user=test_user,
                defaults={
                    'profile_name': 'Baseline Test User',
                    'is_real': False  # Mark as test profile
                }
            )

            return str(user_profile.id)

        self.test_user_id = await create_test_user()
        logger.info(f"📝 Test user created: {self.test_user_id}")
    
    async def test_mentor_agent_configuration(self):
        """Test current Mentor Agent configuration."""
        logger.info("🔍 Testing Mentor Agent Configuration")

        try:
            from channels.db import database_sync_to_async

            @database_sync_to_async
            def get_mentor_agent_config():
                # Get mentor agent from database
                mentor_agent = GenericAgent.objects.filter(role='mentor', is_active=True).first()

                if mentor_agent:
                    # Capture current configuration
                    tools = [
                        {
                            'code': tool.code,
                            'name': tool.name,
                            'description': tool.description
                        }
                        for tool in mentor_agent.available_tools.all()
                    ]

                    return {
                        'tools': tools,
                        'instructions': mentor_agent.system_instructions,
                        'found': True
                    }
                else:
                    return {'found': False}

            config = await get_mentor_agent_config()

            if config['found']:
                self.results['mentor_agent_tools'] = config['tools']
                self.results['mentor_agent_instructions'] = config['instructions']

                logger.info(f"📊 Mentor Agent has {len(self.results['mentor_agent_tools'])} tools")
                logger.info(f"📊 System instructions length: {len(self.results['mentor_agent_instructions'])} chars")

                # Test tool loading in actual agent
                from apps.main.agents.mentor_agent import MentorAgent
                agent = MentorAgent(user_profile_id=self.test_user_id)

                # Load definition and tools
                success = await agent.load_definition_and_tools()

                if success:
                    self.results['mentor_agent_tools_loaded'] = len(agent.available_tools)
                    logger.info(f"✅ Agent loaded {len(agent.available_tools)} tools successfully")
                else:
                    logger.warning("⚠️ Agent failed to load tools")

            else:
                logger.error("❌ No active mentor agent found in database")

        except Exception as e:
            logger.error(f"❌ Mentor Agent configuration test failed: {str(e)}")
    
    async def test_conversation_dispatcher_capabilities(self):
        """Test ConversationDispatcher current capabilities."""
        logger.info("🔍 Testing ConversationDispatcher Capabilities")
        
        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_id,
                user_ws_session_name='baseline_test_session'
            )
            
            # Test profile completion check
            profile_completion = await dispatcher._check_profile_completion()
            self.results['conversation_dispatcher_capabilities']['profile_completion_check'] = profile_completion
            
            # Test message classification
            test_message = {
                'text': 'I need help with activities',
                'metadata': {}
            }
            
            context = await dispatcher._extract_message_context(test_message)
            classification = await dispatcher._classify_message(test_message, context, profile_completion)
            
            self.results['conversation_dispatcher_capabilities']['message_classification'] = classification
            self.results['conversation_dispatcher_capabilities']['context_extraction'] = context
            
            logger.info(f"📊 Profile completion: {profile_completion:.2%}")
            logger.info(f"📊 Message classified as: {classification.get('workflow_type', 'unknown')}")
            
        except Exception as e:
            logger.error(f"❌ ConversationDispatcher test failed: {str(e)}")
    
    async def test_mentor_service_state(self):
        """Test MentorService state management."""
        logger.info("🔍 Testing MentorService State Management")
        
        try:
            mentor_service = MentorService.get_instance(
                user_profile_id=self.test_user_id,
                session_id='baseline_test_session'
            )
            
            # Test message processing
            test_message = {
                'text': 'Hello, I need some guidance',
                'metadata': {}
            }
            
            enhanced_message = await mentor_service.process_incoming_message(test_message)
            
            self.results['mentor_service_state'] = {
                'state_summary': mentor_service.get_state_summary(),
                'enhanced_message_keys': list(enhanced_message.keys()),
                'has_mentor_context': 'mentor_context' in enhanced_message
            }
            
            logger.info(f"📊 MentorService state: {mentor_service.get_state_summary()}")
            logger.info(f"📊 Enhanced message has mentor context: {'mentor_context' in enhanced_message}")
            
        except Exception as e:
            logger.error(f"❌ MentorService test failed: {str(e)}")
    
    async def test_profile_completion_workflow(self):
        """Test profile completion workflow behavior."""
        logger.info("🔍 Testing Profile Completion Workflow")
        
        try:
            # Test workflow execution
            initial_input = {
                'text': 'I am a student and I need help organizing my studies',
                'user_ws_session_name': 'baseline_test_session'
            }
            
            result = await run_profile_completion_workflow(
                user_profile_id=self.test_user_id,
                initial_input=initial_input
            )
            
            self.results['profile_completion_workflow_behavior'] = {
                'completed': result.get('completed', False),
                'output_data_keys': list(result.get('output_data', {}).keys()),
                'conversation_state_updates': result.get('conversation_state_updates', {}),
                'iteration_count': result.get('iteration_count', 0),
                'profile_completion_stage': result.get('profile_completion_stage', 'unknown')
            }
            
            logger.info(f"📊 Workflow completed: {result.get('completed', False)}")
            logger.info(f"📊 Iterations: {result.get('iteration_count', 0)}")
            logger.info(f"📊 Final stage: {result.get('profile_completion_stage', 'unknown')}")
            
        except Exception as e:
            logger.error(f"❌ Profile completion workflow test failed: {str(e)}")
    
    async def test_end_to_end_flow(self):
        """Test complete end-to-end flow."""
        logger.info("🔍 Testing End-to-End Flow")
        
        try:
            # Initialize dispatcher
            dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_id,
                user_ws_session_name='baseline_test_session'
            )
            
            # Process a typical user message
            user_message = {
                'text': 'Hi, I am feeling overwhelmed with my studies and need some activities to help me focus',
                'metadata': {}
            }
            
            response = await dispatcher.process_message(user_message)
            
            self.results['end_to_end_flow'] = {
                'workflow_type': response.get('workflow_type', 'unknown'),
                'confidence': response.get('confidence', 0.0),
                'status': response.get('status', 'unknown'),
                'has_context_packet': 'context_packet' in response,
                'estimated_completion_time': response.get('estimated_completion_time', 0)
            }
            
            logger.info(f"📊 End-to-end flow result: {response.get('workflow_type', 'unknown')} workflow")
            logger.info(f"📊 Confidence: {response.get('confidence', 0.0):.2f}")
            logger.info(f"📊 Status: {response.get('status', 'unknown')}")
            
        except Exception as e:
            logger.error(f"❌ End-to-end flow test failed: {str(e)}")
    
    def generate_baseline_report(self):
        """Generate comprehensive baseline report."""
        logger.info("📋 Generating Baseline Report")
        
        report = f"""
# Profile Completion Architecture Baseline Report

## Test Results Summary

### Mentor Agent Configuration
- **Tools Available**: {len(self.results['mentor_agent_tools'])} tools
- **System Instructions Length**: {len(self.results['mentor_agent_instructions'])} characters
- **Tools Successfully Loaded**: {self.results.get('mentor_agent_tools_loaded', 'N/A')}

### Top Mentor Agent Tools:
"""
        
        for i, tool in enumerate(self.results['mentor_agent_tools'][:10]):
            report += f"  {i+1}. {tool['name']} ({tool['code']})\n"
        
        report += f"""
### ConversationDispatcher Capabilities
- **Profile Completion Check**: {self.results['conversation_dispatcher_capabilities'].get('profile_completion_check', 'N/A'):.2%}
- **Message Classification**: {self.results['conversation_dispatcher_capabilities'].get('message_classification', {}).get('workflow_type', 'N/A')}
- **Classification Confidence**: {self.results['conversation_dispatcher_capabilities'].get('message_classification', {}).get('confidence', 0.0):.2f}

### MentorService State Management
- **Has LLM Client**: {self.results['mentor_service_state'].get('state_summary', {}).get('has_llm_client', False)}
- **Trust Level**: {self.results['mentor_service_state'].get('state_summary', {}).get('trust_level', 0.0)}
- **Enhanced Message Processing**: {self.results['mentor_service_state'].get('has_mentor_context', False)}

### Profile Completion Workflow
- **Workflow Completed**: {self.results['profile_completion_workflow_behavior'].get('completed', False)}
- **Iteration Count**: {self.results['profile_completion_workflow_behavior'].get('iteration_count', 0)}
- **Final Stage**: {self.results['profile_completion_workflow_behavior'].get('profile_completion_stage', 'unknown')}

### End-to-End Flow
- **Workflow Type**: {self.results['end_to_end_flow'].get('workflow_type', 'unknown')}
- **Confidence**: {self.results['end_to_end_flow'].get('confidence', 0.0):.2f}
- **Status**: {self.results['end_to_end_flow'].get('status', 'unknown')}

## Architecture Analysis

### Current Strengths
1. Mentor Agent has comprehensive tool suite ({len(self.results['mentor_agent_tools'])} tools)
2. ConversationDispatcher provides basic message classification
3. MentorService implements singleton pattern correctly
4. Profile completion workflow has safety mechanisms

### Identified Issues
1. Mentor Agent has too many responsibilities (business logic + conversation)
2. ConversationDispatcher underutilized for intelligent routing
3. Profile completion workflow mixes conversation and data processing
4. Complex tool management in Mentor Agent

### Refactoring Opportunities
1. Move business logic from Mentor Agent to ConversationDispatcher
2. Implement runtime tool injection for Mentor Agent
3. Separate data processing from conversation in profile completion
4. Enhance MentorService for contextual coordination

---
Generated: {asyncio.get_event_loop().time()}
Test User ID: {self.test_user_id}
"""
        
        # Save report
        with open('/usr/src/app/real_condition_tests/BASELINE_REPORT.md', 'w') as f:
            f.write(report)
        
        logger.info("📋 Baseline report saved to BASELINE_REPORT.md")
        print(report)

async def main():
    """Main test execution."""
    baseline_test = ProfileCompletionArchitectureBaseline()
    success = await baseline_test.run_all_tests()
    
    if success:
        print("\n✅ All baseline tests completed successfully!")
        return 0
    else:
        print("\n❌ Some baseline tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
