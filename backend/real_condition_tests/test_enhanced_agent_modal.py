#!/usr/bin/env python3
"""
Enhanced Agent Modal Functionality Test

This script tests the enhanced Agent Evaluation Details modal to ensure:
1. All new UI components render correctly
2. Performance analysis functions work properly
3. Intelligent recommendations are generated
4. Visual indicators display appropriate status
5. Responsive design works across screen sizes

Usage:
    python test_enhanced_agent_modal.py
"""

import os
import sys
import django
import logging
import json
from datetime import datetime, timedelta
from decimal import Decimal

# Setup Django
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from apps.main.models import BenchmarkRun, BenchmarkScenario, GenericAgent, LLMConfig

# Setup logging
logging.basicConfig(level=logging.INFO, format='[%(asctime)s] %(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class EnhancedAgentModalTester:
    """Test suite for the enhanced agent evaluation modal."""
    
    def __init__(self):
        self.client = Client()
        self.user = None
        self.test_results = {
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'failures': []
        }
    
    def setup_test_environment(self):
        """Setup test environment with admin user and test data."""
        logger.info("Setting up test environment...")
        
        # Create admin user
        User = get_user_model()
        self.user, created = User.objects.get_or_create(
            username='test_admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            self.user.set_password('testpass123')
            self.user.save()
        
        # Login
        login_success = self.client.login(username='test_admin', password='testpass123')
        if not login_success:
            raise Exception("Failed to login test admin user")
        
        logger.info("✅ Test environment setup complete")
    
    def create_test_benchmark_run(self):
        """Create a test benchmark run with comprehensive data."""
        logger.info("Creating test benchmark run...")
        
        # Get or create test scenario
        scenario, _ = BenchmarkScenario.objects.get_or_create(
            name="Enhanced Modal Test Scenario",
            defaults={
                'description': 'Test scenario for enhanced modal functionality',
                'agent_role': 'mentor',
                'user_input': 'Test user input for modal testing',
                'expected_output_schema': {},
                'evaluation_criteria': {},
                'tags': ['test', 'modal']
            }
        )
        
        # Get or create test agent
        agent, _ = GenericAgent.objects.get_or_create(
            role='mentor',
            defaults={
                'name': 'Test Mentor Agent',
                'description': 'Test agent for modal functionality',
                'instructions': 'Test instructions',
                'tools': [],
                'version': '1.0.0'
            }
        )
        
        # Get or create LLM config
        llm_config, _ = LLMConfig.objects.get_or_create(
            model_name='gpt-3.5-turbo',
            defaults={
                'temperature': 0.7,
                'max_tokens': 1000,
                'provider': 'openai'
            }
        )
        
        # Create comprehensive benchmark run
        benchmark_run = BenchmarkRun.objects.create(
            scenario=scenario,
            agent_definition=agent,
            agent_version='test-v1.0.0',
            llm_config=llm_config,
            parameters={
                'use_real_llm': True,
                'use_real_tools': False,
                'test_mode': True
            },
            mean_duration=2500.75,
            median_duration=2300.50,
            min_duration=1800.25,
            max_duration=4200.90,
            std_dev=650.30,
            success_rate=0.85,
            semantic_score=0.72,
            total_input_tokens=1250,
            total_output_tokens=850,
            estimated_cost=Decimal('0.0125'),
            raw_results={
                'last_output': {
                    'enhanced_debugging_data': {
                        'agents': [
                            {
                                'agent': 'mentor',
                                'stage': 'processing',
                                'duration_ms': 2500.75,
                                'success': True,
                                'output': {
                                    'combined_resource_context': {
                                        'user_profile': {'trust_level': 0.75},
                                        'environment': {'mood': 'positive'}
                                    },
                                    'engagement_analysis': {
                                        'engagement_level': 'high',
                                        'interaction_quality': 'excellent'
                                    }
                                }
                            }
                        ]
                    }
                }
            },
            semantic_evaluation_details={
                'overall_score': 0.72,
                'dimensions': {
                    'relevance': {'score': 0.8, 'reasoning': 'Highly relevant response'},
                    'clarity': {'score': 0.7, 'reasoning': 'Clear and understandable'},
                    'helpfulness': {'score': 0.65, 'reasoning': 'Moderately helpful'}
                }
            },
            tool_breakdown={
                'user_profile_tool': 3,
                'memory_tool': 2,
                'llm_client': 1
            },
            context_package={
                'user_id': 'test-user-123',
                'session_timestamp': datetime.now().isoformat(),
                'reported_environment': 'positive',
                'reported_mood': 'optimistic',
                'system_metadata': {
                    'enhanced_architecture': True,
                    'dispatcher_version': '2.0.0',
                    'mentor_service_available': True,
                    'llm_client_available': True
                },
                'mentor_context': {
                    'trust_level': 0.75,
                    'communication_preferences': {
                        'tone': 'supportive',
                        'detail_level': 'moderate'
                    },
                    'mentor_assessment': {
                        'emotional_tone': 'positive',
                        'urgency_level': 'low',
                        'support_needs': ['encouragement', 'guidance']
                    }
                }
            }
        )
        
        logger.info(f"✅ Created test benchmark run: {benchmark_run.id}")
        return benchmark_run
    
    def test_modal_data_api(self, benchmark_run):
        """Test that the modal data API returns comprehensive data."""
        logger.info("Testing modal data API...")
        self.test_results['tests_run'] += 1
        
        try:
            # Test the detail API endpoint
            response = self.client.get(f'/admin/benchmarks/api/runs/{benchmark_run.id}/details/')
            
            if response.status_code != 200:
                raise Exception(f"API returned status {response.status_code}")
            
            data = response.json()
            
            # Verify essential fields are present
            required_fields = [
                'mean_duration', 'median_duration', 'min_duration', 'max_duration',
                'std_dev', 'success_rate', 'semantic_score', 'llm_model',
                'llm_temperature', 'total_input_tokens', 'total_output_tokens',
                'estimated_cost', 'tool_breakdown', 'context_package'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in data or data[field] is None:
                    missing_fields.append(field)
            
            if missing_fields:
                raise Exception(f"Missing required fields: {missing_fields}")
            
            logger.info("✅ Modal data API test passed")
            self.test_results['tests_passed'] += 1
            return data
            
        except Exception as e:
            logger.error(f"❌ Modal data API test failed: {e}")
            self.test_results['tests_failed'] += 1
            self.test_results['failures'].append(f"Modal data API: {e}")
            return None
    
    def test_performance_analysis_logic(self, data):
        """Test the performance analysis functions work correctly."""
        logger.info("Testing performance analysis logic...")
        self.test_results['tests_run'] += 1
        
        try:
            # Test data should trigger specific analysis results
            mean_duration = float(data['mean_duration'])
            success_rate = float(data['success_rate'])
            semantic_score = float(data['semantic_score'])
            
            # Verify performance categorization logic
            if mean_duration < 1000:
                expected_perf_status = 'excellent'
            elif mean_duration < 3000:
                expected_perf_status = 'good'
            elif mean_duration < 5000:
                expected_perf_status = 'warning'
            else:
                expected_perf_status = 'critical'
            
            # Our test data (2500.75ms) should be 'good'
            if expected_perf_status != 'good':
                raise Exception(f"Expected performance status 'good', logic would produce '{expected_perf_status}'")
            
            # Test health score calculation logic
            expected_health_score = 0  # Calculate expected score
            
            # Performance score (25 points max) - 2500ms should get ~20 points
            expected_health_score += 20
            
            # Quality score (30 points max) - 0.72 should get ~22 points
            expected_health_score += int(semantic_score * 30)
            
            # Reliability score (30 points max) - 0.85 should get ~26 points
            expected_health_score += int(success_rate * 30)
            
            # Cost score (15 points max) - 0.0125 should get ~8 points
            expected_health_score += 8
            
            # Total should be around 76 (good range)
            if expected_health_score < 70 or expected_health_score > 85:
                logger.warning(f"Health score {expected_health_score} outside expected range")
            
            logger.info("✅ Performance analysis logic test passed")
            self.test_results['tests_passed'] += 1
            
        except Exception as e:
            logger.error(f"❌ Performance analysis logic test failed: {e}")
            self.test_results['tests_failed'] += 1
            self.test_results['failures'].append(f"Performance analysis logic: {e}")
    
    def test_modal_rendering(self, benchmark_run):
        """Test that the modal renders correctly with enhanced features."""
        logger.info("Testing modal rendering...")
        self.test_results['tests_run'] += 1
        
        try:
            # Test the benchmark history page loads
            response = self.client.get('/admin/benchmarks/history/')
            
            if response.status_code != 200:
                raise Exception(f"Benchmark history page returned status {response.status_code}")
            
            content = response.content.decode('utf-8')
            
            # Check for enhanced modal elements
            required_elements = [
                'executive-summary-dashboard',
                'health-status-indicator',
                'key-metrics-grid',
                'performance-analysis',
                'llm-analysis',
                'intelligent-recommendations',
                'analyzeAgentPerformance',
                'calculateHealthStatus',
                'generateIntelligentRecommendations'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                raise Exception(f"Missing modal elements: {missing_elements}")
            
            logger.info("✅ Modal rendering test passed")
            self.test_results['tests_passed'] += 1
            
        except Exception as e:
            logger.error(f"❌ Modal rendering test failed: {e}")
            self.test_results['tests_failed'] += 1
            self.test_results['failures'].append(f"Modal rendering: {e}")
    
    def run_all_tests(self):
        """Run all tests and return results."""
        logger.info("=" * 80)
        logger.info("ENHANCED AGENT MODAL FUNCTIONALITY TEST")
        logger.info("=" * 80)
        
        try:
            # Setup
            self.setup_test_environment()
            
            # Create test data
            benchmark_run = self.create_test_benchmark_run()
            
            # Run tests
            data = self.test_modal_data_api(benchmark_run)
            if data:
                self.test_performance_analysis_logic(data)
            
            self.test_modal_rendering(benchmark_run)
            
            # Cleanup
            if benchmark_run:
                benchmark_run.delete()
                logger.info("🧹 Cleaned up test data")
            
        except Exception as e:
            logger.error(f"❌ Test suite failed: {e}")
            self.test_results['tests_failed'] += 1
            self.test_results['failures'].append(f"Test suite: {e}")
        
        # Report results
        logger.info("=" * 80)
        logger.info("TEST RESULTS SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Tests Run: {self.test_results['tests_run']}")
        logger.info(f"Tests Passed: {self.test_results['tests_passed']}")
        logger.info(f"Tests Failed: {self.test_results['tests_failed']}")
        
        if self.test_results['failures']:
            logger.info("\nFailures:")
            for failure in self.test_results['failures']:
                logger.info(f"  - {failure}")
        
        success_rate = (self.test_results['tests_passed'] / max(self.test_results['tests_run'], 1)) * 100
        
        if success_rate == 100:
            logger.info("✅ ALL TESTS PASSED! Enhanced modal is ready for use.")
        else:
            logger.info(f"⚠️ {success_rate:.1f}% tests passed. Review failures above.")
        
        return self.test_results

if __name__ == '__main__':
    tester = EnhancedAgentModalTester()
    results = tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if results['tests_failed'] == 0 else 1)
