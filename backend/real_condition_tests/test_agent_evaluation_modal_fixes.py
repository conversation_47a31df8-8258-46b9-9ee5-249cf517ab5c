#!/usr/bin/env python3
"""
Agent Evaluation Modal Fixes Validation Test

This test validates that all the fixes for the agent evaluation modal are working correctly:
1. LLM interactions are properly detected and displayed
2. Agent output is clearly visible and user-friendly
3. Semantic evaluation details are properly explained
4. Tool usage analysis works correctly
5. Enhanced debugging data is properly rendered

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_agent_evaluation_modal_fixes.py
"""

import os
import sys
import json
import asyncio
import django
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.models import BenchmarkRun
from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from django.contrib.auth.models import User
from django.test import RequestFactory
from django.http import JsonResponse
from asgiref.sync import sync_to_async

class AgentEvaluationModalFixesTest:
    def __init__(self):
        self.factory = RequestFactory()
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'test_name': 'Agent Evaluation Modal Fixes Validation',
            'tests': [],
            'summary': {
                'total': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }

    def log_test(self, test_name, passed, details=None, error=None):
        """Log test result"""
        test_result = {
            'name': test_name,
            'passed': passed,
            'details': details or {},
            'error': str(error) if error else None,
            'timestamp': datetime.now().isoformat()
        }
        
        self.test_results['tests'].append(test_result)
        self.test_results['summary']['total'] += 1
        
        if passed:
            self.test_results['summary']['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.test_results['summary']['failed'] += 1
            print(f"❌ {test_name}")
            if error:
                print(f"   Error: {error}")
                self.test_results['summary']['errors'].append(str(error))

    async def test_quick_benchmark_execution(self):
        """Test that quick benchmark can be executed and produces data for modal testing"""
        try:
            print("\n🧪 Testing Quick Benchmark Execution...")
            
            # Create a test user if needed
            user, created = await sync_to_async(User.objects.get_or_create)(
                username='test_modal_user',
                defaults={'email': '<EMAIL>'}
            )

            # Get available options
            service = QuickBenchmarkService()
            options = await sync_to_async(service.get_available_options)()
            
            self.log_test(
                "Quick Benchmark Options Available",
                bool(options.get('available_agents') and options.get('user_profiles')),
                {'agents_count': len(options.get('available_agents', [])),
                 'profiles_count': len(options.get('user_profiles', []))}
            )
            
            # Find a suitable agent and profile
            agents = options.get('available_agents', [])
            profiles = options.get('user_profiles', [])
            
            if not agents or not profiles:
                raise Exception("No agents or profiles available for testing")
            
            # Use mentor agent if available, otherwise first agent
            test_agent = next((a for a in agents if a['role'] == 'mentor'), agents[0])
            test_profile = profiles[0]
            
            # Execute benchmark
            benchmark_data = {
                'agent_name': test_agent['role'],
                'user_profile_id': test_profile['id'],
                'evaluation_template': 'mentor_agent_evaluation',
                'scenario_context': {
                    'user_input': 'Hello, I need help with my goals'
                }
            }
            
            print(f"   Executing benchmark with agent: {test_agent['role']}")
            benchmark_run = await sync_to_async(service.run_quick_benchmark_sync)(
                agent_name=test_agent['role'],
                user_profile_id=test_profile['id'],
                evaluation_template='mentor_agent_evaluation',
                scenario_context=benchmark_data['scenario_context']
            )

            self.log_test(
                "Quick Benchmark Execution",
                benchmark_run is not None,
                {'benchmark_run_id': str(benchmark_run.id) if benchmark_run else None,
                 'execution_time': benchmark_run.mean_duration if benchmark_run else None,
                 'agent_role': benchmark_run.agent_definition.role if benchmark_run and benchmark_run.agent_definition else None}
            )

            return benchmark_run.id if benchmark_run else None
            
        except Exception as e:
            self.log_test("Quick Benchmark Execution", False, error=e)
            return None

    async def test_benchmark_data_structure(self, run_id):
        """Test that benchmark run data has the expected structure for modal display"""
        try:
            print(f"\n🔍 Testing Benchmark Data Structure for run {run_id}...")
            
            # Get benchmark run data
            benchmark_run = await BenchmarkRun.objects.select_related().aget(id=run_id)
            
            # Test basic data presence
            has_basic_data = all([
                benchmark_run.agent_definition and benchmark_run.agent_definition.role,
                benchmark_run.execution_date,
                benchmark_run.mean_duration is not None,
                benchmark_run.success_rate is not None
            ])

            self.log_test(
                "Basic Benchmark Data Present",
                has_basic_data,
                {'agent_role': benchmark_run.agent_definition.role if benchmark_run.agent_definition else None,
                 'execution_date': str(benchmark_run.execution_date),
                 'mean_duration': benchmark_run.mean_duration}
            )
            
            # Test enhanced debugging data
            has_enhanced_data = bool(benchmark_run.raw_results and 
                                   benchmark_run.raw_results.get('enhanced_debugging_data'))
            
            self.log_test(
                "Enhanced Debugging Data Present",
                has_enhanced_data,
                {'enhanced_debugging_enabled': has_enhanced_data}
            )
            
            # Test LLM interactions data
            llm_interactions = []
            if benchmark_run.raw_results:
                # Check multiple locations for LLM interactions
                if benchmark_run.raw_results.get('enhanced_debugging_data', {}).get('llm_interactions'):
                    llm_interactions = benchmark_run.raw_results['enhanced_debugging_data']['llm_interactions']
                elif benchmark_run.raw_results.get('llm_interactions'):
                    llm_interactions = benchmark_run.raw_results['llm_interactions']
            
            self.log_test(
                "LLM Interactions Data Available",
                len(llm_interactions) > 0,
                {'llm_interactions_count': len(llm_interactions),
                 'total_input_tokens': benchmark_run.total_input_tokens,
                 'total_output_tokens': benchmark_run.total_output_tokens}
            )
            
            # Test agent output data
            agent_output = None
            if benchmark_run.raw_results:
                agent_output = (benchmark_run.raw_results.get('last_output', {}).get('user_response') or
                              benchmark_run.raw_results.get('enhanced_debugging_data', {})
                              .get('agents', [{}])[0].get('output', {}).get('user_response'))
            
            self.log_test(
                "Agent Output Data Available",
                bool(agent_output),
                {'agent_output_length': len(agent_output) if agent_output else 0,
                 'agent_output_preview': agent_output[:100] + '...' if agent_output and len(agent_output) > 100 else agent_output}
            )
            
            return benchmark_run
            
        except Exception as e:
            self.log_test("Benchmark Data Structure", False, error=e)
            return None

    async def test_modal_api_endpoint(self, run_id):
        """Test that the modal API endpoint returns proper data"""
        try:
            print(f"\n🌐 Testing Modal API Endpoint for run {run_id}...")
            
            # Import the view class
            from apps.admin_tools.views import BenchmarkRunView

            # Create a mock request
            request = self.factory.get(f'/admin/benchmarks/api/run/{run_id}/')
            request.user = await sync_to_async(User.objects.get)(username='test_modal_user')

            # Call the view
            view = BenchmarkRunView()
            response = await sync_to_async(view.get)(request, run_id=str(run_id))
            
            self.log_test(
                "Modal API Endpoint Accessible",
                response.status_code == 200,
                {'status_code': response.status_code}
            )
            
            if response.status_code == 200:
                data = json.loads(response.content)
                
                # Test data structure
                required_fields = ['id', 'execution_date', 'mean_duration', 'success_rate']
                has_required_fields = all(field in data for field in required_fields)

                # Check for agent information (could be in agent_definition or agent_role)
                has_agent_info = 'agent_definition' in data or 'agent_role' in data

                self.log_test(
                    "Modal API Data Structure",
                    has_required_fields and has_agent_info,
                    {'available_fields': list(data.keys()),
                     'missing_fields': [f for f in required_fields if f not in data],
                     'has_agent_info': has_agent_info}
                )
                
                return data
            
            return None
            
        except Exception as e:
            self.log_test("Modal API Endpoint", False, error=e)
            return None

    async def test_semantic_evaluation_handling(self, benchmark_data):
        """Test that semantic evaluation is properly handled"""
        try:
            print("\n📊 Testing Semantic Evaluation Handling...")
            
            if not benchmark_data:
                self.log_test("Semantic Evaluation Handling", False, error="No benchmark data available")
                return
            
            # Check semantic score
            has_semantic_score = benchmark_data.get('semantic_score') is not None
            
            # Check semantic evaluation details
            semantic_details = benchmark_data.get('semantic_evaluation_details', {})
            has_evaluation_details = bool(semantic_details)
            
            self.log_test(
                "Semantic Score Present",
                has_semantic_score,
                {'semantic_score': benchmark_data.get('semantic_score'),
                 'evaluation_details': semantic_details}
            )
            
            # If evaluation was skipped, check that we have proper explanation
            if not has_semantic_score:
                skip_reason = semantic_details.get('notes', '')
                has_skip_explanation = bool(skip_reason)
                
                self.log_test(
                    "Semantic Evaluation Skip Explanation",
                    has_skip_explanation,
                    {'skip_reason': skip_reason}
                )
            
        except Exception as e:
            self.log_test("Semantic Evaluation Handling", False, error=e)

    def test_modal_javascript_syntax(self):
        """Test that the modal JavaScript has valid syntax"""
        try:
            print("\n🔧 Testing Modal JavaScript Syntax...")

            modal_file = '/usr/src/app/templates/admin_tools/modals/agent_evaluation_modal.html'
            
            with open(modal_file, 'r') as f:
                content = f.read()
            
            # Check for common JavaScript syntax issues
            issues = []
            
            # Check for unmatched braces
            open_braces = content.count('{')
            close_braces = content.count('}')
            if open_braces != close_braces:
                issues.append(f"Unmatched braces: {open_braces} open, {close_braces} close")
            
            # Check for unmatched parentheses in JavaScript sections
            js_start = content.find('<script>')
            js_end = content.rfind('</script>')
            if js_start != -1 and js_end != -1:
                js_content = content[js_start:js_end]
                open_parens = js_content.count('(')
                close_parens = js_content.count(')')
                if open_parens != close_parens:
                    issues.append(f"Unmatched parentheses in JS: {open_parens} open, {close_parens} close")
            
            # Check for required functions
            required_functions = [
                'renderLLMInteractions',
                'renderEnhancedToolCalls',
                'renderAgentOutputDisplay',
                'setupCopyRunDataButton'
            ]
            
            missing_functions = []
            for func in required_functions:
                if f'function {func}' not in content and f'{func} = function' not in content:
                    missing_functions.append(func)
            
            if missing_functions:
                issues.append(f"Missing functions: {missing_functions}")
            
            self.log_test(
                "Modal JavaScript Syntax",
                len(issues) == 0,
                {'issues_found': issues}
            )
            
        except Exception as e:
            self.log_test("Modal JavaScript Syntax", False, error=e)

    async def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Agent Evaluation Modal Fixes Validation Tests")
        print("=" * 60)
        
        # Test 1: JavaScript syntax
        self.test_modal_javascript_syntax()
        
        # Test 2: Execute quick benchmark
        run_id = await self.test_quick_benchmark_execution()
        
        if run_id:
            # Test 3: Validate benchmark data structure
            benchmark_run = await self.test_benchmark_data_structure(run_id)
            
            # Test 4: Test modal API endpoint
            api_data = await self.test_modal_api_endpoint(run_id)
            
            # Test 5: Test semantic evaluation handling
            await self.test_semantic_evaluation_handling(api_data)
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        summary = self.test_results['summary']
        print(f"Total Tests: {summary['total']}")
        print(f"Passed: {summary['passed']} ✅")
        print(f"Failed: {summary['failed']} ❌")
        
        if summary['errors']:
            print(f"\n🚨 Errors encountered:")
            for error in summary['errors']:
                print(f"   - {error}")
        
        # Save detailed results
        results_file = '/usr/src/app/real_condition_tests/results/agent_modal_fixes_test_results.json'
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: {results_file}")
        
        # Return success status
        return summary['failed'] == 0

async def main():
    """Main test execution"""
    test = AgentEvaluationModalFixesTest()
    success = await test.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! Agent evaluation modal fixes are working correctly.")
        return 0
    else:
        print("\n💥 Some tests failed. Please review the issues above.")
        return 1

if __name__ == '__main__':
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
