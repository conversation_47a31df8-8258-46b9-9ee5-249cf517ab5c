#!/usr/bin/env python3
"""
Agent Evaluation Debugging Test
Comprehensive test to diagnose and fix agent evaluation issues including:
- LLM interaction tracking
- Tool call visibility
- Semantic evaluation
- Form state restoration
"""

import os
import sys
import json
import time
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import RequestFactory
from django.contrib.auth.models import User
from apps.user.models import UserProfile
from apps.main.models import BenchmarkRun, GenericAgent
from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from apps.admin_tools.benchmark.views import QuickBenchmarkView

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentEvaluationDebugger:
    """Comprehensive agent evaluation debugging and improvement system."""
    
    def __init__(self):
        self.factory = RequestFactory()
        self.issues_found = []  # Initialize this attribute
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'issues_found': [],
            'fixes_applied': [],
            'recommendations': []
        }
    
    def log(self, message: str, level: str = 'INFO'):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        if level == 'ERROR':
            logger.error(message)
        else:
            logger.info(message)
    
    def run_comprehensive_test(self):
        """Run comprehensive agent evaluation debugging test."""
        self.log("🔍 Starting comprehensive agent evaluation debugging...")
        
        try:
            # Test 1: Basic benchmark execution
            self.test_basic_benchmark_execution()
            
            # Test 2: LLM interaction tracking
            self.test_llm_interaction_tracking()
            
            # Test 3: Tool call visibility
            self.test_tool_call_visibility()
            
            # Test 4: Semantic evaluation
            self.test_semantic_evaluation()
            
            # Test 5: Agent response extraction
            self.test_agent_response_extraction()
            
            # Test 6: Form state restoration
            self.test_form_state_restoration()
            
            # Test 7: Copy run data feature
            self.test_copy_run_data_feature()
            
            # Generate comprehensive report
            self.generate_report()
            
        except Exception as e:
            self.log(f"Critical error in comprehensive test: {e}", 'ERROR')
            self.results['critical_error'] = str(e)
            return False
        
        return True
    
    def test_basic_benchmark_execution(self):
        """Test basic benchmark execution and data capture."""
        self.log("Testing basic benchmark execution...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': [],
            'execution_time': 0
        }
        
        try:
            # Get a real user and agent
            user_profile = UserProfile.objects.filter(is_real=True).first()
            if not user_profile:
                user_profile = UserProfile.objects.first()
            
            agent = GenericAgent.objects.filter(is_active=True).first()
            
            if not user_profile or not agent:
                test_result['status'] = 'FAIL'
                test_result['issues'].append('No user profile or agent available')
                self.results['tests']['basic_benchmark_execution'] = test_result
                return
            
            # Execute benchmark
            start_time = time.time()
            service = QuickBenchmarkService()
            
            benchmark_run = service.run_quick_benchmark_sync(
                agent_name=agent.role,
                user_profile_id=str(user_profile.id),
                evaluation_template='mentor_helpfulness',
                scenario_context={'user_input': 'Hello, I need help with debugging'},
                use_real_tools=True,
                use_real_db=True
            )
            
            execution_time = time.time() - start_time
            test_result['execution_time'] = execution_time
            
            # Analyze results
            test_result['details'] = {
                'benchmark_run_id': benchmark_run.id,
                'agent_role': getattr(benchmark_run, 'agent_role', benchmark_run.agent_definition.role if benchmark_run.agent_definition else 'unknown'),
                'execution_time': execution_time,
                'llm_calls': benchmark_run.llm_calls,
                'tool_calls': benchmark_run.tool_calls,
                'total_input_tokens': benchmark_run.total_input_tokens,
                'total_output_tokens': benchmark_run.total_output_tokens,
                'semantic_score': benchmark_run.semantic_score,
                'has_raw_results': bool(benchmark_run.raw_results),
                'has_enhanced_debugging': bool(getattr(benchmark_run, 'enhanced_debugging_data', None))
            }
            
            # Check for issues
            if benchmark_run.llm_calls == 0:
                test_result['issues'].append('No LLM calls recorded')
                self.issues_found.append('Zero LLM calls in benchmark execution')

            if benchmark_run.tool_calls == 0:
                test_result['issues'].append('No tool calls recorded')
                self.issues_found.append('Zero tool calls in benchmark execution')

            if benchmark_run.total_input_tokens == 0 and benchmark_run.total_output_tokens == 0:
                test_result['issues'].append('No token usage recorded')
                self.issues_found.append('Zero token usage in benchmark execution')

            if benchmark_run.semantic_score is None:
                test_result['issues'].append('No semantic evaluation score')
                self.issues_found.append('Semantic evaluation failed or skipped')
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"Basic benchmark execution failed: {e}", 'ERROR')
        
        self.results['tests']['basic_benchmark_execution'] = test_result
    
    def test_llm_interaction_tracking(self):
        """Test LLM interaction tracking and visibility."""
        self.log("Testing LLM interaction tracking...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            # Check if AgentCommunicationTracker is being used
            from apps.main.services.agent_communication_tracker import AgentCommunicationTracker

            # Test tracker functionality
            tracker = AgentCommunicationTracker(workflow_id='test-workflow', enabled=True)
            
            # Simulate LLM interaction
            tracker.record_llm_interaction(
                agent="test_agent",
                model="test_model",
                prompt="Test prompt",
                response="Test response",
                token_usage={'input': 10, 'output': 20},
                temperature=0.7,
                duration_ms=100.0
            )
            
            # Export data
            exported_data = tracker.export_data()
            
            test_result['details'] = {
                'tracker_enabled': tracker.enabled,
                'llm_interactions_count': len(exported_data.get('llm_interactions', [])),
                'has_export_functionality': bool(exported_data),
                'export_data_structure': list(exported_data.keys()) if exported_data else []
            }
            
            if not exported_data.get('llm_interactions'):
                test_result['issues'].append('LLM interactions not being tracked properly')
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"LLM interaction tracking test failed: {e}", 'ERROR')
        
        self.results['tests']['llm_interaction_tracking'] = test_result
    
    def test_tool_call_visibility(self):
        """Test tool call tracking and visibility in modal."""
        self.log("Testing tool call visibility...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            # Check tool call interceptor
            from apps.main.services.tool_call_interceptor import ToolCallInterceptor
            from apps.main.services.agent_communication_tracker import AgentCommunicationTracker

            tracker = AgentCommunicationTracker(workflow_id='test-workflow', enabled=True)
            interceptor = ToolCallInterceptor(tracker)
            
            # Test tool call recording
            tracker.record_tool_call(
                tool_name="test_tool",
                tool_input={"test": "input"},
                tool_output={"test": "output"},
                execution_mode="real",
                duration_ms=50.0,
                success=True
            )
            
            exported_data = tracker.export_data()
            
            test_result['details'] = {
                'interceptor_available': bool(interceptor),
                'tool_calls_count': len(exported_data.get('tool_calls', [])),
                'has_tool_call_details': bool(exported_data.get('tool_calls')),
                'tool_call_structure': list(exported_data.get('tool_calls', [{}])[0].keys()) if exported_data.get('tool_calls') else []
            }
            
            if not exported_data.get('tool_calls'):
                test_result['issues'].append('Tool calls not being tracked properly')
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"Tool call visibility test failed: {e}", 'ERROR')
        
        self.results['tests']['tool_call_visibility'] = test_result
    
    def test_semantic_evaluation(self):
        """Test semantic evaluation functionality."""
        self.log("Testing semantic evaluation...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            from apps.main.services.simple_evaluation_adapter import SimpleEvaluationAdapter
            
            adapter = SimpleEvaluationAdapter()
            
            # Test evaluation
            evaluation_result = adapter.evaluate_with_simple_prompt_sync(
                agent_response="This is a test response for evaluation",
                evaluation_template="mentor_helpfulness",
                context={'test': 'context'}
            )
            
            test_result['details'] = {
                'adapter_available': bool(adapter),
                'evaluation_result': evaluation_result,
                'has_score': evaluation_result is not None
            }
            
            if evaluation_result is None:
                test_result['issues'].append('Semantic evaluation returning None')
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"Semantic evaluation test failed: {e}", 'ERROR')
        
        self.results['tests']['semantic_evaluation'] = test_result
    
    def test_agent_response_extraction(self):
        """Test agent response extraction from benchmark results."""
        self.log("Testing agent response extraction...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            service = QuickBenchmarkService()
            
            # Create mock benchmark run data
            mock_raw_results = {
                'last_output': {
                    'user_response': 'Test agent response',
                    'next_agent': 'engagement'
                },
                'operations': {
                    'tool_calls': {},
                    'memory_operations': 0
                }
            }
            
            # Test extraction with different data structures
            test_cases = [
                {'last_output': {'user_response': 'Test response 1'}},
                {'last_output_data': {'response_text': 'Test response 2'}},
                {'user_response': 'Test response 3'},
                {'output': 'Test response 4'}
            ]
            
            extraction_results = []
            for i, test_case in enumerate(test_cases):
                # Create a mock benchmark run
                class MockBenchmarkRun:
                    def __init__(self, raw_results):
                        self.raw_results = raw_results
                        self.id = f"test_{i}"
                
                mock_run = MockBenchmarkRun(test_case)
                extracted = service._extract_agent_response(mock_run)
                extraction_results.append({
                    'test_case': test_case,
                    'extracted': extracted,
                    'success': extracted is not None
                })
            
            test_result['details'] = {
                'extraction_results': extraction_results,
                'successful_extractions': sum(1 for r in extraction_results if r['success'])
            }
            
            if test_result['details']['successful_extractions'] == 0:
                test_result['issues'].append('Agent response extraction failing for all test cases')
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"Agent response extraction test failed: {e}", 'ERROR')
        
        self.results['tests']['agent_response_extraction'] = test_result
    
    def test_form_state_restoration(self):
        """Test form state restoration functionality."""
        self.log("Testing form state restoration...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            # This test would need to be run in a browser context
            # For now, we'll check if the JavaScript functionality exists
            
            js_file_path = '/usr/src/app/static/admin/js/quick_benchmark.js'
            
            if os.path.exists(js_file_path):
                with open(js_file_path, 'r') as f:
                    js_content = f.read()
                
                # Check for key functions
                has_save_config = 'saveCurrentConfiguration' in js_content
                has_restore_config = 'restorePreviousConfiguration' in js_content
                has_local_storage = 'localStorage' in js_content
                
                test_result['details'] = {
                    'js_file_exists': True,
                    'has_save_config': has_save_config,
                    'has_restore_config': has_restore_config,
                    'has_local_storage': has_local_storage
                }
                
                if not all([has_save_config, has_restore_config, has_local_storage]):
                    test_result['issues'].append('Missing form state restoration functionality')
                
            else:
                test_result['issues'].append('Quick benchmark JavaScript file not found')
                test_result['details'] = {'js_file_exists': False}
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"Form state restoration test failed: {e}", 'ERROR')
        
        self.results['tests']['form_state_restoration'] = test_result
    
    def test_copy_run_data_feature(self):
        """Test copy run data feature functionality."""
        self.log("Testing copy run data feature...")
        
        test_result = {
            'status': 'UNKNOWN',
            'details': {},
            'issues': []
        }
        
        try:
            # Check if the copy run data functionality exists in the modal
            modal_file_path = '/usr/src/app/templates/admin_tools/modals/agent_evaluation_modal.html'
            
            if os.path.exists(modal_file_path):
                with open(modal_file_path, 'r') as f:
                    modal_content = f.read()
                
                has_copy_button = 'copy-run-data-btn' in modal_content
                has_copy_functionality = 'Copy Run Data' in modal_content
                
                test_result['details'] = {
                    'modal_file_exists': True,
                    'has_copy_button': has_copy_button,
                    'has_copy_functionality': has_copy_functionality
                }
                
                if not has_copy_button:
                    test_result['issues'].append('Copy run data button not found in modal')
                
            else:
                test_result['issues'].append('Agent evaluation modal file not found')
                test_result['details'] = {'modal_file_exists': False}
            
            test_result['status'] = 'PASS' if not test_result['issues'] else 'ISSUES_FOUND'
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['error'] = str(e)
            self.log(f"Copy run data feature test failed: {e}", 'ERROR')
        
        self.results['tests']['copy_run_data_feature'] = test_result
    
    def generate_report(self):
        """Generate comprehensive debugging report."""
        self.log("Generating comprehensive debugging report...")
        
        # Count issues and successes
        total_tests = len(self.results['tests'])
        passed_tests = sum(1 for test in self.results['tests'].values() if test['status'] == 'PASS')
        failed_tests = sum(1 for test in self.results['tests'].values() if test['status'] == 'FAIL')
        issues_found = sum(1 for test in self.results['tests'].values() if test['status'] == 'ISSUES_FOUND')
        
        # Generate recommendations
        recommendations = []
        
        if any('Zero LLM calls' in issue for issue in self.issues_found):
            recommendations.append("Implement real LLM calls in ResourceAgent instead of hardcoded responses")
        
        if any('Zero tool calls' in issue for issue in self.issues_found):
            recommendations.append("Ensure tool call interceptor is properly set up during benchmark execution")
        
        if any('Semantic evaluation failed' in issue for issue in self.issues_found):
            recommendations.append("Fix agent response extraction to properly capture agent output for evaluation")
        
        self.results.update({
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'issues_found': issues_found,
                'overall_status': 'HEALTHY' if failed_tests == 0 and issues_found == 0 else 'NEEDS_ATTENTION'
            },
            'recommendations': recommendations
        })
        
        # Save results
        results_dir = '/usr/src/app/real_condition_tests/results'
        os.makedirs(results_dir, exist_ok=True)
        
        results_file = os.path.join(results_dir, 'agent_evaluation_debugging.json')
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        self.log(f"Results saved to: {results_file}")
        
        # Print summary
        print("\n" + "="*80)
        print("🔍 AGENT EVALUATION DEBUGGING REPORT")
        print("="*80)
        print(f"📊 Tests: {passed_tests}/{total_tests} passed, {failed_tests} failed, {issues_found} with issues")
        print(f"🎯 Overall Status: {self.results['summary']['overall_status']}")
        
        if recommendations:
            print("\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        
        print("\n📁 Detailed results saved to:", results_file)
        print("="*80)

def main():
    """Main execution function."""
    debugger = AgentEvaluationDebugger()
    success = debugger.run_comprehensive_test()
    
    if success:
        print("✅ Agent evaluation debugging completed successfully")
        return 0
    else:
        print("❌ Agent evaluation debugging failed")
        return 1

if __name__ == "__main__":
    exit(main())
