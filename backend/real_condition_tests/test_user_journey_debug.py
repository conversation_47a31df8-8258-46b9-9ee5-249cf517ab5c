#!/usr/bin/env python3
"""
User Journey Debug Test

This test reproduces the exact issue described:
1. New user with ~25% profile completion
2. User writes "make me a wheel"
3. System should ask for more information but instead generates wheel immediately
4. After wheel selection, system switches to asking for more information (wrong UX)

This test will help identify the root cause and validate fixes.
"""

import asyncio
import sys
import os
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile
from django.contrib.auth import get_user_model
from asgiref.sync import sync_to_async
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserJourneyDebugTest:
    """Test class to debug the user journey issue"""
    
    def __init__(self):
        self.test_user_id = None
        self.test_user_profile_id = None
        self.dispatcher = None
        
    async def setup_test_user(self):
        """Create a test user with ~25% profile completion"""
        try:
            # Create test user using sync_to_async
            @sync_to_async
            def create_user():
                User = get_user_model()
                # Use timestamp to ensure unique user for each test
                import time
                timestamp = str(int(time.time()))
                test_user, _ = User.objects.get_or_create(
                    username=f'debug_user_journey_{timestamp}',
                    defaults={
                        'email': f'debug_journey_{timestamp}@test.com',
                        'first_name': 'Debug',
                        'last_name': 'User'
                    }
                )
                return test_user

            @sync_to_async
            def create_profile(user):
                from apps.user.models import Demographics

                user_profile, _ = UserProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        'profile_name': 'Debug Journey User',
                        'is_real': False  # Test profile
                    }
                )

                # Create demographics separately
                demographics, _ = Demographics.objects.get_or_create(
                    user_profile=user_profile,
                    defaults={
                        'full_name': 'Debug Journey User',
                        'age': 22,
                        'location': 'Berlin, Germany',
                        'gender': 'female',
                        'occupation': 'student',
                        'language': 'English'
                    }
                )

                return user_profile

            test_user = await create_user()
            user_profile = await create_profile(test_user)

            self.test_user_id = str(test_user.id)
            self.test_user_profile_id = str(user_profile.id)

            print(f"✅ Test user created: ID={self.test_user_id}, Profile ID={self.test_user_profile_id}")
            return True

        except Exception as e:
            print(f"❌ Error setting up test user: {e}")
            return False
    
    async def check_profile_completion(self):
        """Check the current profile completion percentage"""
        try:
            self.dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_profile_id,
                user_ws_session_name='debug_journey_session'
            )
            
            completion = await self.dispatcher._check_profile_completion()
            print(f"📊 Profile completion: {completion:.1%}")
            
            # Also check profile gaps
            gaps = await self.dispatcher._analyze_profile_gaps()
            print(f"📋 Profile gaps analysis:")
            print(f"   - Critical gaps: {len(gaps.get('critical', []))}")
            print(f"   - Important gaps: {len(gaps.get('important', []))}")
            print(f"   - Optional gaps: {len(gaps.get('optional', []))}")
            print(f"   - Profile readiness: {gaps.get('profile_readiness', 'unknown')}")
            
            return completion, gaps
            
        except Exception as e:
            print(f"❌ Error checking profile completion: {e}")
            return 0.0, {}
    
    async def test_wheel_request_classification(self):
        """Test how the system classifies 'make me a wheel' request"""
        try:
            message_data = {
                'text': 'make me a wheel',
                'metadata': {
                    'timestamp': '2025-06-18T15:00:00Z',
                    'session_id': 'debug_journey_session'
                }
            }

            print(f"\n🔍 Testing message classification for: '{message_data['text']}'")

            # Test the internal classification method
            context = {'mood': 'neutral', 'environment': 'unknown'}
            completion = await self.dispatcher._check_profile_completion()
            classification = await self.dispatcher._classify_message(message_data, context, completion)

            print(f"📝 Classification result:")
            print(f"   - Workflow type: {classification.get('workflow_type')}")
            print(f"   - Confidence: {classification.get('confidence')}")
            print(f"   - Reason: {classification.get('reason')}")

            return classification

        except Exception as e:
            print(f"❌ Error in message classification: {e}")
            return {}
    
    async def test_process_message(self):
        """Test the full message processing pipeline"""
        try:
            message_data = {
                'text': 'make me a wheel',
                'metadata': {
                    'timestamp': '2025-06-18T15:00:00Z',
                    'session_id': 'debug_journey_session'
                }
            }

            print(f"\n🎯 Testing full message processing for: '{message_data['text']}'")

            # Test the full process_message method
            result = await self.dispatcher.process_message(message_data)

            print(f"📤 Process message result:")
            print(f"   - Success: {result.get('success')}")
            print(f"   - Workflow type: {result.get('workflow_type')}")
            print(f"   - Workflow ID: {result.get('workflow_id')}")
            print(f"   - Response: {result.get('response', 'No response')[:100]}...")

            return result

        except Exception as e:
            print(f"❌ Error in process message: {e}")
            return {}

    async def test_workflow_completion(self, workflow_id):
        """Test workflow completion and monitor for errors"""
        try:
            print(f"\n⏳ Monitoring workflow completion: {workflow_id}")

            # Wait for workflow to complete (up to 30 seconds)
            max_wait = 30
            wait_interval = 2
            waited = 0

            while waited < max_wait:
                await asyncio.sleep(wait_interval)
                waited += wait_interval

                # Check if workflow completed by looking at conversation history
                @sync_to_async
                def check_completion():
                    from apps.main.models import HistoryEvent
                    events = HistoryEvent.objects.filter(
                        user_profile_id=self.test_user_profile_id
                    ).order_by('-timestamp')[:5]

                    return [(e.event_type, str(e.details)[:100] if e.details else 'No details') for e in events]

                recent_events = await check_completion()
                print(f"   ⏱️ {waited}s - Recent events: {len(recent_events)}")

                # Look for completion indicators
                for event_type, content in recent_events:
                    if 'assistant' in event_type.lower() and len(content) > 20:
                        print(f"   ✅ Found assistant response: {content[:80]}...")
                        return True

                if waited >= max_wait:
                    print(f"   ⚠️ Workflow timeout after {max_wait}s")
                    break

            return False

        except Exception as e:
            print(f"❌ Error monitoring workflow: {e}")
            return False

    async def check_backend_errors(self):
        """Check for backend errors in the logs"""
        try:
            print(f"\n🔍 Checking for backend errors...")

            # This would ideally check Celery logs, but for now we'll check the database
            @sync_to_async
            def check_errors():
                from apps.main.models import HistoryEvent
                from django.db import connection

                # Check for any error events
                error_events = HistoryEvent.objects.filter(
                    user_profile_id=self.test_user_profile_id,
                    event_type__icontains='error'
                ).order_by('-timestamp')[:5]

                errors = []
                for event in error_events:
                    errors.append({
                        'type': event.event_type,
                        'details': event.details,
                        'timestamp': event.timestamp
                    })

                return errors

            errors = await check_errors()

            if errors:
                print(f"   ❌ Found {len(errors)} error events:")
                for error in errors:
                    print(f"      - {error['type']}: {error['content'][:100]}...")
            else:
                print(f"   ✅ No error events found in database")

            return errors

        except Exception as e:
            print(f"❌ Error checking backend errors: {e}")
            return []
    
    async def run_comprehensive_test(self):
        """Run the comprehensive user journey debug test"""
        print("🚀 Starting User Journey Debug Test")
        print("=" * 60)
        
        # Step 1: Setup test user
        print("\n1️⃣ Setting up test user...")
        if not await self.setup_test_user():
            return False
        
        # Step 2: Check profile completion
        print("\n2️⃣ Checking profile completion...")
        completion, gaps = await self.check_profile_completion()
        
        # Step 3: Test message classification
        print("\n3️⃣ Testing message classification...")
        classification = await self.test_wheel_request_classification()
        
        # Step 4: Test full message processing
        print("\n4️⃣ Testing full message processing...")
        process_result = await self.test_process_message()

        # Step 5: Monitor workflow completion
        workflow_id = process_result.get('workflow_id')
        if workflow_id:
            print("\n5️⃣ Monitoring workflow completion...")
            workflow_completed = await self.test_workflow_completion(workflow_id)
        else:
            print("\n5️⃣ No workflow ID found, skipping completion monitoring")
            workflow_completed = False

        # Step 6: Check for backend errors
        print("\n6️⃣ Checking for backend errors...")
        backend_errors = await self.check_backend_errors()

        # Step 7: Analysis and recommendations
        print("\n7️⃣ Analysis and Recommendations")
        print("=" * 40)
        
        expected_behavior = "Should ask for more information (onboarding workflow)"
        actual_workflow = classification.get('workflow_type', 'unknown')
        
        if completion < 0.5 and actual_workflow == 'wheel_generation':
            print("❌ ISSUE CONFIRMED: System generates wheel despite incomplete profile")
            print(f"   - Profile completion: {completion:.1%} (< 50%)")
            print(f"   - Actual workflow: {actual_workflow}")
            print(f"   - Expected workflow: onboarding")
            print(f"   - Classification reason: {classification.get('reason')}")
        elif completion < 0.5 and actual_workflow == 'onboarding':
            print("✅ CORRECT BEHAVIOR: System asks for more information")
            print(f"   - Profile completion: {completion:.1%} (< 50%)")
            print(f"   - Workflow: {actual_workflow}")
        else:
            print(f"🤔 UNEXPECTED SCENARIO:")
            print(f"   - Profile completion: {completion:.1%}")
            print(f"   - Workflow: {actual_workflow}")
        
        return True

async def main():
    """Main test execution"""
    test = UserJourneyDebugTest()
    await test.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
