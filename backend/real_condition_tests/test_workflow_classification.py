#!/usr/bin/env python3
"""
Test workflow classification for user 39 who now has 50% profile completion
"""

import os
import sys
import django
import asyncio

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Configure Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher

async def test_workflow_classification():
    """Test workflow classification for user 39."""
    try:
        print("Testing workflow classification for user 40 (50% profile completion)")
        
        # Create fresh conversation dispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id="40",
            user_ws_session_name="test_classification_40",
            fail_fast_on_errors=True
        )
        
        # Test wheel generation request
        user_message = {
            "text": "I want to spin the wheel and get some activity recommendations for managing my stress",
            "metadata": {
                "test_context": "workflow_classification_test"
            }
        }
        
        print(f"Testing message: {user_message['text']}")
        
        # Check profile completion first
        profile_completion = await dispatcher._check_profile_completion()
        print(f"Profile completion: {profile_completion:.1%}")
        print(f"Above threshold (50%): {profile_completion >= 0.5}")
        
        # Test workflow classification
        response = await dispatcher.process_message(user_message)
        
        workflow_type = response.get('workflow_type')
        confidence = response.get('confidence', 0)
        
        print(f"Classified as: {workflow_type} (confidence: {confidence:.2f})")
        
        if workflow_type == "wheel_generation":
            print("✅ SUCCESS: Correctly classified as wheel_generation")
        elif workflow_type == "onboarding":
            print("❌ ISSUE: Still classified as onboarding despite 50% completion")
        else:
            print(f"⚠️  UNEXPECTED: Classified as {workflow_type}")
        
        return workflow_type
        
    except Exception as e:
        print(f"Error testing workflow classification: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_workflow_classification())
