#!/usr/bin/env python3
"""
Quick Benchmark UI Integration Real Condition Test
Tests the admin interface integration and UI functionality.

This test validates:
1. Admin interface accessibility
2. Quick benchmark tab functionality
3. API endpoint integration
4. Form submission and validation
5. Results display and navigation
6. JavaScript functionality

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_quick_benchmark_ui_integration.py
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from django.urls import reverse

class QuickBenchmarkUITester:
    """UI integration tester for quick benchmark system."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Quick Benchmark UI Integration Real Condition Test',
            'timestamp': datetime.now().isoformat(),
            'ui_tests': {},
            'api_tests': {},
            'integration_tests': {},
            'summary': {},
            'recommendations': []
        }
        self.client = Client()
        self.User = get_user_model()
        self.admin_user = None
        
    def log(self, message, level='INFO'):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        filename = f"quick_benchmark_ui_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        self.log(f"Results saved to: {filepath}")
        return filepath

    def setup_admin_user(self):
        """Set up admin user for testing."""
        self.log("Setting up admin user...")
        
        try:
            self.admin_user, created = self.User.objects.get_or_create(
                username='test_admin_ui',
                defaults={
                    'email': '<EMAIL>',
                    'is_staff': True,
                    'is_superuser': True,
                    'is_active': True
                }
            )
            
            if created:
                self.admin_user.set_password('testpass123')
                self.admin_user.save()
            
            # Login
            self.client.force_login(self.admin_user)
            self.log(f"Admin user ready: {self.admin_user.username}")
            return True
            
        except Exception as e:
            self.log(f"Failed to setup admin user: {e}", 'ERROR')
            return False

    def test_admin_interface_access(self):
        """Test admin interface accessibility."""
        test_name = 'admin_interface_access'
        self.log("Testing admin interface access...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Test admin index
            response = self.client.get('/admin/')
            test_result['details']['admin_index'] = {
                'status_code': response.status_code,
                'accessible': response.status_code == 200
            }
            
            # Test benchmark management page
            response = self.client.get('/admin/benchmarks/manage/')
            test_result['details']['benchmark_management'] = {
                'status_code': response.status_code,
                'accessible': response.status_code == 200
            }
            
            if response.status_code == 200:
                content = response.content.decode()
                test_result['details']['page_content'] = {
                    'has_quick_benchmark_tab': 'quick-benchmark' in content,
                    'has_quick_benchmark_form': 'quick-benchmark-form' in content,
                    'has_api_urls': 'QUICK_BENCHMARK_API_URL' in content,
                    'has_required_elements': all(element in content for element in [
                        'quick-agent-name',
                        'quick-profile-template', 
                        'quick-evaluation-template'
                    ])
                }
                self.log("Admin interface accessible with required elements")
            else:
                test_result['errors'].append(f"Benchmark management page not accessible: {response.status_code}")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"Admin interface access test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['ui_tests'][test_name] = test_result

    def test_api_endpoints(self):
        """Test API endpoint functionality."""
        test_name = 'api_endpoints'
        self.log("Testing API endpoints...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Test quick benchmark options endpoint
            response = self.client.get('/admin/benchmarks/api/quick-benchmark/')
            test_result['details']['options_endpoint'] = {
                'status_code': response.status_code,
                'accessible': response.status_code == 200
            }
            
            if response.status_code == 200:
                data = response.json()
                test_result['details']['options_data'] = {
                    'has_success': data.get('success', False),
                    'has_options': 'options' in data,
                    'profile_templates_count': len(data.get('options', {}).get('profile_templates', [])),
                    'evaluation_templates_count': len(data.get('options', {}).get('evaluation_templates', [])),
                    'agents_count': len(data.get('options', {}).get('available_agents', []))
                }
                
                # Validate data structure
                options = data.get('options', {})
                if options.get('profile_templates'):
                    sample_template = options['profile_templates'][0]
                    test_result['details']['template_structure'] = {
                        'has_template_name': 'template_name' in sample_template,
                        'has_profile_name': 'profile_name' in sample_template,
                        'has_description': 'description' in sample_template
                    }
                
                self.log(f"API endpoint working: {test_result['details']['options_data']}")
            else:
                test_result['errors'].append(f"Options endpoint failed: {response.status_code}")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"API endpoint test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['api_tests'][test_name] = test_result

    def test_form_validation(self):
        """Test form validation and submission."""
        test_name = 'form_validation'
        self.log("Testing form validation...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Test invalid form submission (missing required fields)
            response = self.client.post('/admin/benchmarks/api/quick-benchmark/', 
                                      json.dumps({}),
                                      content_type='application/json')
            
            test_result['details']['invalid_submission'] = {
                'status_code': response.status_code,
                'properly_rejected': response.status_code == 400
            }
            
            if response.status_code == 400:
                data = response.json()
                test_result['details']['error_response'] = {
                    'has_error': 'error' in data,
                    'mentions_missing_fields': 'Missing required fields' in data.get('error', '')
                }
                self.log("Form validation working correctly")
            else:
                test_result['errors'].append(f"Invalid form not properly rejected: {response.status_code}")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"Form validation test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['integration_tests'][test_name] = test_result

    def test_javascript_integration(self):
        """Test JavaScript integration and functionality."""
        test_name = 'javascript_integration'
        self.log("Testing JavaScript integration...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Get benchmark management page
            response = self.client.get('/admin/benchmarks/management/')
            
            if response.status_code == 200:
                content = response.content.decode()
                
                # Check for required JavaScript elements
                js_checks = {
                    'has_tab_functionality': 'tab-button' in content and 'data-tab=' in content,
                    'has_form_handling': 'quick-benchmark-form' in content,
                    'has_api_integration': 'QUICK_BENCHMARK_API_URL' in content,
                    'has_status_display': 'quick-benchmark-status' in content,
                    'has_results_display': 'quick-benchmark-results' in content,
                    'has_progress_indicator': 'quick-benchmark-progress' in content
                }
                
                test_result['details']['javascript_elements'] = js_checks
                
                # Check for required JavaScript functions/variables
                js_features = {
                    'has_api_url_variable': 'window.QUICK_BENCHMARK_API_URL' in content,
                    'has_form_submission': 'addEventListener' in content or 'onclick' in content,
                    'has_tab_switching': 'tab-content' in content
                }
                
                test_result['details']['javascript_features'] = js_features
                
                # Overall JavaScript readiness
                all_checks_pass = all(js_checks.values()) and all(js_features.values())
                test_result['details']['javascript_ready'] = all_checks_pass
                
                if all_checks_pass:
                    self.log("JavaScript integration complete and ready")
                else:
                    missing_features = [k for k, v in {**js_checks, **js_features}.items() if not v]
                    test_result['errors'].append(f"Missing JavaScript features: {missing_features}")
            else:
                test_result['errors'].append(f"Could not access page for JavaScript testing: {response.status_code}")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"JavaScript integration test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['integration_tests'][test_name] = test_result

    def test_url_routing(self):
        """Test URL routing and navigation."""
        test_name = 'url_routing'
        self.log("Testing URL routing...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Test all relevant URLs
            urls_to_test = [
                ('/admin/', 'admin_index'),
                ('/admin/benchmarks/manage/', 'benchmark_management'),
                ('/admin/benchmarks/api/quick-benchmark/', 'quick_benchmark_api'),
                ('/admin/benchmarks/history/', 'benchmark_history')
            ]
            
            for url, name in urls_to_test:
                try:
                    response = self.client.get(url)
                    test_result['details'][name] = {
                        'url': url,
                        'status_code': response.status_code,
                        'accessible': response.status_code in [200, 302]  # 302 for redirects
                    }
                except Exception as e:
                    test_result['details'][name] = {
                        'url': url,
                        'status_code': None,
                        'accessible': False,
                        'error': str(e)
                    }
                    test_result['errors'].append(f"URL {url} failed: {e}")
            
            # Check overall routing health
            accessible_urls = sum(1 for details in test_result['details'].values() 
                                if details.get('accessible', False))
            total_urls = len(urls_to_test)
            
            test_result['details']['routing_summary'] = {
                'accessible_urls': accessible_urls,
                'total_urls': total_urls,
                'routing_health': f"{(accessible_urls/total_urls)*100:.1f}%"
            }
            
            self.log(f"URL routing test: {accessible_urls}/{total_urls} URLs accessible")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"URL routing test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['integration_tests'][test_name] = test_result

    def run_all_tests(self):
        """Run all UI integration tests."""
        self.log("Starting Quick Benchmark UI Integration Test")
        self.log("=" * 60)
        
        # Setup
        if not self.setup_admin_user():
            self.log("Admin user setup failed", 'ERROR')
            return self.save_results()
        
        # Run tests
        self.test_admin_interface_access()
        self.test_api_endpoints()
        self.test_form_validation()
        self.test_javascript_integration()
        self.test_url_routing()
        
        # Generate summary
        all_tests = {**self.results['ui_tests'], **self.results['api_tests'], **self.results['integration_tests']}
        total_tests = len(all_tests)
        passed_tests = sum(1 for test in all_tests.values() if test['status'] == 'PASS')
        failed_tests = total_tests - passed_tests
        
        self.results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': f"{(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
            'ui_ready': failed_tests == 0
        }
        
        # Generate recommendations
        if failed_tests > 0:
            self.results['recommendations'].append("Some UI tests failed - fix issues before frontend deployment")
        if not self.results['integration_tests'].get('javascript_integration', {}).get('details', {}).get('javascript_ready', False):
            self.results['recommendations'].append("JavaScript integration incomplete - verify all required features are implemented")
        if failed_tests == 0:
            self.results['recommendations'].append("All UI tests passed - system ready for user testing")
        
        # Log summary
        self.log("=" * 60)
        self.log(f"UI Integration Test Summary: {passed_tests}/{total_tests} tests passed ({self.results['summary']['success_rate']})")
        
        if failed_tests == 0:
            self.log("✅ All UI integration tests passed! Quick benchmark UI is ready for use.", 'SUCCESS')
        else:
            self.log(f"❌ {failed_tests} tests failed. Check results for details.", 'ERROR')
        
        return self.save_results()

def main():
    """Main test execution."""
    tester = QuickBenchmarkUITester()
    results_file = tester.run_all_tests()
    
    print(f"\n📊 Detailed results saved to: {results_file}")
    print("\n🚀 Next steps:")
    print("1. Review UI test results for any failures")
    print("2. Test the interface manually in browser")
    print("3. Validate complete user workflow")
    print("\n💡 Manual Testing:")
    print("   1. Open: http://localhost:8000/admin/benchmarks/manage/")
    print("   2. Click 'Quick Benchmark' tab")
    print("   3. Fill form and submit benchmark")
    print("   4. Verify results display correctly")
    print("   5. Check browser console for JavaScript errors")

if __name__ == '__main__':
    main()
