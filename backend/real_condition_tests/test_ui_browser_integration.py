#!/usr/bin/env python3
"""
UI Browser Integration Test

This test uses <PERSON><PERSON> to test the actual browser behavior of the benchmarking system,
including modal opening, JavaScript function availability, and form state preservation.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_ui_browser_integration.py
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.contrib.auth.models import User
from apps.user.models import UserProfile
from apps.main.models import GenericAgent

logger = logging.getLogger(__name__)

class UIBrowserIntegrationTest:
    """Test the actual browser behavior of the benchmarking UI."""
    
    def __init__(self):
        self.results = {
            'test_name': 'UI Browser Integration Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
        
    def log_test_result(self, test_name: str, success: bool, details: dict = None, error: str = None):
        """Log the result of a test."""
        self.results['tests'][test_name] = {
            'success': success,
            'details': details or {},
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results['summary']['total_tests'] += 1
        if success:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: {error}")
            if error:
                self.results['summary']['errors'].append(f"{test_name}: {error}")
    
    async def test_playwright_availability(self):
        """Test if Playwright is available for browser testing."""
        try:
            from playwright.async_api import async_playwright
            
            async with async_playwright() as p:
                # Try to launch a browser
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                await page.goto('about:blank')
                await browser.close()
                
            self.log_test_result('Playwright Availability', True, 
                               {'browser': 'chromium', 'headless': True})
            return True
            
        except ImportError:
            self.log_test_result('Playwright Availability', False, 
                               error="Playwright not installed. Install with: pip install playwright && playwright install")
            return False
        except Exception as e:
            self.log_test_result('Playwright Availability', False, error=str(e))
            return False
    
    async def test_benchmark_management_page_load(self):
        """Test that the benchmark management page loads correctly."""
        try:
            from playwright.async_api import async_playwright
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context()
                page = await context.new_page()
                
                # Create a test user and login
                user, created = User.objects.get_or_create(
                    username='test_ui_browser_user',
                    defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
                )
                user.set_password('testpass123')
                user.save()
                
                # Navigate to login page
                await page.goto('http://localhost:8000/admin/login/')
                
                # Login
                await page.fill('input[name="username"]', 'test_ui_browser_user')
                await page.fill('input[name="password"]', 'testpass123')
                await page.click('input[type="submit"]')
                
                # Navigate to benchmark management
                await page.goto('http://localhost:8000/admin/benchmarks/manage/')
                
                # Wait for page to load
                await page.wait_for_selector('#quick-benchmark-tab', timeout=10000)
                
                # Check for required elements
                elements_to_check = [
                    '#quick-benchmark-tab',
                    '#quick-agent-name',
                    '#quick-profile-template',
                    '#quick-evaluation-template',
                    '#run-quick-benchmark-btn',
                    '#agent-details-modal',
                    '#agent-modal-body'
                ]
                
                element_status = {}
                for selector in elements_to_check:
                    element = await page.query_selector(selector)
                    element_status[selector] = element is not None
                
                # Check for JavaScript functions
                js_functions_available = await page.evaluate("""
                    () => {
                        return {
                            renderAgentDetails: typeof window.renderAgentDetails === 'function',
                            renderLLMInteractions: typeof window.renderLLMInteractions === 'function',
                            renderEnhancedToolCalls: typeof window.renderEnhancedToolCalls === 'function',
                            setupCopyRunDataButton: typeof window.setupCopyRunDataButton === 'function',
                            openAgentEvaluationModal: typeof openAgentEvaluationModal === 'function'
                        };
                    }
                """)
                
                await browser.close()
                
                details = {
                    'elements_status': element_status,
                    'js_functions_available': js_functions_available,
                    'all_elements_present': all(element_status.values()),
                    'all_js_functions_available': all(js_functions_available.values())
                }
                
                success = details['all_elements_present'] and details['all_js_functions_available']
                error = None if success else "Missing required elements or JavaScript functions"
                
                self.log_test_result('Benchmark Management Page Load', success, details, error)
                return success
                
        except Exception as e:
            self.log_test_result('Benchmark Management Page Load', False, error=str(e))
            return False
    
    async def test_quick_benchmark_form_interaction(self):
        """Test quick benchmark form interaction and state preservation."""
        try:
            from playwright.async_api import async_playwright
            
            # Get required data
            user_profile = UserProfile.objects.filter(is_real=True).first()
            agent = GenericAgent.objects.filter(role='mentor').first()
            
            if not user_profile or not agent:
                self.log_test_result('Quick Benchmark Form Interaction', False,
                                   error="Missing user profile or agent")
                return False
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context()
                page = await context.new_page()
                
                # Login and navigate
                user, created = User.objects.get_or_create(
                    username='test_form_user',
                    defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
                )
                user.set_password('testpass123')
                user.save()
                
                await page.goto('http://localhost:8000/admin/login/')
                await page.fill('input[name="username"]', 'test_form_user')
                await page.fill('input[name="password"]', 'testpass123')
                await page.click('input[type="submit"]')
                
                await page.goto('http://localhost:8000/admin/benchmarks/manage/')
                await page.wait_for_selector('#quick-benchmark-tab', timeout=10000)
                
                # Click on quick benchmark tab
                await page.click('#quick-benchmark-tab')
                await page.wait_for_selector('#quick-agent-name', timeout=5000)
                
                # Fill out the form
                await page.select_option('#quick-agent-name', agent.role)
                await page.select_option('#quick-profile-template', str(user_profile.id))
                await page.select_option('#quick-evaluation-template', 'mentor_helpfulness')
                
                # Get form values before submission
                form_values_before = await page.evaluate("""
                    () => {
                        return {
                            agent: document.getElementById('quick-agent-name').value,
                            profile: document.getElementById('quick-profile-template').value,
                            evaluation: document.getElementById('quick-evaluation-template').value
                        };
                    }
                """)
                
                # Submit the form (but don't actually run benchmark to avoid long wait)
                # Instead, just check if the form state is preserved
                
                # Simulate clicking "Run Another Benchmark" button
                await page.evaluate("""
                    () => {
                        const btn = document.getElementById('run-another-benchmark-btn');
                        if (btn) {
                            btn.click();
                        }
                    }
                """)
                
                # Wait a moment for any state changes
                await page.wait_for_timeout(1000)
                
                # Get form values after "Run Another Benchmark"
                form_values_after = await page.evaluate("""
                    () => {
                        return {
                            agent: document.getElementById('quick-agent-name').value,
                            profile: document.getElementById('quick-profile-template').value,
                            evaluation: document.getElementById('quick-evaluation-template').value
                        };
                    }
                """)
                
                await browser.close()
                
                details = {
                    'form_values_before': form_values_before,
                    'form_values_after': form_values_after,
                    'state_preserved': form_values_before == form_values_after
                }
                
                success = details['state_preserved']
                error = None if success else "Form state not preserved after 'Run Another Benchmark'"
                
                self.log_test_result('Quick Benchmark Form Interaction', success, details, error)
                return success
                
        except Exception as e:
            self.log_test_result('Quick Benchmark Form Interaction', False, error=str(e))
            return False
    
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'ui_browser_integration_{timestamp}.json'
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Results saved to: {filepath}")
        return filepath
    
    async def run_all_tests(self):
        """Run all tests in sequence."""
        print("🚀 Starting UI Browser Integration Test")
        print("=" * 60)
        
        # Test 1: Playwright Availability
        playwright_available = await self.test_playwright_availability()
        
        if playwright_available:
            # Test 2: Page Load
            await self.test_benchmark_management_page_load()
            
            # Test 3: Form Interaction
            await self.test_quick_benchmark_form_interaction()
        else:
            print("⚠️ Skipping browser tests - Playwright not available")
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.results['summary']['total_tests']}")
        print(f"Passed: {self.results['summary']['passed']}")
        print(f"Failed: {self.results['summary']['failed']}")
        
        if self.results['summary']['errors']:
            print("\n❌ ERRORS:")
            for error in self.results['summary']['errors']:
                print(f"  - {error}")
        
        if self.results['summary']['total_tests'] > 0:
            success_rate = (self.results['summary']['passed'] / self.results['summary']['total_tests']) * 100
            print(f"\n🎯 Success Rate: {success_rate:.1f}%")
            
            if success_rate >= 90:
                print("✅ UI Browser Integration is working perfectly!")
            elif success_rate >= 70:
                print("⚠️ UI Browser Integration has some issues")
            else:
                print("❌ UI Browser Integration needs significant fixes")
        
        return self.save_results()

async def main():
    """Main test execution."""
    test = UIBrowserIntegrationTest()
    results_file = await test.run_all_tests()
    
    print(f"\n🔗 View detailed results: {results_file}")
    print("\n🎯 Next Steps:")
    print("1. If Playwright not available: pip install playwright && playwright install")
    print("2. If tests failed: Check browser console errors and JavaScript function availability")
    print("3. Manual testing: Open http://localhost:8000/admin/benchmarks/manage/")

if __name__ == '__main__':
    asyncio.run(main())
