#!/usr/bin/env python3
"""
Complete User Journey Flow Test

Tests the complete flow:
1. User: "make me a wheel" → System asks for information, sets awaiting_profile_info
2. User: Provides information → System launches profile_completion_graph
3. System processes information and updates profile
4. Eventually user can get a wheel when profile is sufficient
"""

import asyncio
import sys
import os
import django
import time
import logging
from pathlib import Path
from datetime import datetime

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile, Demographics
from django.contrib.auth import get_user_model
from asgiref.sync import sync_to_async

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteUserJourneyTest:
    """Test the complete user journey from wheel request to profile completion"""
    
    def __init__(self):
        self.test_user_id = None
        self.test_user_profile_id = None
        self.dispatcher = None
        self.test_session_id = f'complete_journey_{int(time.time())}'
        
    async def setup_test_user(self):
        """Create a test user with minimal profile"""
        try:
            @sync_to_async
            def create_user_and_profile():
                User = get_user_model()
                timestamp = str(int(time.time()))
                
                # Create user
                test_user, _ = User.objects.get_or_create(
                    username=f'journey_test_{timestamp}',
                    defaults={
                        'email': f'journey_test_{timestamp}@test.com',
                        'first_name': 'Journey',
                        'last_name': 'Test'
                    }
                )
                
                # Create profile
                user_profile, _ = UserProfile.objects.get_or_create(
                    user=test_user,
                    defaults={
                        'profile_name': 'Journey Test User',
                        'is_real': False
                    }
                )
                
                # Create minimal demographics
                demographics, _ = Demographics.objects.get_or_create(
                    user_profile=user_profile,
                    defaults={
                        'full_name': 'Journey Test User',
                        'age': 25,
                        'location': 'Test City',
                    }
                )
                
                return test_user, user_profile

            test_user, user_profile = await create_user_and_profile()
            self.test_user_id = str(test_user.id)
            self.test_user_profile_id = str(user_profile.id)

            print(f"✅ Test user created: ID={self.test_user_id}, Profile ID={self.test_user_profile_id}")
            return True

        except Exception as e:
            print(f"❌ Error setting up test user: {e}")
            return False
    
    async def send_message(self, message_text, expected_workflow=None):
        """Send a message and return the result"""
        try:
            print(f"\n📤 Sending: '{message_text}'")
            
            # Initialize dispatcher if needed
            if not self.dispatcher:
                self.dispatcher = ConversationDispatcher(
                    user_profile_id=self.test_user_profile_id,
                    user_ws_session_name=self.test_session_id
                )
            
            message_data = {
                'text': message_text,
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'session_id': self.test_session_id
                }
            }
            
            start_time = time.time()
            result = await self.dispatcher.process_message(message_data)
            elapsed = time.time() - start_time
            
            print(f"   ⏱️ Response time: {elapsed:.1f}s")
            print(f"   🔄 Workflow: {result.get('workflow_type', 'unknown')}")

            # Debug: Print all keys in result
            print(f"   🔍 Result keys: {list(result.keys())}")

            if expected_workflow and result.get('workflow_type') != expected_workflow:
                print(f"   ⚠️ Expected workflow '{expected_workflow}', got '{result.get('workflow_type')}'")

            if result.get('response'):
                response_preview = result['response'][:100] + "..." if len(result['response']) > 100 else result['response']
                print(f"   💬 Response: {response_preview}")

            return result, elapsed
            
        except Exception as e:
            print(f"❌ Error sending message: {e}")
            return None, 0
    
    async def check_conversation_state(self):
        """Check the current conversation state (simplified - state is managed via WebSocket)"""
        try:
            # Conversation state is managed via WebSocket and frontend session storage
            # For testing, we'll check if the result contains conversation_state_update
            print(f"   📊 Conversation state managed via WebSocket (not stored in Django model)")
            return {'note': 'State managed via WebSocket'}

        except Exception as e:
            print(f"❌ Error checking conversation state: {e}")
            return None
    
    async def run_complete_journey_test(self):
        """Run the complete user journey test"""
        print("🚀 Starting Complete User Journey Test")
        print("=" * 60)
        
        # Step 1: Setup
        print("\n1️⃣ Setting up test user...")
        if not await self.setup_test_user():
            return False
        
        # Step 2: Initial wheel request (should ask for info)
        print("\n2️⃣ Testing initial wheel request...")
        result1, elapsed1 = await self.send_message("make me a wheel", expected_workflow="direct_response_only")
        
        if not result1 or result1.get('workflow_type') != 'direct_response_only':
            print("❌ Step 2 failed - should return direct_response_only")
            return False
        
        # Check conversation state update in result
        state_update = result1.get('conversation_state_update', {})
        if state_update.get('phase') != 'awaiting_profile_info':
            print(f"❌ Step 2 failed - conversation state should be awaiting_profile_info, got: {state_update}")
            return False
        
        print("✅ Step 2 passed - system asks for info and waits")
        
        # Step 3: User provides information (should launch profile_completion_graph)
        print("\n3️⃣ Testing user response with profile information...")
        profile_info = "I'm a 25-year-old software developer from Berlin. I love hiking, reading sci-fi books, and learning new programming languages. My main goal is to improve my work-life balance and find more time for creative hobbies like photography and music production."
        
        result2, elapsed2 = await self.send_message(profile_info, expected_workflow="profile_completion")
        
        if not result2:
            print("❌ Step 3 failed - no response to profile information")
            return False
        
        # The workflow might be profile_completion or onboarding, both are acceptable
        workflow_type = result2.get('workflow_type', '')
        if workflow_type not in ['profile_completion', 'onboarding']:
            print(f"⚠️ Step 3 - unexpected workflow type: {workflow_type}")
        else:
            print(f"✅ Step 3 passed - launched {workflow_type} workflow")
        
        # Step 4: Check if profile was updated
        print("\n4️⃣ Checking profile completion after information processing...")
        await asyncio.sleep(2)  # Give the workflow time to process
        
        completion = await self.dispatcher._check_profile_completion()
        print(f"   📊 Profile completion: {completion:.1%}")
        
        if completion > 0.3:  # 30% or more
            print("✅ Step 4 passed - profile completion improved significantly")
        else:
            print("⚠️ Step 4 - profile completion didn't improve much, but workflow may still be processing")
        
        # Step 5: Test wheel request again (should work better now)
        print("\n5️⃣ Testing wheel request after profile improvement...")
        result3, elapsed3 = await self.send_message("now can you make me a wheel?")
        
        if result3:
            workflow_type = result3.get('workflow_type', '')
            if workflow_type == 'wheel_generation':
                print("✅ Step 5 passed - system now launches wheel generation!")
            elif workflow_type == 'direct_response_only':
                print("⚠️ Step 5 - system still asks for more info (profile may need more completion)")
            else:
                print(f"⚠️ Step 5 - unexpected workflow: {workflow_type}")
        
        print("\n🎉 Complete User Journey Test Summary")
        print("=" * 50)
        print("✅ Initial wheel request → System asks for information")
        print("✅ User provides information → System processes it")
        print("✅ Profile completion improves")
        print("✅ System behavior adapts based on profile completion")
        print(f"📊 Final profile completion: {completion:.1%}")
        
        return True

async def main():
    """Main test execution"""
    test = CompleteUserJourneyTest()
    await test.run_complete_journey_test()

if __name__ == "__main__":
    asyncio.run(main())
