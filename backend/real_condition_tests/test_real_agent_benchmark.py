#!/usr/bin/env python3
"""
Real Agent Benchmark Test

Test the actual agent benchmarking system through the admin interface
to validate tool call tracking and display.
"""

import os
import sys
import django

import json
from datetime import datetime

# Setup Django
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from apps.main.models import BenchmarkRun
from django.contrib.auth.models import User

def test_real_agent_benchmark():
    """Test real agent benchmark through QuickBenchmarkService"""
    print("🔍 Testing real agent benchmark through QuickBenchmarkService...")

    try:
        # Initialize the service
        service = QuickBenchmarkService()

        print("📊 Running agent benchmark...")

        # Run the benchmark
        benchmark_run = service.run_quick_benchmark_sync(
            agent_name='resource',
            user_profile_id='2',  # PhiPhi
            evaluation_template='mentor_response_quality',
            scenario_context={'user_input': 'Hello, I need help with my resources'},
            use_real_tools=True,
            use_real_db=True
        )
        
        if benchmark_run:
            print(f"✅ Benchmark completed successfully")
            print(f"📊 Benchmark run ID: {benchmark_run.id}")

            print(f"📋 Benchmark run details:")
            print(f"   - Agent role: {benchmark_run.agent_definition.role if benchmark_run.agent_definition else 'N/A'}")
            print(f"   - Scenario: {benchmark_run.scenario.name if benchmark_run.scenario else 'N/A'}")
            print(f"   - Execution time: {benchmark_run.mean_duration}s")
            print(f"   - Total tokens: {benchmark_run.total_tokens}")
            print(f"   - Cost estimate: ${benchmark_run.estimated_cost}")
            print(f"   - Tool calls count: {benchmark_run.tool_calls}")
            print(f"   - Tool call details: {benchmark_run.tool_call_details}")
            print(f"   - Raw results keys: {list(benchmark_run.raw_results.keys()) if benchmark_run.raw_results else []}")

            # Check if PhiPhi's inventory is in the raw results
            if benchmark_run.raw_results:
                raw_data_str = json.dumps(benchmark_run.raw_results).lower()
                has_inventory = 'laptop' in raw_data_str or 'guitar' in raw_data_str
                print(f"   - Contains PhiPhi's inventory: {has_inventory}")

                if has_inventory:
                    print("✅ SUCCESS: Benchmark results contain PhiPhi's inventory items!")
                else:
                    print("❌ ISSUE: Benchmark results don't contain expected inventory items")

                # Check for empty inventory specifically
                has_empty_inventory = 'available_inventory": []' in raw_data_str
                if has_empty_inventory:
                    print("❌ ISSUE: Available inventory is empty - MockDatabaseService being used instead of real DB")

            # Check tool calls
            if benchmark_run.tool_calls > 0:
                print(f"✅ SUCCESS: {benchmark_run.tool_calls} tool calls were tracked!")

                # Check tool call details
                if benchmark_run.tool_call_details:
                    print(f"   - Tool call breakdown: {benchmark_run.tool_call_details}")
                else:
                    print("❌ ISSUE: No tool call details available")
            else:
                print("❌ ISSUE: No tool calls tracked")

        else:
            print(f"❌ Benchmark run failed or returned None")
            
        # Save results
        results_file = '/usr/src/app/real_condition_tests/results/real_agent_benchmark_test.json'
        os.makedirs(os.path.dirname(results_file), exist_ok=True)

        results = {
            'timestamp': datetime.now().isoformat(),
            'benchmark_run_id': str(benchmark_run.id) if benchmark_run else None,
            'success': benchmark_run is not None,
            'raw_results_available': bool(benchmark_run.raw_results) if benchmark_run else False,
            'execution_time': benchmark_run.mean_duration if benchmark_run else 0,
            'total_tokens': benchmark_run.total_tokens if benchmark_run else 0,
            'tool_calls_available': bool(benchmark_run.tool_calls) if benchmark_run else False,
            'contains_phiphi_inventory': 'laptop' in str(benchmark_run.raw_results).lower() if benchmark_run and benchmark_run.raw_results else False
        }

        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"📄 Results saved to {results_file}")

        return results
        
    except Exception as e:
        print(f"❌ Real agent benchmark test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_real_agent_benchmark()
