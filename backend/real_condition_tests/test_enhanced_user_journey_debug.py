#!/usr/bin/env python3
"""
Enhanced User Journey Debug Test

This test specifically addresses the hanging issue described in the user's request:
1. New user with ~25% profile completion
2. User writes "make me a wheel"
3. System correctly announces it needs more information
4. But then hangs instead of asking questions

This enhanced version includes:
- Real-time backend error monitoring
- Celery task tracking
- Tool call validation
- Response time monitoring
- Comprehensive logging
"""

import asyncio
import sys
import os
import django
import time
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile, Demographics
from django.contrib.auth import get_user_model
from asgiref.sync import sync_to_async
from apps.main.models import HistoryEvent, AgentRun

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedUserJourneyDebugTest:
    """Enhanced test class to debug the user journey hanging issue"""
    
    def __init__(self):
        self.test_user_id = None
        self.test_user_profile_id = None
        self.dispatcher = None
        self.start_time = None
        self.test_session_id = f'debug_enhanced_{int(time.time())}'
        
    async def setup_test_user(self):
        """Create a test user with ~25% profile completion"""
        try:
            @sync_to_async
            def create_user_and_profile():
                User = get_user_model()
                timestamp = str(int(time.time()))
                
                # Create user
                test_user, _ = User.objects.get_or_create(
                    username=f'enhanced_debug_{timestamp}',
                    defaults={
                        'email': f'enhanced_debug_{timestamp}@test.com',
                        'first_name': 'Enhanced',
                        'last_name': 'Debug'
                    }
                )
                
                # Create profile
                user_profile, _ = UserProfile.objects.get_or_create(
                    user=test_user,
                    defaults={
                        'profile_name': 'Enhanced Debug User',
                        'is_real': False
                    }
                )
                
                # Create minimal demographics for ~25% completion
                demographics, _ = Demographics.objects.get_or_create(
                    user_profile=user_profile,
                    defaults={
                        'full_name': 'Enhanced Debug User',
                        'age': 22,
                        'location': 'Berlin, Germany',
                        # Deliberately leave other fields empty for low completion
                    }
                )
                
                return test_user, user_profile

            test_user, user_profile = await create_user_and_profile()
            self.test_user_id = str(test_user.id)
            self.test_user_profile_id = str(user_profile.id)

            print(f"✅ Enhanced test user created: ID={self.test_user_id}, Profile ID={self.test_user_profile_id}")
            return True

        except Exception as e:
            print(f"❌ Error setting up test user: {e}")
            logger.exception("Failed to setup test user")
            return False
    
    async def monitor_backend_errors_realtime(self, duration_seconds=30):
        """Monitor backend errors in real-time during test execution"""
        try:
            print(f"🔍 Starting real-time backend error monitoring for {duration_seconds}s...")
            
            @sync_to_async
            def get_recent_errors():
                # Check for recent errors in the last minute
                cutoff_time = datetime.now() - timedelta(minutes=1)
                
                error_events = HistoryEvent.objects.filter(
                    user_profile_id=self.test_user_profile_id,
                    timestamp__gte=cutoff_time,
                    event_type__icontains='error'
                ).order_by('-timestamp')
                
                agent_runs = AgentRun.objects.filter(
                    user_profile_id=self.test_user_profile_id,
                    created_at__gte=cutoff_time,
                    status__in=['failed', 'error']
                ).order_by('-created_at')
                
                return list(error_events), list(agent_runs)
            
            start_time = time.time()
            error_count = 0
            
            while time.time() - start_time < duration_seconds:
                await asyncio.sleep(2)  # Check every 2 seconds
                
                error_events, failed_runs = await get_recent_errors()
                
                if error_events or failed_runs:
                    error_count += len(error_events) + len(failed_runs)
                    print(f"   ⚠️ Found {len(error_events)} error events, {len(failed_runs)} failed runs")
                    
                    for event in error_events:
                        print(f"      ERROR EVENT: {event.event_type} - {str(event.details)[:100]}...")
                    
                    for run in failed_runs:
                        print(f"      FAILED RUN: {run.agent_code} - {run.status}")
            
            print(f"   📊 Total errors detected: {error_count}")
            return error_count
            
        except Exception as e:
            print(f"❌ Error in real-time monitoring: {e}")
            logger.exception("Failed to monitor backend errors")
            return -1
    
    async def test_message_with_timeout(self, message_text, timeout_seconds=15):
        """Test message processing with timeout and detailed monitoring"""
        try:
            print(f"\n🎯 Testing message with timeout: '{message_text}'")
            print(f"   ⏱️ Timeout: {timeout_seconds}s")
            
            self.start_time = time.time()
            
            # Initialize dispatcher
            self.dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_profile_id,
                user_ws_session_name=self.test_session_id
            )
            
            # Check initial profile completion
            completion = await self.dispatcher._check_profile_completion()
            print(f"   📊 Initial profile completion: {completion:.1%}")
            
            message_data = {
                'text': message_text,
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'session_id': self.test_session_id
                }
            }
            
            # Start backend error monitoring in parallel
            monitor_task = asyncio.create_task(
                self.monitor_backend_errors_realtime(timeout_seconds + 5)
            )
            
            # Process message with timeout
            try:
                result = await asyncio.wait_for(
                    self.dispatcher.process_message(message_data),
                    timeout=timeout_seconds
                )
                
                elapsed = time.time() - self.start_time
                print(f"   ✅ Message processed in {elapsed:.1f}s")
                print(f"   📤 Result: {result.get('success', False)}")
                print(f"   🔄 Workflow: {result.get('workflow_type', 'unknown')}")
                
                if result.get('response'):
                    response_preview = result['response'][:150] + "..." if len(result['response']) > 150 else result['response']
                    print(f"   💬 Response: {response_preview}")
                
                return result, elapsed
                
            except asyncio.TimeoutError:
                elapsed = time.time() - self.start_time
                print(f"   ❌ TIMEOUT after {elapsed:.1f}s - This is the hanging issue!")
                return None, elapsed
            
            finally:
                # Wait for monitoring to complete
                await monitor_task
                
        except Exception as e:
            elapsed = time.time() - self.start_time if self.start_time else 0
            print(f"❌ Error testing message: {e}")
            logger.exception("Failed to test message")
            return None, elapsed
    
    async def validate_tool_registration(self):
        """Validate that required tools are properly registered"""
        try:
            print(f"\n🔧 Validating tool registration...")
            
            @sync_to_async
            def check_tools():
                from apps.main.models import AgentTool
                
                required_tools = [
                    'create_user_belief',
                    'create_user_trait', 
                    'create_user_preference',
                    'store_conversation_message'
                ]
                
                tool_status = {}
                for tool_code in required_tools:
                    try:
                        tool = AgentTool.objects.get(code=tool_code)
                        tool_status[tool_code] = {
                            'exists': True,
                            'active': tool.is_active,
                            'name': tool.name
                        }
                    except AgentTool.DoesNotExist:
                        tool_status[tool_code] = {
                            'exists': False,
                            'active': False,
                            'name': 'NOT FOUND'
                        }
                
                return tool_status
            
            tool_status = await check_tools()
            
            all_good = True
            for tool_code, status in tool_status.items():
                if status['exists'] and status['active']:
                    print(f"   ✅ {tool_code}: Active")
                elif status['exists']:
                    print(f"   ⚠️ {tool_code}: Exists but inactive")
                    all_good = False
                else:
                    print(f"   ❌ {tool_code}: NOT FOUND")
                    all_good = False
            
            return all_good, tool_status
            
        except Exception as e:
            print(f"❌ Error validating tools: {e}")
            return False, {}
    
    async def run_comprehensive_debug_test(self):
        """Run the comprehensive enhanced debug test"""
        print("🚀 Starting Enhanced User Journey Debug Test")
        print("=" * 70)
        print(f"🎯 Target: Reproduce and debug the hanging issue")
        print(f"📅 Session ID: {self.test_session_id}")
        print("=" * 70)
        
        # Step 1: Setup
        print("\n1️⃣ Setting up enhanced test user...")
        if not await self.setup_test_user():
            return False
        
        # Step 2: Validate tools
        print("\n2️⃣ Validating tool registration...")
        tools_ok, tool_status = await self.validate_tool_registration()
        if not tools_ok:
            print("   ⚠️ Some tools are missing or inactive - this may cause issues")
        
        # Step 3: Test the problematic message
        print("\n3️⃣ Testing 'make me a wheel' with timeout monitoring...")
        result, elapsed_time = await self.test_message_with_timeout("make me a wheel", timeout_seconds=20)
        
        # Step 4: Analysis
        print("\n4️⃣ Analysis and Diagnosis")
        print("=" * 50)
        
        if result is None:
            print("❌ CONFIRMED: System hangs on 'make me a wheel' request")
            print(f"   ⏱️ Hung for: {elapsed_time:.1f}s before timeout")
            print("   🔍 Likely causes:")
            print("      - Tool registration issues (create_user_trait 'confidence' field error)")
            print("      - Infinite loop in profile completion workflow")
            print("      - LLM call timeout or retry loops")
            print("      - Database constraint violations")
        else:
            print("✅ Message processed successfully")
            print(f"   ⏱️ Response time: {elapsed_time:.1f}s")
            if elapsed_time > 10:
                print("   ⚠️ Response time exceeds 10s target")
        
        return True

async def main():
    """Main test execution"""
    test = EnhancedUserJourneyDebugTest()
    await test.run_comprehensive_debug_test()

if __name__ == "__main__":
    asyncio.run(main())
