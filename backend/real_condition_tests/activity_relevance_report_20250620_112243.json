{"test_summary": {"total_scenarios": 16, "successful_generations": 0, "constraint_violations": 0, "other_errors": 16}, "scenario_analysis": [{"scenario": {"time_minutes": 10, "energy_level": 20, "context": "Low energy, quick break"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 30, "energy_level": 25, "context": "Low energy, short session"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 60, "energy_level": 30, "context": "Low energy, medium time"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 120, "energy_level": 35, "context": "Low energy, long session"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 15, "energy_level": 50, "context": "Medium energy, quick activity"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 45, "energy_level": 55, "context": "Medium energy, standard session"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 90, "energy_level": 60, "context": "Medium energy, extended time"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 180, "energy_level": 65, "context": "Medium energy, long period"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 20, "energy_level": 80, "context": "High energy, focused burst"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 60, "energy_level": 85, "context": "High energy, workout time"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 120, "energy_level": 90, "context": "High energy, project time"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 240, "energy_level": 95, "context": "High energy, full session"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 5, "energy_level": 10, "context": "Minimal time and energy"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 5, "energy_level": 100, "context": "Minimal time, max energy"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 240, "energy_level": 10, "context": "Max time, minimal energy"}, "status": "error", "activity_count": 0, "metrics": {}}, {"scenario": {"time_minutes": 240, "energy_level": 100, "context": "Max time and energy"}, "status": "error", "activity_count": 0, "metrics": {}}], "overall_metrics": {}, "recommendations": []}