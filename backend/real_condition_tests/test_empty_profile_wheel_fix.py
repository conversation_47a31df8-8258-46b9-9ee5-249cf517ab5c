#!/usr/bin/env python3
"""
Test to verify that empty profiles correctly route to onboarding instead of wheel generation.

This test validates the fix for the issue where empty profiles were defaulting to 50% completion
instead of 0% completion, causing inappropriate wheel generation.
"""

import asyncio
import logging
import sys
import os
import django

# Add the backend directory to the Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.user.models import UserProfile
from apps.main.services.conversation_dispatcher import ConversationDispatcher
from django.contrib.auth.models import User
from asgiref.sync import sync_to_async

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_empty_profile_wheel_fix():
    """Test that empty profiles correctly route to onboarding."""
    
    logger.info("🚀 Starting Empty Profile Wheel Fix Test")
    logger.info("=" * 60)
    
    # Create a test user with completely empty profile
    @sync_to_async
    def create_test_user():
        # First create a Django user
        django_user = User.objects.create_user(
            username="test_empty_profile_fix",
            email="<EMAIL>"
        )

        # Then create the UserProfile
        test_user = UserProfile.objects.create(
            user=django_user,
            profile_name="test_empty_profile_fix",
            is_real=False  # Mark as test profile
        )
        return test_user

    test_user = await create_test_user()
    
    logger.info(f"✅ Created test user with profile ID: {test_user.id}")
    logger.info(f"📊 Profile is_real: {test_user.is_real}")
    
    try:
        # Initialize conversation dispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=str(test_user.id),
            user_ws_session_name="test_session_empty_fix",
            fail_fast_on_errors=True  # For testing
        )
        
        logger.info("📋 Test 1: Wheel Request from Empty Profile")
        logger.info("🧪 Testing wheel request from completely empty profile...")
        
        # Test wheel request message
        wheel_message = {
            "text": "make me a wheel",
            "metadata": {}
        }
        
        logger.info("📤 Sending wheel request message...")
        result = await dispatcher.process_message(wheel_message)
        
        logger.info(f"📥 Received result: workflow_type={result.get('workflow_type')}")
        
        # Verify routing
        if result.get('workflow_type') == 'onboarding':
            logger.info("✅ CORRECT: Empty profile routed to onboarding")
            success = True
        elif result.get('workflow_type') == 'wheel_generation':
            logger.error("❌ INCORRECT: Empty profile routed to wheel generation")
            success = False
        else:
            logger.warning(f"⚠️ UNEXPECTED: Routed to {result.get('workflow_type')}")
            success = False
        
        # Check profile completion calculation
        profile_status = await dispatcher._check_profile_completion()
        logger.info(f"📊 Profile completion calculated as: {profile_status:.1%}")
        
        if profile_status == 0.0:
            logger.info("✅ CORRECT: Profile completion calculated as 0.0%")
        else:
            logger.error(f"❌ INCORRECT: Profile completion should be 0.0%, got {profile_status:.1%}")
            success = False
        
        # Test profile gaps analysis
        profile_gaps = await dispatcher._analyze_profile_gaps()
        completion_from_gaps = profile_gaps.get('completion_percentage', -1)
        logger.info(f"📊 Profile completion from gaps analysis: {completion_from_gaps:.1%}")
        
        if completion_from_gaps == 0.0:
            logger.info("✅ CORRECT: Profile gaps analysis shows 0.0% completion")
        else:
            logger.error(f"❌ INCORRECT: Profile gaps should show 0.0%, got {completion_from_gaps:.1%}")
            success = False
        
        logger.info("")
        logger.info("📋 Test 2: Multiple Wheel Requests")
        logger.info("🧪 Testing consistency across multiple requests...")
        
        # Test multiple requests to ensure consistency
        test_messages = [
            "create a wheel for me",
            "I need activities",
            "suggest something to do",
            "give me a wheel"
        ]
        
        onboarding_count = 0
        wheel_count = 0
        
        for i, message_text in enumerate(test_messages, 1):
            message = {"text": message_text, "metadata": {}}
            result = await dispatcher.process_message(message)
            workflow_type = result.get('workflow_type')
            
            logger.info(f"  Request {i}: '{message_text}' → {workflow_type}")
            
            if workflow_type == 'onboarding':
                onboarding_count += 1
            elif workflow_type == 'wheel_generation':
                wheel_count += 1
        
        logger.info(f"📊 Results: {onboarding_count} onboarding, {wheel_count} wheel generation")
        
        if onboarding_count == len(test_messages) and wheel_count == 0:
            logger.info("✅ PERFECT CONSISTENCY: All requests routed to onboarding")
        else:
            logger.error(f"❌ INCONSISTENT: Expected all onboarding, got {onboarding_count}/{len(test_messages)}")
            success = False
        
        logger.info("")
        logger.info("=" * 60)
        logger.info("📊 EMPTY PROFILE WHEEL FIX TEST SUMMARY")
        logger.info("=" * 60)
        
        if success:
            logger.info("✅ SUCCESS: Empty profile wheel fix is working correctly!")
            logger.info("✅ Empty profiles correctly route to onboarding")
            logger.info("✅ Profile completion correctly calculated as 0.0%")
            logger.info("✅ Consistent behavior across multiple requests")
        else:
            logger.error("❌ FAILURE: Empty profile wheel fix needs attention")
            logger.error("❌ Check profile completion calculation logic")
            logger.error("❌ Verify conversation dispatcher routing")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up test user and Django user
        @sync_to_async
        def cleanup_test_user():
            django_user = test_user.user
            test_user.delete()
            django_user.delete()

        await cleanup_test_user()
        logger.info(f"🧹 Cleaned up test user {test_user.id}")

if __name__ == "__main__":
    success = asyncio.run(test_empty_profile_wheel_fix())
    sys.exit(0 if success else 1)
