#!/usr/bin/env python3
"""
Test post_activity workflow classification for user 40 who has 50% profile completion
"""

import os
import sys
import django
import asyncio

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Configure Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher

async def test_post_activity_classification():
    """Test post_activity workflow classification for user 40."""
    try:
        print("Testing post_activity workflow classification for user 40 (50% profile completion)")
        
        # Create fresh conversation dispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id="40",
            user_ws_session_name="test_post_activity_40",
            fail_fast_on_errors=True
        )
        
        # Test post activity feedback message
        user_message = {
            "text": "I just finished the meditation activity you recommended. It was really helpful for managing my stress. I found it a bit challenging at first but got the hang of it. I feel much more relaxed now.",
            "metadata": {
                "activity_id": "meditation_001",
                "activity_name": "Mindful Breathing Meditation",
                "activity_completion_time": "2025-06-16T16:00:00Z"
            }
        }
        
        print(f"Testing message: {user_message['text']}")
        
        # Check profile completion first
        profile_completion = await dispatcher._check_profile_completion()
        print(f"Profile completion: {profile_completion:.1%}")
        print(f"Above threshold (50%): {profile_completion >= 0.5}")
        
        # Test workflow classification
        response = await dispatcher.process_message(user_message)
        
        workflow_type = response.get('workflow_type')
        confidence = response.get('confidence', 0)
        
        print(f"Classified as: {workflow_type} (confidence: {confidence:.2f})")
        
        if workflow_type == "post_activity":
            print("✅ SUCCESS: Correctly classified as post_activity")
        elif workflow_type == "onboarding":
            print("❌ ISSUE: Still classified as onboarding despite 50% completion")
        else:
            print(f"⚠️  UNEXPECTED: Classified as {workflow_type}")
        
        return workflow_type
        
    except Exception as e:
        print(f"Error testing post_activity classification: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_post_activity_classification())
