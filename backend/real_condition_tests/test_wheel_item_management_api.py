#!/usr/bin/env python3
"""
Test script for wheel item management API endpoints.

This script tests the new API endpoints for:
- Creating user feedback
- Removing wheel items
- Adding activities to wheels
- Enhanced activity search

Usage:
    python test_wheel_item_management_api.py
"""

import os
import sys
import django
import json
import requests
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, '/usr/src/app')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.contrib.auth.models import User
from apps.user.models import UserProfile, UserEnvironment, Demographics
from apps.activity.models import GenericActivity, ActivityTailored
from apps.main.models import Wheel, WheelItem, UserFeedback
from django.contrib.contenttypes.models import ContentType

class WheelItemManagementAPITester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.session = requests.Session()
        self.test_user = None
        self.test_wheel = None
        self.test_wheel_items = []
        
    def setup_test_data(self):
        """Create test data for the API tests"""
        print("🔧 Setting up test data...")
        
        # Create test user
        self.test_user, created = User.objects.get_or_create(
            username='test_wheel_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        
        # Create user profile
        user_profile, created = UserProfile.objects.get_or_create(
            user=self.test_user,
            defaults={
                'profile_name': 'Test Wheel User',
                'is_real': False
            }
        )
        
        # Create user environment
        user_env, created = UserEnvironment.objects.get_or_create(
            user_profile=user_profile,
            environment_name='Test Environment',
            defaults={
                'environment_description': 'Test environment for wheel management',
                'is_current': True,
                'environment_details': {}
            }
        )
        
        # Set current environment
        user_profile.current_environment = user_env
        user_profile.save()
        
        # Create some generic activities
        activities_data = [
            {
                'name': 'Morning Meditation',
                'description': 'Start your day with mindfulness',
                'code': 'test_meditation',
                'base_challenge_rating': 30,
                'duration_range': '10-20 minutes',
                'instructions': 'Sit quietly and focus on your breath'
            },
            {
                'name': 'Quick Exercise',
                'description': 'Get your body moving',
                'code': 'test_exercise',
                'base_challenge_rating': 50,
                'duration_range': '15-30 minutes',
                'instructions': 'Do some jumping jacks and stretches'
            },
            {
                'name': 'Creative Writing',
                'description': 'Express yourself through words',
                'code': 'test_writing',
                'base_challenge_rating': 40,
                'duration_range': '20-45 minutes',
                'instructions': 'Write about anything that comes to mind'
            }
        ]
        
        generic_activities = []
        for activity_data in activities_data:
            activity, created = GenericActivity.objects.get_or_create(
                code=activity_data['code'],
                defaults=activity_data
            )
            generic_activities.append(activity)
        
        # Create tailored activities
        tailored_activities = []
        for generic_activity in generic_activities:
            tailored_activity, created = ActivityTailored.objects.get_or_create(
                user_profile=user_profile,
                generic_activity=generic_activity,
                user_environment=user_env,
                defaults={
                    'name': generic_activity.name,
                    'description': generic_activity.description,
                    'instructions': generic_activity.instructions,
                    'base_challenge_rating': generic_activity.base_challenge_rating,
                    'challengingness': {},
                    'version': 1,
                    'tailorization_level': 50,
                    'duration_range': generic_activity.duration_range,
                    'social_requirements': {}
                }
            )
            tailored_activities.append(tailored_activity)
        
        # Create test wheel
        self.test_wheel, created = Wheel.objects.get_or_create(
            name=f"{user_profile.profile_name}'s Test Wheel",
            defaults={
                'created_by': 'test_script',
                'created_at': datetime.now().date()
            }
        )
        
        # Create wheel items
        self.test_wheel_items = []
        for i, tailored_activity in enumerate(tailored_activities[:2]):  # Only use first 2
            wheel_item, created = WheelItem.objects.get_or_create(
                id=f"test_item_{i+1}",
                wheel=self.test_wheel,
                defaults={
                    'percentage': 50.0,
                    'activity_tailored': tailored_activity
                }
            )
            self.test_wheel_items.append(wheel_item)
        
        print(f"✅ Test data created:")
        print(f"   - User: {self.test_user.username}")
        print(f"   - Wheel: {self.test_wheel.name}")
        print(f"   - Wheel items: {len(self.test_wheel_items)}")
        print(f"   - Generic activities: {len(generic_activities)}")
        print(f"   - Tailored activities: {len(tailored_activities)}")
        
        return user_profile, tailored_activities[2]  # Return unused activity for adding test
    
    def authenticate(self):
        """Authenticate with the test user"""
        print("🔐 Authenticating...")
        
        # For testing, we'll use Django's session authentication
        # In a real scenario, you'd use the login endpoint
        login_data = {
            'username': self.test_user.username,
            'password': 'testpass123'  # You'd need to set this password
        }
        
        # Set password for test user
        self.test_user.set_password('testpass123')
        self.test_user.save()
        
        response = self.session.post(f"{self.base_url}/api/auth/login/", json=login_data)
        if response.status_code == 200:
            print("✅ Authentication successful")
            return True
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    
    def test_activity_catalog_search(self):
        """Test the enhanced activity catalog with search functionality"""
        print("\n🔍 Testing activity catalog search...")
        
        # Test basic catalog retrieval
        response = self.session.get(f"{self.base_url}/api/activities/catalog/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Basic catalog retrieval: {data['total_count']} activities")
        else:
            print(f"❌ Basic catalog failed: {response.status_code}")
            return False
        
        # Test search functionality
        search_response = self.session.get(f"{self.base_url}/api/activities/catalog/?search=meditation")
        if search_response.status_code == 200:
            search_data = search_response.json()
            print(f"✅ Search functionality: {search_data['total_count']} results for 'meditation'")
        else:
            print(f"❌ Search failed: {search_response.status_code}")
            return False
        
        return True
    
    def test_user_feedback_creation(self):
        """Test creating user feedback"""
        print("\n💬 Testing user feedback creation...")
        
        wheel_item = self.test_wheel_items[0]
        feedback_data = {
            'feedback_type': 'wheel_item_refusal',
            'content_type': 'wheelitem',
            'object_id': wheel_item.id,
            'user_comment': 'I don\'t like this activity because it\'s too early in the morning',
            'criticality': 1,
            'context_data': {'test': True}
        }
        
        response = self.session.post(f"{self.base_url}/api/feedback/", json=feedback_data)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Feedback created successfully: ID {data['feedback_id']}")
            
            # Verify feedback was created in database
            feedback = UserFeedback.objects.get(id=data['feedback_id'])
            print(f"   - Feedback type: {feedback.feedback_type}")
            print(f"   - Comment: {feedback.user_comment}")
            return True
        else:
            print(f"❌ Feedback creation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    
    def test_wheel_item_removal(self):
        """Test removing a wheel item"""
        print("\n🗑️ Testing wheel item removal...")
        
        wheel_item = self.test_wheel_items[0]
        initial_count = WheelItem.objects.filter(wheel=self.test_wheel).count()
        
        response = self.session.delete(f"{self.base_url}/api/wheel-items/{wheel_item.id}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Wheel item removed successfully")
            print(f"   - Updated wheel has {len(data['wheel_data']['segments'])} segments")
            
            # Verify item was removed from database
            final_count = WheelItem.objects.filter(wheel=self.test_wheel).count()
            print(f"   - Database count: {initial_count} → {final_count}")
            return True
        else:
            print(f"❌ Wheel item removal failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    
    def test_wheel_item_addition(self, unused_activity):
        """Test adding an activity to the wheel"""
        print("\n➕ Testing wheel item addition...")
        
        initial_count = WheelItem.objects.filter(wheel=self.test_wheel).count()
        
        add_data = {
            'activity_id': f'tailored-{unused_activity.id}',
            'activity_type': 'tailored'
        }
        
        response = self.session.post(f"{self.base_url}/api/wheel-items/", json=add_data)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Activity added to wheel successfully")
            print(f"   - Updated wheel has {len(data['wheel_data']['segments'])} segments")
            
            # Verify item was added to database
            final_count = WheelItem.objects.filter(wheel=self.test_wheel).count()
            print(f"   - Database count: {initial_count} → {final_count}")
            return True
        else:
            print(f"❌ Activity addition failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    
    def cleanup_test_data(self):
        """Clean up test data"""
        print("\n🧹 Cleaning up test data...")
        
        # Remove test wheel items
        WheelItem.objects.filter(wheel=self.test_wheel).delete()
        
        # Remove test wheel
        if self.test_wheel:
            self.test_wheel.delete()
        
        # Remove test user feedback
        UserFeedback.objects.filter(user_profile__user=self.test_user).delete()
        
        # Remove test activities
        ActivityTailored.objects.filter(user_profile__user=self.test_user).delete()
        GenericActivity.objects.filter(code__startswith='test_').delete()
        
        # Remove test user profile and environment
        UserProfile.objects.filter(user=self.test_user).delete()
        
        # Remove test user
        if self.test_user:
            self.test_user.delete()
        
        print("✅ Test data cleaned up")
    
    def run_tests(self):
        """Run all tests"""
        print("🚀 Starting Wheel Item Management API Tests")
        print("=" * 50)
        
        try:
            # Setup
            user_profile, unused_activity = self.setup_test_data()
            
            if not self.authenticate():
                return False
            
            # Run tests
            tests = [
                self.test_activity_catalog_search,
                self.test_user_feedback_creation,
                self.test_wheel_item_removal,
                lambda: self.test_wheel_item_addition(unused_activity)
            ]
            
            passed = 0
            total = len(tests)
            
            for test in tests:
                try:
                    if test():
                        passed += 1
                except Exception as e:
                    print(f"❌ Test failed with exception: {e}")
            
            print("\n" + "=" * 50)
            print(f"🏁 Test Results: {passed}/{total} tests passed")
            
            if passed == total:
                print("🎉 All tests passed!")
                return True
            else:
                print("⚠️ Some tests failed")
                return False
                
        except Exception as e:
            print(f"❌ Test suite failed with exception: {e}")
            return False
        finally:
            self.cleanup_test_data()

if __name__ == "__main__":
    tester = WheelItemManagementAPITester()
    success = tester.run_tests()
    sys.exit(0 if success else 1)
