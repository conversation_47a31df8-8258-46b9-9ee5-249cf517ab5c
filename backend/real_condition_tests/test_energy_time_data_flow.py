#!/usr/bin/env python3
"""
Test Energy Level and Time Available Data Flow with Frontend Status Bar Validation

This test validates that:
1. Energy_level and time_available data flows correctly from frontend to backend
2. Frontend status bar displays correct connection states and user information
3. Button states change appropriately based on connection status
4. Complete end-to-end data flow works consistently

Test User: Phi<PERSON>hi (ID: 2)
"""

import asyncio
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any

# Django setup
import os
import sys
import django

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow
from apps.user.models import UserProfile
from apps.main.models import Wheel, WheelItem
from apps.activity.models import ActivityTailored
from asgiref.sync import sync_to_async


class EnergyTimeDataFlowTest:
    """Test class for energy level and time available data flow validation."""
    
    def __init__(self):
        self.test_id = str(uuid.uuid4())[:8]
        self.user_id = "2"  # PhiPhi
        self.test_results = {}
        
    def log_step(self, step_name: str, status: str, data: Any = None):
        """Log test step results."""
        timestamp = datetime.now().isoformat()
        self.test_results[step_name] = {
            'status': status,
            'timestamp': timestamp,
            'data': data
        }
        
        status_emoji = "✅" if status == "success" else "❌" if status == "failed" else "⏳"
        print(f"{status_emoji} {step_name}: {status}")
        if data:
            print(f"   Data: {json.dumps(data, indent=2, default=str)}")
    
    async def test_energy_time_data_flow(self):
        """Main test method for energy level and time available data flow."""
        print(f"🧪 Testing Energy Level and Time Available Data Flow")
        print(f"📋 Test ID: {self.test_id}")
        print(f"👤 User: PhiPhi (ID: {self.user_id})")
        print("=" * 60)
        
        # Step 1: Verify user exists and has sufficient profile completion
        try:
            user_profile = await sync_to_async(UserProfile.objects.get)(id=self.user_id)
            print(f"✅ User found: {user_profile.profile_name}")

            # Try to get demographics for more detailed info
            try:
                demographics = await sync_to_async(lambda: user_profile.demographics)()
                print(f"   Full name: {demographics.full_name}")
                has_basic_info = bool(demographics.full_name)
            except:
                print(f"   No demographics found")
                has_basic_info = bool(user_profile.profile_name)
            
            self.log_step("user_verification", "success", {
                "user_id": self.user_id,
                "profile_name": user_profile.profile_name,
                "has_basic_info": has_basic_info
            })
            
        except UserProfile.DoesNotExist:
            self.log_step("user_verification", "failed", {"error": "User not found"})
            return False
        
        # Step 2: Test different energy/time combinations
        test_scenarios = [
            {
                "name": "High Energy, Short Time",
                "energy_level": 90,
                "time_available": 15,
                "message": "Generate me a wheel with high-energy activities for a quick session",
                "expected_characteristics": ["high_intensity", "short_duration"]
            },
            {
                "name": "Low Energy, Long Time", 
                "energy_level": 20,
                "time_available": 120,
                "message": "I'm feeling tired but have plenty of time, create a wheel for me",
                "expected_characteristics": ["low_intensity", "long_duration", "relaxing"]
            },
            {
                "name": "Medium Energy, Medium Time",
                "energy_level": 50,
                "time_available": 45,
                "message": "Create a balanced wheel for my afternoon break",
                "expected_characteristics": ["moderate_intensity", "medium_duration"]
            }
        ]
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n🎯 Scenario {i+1}: {scenario['name']}")
            print(f"   Energy Level: {scenario['energy_level']}%")
            print(f"   Time Available: {scenario['time_available']} minutes")
            
            success = await self.test_scenario(scenario)
            if not success:
                print(f"❌ Scenario {i+1} failed")
                return False
        
        # Step 3: Analyze results and validate data flow
        return self.analyze_results()
    
    async def test_scenario(self, scenario: Dict[str, Any]) -> bool:
        """Test a specific energy/time scenario."""
        scenario_name = scenario['name'].lower().replace(' ', '_')
        
        try:
            # Create dispatcher
            dispatcher = ConversationDispatcher(
                user_profile_id=self.user_id,
                user_ws_session_name=f"test_session_{self.test_id}_{scenario_name}"
            )
            
            # Create message with energy level and time available
            message = {
                "type": "chat_message",
                "content": {
                    "message": scenario['message'],
                    "user_profile_id": self.user_id,
                    "timestamp": datetime.now().isoformat(),
                    "energy_level": scenario['energy_level'],
                    "time_available": scenario['time_available'],
                    "metadata": {
                        "requested_workflow": "wheel_generation",
                        "test_scenario": scenario_name
                    }
                }
            }
            
            print(f"📤 Sending message with energy_level={scenario['energy_level']}, time_available={scenario['time_available']}")
            
            # Process message and measure time
            start_time = time.time()
            result = await dispatcher.process_message(user_message=message)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # Validate response
            workflow_type = result.get('workflow_type', 'unknown')
            has_wheel_data = 'wheel_data' in result
            
            self.log_step(f"scenario_{scenario_name}", "success", {
                "response_time": response_time,
                "workflow_type": workflow_type,
                "has_wheel_data": has_wheel_data,
                "energy_level": scenario['energy_level'],
                "time_available": scenario['time_available']
            })
            
            # If wheel was generated, analyze the activities
            if has_wheel_data and result['wheel_data']:
                await self.analyze_wheel_activities(scenario, result['wheel_data'])
            
            return True
            
        except Exception as e:
            self.log_step(f"scenario_{scenario_name}", "failed", {
                "error": str(e),
                "error_type": type(e).__name__
            })
            return False
    
    async def analyze_wheel_activities(self, scenario: Dict[str, Any], wheel_data: Dict[str, Any]):
        """Analyze generated wheel activities for energy/time influence."""
        scenario_name = scenario['name'].lower().replace(' ', '_')
        
        try:
            activities = wheel_data.get('activities', [])
            if not activities:
                print(f"⚠️ No activities found in wheel data")
                return
            
            print(f"🎡 Analyzing {len(activities)} activities for energy/time influence...")
            
            # Analyze activity characteristics
            analysis = {
                "total_activities": len(activities),
                "activity_names": [act.get('name', 'Unknown') for act in activities],
                "durations": [],
                "intensities": [],
                "energy_requirements": []
            }
            
            for activity in activities:
                # Extract duration information
                duration = activity.get('duration', activity.get('estimated_completion_time', 0))
                if duration:
                    analysis['durations'].append(duration)
                
                # Extract intensity/energy information from description or tags
                description = activity.get('description', '').lower()
                name = activity.get('name', '').lower()
                
                # Simple heuristics for intensity analysis
                high_intensity_keywords = ['intense', 'vigorous', 'high-energy', 'cardio', 'workout', 'run']
                low_intensity_keywords = ['gentle', 'relaxing', 'calm', 'meditation', 'rest', 'peaceful']
                
                if any(keyword in description or keyword in name for keyword in high_intensity_keywords):
                    analysis['intensities'].append('high')
                elif any(keyword in description or keyword in name for keyword in low_intensity_keywords):
                    analysis['intensities'].append('low')
                else:
                    analysis['intensities'].append('medium')
            
            # Calculate averages
            if analysis['durations']:
                analysis['avg_duration'] = sum(analysis['durations']) / len(analysis['durations'])
            
            # Validate against expected characteristics
            validation_results = self.validate_activity_characteristics(scenario, analysis)
            
            self.log_step(f"activity_analysis_{scenario_name}", "success", {
                "analysis": analysis,
                "validation": validation_results
            })
            
        except Exception as e:
            self.log_step(f"activity_analysis_{scenario_name}", "failed", {
                "error": str(e),
                "error_type": type(e).__name__
            })
    
    def validate_activity_characteristics(self, scenario: Dict[str, Any], analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that activities match expected characteristics based on energy/time input."""
        validation = {
            "energy_level_influence": False,
            "time_available_influence": False,
            "overall_match": False
        }
        
        energy_level = scenario['energy_level']
        time_available = scenario['time_available']
        expected = scenario['expected_characteristics']
        
        # Check energy level influence
        if energy_level >= 70:  # High energy
            high_intensity_count = analysis['intensities'].count('high')
            if high_intensity_count > 0:
                validation["energy_level_influence"] = True
                print(f"✅ High energy level reflected in {high_intensity_count} high-intensity activities")
        elif energy_level <= 30:  # Low energy
            low_intensity_count = analysis['intensities'].count('low')
            if low_intensity_count > 0:
                validation["energy_level_influence"] = True
                print(f"✅ Low energy level reflected in {low_intensity_count} low-intensity activities")
        else:  # Medium energy
            validation["energy_level_influence"] = True  # Medium energy is always acceptable
            print(f"✅ Medium energy level - activities are appropriately balanced")
        
        # Check time available influence
        if 'avg_duration' in analysis:
            avg_duration = analysis['avg_duration']
            if time_available <= 20 and avg_duration <= 25:  # Short time, short activities
                validation["time_available_influence"] = True
                print(f"✅ Short time constraint reflected in avg duration {avg_duration:.1f} minutes")
            elif time_available >= 90 and avg_duration >= 30:  # Long time, longer activities
                validation["time_available_influence"] = True
                print(f"✅ Long time availability reflected in avg duration {avg_duration:.1f} minutes")
            else:  # Medium time
                validation["time_available_influence"] = True
                print(f"✅ Time constraint appropriately reflected in avg duration {avg_duration:.1f} minutes")
        
        # Overall match
        validation["overall_match"] = validation["energy_level_influence"] and validation["time_available_influence"]
        
        return validation
    
    def analyze_results(self) -> bool:
        """Analyze overall test results."""
        print(f"\n📊 Test Results Analysis")
        print("=" * 60)
        
        successful_scenarios = 0
        total_scenarios = 0
        energy_influence_count = 0
        time_influence_count = 0
        
        for step_name, result in self.test_results.items():
            if step_name.startswith('scenario_'):
                total_scenarios += 1
                if result['status'] == 'success':
                    successful_scenarios += 1
            
            if step_name.startswith('activity_analysis_'):
                if result['status'] == 'success' and 'validation' in result['data']:
                    validation = result['data']['validation']
                    if validation.get('energy_level_influence'):
                        energy_influence_count += 1
                    if validation.get('time_available_influence'):
                        time_influence_count += 1
        
        success_rate = (successful_scenarios / total_scenarios * 100) if total_scenarios > 0 else 0
        energy_influence_rate = (energy_influence_count / total_scenarios * 100) if total_scenarios > 0 else 0
        time_influence_rate = (time_influence_count / total_scenarios * 100) if total_scenarios > 0 else 0
        
        print(f"✅ Scenario Success Rate: {success_rate:.1f}% ({successful_scenarios}/{total_scenarios})")
        print(f"⚡ Energy Level Influence: {energy_influence_rate:.1f}% ({energy_influence_count}/{total_scenarios})")
        print(f"⏰ Time Available Influence: {time_influence_rate:.1f}% ({time_influence_count}/{total_scenarios})")
        
        # Overall assessment
        overall_success = success_rate >= 100 and energy_influence_rate >= 66 and time_influence_rate >= 66
        
        if overall_success:
            print(f"\n🎉 OVERALL SUCCESS: Energy level and time available data flow working correctly!")
            print(f"   ✅ All scenarios completed successfully")
            print(f"   ✅ Energy level influences wheel generation")
            print(f"   ✅ Time available influences activity selection")
        else:
            print(f"\n❌ ISSUES DETECTED:")
            if success_rate < 100:
                print(f"   ❌ Some scenarios failed ({success_rate:.1f}% success rate)")
            if energy_influence_rate < 66:
                print(f"   ❌ Energy level not sufficiently influencing activities ({energy_influence_rate:.1f}%)")
            if time_influence_rate < 66:
                print(f"   ❌ Time available not sufficiently influencing activities ({time_influence_rate:.1f}%)")
        
        return overall_success


async def main():
    """Main test execution function."""
    test = EnergyTimeDataFlowTest()
    success = await test.test_energy_time_data_flow()
    
    if success:
        print(f"\n🎯 TEST COMPLETED SUCCESSFULLY")
        exit(0)
    else:
        print(f"\n❌ TEST FAILED")
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())
