#!/usr/bin/env python3
"""
Debug Conversation Dispatcher Profile Issue

This script investigates the exact data being returned by execute_tool
in the conversation dispatcher vs direct tool calls.
"""

import os
import sys
import django
import asyncio

# Add the backend directory to the Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.agents.tools.get_user_profile_tool import get_user_profile
from apps.main.agents.tools.tools_util import execute_tool


async def debug_conversation_dispatcher_profile():
    """Debug the exact profile data being used by conversation dispatcher."""
    
    print("🔍 Debugging Conversation Dispatcher Profile Data")
    print("=" * 60)
    
    user_profile_id = "2"
    session_id = "debug_session"
    
    # Test 1: Direct tool call (what we expect)
    print("\n1️⃣ Direct get_user_profile tool call:")
    try:
        result = await get_user_profile({'input_data': {'user_profile_id': user_profile_id}})
        user_profile_data = result.get('user_profile', {})
        completion = user_profile_data.get('profile_completion', 0.0)
        
        print(f"   Profile Completion: {completion:.1%}")
        print(f"   Demographics: {'✓' if user_profile_data.get('demographics') else '✗'}")
        print(f"   Goals: {len(user_profile_data.get('goals', []))} records")
        print(f"   Current Environment: {'✓' if user_profile_data.get('current_environment') else '✗'}")
        print(f"   Trust Level: {'✓' if user_profile_data.get('trust_level') else '✗'}")
        print(f"   Current Mood: {'✓' if user_profile_data.get('current_mood') else '✗'}")
        
        # Check critical fields as defined in conversation dispatcher
        critical_missing = []
        if not user_profile_data.get('demographics'):
            critical_missing.append('demographics')
        if not user_profile_data.get('goals') or len(user_profile_data.get('goals', [])) < 1:
            critical_missing.append('goals (min 1)')
        if not user_profile_data.get('current_environment'):
            critical_missing.append('current_environment')
            
        print(f"   Critical Missing Fields: {critical_missing if critical_missing else 'None'}")
        
    except Exception as e:
        print(f"   ERROR: {e}")
    
    # Test 2: execute_tool call (what conversation dispatcher uses)
    print("\n2️⃣ execute_tool call (ConversationDispatcher method):")
    try:
        result = await execute_tool(
            tool_code="get_user_profile",
            tool_input={"input_data": {"user_profile_id": user_profile_id}},
            user_profile_id=user_profile_id,
            session_id=session_id
        )
        user_profile_data = result.get('user_profile', {})
        completion = user_profile_data.get('profile_completion', 0.0)
        
        print(f"   Profile Completion: {completion:.1%}")
        print(f"   Demographics: {'✓' if user_profile_data.get('demographics') else '✗'}")
        print(f"   Goals: {len(user_profile_data.get('goals', []))} records")
        print(f"   Current Environment: {'✓' if user_profile_data.get('current_environment') else '✗'}")
        print(f"   Trust Level: {'✓' if user_profile_data.get('trust_level') else '✗'}")
        print(f"   Current Mood: {'✓' if user_profile_data.get('current_mood') else '✗'}")
        
        # Check critical fields as defined in conversation dispatcher
        critical_missing = []
        if not user_profile_data.get('demographics'):
            critical_missing.append('demographics')
        if not user_profile_data.get('goals') or len(user_profile_data.get('goals', [])) < 1:
            critical_missing.append('goals (min 1)')
        if not user_profile_data.get('current_environment'):
            critical_missing.append('current_environment')
            
        print(f"   Critical Missing Fields: {critical_missing if critical_missing else 'None'}")
        
        # Show raw data structure for comparison
        print(f"\n   Raw Data Keys: {list(user_profile_data.keys())}")
        
        # Check specific fields that might be causing the issue
        demographics = user_profile_data.get('demographics')
        if demographics:
            print(f"   Demographics Type: {type(demographics)}")
            print(f"   Demographics Content: {demographics}")
            
        goals = user_profile_data.get('goals', [])
        if goals:
            print(f"   Goals Type: {type(goals)}")
            print(f"   Goals Count: {len(goals)}")
            print(f"   First Goal: {goals[0] if goals else 'None'}")
        
    except Exception as e:
        print(f"   ERROR: {e}")
    
    # Test 3: Simulate conversation dispatcher gap analysis
    print("\n3️⃣ Simulating ConversationDispatcher gap analysis:")
    try:
        # Use the same call as conversation dispatcher
        result = await execute_tool(
            tool_code="get_user_profile",
            tool_input={"input_data": {"user_profile_id": user_profile_id}},
            user_profile_id=user_profile_id,
            session_id=session_id
        )
        
        user_profile = result.get("user_profile", {})
        completion_percentage = user_profile.get("profile_completion", 0.0)
        
        print(f"   Retrieved completion_percentage: {completion_percentage}")
        
        # Simulate critical fields check
        critical_gaps = []
        
        # Check basic_demographics
        demographics = user_profile.get('demographics')
        if not demographics:
            critical_gaps.append('basic_demographics')
            print(f"   ❌ Missing: basic_demographics (demographics: {demographics})")
        else:
            print(f"   ✅ Has: basic_demographics (demographics: {type(demographics)})")
        
        # Check goals_aspirations
        goals = user_profile.get('goals')
        if not goals or (isinstance(goals, list) and len(goals) < 1):
            critical_gaps.append('goals_aspirations')
            print(f"   ❌ Missing: goals_aspirations (goals: {len(goals) if goals else 0} items)")
        else:
            print(f"   ✅ Has: goals_aspirations ({len(goals)} goals)")
        
        # Check current_environment
        current_env = user_profile.get('current_environment')
        if not current_env:
            critical_gaps.append('current_environment')
            print(f"   ❌ Missing: current_environment (current_environment: {current_env})")
        else:
            print(f"   ✅ Has: current_environment")
        
        print(f"\n   Total Critical Gaps: {len(critical_gaps)}")
        print(f"   Critical Gap Fields: {critical_gaps}")
        
        # Assess readiness
        if completion_percentage >= 0.7:
            readiness = 'ready'
        elif completion_percentage >= 0.3:
            readiness = 'partial'
        else:
            readiness = 'insufficient'
            
        print(f"   Profile Readiness: {readiness}")
        
        # This should match the log output we saw
        print(f"\n   Expected Log: Enhanced profile gap analysis: {len(critical_gaps)} critical, 0 important, 0 optional, readiness: {readiness}")
        
    except Exception as e:
        print(f"   ERROR: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 ANALYSIS:")
    print("If execute_tool shows 100% completion but direct call shows 0%,")
    print("then the issue is in the execute_tool wrapper or database isolation.")
    print("If both show the same data, then the issue is in the gap analysis logic.")


if __name__ == "__main__":
    asyncio.run(debug_conversation_dispatcher_profile())
