#!/usr/bin/env python3
"""
Progress Tracking Integration Test

This test verifies that progress updates are properly sent during wheel generation
and that the WebSocket communication is working correctly.

Usage: python test_progress_tracking_integration.py
"""

import os
import sys
import django
import asyncio
import json
import uuid
from datetime import datetime
from typing import List, Dict, Any

# Add the backend directory to Python path
sys.path.insert(0, '/usr/src/app')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.progress_tracking_service import ProgressTrackingService
from apps.main.tasks.wheel_generation_tasks import execute_wheel_generation_workflow
from channels.layers import get_channel_layer
from asgiref.sync import sync_to_async
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProgressTrackingTester:
    def __init__(self):
        self.captured_progress = []
        self.captured_websocket_messages = []
        self.channel_layer = get_channel_layer()
        
    async def setup_progress_monitoring(self):
        """Set up monitoring for progress updates"""
        print("🔧 Setting up progress monitoring...")
        
        # Get progress service
        self.progress_service = await ProgressTrackingService.get_instance()
        
        # Add callback to capture progress updates
        async def progress_callback(update):
            self.captured_progress.append(update)
            print(f"📊 Progress captured: {update.stage_name} - {update.progress_percent}%")
            print(f"    Message: {update.message}")
            print(f"    Tracker ID: {update.metadata.get('tracker_id', 'Unknown')}")
        
        # Store callback for later use
        self.progress_callback = progress_callback
        
    async def test_direct_progress_tracking(self):
        """Test progress tracking directly without Celery"""
        print("\n🧪 Testing direct progress tracking...")
        
        # Create a test tracker
        tracker = self.progress_service.create_tracker(
            name="Test Wheel Generation",
            user_id="2",  # Use existing user
            workflow_type="wheel_generation"
        )
        
        print(f"📊 Created tracker: {tracker.tracker_id}")
        
        # Add our callback
        tracker.update_callbacks.append(self.progress_callback)
        
        # Simulate workflow stages
        stages = [
            ("initialization", "Initializing Workflow", "Setting up..."),
            ("workflow_execution", "Executing Workflow", "Running agents..."),
            ("result_processing", "Processing Results", "Finalizing...")
        ]
        
        for i, (stage_id, stage_name, message) in enumerate(stages):
            print(f"\n🔄 Stage {i+1}: {stage_name}")
            
            # Start stage
            stage_id = tracker.start_stage(stage_id, stage_name, message)
            
            # Simulate progress updates
            for progress in [25, 50, 75, 100]:
                tracker.update_stage(stage_id, progress, f"{message} ({progress}%)")
                await asyncio.sleep(0.5)  # Small delay to simulate work
            
            # Complete stage
            tracker.complete_stage(stage_id, f"Completed {stage_name}")
        
        # Complete tracker
        tracker.complete_tracker("Workflow completed successfully")
        
        print(f"✅ Direct tracking test completed. Captured {len(self.captured_progress)} updates")
        
    async def test_celery_wheel_generation(self):
        """Test wheel generation through Celery task"""
        print("\n🎯 Testing Celery wheel generation with progress tracking...")
        
        # Clear previous captures
        self.captured_progress.clear()
        
        # Create test context
        context_packet = {
            'task_type': 'wheel_generation',
            'user_message': 'Test wheel generation with progress tracking',
            'energy_level': 50,
            'time_available_minutes': 60,
            'user_profile_id': '2'
        }
        
        # Execute the task (this will run in the current thread for testing)
        try:
            # Import the task function
            from apps.main.tasks.wheel_generation_tasks import execute_wheel_generation_workflow
            
            # Create a mock Celery request
            class MockRequest:
                def __init__(self):
                    self.id = str(uuid.uuid4())
            
            # Create task instance with mock request
            task = execute_wheel_generation_workflow
            task.request = MockRequest()
            
            print(f"🚀 Starting wheel generation task: {task.request.id}")
            
            # Run the task
            result = task(
                user_profile_id=2,
                context_packet=context_packet,
                workflow_id=str(uuid.uuid4())
            )
            
            print(f"✅ Task completed. Result type: {type(result)}")
            if isinstance(result, dict):
                print(f"    Result keys: {list(result.keys())}")
                if 'performance_metrics' in result:
                    print(f"    Performance metrics: {result['performance_metrics']}")
            
        except Exception as e:
            print(f"❌ Task execution failed: {e}")
            import traceback
            traceback.print_exc()
    
    async def monitor_websocket_messages(self):
        """Monitor WebSocket messages being sent"""
        print("\n📡 Monitoring WebSocket messages...")
        
        # This is a simplified version - in a real test we'd need to set up
        # a WebSocket client to receive messages
        
        # For now, we'll check if the channel layer is working
        if self.channel_layer:
            print("✅ Channel layer is available")
            
            # Try to send a test message
            try:
                await self.channel_layer.group_send(
                    "user_session_group_2",
                    {
                        'type': 'progress_update',
                        'data': {
                            'type': 'progress_update',
                            'tracker_id': 'test-tracker',
                            'stage_id': 'test-stage',
                            'stage_name': 'Test Stage',
                            'stage': 'processing',
                            'progress_percent': 50.0,
                            'message': 'Test message',
                            'timestamp': datetime.now().isoformat(),
                            'priority': 'normal',
                            'workflow_type': 'wheel_generation'
                        }
                    }
                )
                print("✅ Test WebSocket message sent successfully")
            except Exception as e:
                print(f"❌ Failed to send WebSocket message: {e}")
        else:
            print("❌ Channel layer not available")
    
    async def analyze_results(self):
        """Analyze the captured results"""
        print("\n📊 ANALYSIS RESULTS:")
        print("=" * 50)
        
        print(f"📊 Total progress updates captured: {len(self.captured_progress)}")
        
        if self.captured_progress:
            print("\n📊 Progress Update Details:")
            for i, update in enumerate(self.captured_progress):
                print(f"  {i+1}. Stage: {update.stage_name}")
                print(f"     Progress: {update.progress_percent}%")
                print(f"     Message: {update.message}")
                print(f"     Status: {update.stage.value}")
                print(f"     Timestamp: {update.timestamp}")
                if update.metadata:
                    print(f"     Metadata: {update.metadata}")
                print()
        else:
            print("❌ No progress updates captured")
        
        # Check if progress service is working
        active_trackers = len(self.progress_service.active_trackers)
        print(f"📊 Active trackers: {active_trackers}")
        
        # Check WebSocket integration
        await self.monitor_websocket_messages()
    
    async def run_comprehensive_test(self):
        """Run comprehensive progress tracking test"""
        print("🚀 Starting Comprehensive Progress Tracking Test")
        print("=" * 60)
        
        try:
            # Setup
            await self.setup_progress_monitoring()
            
            # Test direct progress tracking
            await self.test_direct_progress_tracking()
            
            # Test Celery integration
            await self.test_celery_wheel_generation()
            
            # Analyze results
            await self.analyze_results()
            
            print("\n✅ Comprehensive test completed successfully")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()

async def main():
    """Main test function"""
    tester = ProgressTrackingTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
