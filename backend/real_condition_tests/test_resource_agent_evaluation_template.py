#!/usr/bin/env python3
"""
Test ResourceAgent Evaluation Template - Create targeted evaluation criteria for ResourceAgent

This test creates a specialized evaluation template for ResourceAgent that focuses on:
- Resource analysis accuracy
- Inventory completeness and categorization
- Capability assessment quality
- Limitation identification and severity
- Environment analysis relevance
- Feasibility score calculation accuracy

The template provides clear, actionable criteria for evaluating ResourceAgent performance.
"""

import os
import sys
import django
from datetime import datetime

# Setup Django
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.models import EvaluationCriteriaTemplate


def create_resource_agent_evaluation_template():
    """Create a comprehensive evaluation template for ResourceAgent"""
    
    print("🎯 Creating ResourceAgent Evaluation Template")
    print("=" * 60)
    
    try:
        # Define the evaluation criteria
        evaluation_criteria = {
            "template_name": "ResourceAgent Comprehensive Analysis",
            "template_version": "1.0",
            "agent_type": "ResourceAgent",
            "description": "Comprehensive evaluation template for ResourceAgent focusing on resource analysis accuracy, inventory assessment, and feasibility calculation.",
            
            "evaluation_dimensions": {
                "resource_analysis_accuracy": {
                    "weight": 0.25,
                    "description": "Accuracy and completeness of resource context analysis",
                    "criteria": [
                        "Inventory items are correctly identified and categorized",
                        "Capabilities are accurately assessed with appropriate skill levels",
                        "Limitations are properly identified with correct severity levels",
                        "Environment analysis reflects actual user context"
                    ]
                },
                
                "data_completeness": {
                    "weight": 0.20,
                    "description": "Completeness of data extraction and processing",
                    "criteria": [
                        "All available inventory items are included",
                        "Capability domains are comprehensive",
                        "Limitation types cover all relevant constraints",
                        "Environment factors are thoroughly analyzed"
                    ]
                },
                
                "feasibility_calculation": {
                    "weight": 0.20,
                    "description": "Accuracy of feasibility score calculation",
                    "criteria": [
                        "Feasibility score reflects actual resource availability",
                        "Score considers all relevant constraints and opportunities",
                        "Calculation methodology is consistent and logical",
                        "Score provides actionable insights for activity planning"
                    ]
                },
                
                "categorization_quality": {
                    "weight": 0.15,
                    "description": "Quality of resource categorization and organization",
                    "criteria": [
                        "Inventory items are logically categorized",
                        "Capabilities are grouped by relevant domains",
                        "Limitations are classified by appropriate types",
                        "Categories facilitate easy understanding and use"
                    ]
                },
                
                "analysis_insights": {
                    "weight": 0.20,
                    "description": "Quality of analytical insights and recommendations",
                    "criteria": [
                        "Key opportunities are accurately identified",
                        "Primary constraints are properly highlighted",
                        "Recommended activity types are relevant and achievable",
                        "Analysis provides actionable guidance for activity selection"
                    ]
                }
            },
            
            "scoring_guidelines": {
                "excellent": {
                    "range": "90-100",
                    "description": "Comprehensive, accurate analysis with high-quality insights"
                },
                "good": {
                    "range": "70-89", 
                    "description": "Solid analysis with minor gaps or inaccuracies"
                },
                "satisfactory": {
                    "range": "50-69",
                    "description": "Basic analysis meeting minimum requirements"
                },
                "needs_improvement": {
                    "range": "30-49",
                    "description": "Significant gaps or inaccuracies in analysis"
                },
                "poor": {
                    "range": "0-29",
                    "description": "Major deficiencies requiring substantial improvement"
                }
            },
            
            "evaluation_prompts": {
                "primary_prompt": """
Evaluate this ResourceAgent analysis based on the following criteria:

1. RESOURCE ANALYSIS ACCURACY (25%):
   - Are inventory items correctly identified and categorized?
   - Are capabilities accurately assessed with appropriate skill levels?
   - Are limitations properly identified with correct severity levels?
   - Does environment analysis reflect actual user context?

2. DATA COMPLETENESS (20%):
   - Are all available inventory items included?
   - Are capability domains comprehensive?
   - Do limitation types cover all relevant constraints?
   - Are environment factors thoroughly analyzed?

3. FEASIBILITY CALCULATION (20%):
   - Does feasibility score reflect actual resource availability?
   - Does score consider all relevant constraints and opportunities?
   - Is calculation methodology consistent and logical?
   - Does score provide actionable insights for activity planning?

4. CATEGORIZATION QUALITY (15%):
   - Are inventory items logically categorized?
   - Are capabilities grouped by relevant domains?
   - Are limitations classified by appropriate types?
   - Do categories facilitate easy understanding and use?

5. ANALYSIS INSIGHTS (20%):
   - Are key opportunities accurately identified?
   - Are primary constraints properly highlighted?
   - Are recommended activity types relevant and achievable?
   - Does analysis provide actionable guidance for activity selection?

Provide a score from 0-100 and detailed feedback for each dimension.
""",
                
                "follow_up_prompts": [
                    "What specific improvements could enhance the resource analysis accuracy?",
                    "Are there any missing inventory categories or capability domains?",
                    "How could the feasibility calculation be made more precise?",
                    "What additional insights would improve activity recommendations?"
                ]
            }
        }
        
        # Create or update the evaluation template
        template, created = EvaluationCriteriaTemplate.objects.update_or_create(
            name="ResourceAgent_Comprehensive_Analysis_v1",
            defaults={
                'description': evaluation_criteria['description'],
                'workflow_type': 'agent_benchmark',
                'category': 'ResourceAgent',
                'criteria': evaluation_criteria,
                'contextual_criteria': {},
                'variable_ranges': {},
                'is_active': True
            }
        )
        
        if created:
            print("✅ Created new ResourceAgent evaluation template")
        else:
            print("✅ Updated existing ResourceAgent evaluation template")
        
        print(f"📋 Template ID: {template.id}")
        print(f"📋 Template Name: {template.name}")
        print(f"📋 Workflow Type: {template.workflow_type}")
        print(f"📋 Category: {template.category}")
        
        # Validate template structure
        print(f"\n🔍 Template Validation:")
        criteria = template.criteria
        
        if 'evaluation_dimensions' in criteria:
            dimensions = criteria['evaluation_dimensions']
            total_weight = sum(dim['weight'] for dim in dimensions.values())
            print(f"✅ Evaluation dimensions: {len(dimensions)}")
            print(f"✅ Total weight: {total_weight:.2f} (should be 1.0)")
            
            for dim_name, dim_data in dimensions.items():
                print(f"   - {dim_name}: {dim_data['weight']:.2f} weight, {len(dim_data['criteria'])} criteria")
        
        if 'scoring_guidelines' in criteria:
            guidelines = criteria['scoring_guidelines']
            print(f"✅ Scoring guidelines: {len(guidelines)} levels")
        
        if 'evaluation_prompts' in criteria:
            prompts = criteria['evaluation_prompts']
            print(f"✅ Evaluation prompts: Primary + {len(prompts.get('follow_up_prompts', []))} follow-up")
        
        print(f"\n🎯 Usage Instructions:")
        print(f"1. This template is now available for ResourceAgent benchmarks")
        print(f"2. Use template ID {template.id} when running evaluations")
        print(f"3. Template focuses on resource analysis quality and accuracy")
        print(f"4. Provides detailed feedback for improving ResourceAgent performance")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create evaluation template: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main execution"""
    print("🚀 ResourceAgent Evaluation Template Creation")
    print("=" * 60)
    
    result = create_resource_agent_evaluation_template()
    
    if result:
        print("\n🎉 ResourceAgent Evaluation Template Creation SUCCESSFUL")
        print("✅ Template ready for use in benchmarks")
        print("✅ Comprehensive evaluation criteria defined")
        print("✅ Targeted for ResourceAgent performance assessment")
        return 0
    else:
        print("\n❌ ResourceAgent Evaluation Template Creation FAILED")
        return 1


if __name__ == "__main__":
    exit(main())
