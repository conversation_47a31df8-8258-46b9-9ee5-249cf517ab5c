#!/usr/bin/env python3
"""
Test: Empty Profile Wheel Request Behavior
Purpose: Verify that users with empty profiles are asked for information first, not given wheels immediately
"""

import os
import sys
import asyncio
import django
from django.conf import settings

# Add the backend directory to the Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.services.mentor_service import MentorService
from apps.user.models import UserProfile
from apps.main.agents.tools.tools_util import execute_tool
from asgiref.sync import sync_to_async
import uuid
import time

async def test_empty_profile_wheel_request():
    """Test that empty profile users are asked for information first, not given wheels"""
    
    print("🔍 Empty Profile Wheel Request Test")
    test_id = str(uuid.uuid4())[:8]
    session_id = f"empty_profile_test_{test_id}"
    print(f"   Test ID: {test_id}")
    print(f"   Session: {session_id}")
    print(f"   Scenario: User with completely empty profile requests wheel")
    
    # Phase 1: Create completely empty user profile
    print("\n📋 Phase 1: Creating completely empty user profile")
    start_time = time.time()

    # Create Django auth user first
    from django.contrib.auth import get_user_model
    User = get_user_model()

    @sync_to_async
    def create_test_user():
        auth_user = User.objects.create_user(
            username=f"empty_test_{test_id}",
            email=f"empty_test_{test_id}@test.local"
        )

        # Create minimal user profile (completely empty)
        user_profile = UserProfile.objects.create(
            user=auth_user,
            profile_name=f"empty_test_user_{test_id}",
            current_environment=None,  # Completely empty
            is_real=False  # Test profile
        )
        return user_profile

    user_profile = await create_test_user()
    user_profile_id = str(user_profile.id)
    
    # Verify profile is completely empty
    profile_result = await execute_tool("get_user_profile", {
        "input_data": {"user_profile_id": user_profile_id}
    })
    
    profile_completion = profile_result.get("user_profile", {}).get("profile_completion", 0.0)
    setup_time = time.time() - start_time
    
    print(f"   ✓ Test user created: ID {user_profile_id}")
    print(f"   ✓ Profile completion: {profile_completion:.1%}")
    print(f"   ✓ Setup completed in {setup_time:.2f}s")
    
    if profile_completion > 0.0:
        print(f"   ⚠️  WARNING: Profile should be 0.0% but is {profile_completion:.1%}")
    
    # Phase 2: Test direct wheel request with empty profile
    print("\n🎯 Phase 2: Testing direct wheel request with empty profile")
    
    # Initialize services
    mentor_service = MentorService(user_profile_id)
    dispatcher = ConversationDispatcher(user_profile_id, user_ws_session_name=None)
    
    # Test different wheel request messages
    test_messages = [
        "give me a wheel",
        "I want activities", 
        "create wheel",
        "show me activities",
        "make me a wheel"
    ]
    
    results = []
    
    for i, message in enumerate(test_messages):
        print(f"\n   Testing message {i+1}/5: '{message}'")
        start_time = time.time()
        
        # Send wheel request
        response = await dispatcher.process_message({
            'text': message,  # Use 'text' instead of 'content'
            'metadata': {
                'workflow_id': str(uuid.uuid4()),
                'session_id': f"{session_id}_{i}"
            }
        })
        
        response_time = time.time() - start_time
        workflow_type = response.get('workflow_type', 'unknown')
        direct_response = response.get('direct_response', '')
        
        # Analyze response
        is_asking_for_info = any(keyword in direct_response.lower() for keyword in [
            'tell me', 'what', 'how are you feeling', 'information', 'about you',
            'help me understand', 'share', 'describe'
        ])
        
        is_giving_wheel = workflow_type == 'wheel_generation'
        is_onboarding = workflow_type == 'onboarding'
        
        result = {
            'message': message,
            'response_time': response_time,
            'workflow_type': workflow_type,
            'is_asking_for_info': is_asking_for_info,
            'is_giving_wheel': is_giving_wheel,
            'is_onboarding': is_onboarding,
            'direct_response_preview': direct_response[:100] + '...' if len(direct_response) > 100 else direct_response
        }
        results.append(result)
        
        print(f"      ✓ Response time: {response_time:.2f}s")
        print(f"      ✓ Workflow: {workflow_type}")
        print(f"      ✓ Asking for info: {'✓' if is_asking_for_info else '❌'}")
        print(f"      ✓ Giving wheel: {'❌' if not is_giving_wheel else '✓ PROBLEM!'}")
        print(f"      ✓ Response preview: '{result['direct_response_preview']}'")
    
    # Phase 3: Analysis and reporting
    print("\n📊 Analysis Report")
    print("=" * 60)
    
    correct_behavior_count = sum(1 for r in results if r['is_onboarding'] and r['is_asking_for_info'] and not r['is_giving_wheel'])
    incorrect_behavior_count = sum(1 for r in results if r['is_giving_wheel'])
    
    print(f"Total Tests: {len(results)}")
    print(f"Correct Behavior (asking for info): {correct_behavior_count}")
    print(f"Incorrect Behavior (giving wheel): {incorrect_behavior_count}")
    print(f"Success Rate: {(correct_behavior_count / len(results)) * 100:.1f}%")
    
    print(f"\nAverage Response Time: {sum(r['response_time'] for r in results) / len(results):.2f}s")
    
    # Detailed results
    print(f"\nDetailed Results:")
    print("-" * 60)
    for i, result in enumerate(results, 1):
        status = "✓ CORRECT" if (result['is_onboarding'] and result['is_asking_for_info'] and not result['is_giving_wheel']) else "❌ INCORRECT"
        print(f"{i}. '{result['message']}' -> {result['workflow_type']} ({result['response_time']:.2f}s) {status}")
    
    # Final assessment
    print(f"\n🎯 Final Assessment:")
    if incorrect_behavior_count == 0:
        print("✅ PERFECT: All empty profile wheel requests correctly trigger onboarding")
        print("✅ System properly asks for information before providing wheels")
    else:
        print(f"❌ ISSUE FOUND: {incorrect_behavior_count} requests incorrectly gave wheels to empty profiles")
        print("❌ System should ask for information first, not provide wheels immediately")
    
    return {
        'total_tests': len(results),
        'correct_behavior': correct_behavior_count,
        'incorrect_behavior': incorrect_behavior_count,
        'success_rate': (correct_behavior_count / len(results)) * 100,
        'results': results
    }

if __name__ == "__main__":
    asyncio.run(test_empty_profile_wheel_request())
