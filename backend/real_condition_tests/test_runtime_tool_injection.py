#!/usr/bin/env python3
"""
Test Runtime Tool Injection for Mentor Agent

This test verifies that the new runtime tool injection mechanism works correctly.
"""

import os
import sys
import django
import asyncio
import logging

# Add the backend directory to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.agents.mentor_agent import MentorAgent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RuntimeToolInjectionTest:
    """Test suite for runtime tool injection functionality."""
    
    def __init__(self):
        self.test_user_id = "test_user_123"
        self.results = {}
    
    async def run_all_tests(self):
        """Run all runtime tool injection tests."""
        logger.info("🚀 Starting Runtime Tool Injection Tests")
        
        try:
            # Test 1: Basic tool injection
            await self.test_basic_tool_injection()
            
            # Test 2: Instruction injection
            await self.test_instruction_injection()
            
            # Test 3: Combined injection
            await self.test_combined_injection()
            
            # Test 4: Clear enhancements
            await self.test_clear_enhancements()
            
            # Generate report
            self.generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ Runtime tool injection test failed: {str(e)}", exc_info=True)
            return False
        
        logger.info("✅ Runtime Tool Injection Tests Completed")
        return True
    
    async def test_basic_tool_injection(self):
        """Test basic tool injection functionality."""
        logger.info("🔍 Testing Basic Tool Injection")
        
        try:
            # Create mentor agent
            agent = MentorAgent(user_profile_id=self.test_user_id)
            
            # Load minimal configuration
            await agent._ensure_loaded(minimal_tools=True)
            
            # Verify base tools are minimal
            base_tool_count = len(agent.available_tools)
            logger.info(f"📊 Base tools loaded: {base_tool_count}")
            
            # Create mock runtime tools
            runtime_tools = [
                {
                    'code': 'test_tool_1',
                    'name': 'Test Tool 1',
                    'description': 'A test tool for runtime injection'
                },
                {
                    'code': 'test_tool_2', 
                    'name': 'Test Tool 2',
                    'description': 'Another test tool for runtime injection'
                }
            ]
            
            # Inject tools
            agent.inject_tools(runtime_tools)
            
            # Verify effective tools include runtime tools
            effective_tool_count = len(agent.effective_tools)
            runtime_tool_count = len(agent.runtime_tools)
            
            logger.info(f"📊 Runtime tools injected: {runtime_tool_count}")
            logger.info(f"📊 Effective tools total: {effective_tool_count}")
            
            # Verify the math
            expected_total = base_tool_count + runtime_tool_count
            assert effective_tool_count == expected_total, f"Expected {expected_total} tools, got {effective_tool_count}"
            
            # Verify runtime tools are in effective tools
            effective_tool_codes = [tool.get('code') for tool in agent.effective_tools]
            for runtime_tool in runtime_tools:
                assert runtime_tool['code'] in effective_tool_codes, f"Runtime tool {runtime_tool['code']} not found in effective tools"
            
            self.results['basic_tool_injection'] = {
                'base_tools': base_tool_count,
                'runtime_tools': runtime_tool_count,
                'effective_tools': effective_tool_count,
                'success': True
            }
            
            logger.info("✅ Basic tool injection test passed")
            
        except Exception as e:
            logger.error(f"❌ Basic tool injection test failed: {str(e)}")
            self.results['basic_tool_injection'] = {'success': False, 'error': str(e)}
    
    async def test_instruction_injection(self):
        """Test instruction injection functionality."""
        logger.info("🔍 Testing Instruction Injection")
        
        try:
            # Create mentor agent
            agent = MentorAgent(user_profile_id=self.test_user_id)
            
            # Load minimal configuration
            await agent._ensure_loaded(minimal_tools=True)
            
            # Get base instructions
            base_instructions = agent.effective_instructions
            base_length = len(base_instructions)
            logger.info(f"📊 Base instructions length: {base_length} chars")
            
            # Inject runtime instructions
            runtime_instructions = """
            RUNTIME ENHANCEMENT:
            You are now operating in a special test mode. Please be extra helpful and provide detailed responses.
            Focus on being encouraging and supportive in all interactions.
            """
            
            agent.inject_instructions(runtime_instructions)
            
            # Verify effective instructions include runtime enhancement
            effective_instructions = agent.effective_instructions
            effective_length = len(effective_instructions)
            
            logger.info(f"📊 Effective instructions length: {effective_length} chars")
            
            # Verify the runtime instructions are included
            assert runtime_instructions.strip() in effective_instructions, "Runtime instructions not found in effective instructions"
            assert effective_length > base_length, "Effective instructions should be longer than base instructions"
            
            self.results['instruction_injection'] = {
                'base_length': base_length,
                'runtime_length': len(runtime_instructions),
                'effective_length': effective_length,
                'success': True
            }
            
            logger.info("✅ Instruction injection test passed")
            
        except Exception as e:
            logger.error(f"❌ Instruction injection test failed: {str(e)}")
            self.results['instruction_injection'] = {'success': False, 'error': str(e)}
    
    async def test_combined_injection(self):
        """Test combined tool and instruction injection."""
        logger.info("🔍 Testing Combined Injection")
        
        try:
            # Create mentor agent
            agent = MentorAgent(user_profile_id=self.test_user_id)
            
            # Load minimal configuration
            await agent._ensure_loaded(minimal_tools=True)
            
            # Inject both tools and instructions
            runtime_tools = [{'code': 'combined_test_tool', 'name': 'Combined Test Tool'}]
            runtime_instructions = "COMBINED TEST: Use the combined test tool when appropriate."
            
            agent.inject_tools(runtime_tools)
            agent.inject_instructions(runtime_instructions)
            
            # Verify both are effective
            effective_tools = agent.effective_tools
            effective_instructions = agent.effective_instructions
            
            # Check tools
            tool_codes = [tool.get('code') for tool in effective_tools]
            assert 'combined_test_tool' in tool_codes, "Combined test tool not found"
            
            # Check instructions
            assert "COMBINED TEST" in effective_instructions, "Combined test instructions not found"
            
            self.results['combined_injection'] = {
                'tools_injected': len(agent.runtime_tools),
                'instructions_injected': len(agent.runtime_instructions),
                'success': True
            }
            
            logger.info("✅ Combined injection test passed")
            
        except Exception as e:
            logger.error(f"❌ Combined injection test failed: {str(e)}")
            self.results['combined_injection'] = {'success': False, 'error': str(e)}
    
    async def test_clear_enhancements(self):
        """Test clearing runtime enhancements."""
        logger.info("🔍 Testing Clear Enhancements")
        
        try:
            # Create mentor agent
            agent = MentorAgent(user_profile_id=self.test_user_id)
            
            # Load minimal configuration
            await agent._ensure_loaded(minimal_tools=True)
            
            # Get baseline
            baseline_tools = len(agent.effective_tools)
            baseline_instructions = agent.effective_instructions
            
            # Inject enhancements
            agent.inject_tools([{'code': 'temp_tool', 'name': 'Temporary Tool'}])
            agent.inject_instructions("TEMPORARY: This is temporary.")
            
            # Verify enhancements are active
            enhanced_tools = len(agent.effective_tools)
            enhanced_instructions = agent.effective_instructions
            
            assert enhanced_tools > baseline_tools, "Tools should be enhanced"
            assert "TEMPORARY" in enhanced_instructions, "Instructions should be enhanced"
            
            # Clear enhancements
            agent.clear_runtime_enhancements()
            
            # Verify back to baseline
            cleared_tools = len(agent.effective_tools)
            cleared_instructions = agent.effective_instructions
            
            assert cleared_tools == baseline_tools, f"Tools should be back to baseline: {cleared_tools} vs {baseline_tools}"
            assert cleared_instructions == baseline_instructions, "Instructions should be back to baseline"
            
            self.results['clear_enhancements'] = {
                'baseline_tools': baseline_tools,
                'enhanced_tools': enhanced_tools,
                'cleared_tools': cleared_tools,
                'success': True
            }
            
            logger.info("✅ Clear enhancements test passed")
            
        except Exception as e:
            logger.error(f"❌ Clear enhancements test failed: {str(e)}")
            self.results['clear_enhancements'] = {'success': False, 'error': str(e)}
    
    def generate_test_report(self):
        """Generate comprehensive test report."""
        logger.info("📋 Generating Test Report")
        
        report = f"""
# Runtime Tool Injection Test Report

## Test Results Summary

### Basic Tool Injection
- **Status**: {'✅ PASSED' if self.results.get('basic_tool_injection', {}).get('success') else '❌ FAILED'}
- **Base Tools**: {self.results.get('basic_tool_injection', {}).get('base_tools', 'N/A')}
- **Runtime Tools**: {self.results.get('basic_tool_injection', {}).get('runtime_tools', 'N/A')}
- **Effective Tools**: {self.results.get('basic_tool_injection', {}).get('effective_tools', 'N/A')}

### Instruction Injection
- **Status**: {'✅ PASSED' if self.results.get('instruction_injection', {}).get('success') else '❌ FAILED'}
- **Base Length**: {self.results.get('instruction_injection', {}).get('base_length', 'N/A')} chars
- **Runtime Length**: {self.results.get('instruction_injection', {}).get('runtime_length', 'N/A')} chars
- **Effective Length**: {self.results.get('instruction_injection', {}).get('effective_length', 'N/A')} chars

### Combined Injection
- **Status**: {'✅ PASSED' if self.results.get('combined_injection', {}).get('success') else '❌ FAILED'}
- **Tools Injected**: {self.results.get('combined_injection', {}).get('tools_injected', 'N/A')}
- **Instructions Injected**: {self.results.get('combined_injection', {}).get('instructions_injected', 'N/A')} chars

### Clear Enhancements
- **Status**: {'✅ PASSED' if self.results.get('clear_enhancements', {}).get('success') else '❌ FAILED'}
- **Baseline Tools**: {self.results.get('clear_enhancements', {}).get('baseline_tools', 'N/A')}
- **Enhanced Tools**: {self.results.get('clear_enhancements', {}).get('enhanced_tools', 'N/A')}
- **Cleared Tools**: {self.results.get('clear_enhancements', {}).get('cleared_tools', 'N/A')}

## Architecture Validation

### ✅ Achievements
1. **Runtime Tool Injection**: Successfully implemented dynamic tool injection
2. **Runtime Instruction Enhancement**: Successfully implemented dynamic instruction enhancement
3. **Minimal Base Configuration**: Agent loads with minimal tools by default
4. **Clean Enhancement Management**: Clear separation between base and runtime enhancements
5. **Effective Property Pattern**: Clean interface for accessing combined configurations

### 🎯 Next Steps
1. Integrate with MentorService for contextual enhancement
2. Implement ConversationDispatcher intelligence
3. Refactor profile completion workflow
4. Create comprehensive integration tests

---
Generated: {asyncio.get_event_loop().time()}
Test User ID: {self.test_user_id}
"""
        
        # Save report
        with open('/usr/src/app/real_condition_tests/RUNTIME_INJECTION_REPORT.md', 'w') as f:
            f.write(report)
        
        logger.info("📋 Test report saved to RUNTIME_INJECTION_REPORT.md")
        print(report)

async def main():
    """Main test execution."""
    test = RuntimeToolInjectionTest()
    success = await test.run_all_tests()
    
    if success:
        print("\n✅ All runtime tool injection tests completed successfully!")
        return 0
    else:
        print("\n❌ Some runtime tool injection tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
