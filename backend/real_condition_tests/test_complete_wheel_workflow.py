#!/usr/bin/env python3
"""
Complete workflow test for wheel item management.

This script tests the entire user workflow:
1. Generate wheel → 2. Remove item → 3. Add item → 4. Verify wheel updates

Usage:
    python test_complete_wheel_workflow.py
"""

import os
import sys
import django
import json
import time
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, '/usr/src/app')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.contrib.auth.models import User
from apps.user.models import UserProfile, UserEnvironment
from apps.activity.models import GenericActivity, ActivityTailored
from apps.main.models import Wheel, WheelItem, UserFeedback
from django.test import Client
from django.contrib.contenttypes.models import ContentType

class CompleteWheelWorkflowTester:
    def __init__(self):
        self.client = Client()
        self.test_user = None
        self.user_profile = None
        
    def setup_test_environment(self):
        """Setup test user and environment"""
        print("🔧 Setting up test environment...")
        
        # Create test user
        self.test_user, created = User.objects.get_or_create(
            username='workflow_test_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Workflow',
                'last_name': 'Tester'
            }
        )
        self.test_user.set_password('testpass123')
        self.test_user.save()
        
        # Create user profile
        self.user_profile, created = UserProfile.objects.get_or_create(
            user=self.test_user,
            defaults={
                'profile_name': 'Workflow Test User',
                'is_real': False
            }
        )
        
        # Create user environment
        user_env, created = UserEnvironment.objects.get_or_create(
            user_profile=self.user_profile,
            environment_name='Test Environment',
            defaults={
                'environment_description': 'Test environment for workflow',
                'is_current': True,
                'environment_details': {}
            }
        )
        
        self.user_profile.current_environment = user_env
        self.user_profile.save()
        
        # Login
        login_success = self.client.login(username='workflow_test_user', password='testpass123')
        if not login_success:
            raise Exception("Failed to login test user")
        
        print(f"✅ Test environment ready for user: {self.test_user.username}")
        return True
    
    def create_test_activities(self):
        """Create test activities for the workflow"""
        print("🎯 Creating test activities...")
        
        activities_data = [
            {
                'name': 'Morning Yoga',
                'description': 'Start your day with gentle stretching',
                'code': 'workflow_yoga',
                'base_challenge_rating': 35,
                'duration_range': '15-30 minutes',
                'instructions': 'Follow a gentle yoga routine'
            },
            {
                'name': 'Reading Session',
                'description': 'Expand your knowledge through reading',
                'code': 'workflow_reading',
                'base_challenge_rating': 25,
                'duration_range': '20-45 minutes',
                'instructions': 'Read a book or article of your choice'
            },
            {
                'name': 'Nature Walk',
                'description': 'Connect with nature and get fresh air',
                'code': 'workflow_walk',
                'base_challenge_rating': 30,
                'duration_range': '20-60 minutes',
                'instructions': 'Take a peaceful walk in nature'
            },
            {
                'name': 'Cooking Practice',
                'description': 'Learn new culinary skills',
                'code': 'workflow_cooking',
                'base_challenge_rating': 45,
                'duration_range': '30-90 minutes',
                'instructions': 'Try a new recipe or cooking technique'
            }
        ]
        
        generic_activities = []
        tailored_activities = []
        
        for activity_data in activities_data:
            # Create generic activity
            generic_activity, created = GenericActivity.objects.get_or_create(
                code=activity_data['code'],
                defaults=activity_data
            )
            generic_activities.append(generic_activity)
            
            # Create tailored activity
            tailored_activity, created = ActivityTailored.objects.get_or_create(
                user_profile=self.user_profile,
                generic_activity=generic_activity,
                user_environment=self.user_profile.current_environment,
                defaults={
                    'name': generic_activity.name,
                    'description': generic_activity.description,
                    'instructions': generic_activity.instructions,
                    'base_challenge_rating': generic_activity.base_challenge_rating,
                    'challengingness': {},
                    'version': 1,
                    'tailorization_level': 60,
                    'duration_range': generic_activity.duration_range,
                    'social_requirements': {}
                }
            )
            tailored_activities.append(tailored_activity)
        
        print(f"✅ Created {len(generic_activities)} generic and {len(tailored_activities)} tailored activities")
        return generic_activities, tailored_activities
    
    def test_step_1_generate_wheel(self, tailored_activities):
        """Step 1: Generate initial wheel"""
        print("\n🎡 Step 1: Generating initial wheel...")
        
        # Create wheel
        wheel = Wheel.objects.create(
            name=f"{self.user_profile.profile_name}'s Workflow Test Wheel",
            created_by='workflow_test',
            created_at=datetime.now().date()
        )
        
        # Add first 3 activities to wheel
        wheel_items = []
        for i, activity in enumerate(tailored_activities[:3]):
            wheel_item = WheelItem.objects.create(
                id=f"workflow_item_{i+1}",
                wheel=wheel,
                percentage=33.33,
                activity_tailored=activity
            )
            wheel_items.append(wheel_item)
        
        print(f"✅ Wheel created with {len(wheel_items)} items")
        return wheel, wheel_items
    
    def test_step_2_submit_feedback_and_remove(self, wheel_item):
        """Step 2: Submit feedback and remove wheel item"""
        print("\n💬 Step 2: Submitting feedback and removing item...")
        
        # Test feedback API
        feedback_data = {
            'feedback_type': 'wheel_item_refusal',
            'content_type': 'wheelitem',
            'object_id': wheel_item.id,
            'user_comment': 'This activity doesn\'t fit my current schedule',
            'criticality': 1,
            'context_data': {'workflow_test': True}
        }
        
        response = self.client.post('/api/feedback/', 
                                  data=json.dumps(feedback_data),
                                  content_type='application/json')
        
        if response.status_code == 200:
            feedback_result = response.json()
            print(f"✅ Feedback submitted: ID {feedback_result['feedback_id']}")
        else:
            print(f"❌ Feedback submission failed: {response.status_code}")
            print(f"Response: {response.content.decode()}")
            return False
        
        # Test wheel item removal API
        response = self.client.delete(f'/api/wheel-items/{wheel_item.id}/')
        
        if response.status_code == 200:
            removal_result = response.json()
            print(f"✅ Wheel item removed successfully")
            print(f"   - Updated wheel has {len(removal_result['wheel_data']['segments'])} segments")
            return True
        else:
            print(f"❌ Wheel item removal failed: {response.status_code}")
            print(f"Response: {response.content.decode()}")
            return False
    
    def test_step_3_add_new_activity(self, unused_activity):
        """Step 3: Add new activity to wheel"""
        print("\n➕ Step 3: Adding new activity to wheel...")
        
        add_data = {
            'activity_id': f'tailored-{unused_activity.id}',
            'activity_type': 'tailored'
        }
        
        response = self.client.post('/api/wheel-items/',
                                  data=json.dumps(add_data),
                                  content_type='application/json')
        
        if response.status_code == 200:
            addition_result = response.json()
            print(f"✅ Activity added to wheel successfully")
            print(f"   - Updated wheel has {len(addition_result['wheel_data']['segments'])} segments")
            return True
        else:
            print(f"❌ Activity addition failed: {response.status_code}")
            print(f"Response: {response.content.decode()}")
            return False
    
    def test_step_4_verify_wheel_state(self, original_wheel):
        """Step 4: Verify final wheel state"""
        print("\n🔍 Step 4: Verifying final wheel state...")
        
        # Get current wheel items
        current_items = WheelItem.objects.filter(wheel=original_wheel)
        
        print(f"✅ Final wheel verification:")
        print(f"   - Wheel items count: {current_items.count()}")
        
        for item in current_items:
            print(f"   - {item.activity_tailored.name} ({item.percentage:.1f}%)")
        
        # Verify percentages add up to 100%
        total_percentage = sum(item.percentage for item in current_items)
        if abs(total_percentage - 100.0) < 0.1:
            print(f"✅ Percentages correctly sum to {total_percentage:.1f}%")
            return True
        else:
            print(f"❌ Percentages sum to {total_percentage:.1f}% (should be 100%)")
            return False
    
    def test_activity_catalog_integration(self):
        """Test activity catalog API integration"""
        print("\n📚 Testing activity catalog integration...")
        
        # Test basic catalog
        response = self.client.get('/api/activities/catalog/')
        if response.status_code == 200:
            catalog_data = response.json()
            print(f"✅ Activity catalog: {catalog_data['total_count']} activities")
            print(f"   - Tailored: {catalog_data['tailored_count']}")
            print(f"   - Generic: {catalog_data['generic_count']}")
        else:
            print(f"❌ Activity catalog failed: {response.status_code}")
            return False
        
        # Test search
        response = self.client.get('/api/activities/catalog/?search=yoga')
        if response.status_code == 200:
            search_data = response.json()
            print(f"✅ Search results for 'yoga': {search_data['total_count']} activities")
        else:
            print(f"❌ Activity search failed: {response.status_code}")
            return False
        
        return True
    
    def cleanup_test_data(self):
        """Clean up all test data"""
        print("\n🧹 Cleaning up test data...")
        
        # Remove feedback
        UserFeedback.objects.filter(user_profile=self.user_profile).delete()
        
        # Remove wheel items and wheels
        WheelItem.objects.filter(wheel__name__contains='Workflow Test').delete()
        Wheel.objects.filter(name__contains='Workflow Test').delete()
        
        # Remove activities
        ActivityTailored.objects.filter(user_profile=self.user_profile).delete()
        GenericActivity.objects.filter(code__startswith='workflow_').delete()
        
        # Remove user profile and environment
        UserEnvironment.objects.filter(user_profile=self.user_profile).delete()
        self.user_profile.delete()
        
        # Remove user
        self.test_user.delete()
        
        print("✅ Test data cleaned up")
    
    def run_complete_workflow_test(self):
        """Run the complete workflow test"""
        print("🚀 Starting Complete Wheel Workflow Test")
        print("=" * 60)
        
        try:
            # Setup
            self.setup_test_environment()
            generic_activities, tailored_activities = self.create_test_activities()
            
            # Test activity catalog integration
            if not self.test_activity_catalog_integration():
                return False
            
            # Step 1: Generate wheel
            wheel, wheel_items = self.test_step_1_generate_wheel(tailored_activities)
            
            # Step 2: Submit feedback and remove item
            if not self.test_step_2_submit_feedback_and_remove(wheel_items[0]):
                return False
            
            # Step 3: Add new activity
            unused_activity = tailored_activities[3]  # 4th activity not in original wheel
            if not self.test_step_3_add_new_activity(unused_activity):
                return False
            
            # Step 4: Verify final state
            if not self.test_step_4_verify_wheel_state(wheel):
                return False
            
            print("\n" + "=" * 60)
            print("🎉 Complete Workflow Test PASSED!")
            print("✅ All steps completed successfully:")
            print("   1. ✅ Wheel generation")
            print("   2. ✅ Feedback submission and item removal")
            print("   3. ✅ Activity addition")
            print("   4. ✅ Wheel state verification")
            print("   5. ✅ Activity catalog integration")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Workflow test failed with exception: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self.cleanup_test_data()

if __name__ == "__main__":
    tester = CompleteWheelWorkflowTester()
    success = tester.run_complete_workflow_test()
    sys.exit(0 if success else 1)
