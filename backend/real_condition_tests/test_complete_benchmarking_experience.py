#!/usr/bin/env python3
"""
Complete Benchmarking Experience Test

This test validates the entire user experience from quick benchmark execution
to viewing detailed results in the enhanced agent evaluation modal.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_complete_benchmarking_experience.py
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import RequestFactory, Client
from django.contrib.auth.models import User
from apps.user.models import UserProfile
from apps.main.models import BenchmarkRun, GenericAgent
from apps.admin_tools.benchmark.views import QuickBenchmarkView
from apps.admin_tools.views import BenchmarkRunView

logger = logging.getLogger(__name__)

class CompleteBenchmarkingExperienceTest:
    """Test the complete benchmarking experience from start to finish."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Complete Benchmarking Experience Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
        self.factory = RequestFactory()
        self.client = Client()
        
    def log_test_result(self, test_name: str, success: bool, details: dict = None, error: str = None):
        """Log the result of a test."""
        self.results['tests'][test_name] = {
            'success': success,
            'details': details or {},
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results['summary']['total_tests'] += 1
        if success:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: {error}")
            if error:
                self.results['summary']['errors'].append(f"{test_name}: {error}")
    
    def test_quick_benchmark_execution(self):
        """Test executing a quick benchmark with real LLM and tools."""
        try:
            # Get required data
            user_profile = UserProfile.objects.filter(is_real=True).first()
            agent = GenericAgent.objects.filter(role='mentor').first()
            
            if not user_profile or not agent:
                self.log_test_result('Quick Benchmark Execution', False, 
                                   error="Missing user profile or agent")
                return False, None
            
            # Create a staff user
            user, created = User.objects.get_or_create(
                username='test_complete_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            
            # Execute quick benchmark
            request_data = {
                'agent_name': agent.role,
                'user_profile_id': str(user_profile.id),
                'evaluation_template': 'mentor_helpfulness',
                'scenario_context': {'user_input': 'Hello, I need comprehensive help with my life goals'},
                'use_real_tools': True,
                'use_real_db': True
            }
            
            request = self.factory.post(
                '/admin/benchmarks/api/quick-benchmark/',
                data=json.dumps(request_data),
                content_type='application/json'
            )
            request.user = user
            
            start_time = time.time()
            view = QuickBenchmarkView()
            response = view.post(request)
            execution_time = time.time() - start_time
            
            details = {
                'status_code': response.status_code,
                'execution_time_seconds': execution_time,
                'request_data': request_data
            }
            
            if response.status_code in [200, 201]:
                response_data = json.loads(response.content)
                benchmark_run_id = response_data.get('benchmark_run_id')
                
                details.update({
                    'benchmark_run_id': benchmark_run_id,
                    'response_success': response_data.get('success', False),
                    'has_results_summary': 'results_summary' in response_data
                })
                
                success = response_data.get('success', False) and benchmark_run_id
                error = None if success else "No benchmark run ID returned"
                
            else:
                success = False
                error = f"HTTP {response.status_code}"
            
            self.log_test_result('Quick Benchmark Execution', success, details, error)
            return success, benchmark_run_id if success else None
            
        except Exception as e:
            self.log_test_result('Quick Benchmark Execution', False, error=str(e))
            return False, None
    
    def test_enhanced_debugging_data_capture(self, benchmark_run_id):
        """Test that enhanced debugging data is properly captured."""
        try:
            # Get the benchmark run
            benchmark_run = BenchmarkRun.objects.get(id=benchmark_run_id)
            raw_results = benchmark_run.raw_results or {}
            enhanced_data = raw_results.get('enhanced_debugging_data', {})
            
            details = {
                'benchmark_run_id': benchmark_run_id,
                'enhanced_debugging_enabled': enhanced_data.get('enabled', False),
                'llm_interactions_count': len(enhanced_data.get('llm_interactions', [])),
                'tool_calls_count': len(enhanced_data.get('tool_calls', [])),
                'agents_count': len(enhanced_data.get('agents', [])),
                'total_input_tokens': benchmark_run.total_input_tokens,
                'total_output_tokens': benchmark_run.total_output_tokens,
                'estimated_cost': float(benchmark_run.estimated_cost) if benchmark_run.estimated_cost else 0
            }
            
            # Validate LLM interactions
            llm_interactions = enhanced_data.get('llm_interactions', [])
            if llm_interactions:
                sample_interaction = llm_interactions[0]
                details['sample_llm_interaction'] = {
                    'has_agent': bool(sample_interaction.get('agent')),
                    'has_model': bool(sample_interaction.get('model')),
                    'has_prompt': bool(sample_interaction.get('prompt')),
                    'has_response': bool(sample_interaction.get('response')),
                    'has_token_usage': bool(sample_interaction.get('token_usage')),
                    'success': sample_interaction.get('success', False)
                }
            
            # Success criteria
            success = (
                enhanced_data.get('enabled', False) and
                len(llm_interactions) > 0 and
                benchmark_run.total_input_tokens > 0 and
                benchmark_run.total_output_tokens > 0
            )
            
            error = None if success else "Enhanced debugging data not properly captured"
            
            self.log_test_result('Enhanced Debugging Data Capture', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Enhanced Debugging Data Capture', False, error=str(e))
            return False
    
    def test_detail_api_response(self, benchmark_run_id):
        """Test the detail API response structure and content."""
        try:
            # Create a staff user
            user, created = User.objects.get_or_create(
                username='test_detail_api_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            
            # Call the detail API
            request = self.factory.get(f'/admin/benchmarks/api/run/{benchmark_run_id}/')
            request.user = user
            
            view = BenchmarkRunView()
            response = view.get(request, run_id=benchmark_run_id)
            
            details = {
                'status_code': response.status_code,
                'benchmark_run_id': benchmark_run_id
            }
            
            if response.status_code == 200:
                try:
                    response_data = json.loads(response.content)
                    
                    # Validate response structure
                    required_fields = ['id', 'agent_role', 'scenario', 'raw_results']
                    has_required_fields = all(field in response_data for field in required_fields)
                    
                    enhanced_data = response_data.get('raw_results', {}).get('enhanced_debugging_data', {})
                    
                    details.update({
                        'is_valid_json': True,
                        'has_required_fields': has_required_fields,
                        'required_fields_present': [field for field in required_fields if field in response_data],
                        'enhanced_debugging_enabled': enhanced_data.get('enabled', False),
                        'llm_interactions_in_response': len(enhanced_data.get('llm_interactions', [])),
                        'tool_calls_in_response': len(enhanced_data.get('tool_calls', [])),
                        'response_data_keys': list(response_data.keys())
                    })
                    
                    success = has_required_fields and enhanced_data.get('enabled', False)
                    error = None if success else "Missing required fields or enhanced debugging not enabled"
                    
                except json.JSONDecodeError as e:
                    success = False
                    error = f"Invalid JSON response: {str(e)}"
                    details['response_preview'] = response.content.decode('utf-8')[:200]
            else:
                success = False
                error = f"HTTP {response.status_code}"
                details['response_preview'] = response.content.decode('utf-8')[:200]
            
            self.log_test_result('Detail API Response', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Detail API Response', False, error=str(e))
            return False
    
    def test_modal_functionality_simulation(self):
        """Test that the modal HTML and JavaScript are properly included."""
        try:
            # Create a staff user and login
            user, created = User.objects.get_or_create(
                username='test_modal_func_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark management page
            response = self.client.get('/admin/benchmarks/manage/')
            
            details = {
                'status_code': response.status_code
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for modal elements and functions
                modal_checks = {
                    'agent_details_modal': 'id="agent-details-modal"' in content,
                    'agent_modal_body': 'id="agent-modal-body"' in content,
                    'copy_run_data_btn': 'id="copy-run-data-btn"' in content,
                    'refresh_modal_btn': 'id="refresh-modal-btn"' in content,
                    'llm_interactions_content': 'id="llm-interactions-content"' in content,
                    'enhanced_tool_calls_section': 'id="enhanced-tool-calls-section"' in content,
                    'renderAgentDetails_function': 'function renderAgentDetails' in content or 'renderAgentDetails =' in content,
                    'renderLLMInteractions_function': 'function renderLLMInteractions' in content or 'renderLLMInteractions =' in content,
                    'setupCopyRunDataButton_function': 'function setupCopyRunDataButton' in content or 'setupCopyRunDataButton =' in content,
                    'openAgentEvaluationModal_function': 'function openAgentEvaluationModal' in content or 'openAgentEvaluationModal =' in content
                }
                
                details.update({
                    'modal_checks': modal_checks,
                    'all_modal_elements_present': all(modal_checks.values()),
                    'modal_elements_count': sum(1 for v in modal_checks.values() if v),
                    'total_modal_elements': len(modal_checks)
                })
                
                success = all(modal_checks.values())
                error = None if success else f"Missing modal elements: {[k for k, v in modal_checks.items() if not v]}"
                
            else:
                success = False
                error = f"Could not access benchmark management page: HTTP {response.status_code}"
            
            self.log_test_result('Modal Functionality Simulation', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Modal Functionality Simulation', False, error=str(e))
            return False
    
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'complete_benchmarking_experience_{timestamp}.json'
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Results saved to: {filepath}")
        return filepath
    
    def run_all_tests(self):
        """Run all tests in sequence."""
        print("🚀 Starting Complete Benchmarking Experience Test")
        print("=" * 60)
        
        # Test 1: Execute Quick Benchmark
        execution_success, benchmark_run_id = self.test_quick_benchmark_execution()
        
        if execution_success and benchmark_run_id:
            # Test 2: Enhanced Debugging Data Capture
            self.test_enhanced_debugging_data_capture(benchmark_run_id)
            
            # Test 3: Detail API Response
            self.test_detail_api_response(benchmark_run_id)
        
        # Test 4: Modal Functionality
        self.test_modal_functionality_simulation()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.results['summary']['total_tests']}")
        print(f"Passed: {self.results['summary']['passed']}")
        print(f"Failed: {self.results['summary']['failed']}")
        
        if self.results['summary']['errors']:
            print("\n❌ ERRORS:")
            for error in self.results['summary']['errors']:
                print(f"  - {error}")
        
        success_rate = (self.results['summary']['passed'] / self.results['summary']['total_tests']) * 100
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("✅ Complete Benchmarking Experience is working perfectly!")
            print("🎉 Users can now:")
            print("   - Execute quick benchmarks with real LLM calls")
            print("   - View detailed results with enhanced debugging data")
            print("   - See LLM interactions, tool calls, and token usage")
            print("   - Copy complete run data as JSON for analysis")
        elif success_rate >= 70:
            print("⚠️ Complete Benchmarking Experience has some issues")
        else:
            print("❌ Complete Benchmarking Experience needs significant fixes")
        
        return self.save_results()

def main():
    """Main test execution."""
    test = CompleteBenchmarkingExperienceTest()
    results_file = test.run_all_tests()
    
    print(f"\n🔗 View detailed results: {results_file}")
    print("\n🎯 Manual Testing Steps:")
    print("1. Open: http://localhost:8000/admin/benchmarks/manage/")
    print("2. Go to 'Quick Benchmark' tab")
    print("3. Select agent, user profile, and evaluation template")
    print("4. Click 'Run Benchmark'")
    print("5. Click 'View Detailed Results' when complete")
    print("6. Verify LLM interactions and tool calls are displayed")
    print("7. Test 'Copy Run Data' button")

if __name__ == '__main__':
    main()
