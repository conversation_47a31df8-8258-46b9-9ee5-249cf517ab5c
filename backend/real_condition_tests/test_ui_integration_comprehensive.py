#!/usr/bin/env python3
"""
Comprehensive UI Integration Test

This test validates the actual UI integration by checking HTML content,
JavaScript function availability, and modal integration without requiring a browser.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_ui_integration_comprehensive.py
"""

import os
import sys
import json
import re
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.user.models import UserProfile
from apps.main.models import GenericAgent, BenchmarkRun

logger = logging.getLogger(__name__)

class ComprehensiveUIIntegrationTest:
    """Test the complete UI integration without requiring a browser."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Comprehensive UI Integration Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
        self.client = Client()
        
    def log_test_result(self, test_name: str, success: bool, details: dict = None, error: str = None):
        """Log the result of a test."""
        self.results['tests'][test_name] = {
            'success': success,
            'details': details or {},
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results['summary']['total_tests'] += 1
        if success:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: {error}")
            if error:
                self.results['summary']['errors'].append(f"{test_name}: {error}")
    
    def test_benchmark_management_page_content(self):
        """Test that the benchmark management page contains all required elements."""
        try:
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_ui_comprehensive_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark management page
            response = self.client.get('/admin/benchmarks/manage/')
            
            details = {
                'status_code': response.status_code
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for required HTML elements
                required_elements = {
                    'quick_benchmark_tab': 'data-tab="quick-benchmark"',
                    'quick_agent_name': 'id="quick-agent-name"',
                    'quick_profile_template': 'id="quick-profile-template"',
                    'quick_evaluation_template': 'id="quick-evaluation-template"',
                    'run_quick_benchmark_btn': 'Run Quick Benchmark',
                    'view_detailed_results_btn': 'id="view-detailed-results-btn"',
                    'run_another_benchmark_btn': 'id="run-another-benchmark-btn"',
                    'agent_details_modal': 'id="agent-details-modal"',
                    'agent_modal_body': 'id="agent-modal-body"',
                    'copy_run_data_btn': 'id="copy-run-data-btn"',
                    'refresh_modal_btn': 'id="refresh-modal-btn"',
                    'llm_interactions_content': 'id="llm-interactions-content"',
                    'enhanced_tool_calls_section': 'id="enhanced-tool-calls-section"'
                }
                
                element_status = {}
                for name, selector in required_elements.items():
                    element_status[name] = selector in content
                
                # Check for JavaScript functions
                js_function_checks = {
                    'renderAgentDetails_global': 'window.renderAgentDetails' in content,
                    'renderLLMInteractions_global': 'window.renderLLMInteractions' in content,
                    'renderEnhancedToolCalls_global': 'window.renderEnhancedToolCalls' in content,
                    'setupCopyRunDataButton_global': 'window.setupCopyRunDataButton' in content,
                    'openAgentEvaluationModal_function': 'function openAgentEvaluationModal' in content or 'openAgentEvaluationModal =' in content
                }
                
                js_function_status = {}
                for name, check in js_function_checks.items():
                    js_function_status[name] = check
                
                # Check for modal inclusion
                modal_inclusion_checks = {
                    'agent_evaluation_modal_included': "{% include 'admin_tools/modals/agent_evaluation_modal.html' %}" in content,
                    'modal_script_tag_present': '<script>' in content and 'renderAgentDetails' in content
                }
                
                details.update({
                    'required_elements': element_status,
                    'js_functions': js_function_status,
                    'modal_inclusion': modal_inclusion_checks,
                    'all_elements_present': all(element_status.values()),
                    'all_js_functions_present': all(js_function_status.values()),
                    'modal_properly_included': all(modal_inclusion_checks.values())
                })
                
                success = (details['all_elements_present'] and 
                          details['all_js_functions_present'] and 
                          details['modal_properly_included'])
                
                if not success:
                    missing_elements = [k for k, v in element_status.items() if not v]
                    missing_functions = [k for k, v in js_function_status.items() if not v]
                    missing_modal = [k for k, v in modal_inclusion_checks.items() if not v]
                    
                    error_parts = []
                    if missing_elements:
                        error_parts.append(f"Missing elements: {missing_elements}")
                    if missing_functions:
                        error_parts.append(f"Missing JS functions: {missing_functions}")
                    if missing_modal:
                        error_parts.append(f"Modal issues: {missing_modal}")
                    
                    error = "; ".join(error_parts)
                else:
                    error = None
                
            else:
                success = False
                error = f"Could not access benchmark management page: HTTP {response.status_code}"
            
            self.log_test_result('Benchmark Management Page Content', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Benchmark Management Page Content', False, error=str(e))
            return False
    
    def test_benchmark_history_page_integration(self):
        """Test that the benchmark history page has proper modal integration."""
        try:
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_history_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark history page
            response = self.client.get('/admin/benchmarks/history/')
            
            details = {
                'status_code': response.status_code
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for modal integration
                integration_checks = {
                    'agent_evaluation_modal_included': "{% include 'admin_tools/modals/agent_evaluation_modal.html' %}" in content,
                    'renderAgentDetails_call': 'await renderAgentDetails(' in content,
                    'renderAgentDetails_comment': 'renderAgentDetails and renderWorkflowDetails functions are now in separate modal files' in content,
                    'agent_modal_body_reference': 'agent-modal-body' in content,
                    'view_details_handler': 'handleViewDetailsClick' in content
                }
                
                details.update({
                    'integration_checks': integration_checks,
                    'all_integration_present': all(integration_checks.values())
                })
                
                success = details['all_integration_present']
                error = None if success else f"Missing integration: {[k for k, v in integration_checks.items() if not v]}"
                
            else:
                success = False
                error = f"Could not access benchmark history page: HTTP {response.status_code}"
            
            self.log_test_result('Benchmark History Page Integration', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Benchmark History Page Integration', False, error=str(e))
            return False
    
    def test_quick_benchmark_button_integration(self):
        """Test that the quick benchmark button is properly integrated."""
        try:
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_button_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark management page
            response = self.client.get('/admin/benchmarks/manage/')
            
            details = {
                'status_code': response.status_code
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for button integration
                button_checks = {
                    'view_detailed_results_btn_exists': 'id="view-detailed-results-btn"' in content,
                    'button_onclick_setup': 'viewResultsBtn.onclick = function()' in content,
                    'openAgentEvaluationModal_call': 'openAgentEvaluationModal(data.benchmark_run_id)' in content,
                    'button_display_control': 'viewResultsBtn.style.display' in content,
                    'run_another_btn_exists': 'id="run-another-benchmark-btn"' in content,
                    'run_another_no_reset': "Don't reset the form - keep the previous configuration" in content
                }
                
                details.update({
                    'button_checks': button_checks,
                    'all_button_integration_present': all(button_checks.values())
                })
                
                success = details['all_button_integration_present']
                error = None if success else f"Missing button integration: {[k for k, v in button_checks.items() if not v]}"
                
            else:
                success = False
                error = f"Could not access benchmark management page: HTTP {response.status_code}"
            
            self.log_test_result('Quick Benchmark Button Integration', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Quick Benchmark Button Integration', False, error=str(e))
            return False
    
    def test_api_endpoint_accessibility(self):
        """Test that the API endpoint is accessible and returns proper data."""
        try:
            # Get a benchmark run
            benchmark_run = BenchmarkRun.objects.first()
            if not benchmark_run:
                self.log_test_result('API Endpoint Accessibility', False, 
                                   error="No benchmark runs found for testing")
                return False
            
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_api_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Test the API endpoint
            response = self.client.get(f'/admin/benchmarks/api/run/{benchmark_run.id}/')
            
            details = {
                'status_code': response.status_code,
                'benchmark_run_id': str(benchmark_run.id)
            }
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    details.update({
                        'is_json': True,
                        'has_id': 'id' in data,
                        'has_raw_results': 'raw_results' in data,
                        'has_enhanced_debugging': bool(data.get('raw_results', {}).get('enhanced_debugging_data', {}).get('enabled', False)),
                        'data_keys': list(data.keys())[:10]  # First 10 keys
                    })
                    success = True
                    error = None
                except json.JSONDecodeError as e:
                    success = False
                    error = f"API returned invalid JSON: {str(e)}"
                    details['response_preview'] = response.content.decode('utf-8')[:200]
            else:
                success = False
                error = f"API endpoint failed: HTTP {response.status_code}"
                details['response_preview'] = response.content.decode('utf-8')[:200]
            
            self.log_test_result('API Endpoint Accessibility', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('API Endpoint Accessibility', False, error=str(e))
            return False
    
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'ui_integration_comprehensive_{timestamp}.json'
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Results saved to: {filepath}")
        return filepath
    
    def run_all_tests(self):
        """Run all tests in sequence."""
        print("🚀 Starting Comprehensive UI Integration Test")
        print("=" * 60)
        
        # Test 1: Benchmark Management Page Content
        self.test_benchmark_management_page_content()
        
        # Test 2: Benchmark History Page Integration
        self.test_benchmark_history_page_integration()
        
        # Test 3: Quick Benchmark Button Integration
        self.test_quick_benchmark_button_integration()
        
        # Test 4: API Endpoint Accessibility
        self.test_api_endpoint_accessibility()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.results['summary']['total_tests']}")
        print(f"Passed: {self.results['summary']['passed']}")
        print(f"Failed: {self.results['summary']['failed']}")
        
        if self.results['summary']['errors']:
            print("\n❌ ERRORS:")
            for error in self.results['summary']['errors']:
                print(f"  - {error}")
        
        success_rate = (self.results['summary']['passed'] / self.results['summary']['total_tests']) * 100
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("✅ Comprehensive UI Integration is working perfectly!")
        elif success_rate >= 70:
            print("⚠️ Comprehensive UI Integration has some issues")
        else:
            print("❌ Comprehensive UI Integration needs significant fixes")
        
        return self.save_results()

def main():
    """Main test execution."""
    test = ComprehensiveUIIntegrationTest()
    results_file = test.run_all_tests()
    
    print(f"\n🔗 View detailed results: {results_file}")
    print("\n🎯 Next Steps:")
    print("1. If tests failed: Check the specific missing elements or functions")
    print("2. Manual testing: Open http://localhost:8000/admin/benchmarks/manage/")
    print("3. Browser console: Check for JavaScript errors")

if __name__ == '__main__':
    main()
