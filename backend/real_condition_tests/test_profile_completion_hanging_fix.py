#!/usr/bin/env python3
"""
Profile Completion Hanging Fix Test

This test reproduces the exact scenario where a user with ~25% profile completion
requests "make me a wheel" and the system hangs due to LangGraph state handling issues.

The test validates:
1. No hanging issues (response within 10 seconds)
2. Proper LangGraph AddableValuesDict handling
3. Meaningful profile completion questions
4. Correct workflow routing and state management

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_profile_completion_hanging_fix.py
"""

import asyncio
import logging
import time
import sys
import os
from typing import Dict, Any
from datetime import date, timedelta

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile, Demographics, Preference
from apps.main.graphs.profile_completion_graph import run_profile_completion_workflow
from django.contrib.auth.models import User
from asgiref.sync import sync_to_async

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProfileCompletionHangingTest:
    """Test suite for profile completion hanging issue fix."""
    
    def __init__(self):
        self.test_user = None
        self.test_profile = None
        self.results = []
        
    async def setup_test_user(self) -> str:
        """Create a test user with ~25% profile completion."""
        try:
            # Create test user using sync_to_async
            username = f"test_hanging_fix_{int(time.time())}"
            self.test_user = await sync_to_async(User.objects.create_user)(
                username=username,
                email=f"{username}@test.com"
            )

            # Create user profile
            self.test_profile = await sync_to_async(UserProfile.objects.create)(
                user=self.test_user,
                profile_name=username
            )

            # Add some basic demographics to achieve ~25% completion
            await sync_to_async(Demographics.objects.create)(
                user_profile=self.test_profile,
                full_name="Test User",
                age=22,
                gender="Female",
                location="Berlin, Germany",
                language="German",
                occupation="Student"
            )

            # Add a preference (with required TemporalRecord fields)
            today = date.today()
            await sync_to_async(Preference.objects.create)(
                user_profile=self.test_profile,
                pref_name="activity_type",
                pref_description="Enjoys creative activities",
                pref_strength=80,
                user_awareness=80,
                effective_start=today,
                duration_estimate="ongoing",
                effective_end=today + timedelta(days=365)  # 1 year from now
            )

            logger.info(f"✅ Created test user {username} with profile ID {self.test_profile.id}")
            return str(self.test_profile.id)

        except Exception as e:
            logger.error(f"❌ Failed to setup test user: {e}")
            raise
    
    async def test_conversation_dispatcher_routing(self, user_profile_id: str) -> Dict[str, Any]:
        """Test that ConversationDispatcher properly routes wheel requests."""
        logger.info("🔍 Testing ConversationDispatcher routing for wheel request...")
        
        start_time = time.time()
        
        try:
            # Create conversation dispatcher
            dispatcher = ConversationDispatcher(
                user_profile_id=user_profile_id,
                user_ws_session_name=f"test_session_{int(time.time())}"
            )
            
            # Test wheel request message
            message = "make me a wheel"
            
            # Process message with timeout
            response = await asyncio.wait_for(
                dispatcher.process_message(message),
                timeout=10.0  # 10 second timeout
            )
            
            elapsed_time = time.time() - start_time
            
            result = {
                "test": "conversation_dispatcher_routing",
                "success": True,
                "response_time": elapsed_time,
                "response": response,
                "hanging_detected": elapsed_time > 10.0,
                "error": None
            }
            
            logger.info(f"✅ ConversationDispatcher test completed in {elapsed_time:.2f}s")
            return result
            
        except asyncio.TimeoutError:
            elapsed_time = time.time() - start_time
            result = {
                "test": "conversation_dispatcher_routing",
                "success": False,
                "response_time": elapsed_time,
                "response": None,
                "hanging_detected": True,
                "error": "Timeout after 10 seconds - hanging detected"
            }
            logger.error(f"❌ ConversationDispatcher test HANGING detected after {elapsed_time:.2f}s")
            return result
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            result = {
                "test": "conversation_dispatcher_routing",
                "success": False,
                "response_time": elapsed_time,
                "response": None,
                "hanging_detected": elapsed_time > 10.0,
                "error": str(e)
            }
            logger.error(f"❌ ConversationDispatcher test failed: {e}")
            return result
    
    async def test_profile_completion_workflow_direct(self, user_profile_id: str) -> Dict[str, Any]:
        """Test profile completion workflow directly."""
        logger.info("🔍 Testing profile completion workflow directly...")
        
        start_time = time.time()
        
        try:
            # Create context packet
            context_packet = {
                'user_id': user_profile_id,
                'user_profile_id': user_profile_id,
                'session_timestamp': int(time.time()),
                'reported_mood': 'neutral',
                'reported_environment': 'home',
                'reported_time_availability': 'medium',
                'reported_focus': 'general',
                'reported_satisfaction': 'neutral',
                'extraction_confidence': 0.8,
                'entities': [],
                'user_ws_session_name': f'test_session_{int(time.time())}',
                'mentor_context': {},
                'workflow_metadata': {},
                'system_metadata': {}
            }
            
            # Run workflow with timeout
            result = await asyncio.wait_for(
                run_profile_completion_workflow(
                    user_profile_id=user_profile_id,
                    context_packet=context_packet
                ),
                timeout=15.0  # 15 second timeout
            )
            
            elapsed_time = time.time() - start_time
            
            test_result = {
                "test": "profile_completion_workflow_direct",
                "success": True,
                "response_time": elapsed_time,
                "workflow_result": result,
                "hanging_detected": elapsed_time > 10.0,
                "error": None,
                "langgraph_state_handled": 'completed' in result,  # Check if state was properly accessed
                "workflow_completed": result.get('completed', False)
            }
            
            logger.info(f"✅ Profile completion workflow test completed in {elapsed_time:.2f}s")
            logger.info(f"   Workflow completed: {result.get('completed', False)}")
            logger.info(f"   Output data: {result.get('output_data', {})}")
            
            return test_result
            
        except asyncio.TimeoutError:
            elapsed_time = time.time() - start_time
            test_result = {
                "test": "profile_completion_workflow_direct",
                "success": False,
                "response_time": elapsed_time,
                "workflow_result": None,
                "hanging_detected": True,
                "error": "Timeout after 15 seconds - hanging detected",
                "langgraph_state_handled": False,
                "workflow_completed": False
            }
            logger.error(f"❌ Profile completion workflow HANGING detected after {elapsed_time:.2f}s")
            return test_result
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            test_result = {
                "test": "profile_completion_workflow_direct",
                "success": False,
                "response_time": elapsed_time,
                "workflow_result": None,
                "hanging_detected": elapsed_time > 10.0,
                "error": str(e),
                "langgraph_state_handled": False,
                "workflow_completed": False
            }
            logger.error(f"❌ Profile completion workflow test failed: {e}")
            return test_result
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive test suite."""
        logger.info("🚀 Starting comprehensive profile completion hanging fix test...")
        
        try:
            # Setup test user
            user_profile_id = await self.setup_test_user()
            
            # Run tests
            test_results = []
            
            # Test 1: ConversationDispatcher routing
            result1 = await self.test_conversation_dispatcher_routing(user_profile_id)
            test_results.append(result1)
            
            # Test 2: Profile completion workflow direct
            result2 = await self.test_profile_completion_workflow_direct(user_profile_id)
            test_results.append(result2)
            
            # Analyze results
            all_successful = all(r['success'] for r in test_results)
            any_hanging = any(r['hanging_detected'] for r in test_results)
            max_response_time = max(r['response_time'] for r in test_results)
            
            summary = {
                "overall_success": all_successful,
                "hanging_detected": any_hanging,
                "max_response_time": max_response_time,
                "test_results": test_results,
                "fix_validation": {
                    "langgraph_state_fix": not any('AddableValuesDict' in str(r.get('error', '')) for r in test_results),
                    "response_time_acceptable": max_response_time < 10.0,
                    "no_hanging_issues": not any_hanging
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Comprehensive test failed: {e}")
            return {
                "overall_success": False,
                "hanging_detected": True,
                "error": str(e),
                "test_results": []
            }
        
        finally:
            # Cleanup
            await self.cleanup()
    
    async def cleanup(self):
        """Clean up test data."""
        try:
            if self.test_profile:
                # Delete related objects first
                await sync_to_async(Demographics.objects.filter(user_profile=self.test_profile).delete)()
                await sync_to_async(Preference.objects.filter(user_profile=self.test_profile).delete)()
                await sync_to_async(self.test_profile.delete)()

            if self.test_user:
                await sync_to_async(self.test_user.delete)()

            logger.info("✅ Test cleanup completed")

        except Exception as e:
            logger.error(f"⚠️ Cleanup error: {e}")

async def main():
    """Main test execution."""
    test_suite = ProfileCompletionHangingTest()
    
    try:
        results = await test_suite.run_comprehensive_test()
        
        # Print results
        print("\n" + "="*80)
        print("PROFILE COMPLETION HANGING FIX TEST RESULTS")
        print("="*80)
        
        print(f"Overall Success: {'✅ PASS' if results['overall_success'] else '❌ FAIL'}")
        print(f"Hanging Detected: {'❌ YES' if results['hanging_detected'] else '✅ NO'}")
        print(f"Max Response Time: {results.get('max_response_time', 0):.2f}s")
        
        if 'fix_validation' in results:
            fix_validation = results['fix_validation']
            print(f"\nFix Validation:")
            print(f"  LangGraph State Fix: {'✅ WORKING' if fix_validation['langgraph_state_fix'] else '❌ BROKEN'}")
            print(f"  Response Time OK: {'✅ YES' if fix_validation['response_time_acceptable'] else '❌ NO'}")
            print(f"  No Hanging Issues: {'✅ YES' if fix_validation['no_hanging_issues'] else '❌ NO'}")
        
        print(f"\nDetailed Test Results:")
        for i, result in enumerate(results.get('test_results', []), 1):
            print(f"  Test {i}: {result['test']}")
            print(f"    Success: {'✅' if result['success'] else '❌'}")
            print(f"    Response Time: {result['response_time']:.2f}s")
            print(f"    Hanging: {'❌ YES' if result['hanging_detected'] else '✅ NO'}")
            if result.get('error'):
                print(f"    Error: {result['error']}")
        
        print("="*80)
        
        # Return appropriate exit code
        return 0 if results['overall_success'] and not results['hanging_detected'] else 1
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
