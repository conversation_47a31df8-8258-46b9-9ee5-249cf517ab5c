#!/usr/bin/env python3

"""
MentorService Phase 4 Intelligence Test

Tests the Phase 4 enhancements to MentorService including:
1. Contextual instruction injection based on situation and workflow type
2. Dynamic tool injection based on current needs and workflow context
3. Trust-based communication adaptation (0.0-1.0 trust levels)
4. Runtime enhancement coordination with ConversationDispatcher
5. Profile analysis coordination and questioning strategies
"""

import asyncio
import sys
import os
import django
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.mentor_service import MentorService
from apps.user.models import UserProfile
from django.contrib.auth.models import User
from asgiref.sync import sync_to_async


class MentorServicePhase4IntelligenceTest:
    """Test the Phase 4 intelligence enhancements to MentorService."""
    
    def __init__(self):
        self.test_user_id = None
        self.session_name = f"mentor_phase4_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.mentor_service = None
        
    async def setup_test_user(self):
        """Create a test user for MentorService testing."""
        print("🔧 Setting up test user for MentorService...")
        
        @sync_to_async
        def create_user():
            # Create Django user first
            django_user, _ = User.objects.get_or_create(
                username="mentor_phase4_test_user",
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'MentorPhase4',
                    'last_name': 'TestUser'
                }
            )

            # Create or get test user profile
            user_profile, created = UserProfile.objects.get_or_create(
                profile_name="mentor_phase4_test_user",
                defaults={
                    'user': django_user,
                    'is_real': False  # Mark as test user
                }
            )
            
            return str(user_profile.id)
        
        self.test_user_id = await create_user()
        print(f"✅ Test user created: {self.test_user_id}")
        
        # Initialize MentorService for this user
        self.mentor_service = MentorService(
            user_profile_id=self.test_user_id,
            session_id=self.session_name
        )
        print(f"✅ MentorService initialized for user {self.test_user_id}")
        
    async def test_contextual_instruction_injection(self):
        """Test contextual instruction injection for different scenarios."""
        print("\n🧠 Testing Contextual Instruction Injection")
        print("-" * 50)
        
        test_scenarios = [
            {
                'name': 'Profile Completion Scenario',
                'situation_context': {
                    'type': 'profile_completion',
                    'phase': 'initial_gathering',
                    'needs': ['demographic_info', 'goals_clarification']
                },
                'profile_gaps': {
                    'critical': ['age', 'location'],
                    'important': ['goals', 'preferences']
                }
            },
            {
                'name': 'Wheel Generation Scenario',
                'situation_context': {
                    'type': 'wheel_generation',
                    'phase': 'activity_selection',
                    'needs': ['activity_guidance', 'personalization']
                },
                'profile_gaps': {}
            },
            {
                'name': 'Discussion Scenario',
                'situation_context': {
                    'type': 'discussion',
                    'phase': 'mood_assessment',
                    'needs': ['emotional_support', 'trust_building']
                },
                'profile_gaps': {}
            }
        ]
        
        results = []
        for scenario in test_scenarios:
            print(f"\n📋 Testing: {scenario['name']}")
            
            try:
                success = await self.mentor_service.inject_contextual_instructions(
                    situation_context=scenario['situation_context'],
                    profile_gaps=scenario['profile_gaps']
                )
                
                if success:
                    print(f"✅ {scenario['name']}: Instructions injected successfully")
                    results.append(True)
                else:
                    print(f"❌ {scenario['name']}: Failed to inject instructions")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ {scenario['name']}: Error - {str(e)}")
                results.append(False)
        
        success_rate = sum(results) / len(results) * 100
        print(f"\n📊 Contextual Instruction Injection Success Rate: {success_rate:.1f}%")
        return success_rate >= 80.0  # Expect at least 80% success rate
    
    async def test_dynamic_tool_injection(self):
        """Test dynamic tool injection based on workflow type and needs."""
        print("\n🔧 Testing Dynamic Tool Injection")
        print("-" * 40)
        
        test_scenarios = [
            {
                'name': 'Onboarding Workflow',
                'workflow_type': 'onboarding',
                'current_needs': {
                    'profile_enrichment': True,
                    'demographic_collection': True
                }
            },
            {
                'name': 'Wheel Generation Workflow',
                'workflow_type': 'wheel_generation',
                'current_needs': {
                    'activity_guidance': True,
                    'resource_assessment': True
                }
            },
            {
                'name': 'Post Activity Workflow',
                'workflow_type': 'post_activity',
                'current_needs': {
                    'feedback_collection': True,
                    'emotional_support': True
                }
            }
        ]
        
        results = []
        for scenario in test_scenarios:
            print(f"\n🔧 Testing: {scenario['name']}")
            
            try:
                success = await self.mentor_service.inject_dynamic_tools(
                    workflow_type=scenario['workflow_type'],
                    current_needs=scenario['current_needs']
                )
                
                if success:
                    print(f"✅ {scenario['name']}: Tools injected successfully")
                    results.append(True)
                else:
                    print(f"❌ {scenario['name']}: Failed to inject tools")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ {scenario['name']}: Error - {str(e)}")
                results.append(False)
        
        success_rate = sum(results) / len(results) * 100
        print(f"\n📊 Dynamic Tool Injection Success Rate: {success_rate:.1f}%")
        return success_rate >= 80.0  # Expect at least 80% success rate
    
    async def test_trust_based_communication_adaptation(self):
        """Test trust-based communication adaptation across different trust levels."""
        print("\n💝 Testing Trust-Based Communication Adaptation")
        print("-" * 50)
        
        trust_levels = [
            {'level': 0.2, 'name': 'Low Trust (Building Phase)'},
            {'level': 0.5, 'name': 'Medium Trust (Developing Phase)'},
            {'level': 0.8, 'name': 'High Trust (Established Phase)'}
        ]
        
        results = []
        for trust_test in trust_levels:
            print(f"\n🎯 Testing: {trust_test['name']} - Level {trust_test['level']}")
            
            try:
                # Update trust level in MentorService
                await self.mentor_service.update_trust_level(trust_test['level'])
                
                # Test contextual instructions with this trust level
                situation_context = {
                    'type': 'profile_completion',
                    'phase': 'trust_adaptation_test',
                    'needs': ['trust_building']
                }
                
                success = await self.mentor_service.inject_contextual_instructions(
                    situation_context=situation_context
                )
                
                if success:
                    print(f"✅ Trust Level {trust_test['level']}: Communication adapted successfully")
                    results.append(True)
                else:
                    print(f"❌ Trust Level {trust_test['level']}: Failed to adapt communication")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ Trust Level {trust_test['level']}: Error - {str(e)}")
                results.append(False)
        
        success_rate = sum(results) / len(results) * 100
        print(f"\n📊 Trust-Based Communication Adaptation Success Rate: {success_rate:.1f}%")
        return success_rate >= 80.0  # Expect at least 80% success rate
    
    async def test_runtime_enhancement_coordination(self):
        """Test runtime enhancement coordination system."""
        print("\n⚡ Testing Runtime Enhancement Coordination")
        print("-" * 45)
        
        # Simulate conversation state and profile gaps
        conversation_state = {
            'phase': 'profile_completion',
            'awaiting_response_type': 'profile_info',
            'workflow_context': {
                'type': 'onboarding',
                'current_step': 'demographic_collection'
            }
        }
        
        user_profile_gaps = {
            'critical': ['age', 'location', 'occupation'],
            'important': ['goals', 'preferences'],
            'optional': ['interests', 'availability']
        }
        
        try:
            enhancement_results = await self.mentor_service.apply_runtime_enhancements(
                conversation_state=conversation_state,
                user_profile_gaps=user_profile_gaps
            )
            
            # Validate enhancement results
            expected_enhancements = ['instructions_injected', 'tools_injected']
            success_count = 0
            
            for enhancement in expected_enhancements:
                if enhancement_results.get(enhancement, False):
                    print(f"✅ {enhancement.replace('_', ' ').title()}: Success")
                    success_count += 1
                else:
                    print(f"❌ {enhancement.replace('_', ' ').title()}: Failed")
            
            enhancements_applied = enhancement_results.get('enhancements_applied', [])
            print(f"📋 Enhancements Applied: {', '.join(enhancements_applied)}")
            
            success_rate = success_count / len(expected_enhancements) * 100
            print(f"📊 Runtime Enhancement Coordination Success Rate: {success_rate:.1f}%")
            
            return success_rate >= 80.0
            
        except Exception as e:
            print(f"❌ Runtime Enhancement Coordination failed: {str(e)}")
            return False
    
    async def cleanup(self):
        """Clean up test data."""
        print("\n🧹 Cleaning up test data...")
        
        @sync_to_async
        def cleanup_data():
            if self.test_user_id:
                try:
                    user_profile = UserProfile.objects.get(id=self.test_user_id)
                    user_profile.delete()
                    print("✅ Test data cleaned up")
                except UserProfile.DoesNotExist:
                    print("ℹ️  Test user already deleted")
        
        await cleanup_data()
    
    async def run_all_tests(self):
        """Run all MentorService Phase 4 intelligence tests."""
        print("🚀 Starting MentorService Phase 4 Intelligence Tests")
        print("=" * 65)
        
        try:
            await self.setup_test_user()
            
            # Test 1: Contextual instruction injection
            result1 = await self.test_contextual_instruction_injection()
            
            # Test 2: Dynamic tool injection
            result2 = await self.test_dynamic_tool_injection()
            
            # Test 3: Trust-based communication adaptation
            result3 = await self.test_trust_based_communication_adaptation()
            
            # Test 4: Runtime enhancement coordination
            result4 = await self.test_runtime_enhancement_coordination()
            
            # Summary
            print("\n📋 Test Summary")
            print("-" * 25)
            print(f"✅ Contextual Instruction Injection: {'PASS' if result1 else 'FAIL'}")
            print(f"✅ Dynamic Tool Injection: {'PASS' if result2 else 'FAIL'}")
            print(f"✅ Trust-Based Communication: {'PASS' if result3 else 'FAIL'}")
            print(f"✅ Runtime Enhancement Coordination: {'PASS' if result4 else 'FAIL'}")
            
            overall_success = all([result1, result2, result3, result4])
            print(f"\n🎯 Overall Result: {'SUCCESS' if overall_success else 'FAILURE'}")
            
            return overall_success
            
        except Exception as e:
            print(f"❌ Test suite failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            await self.cleanup()


async def main():
    """Main test execution."""
    test_suite = MentorServicePhase4IntelligenceTest()
    success = await test_suite.run_all_tests()
    
    if success:
        print("\n🎉 MentorService Phase 4 Intelligence Tests PASSED!")
        sys.exit(0)
    else:
        print("\n💥 MentorService Phase 4 Intelligence Tests FAILED!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
