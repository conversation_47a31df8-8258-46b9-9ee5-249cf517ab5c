#!/usr/bin/env python3
"""
Debug Profile Completion Issue

This script investigates the discrepancy between the profile completion
calculation in the conversation dispatcher vs direct tool calls.
"""

import os
import sys
import django
import asyncio

# Add the backend directory to the Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.agents.tools.get_user_profile_tool import get_user_profile
from apps.main.agents.tools.tools_util import execute_tool


async def debug_profile_completion_issue():
    """Debug the profile completion calculation discrepancy."""
    
    print("🔍 Debugging Profile Completion Issue")
    print("=" * 60)
    
    user_profile_id = "2"
    
    # Test 1: Direct tool call
    print("\n1️⃣ Direct get_user_profile tool call:")
    try:
        result = await get_user_profile({'input_data': {'user_profile_id': user_profile_id}})
        user_profile_data = result.get('user_profile', {})
        completion = user_profile_data.get('profile_completion', 0.0)
        print(f"   Profile Completion: {completion:.1%}")
        print(f"   Demographics: {'✓' if user_profile_data.get('demographics') else '✗'}")
        print(f"   Goals: {len(user_profile_data.get('goals', []))} records")
        print(f"   Trust Level: {'✓' if user_profile_data.get('trust_level') else '✗'}")
    except Exception as e:
        print(f"   ERROR: {e}")
    
    # Test 2: execute_tool call (same as conversation dispatcher)
    print("\n2️⃣ execute_tool call (same as ConversationDispatcher):")
    try:
        result = await execute_tool(
            tool_code="get_user_profile",
            tool_input={"input_data": {"user_profile_id": user_profile_id}},
            user_profile_id=user_profile_id,
            session_id="debug_session"
        )
        user_profile_data = result.get('user_profile', {})
        completion = user_profile_data.get('profile_completion', 0.0)
        print(f"   Profile Completion: {completion:.1%}")
        print(f"   Demographics: {'✓' if user_profile_data.get('demographics') else '✗'}")
        print(f"   Goals: {len(user_profile_data.get('goals', []))} records")
        print(f"   Trust Level: {'✓' if user_profile_data.get('trust_level') else '✗'}")
    except Exception as e:
        print(f"   ERROR: {e}")
    
    # Test 3: ConversationDispatcher _check_profile_completion method
    print("\n3️⃣ ConversationDispatcher._check_profile_completion:")
    try:
        dispatcher = ConversationDispatcher(
            user_profile_id=user_profile_id,
            user_ws_session_name="debug_session"
        )
        completion = await dispatcher._check_profile_completion()
        print(f"   Profile Completion: {completion:.1%}")
    except Exception as e:
        print(f"   ERROR: {e}")
    
    # Test 4: Check if there's a different user being used
    print("\n4️⃣ Checking all users with profile completion > 0:")
    try:
        from apps.user.models import UserProfile
        from django.db import connection
        
        # Check all users
        for user_id in range(1, 11):  # Check first 10 users
            try:
                result = await get_user_profile({'input_data': {'user_profile_id': str(user_id)}})
                user_profile_data = result.get('user_profile', {})
                completion = user_profile_data.get('profile_completion', 0.0)
                if completion > 0:
                    print(f"   User {user_id}: {completion:.1%} completion")
            except:
                continue  # User doesn't exist
                
    except Exception as e:
        print(f"   ERROR: {e}")
    
    # Test 5: Check if there's caching or session state affecting results
    print("\n5️⃣ Testing for caching/session state issues:")
    try:
        # Multiple calls to see if results are consistent
        for i in range(3):
            result = await execute_tool(
                tool_code="get_user_profile",
                tool_input={"input_data": {"user_profile_id": user_profile_id}},
                user_profile_id=user_profile_id,
                session_id=f"debug_session_{i}"
            )
            completion = result.get('user_profile', {}).get('profile_completion', 0.0)
            print(f"   Call {i+1}: {completion:.1%}")
    except Exception as e:
        print(f"   ERROR: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 CONCLUSION:")
    print("If all direct calls show 0% but ConversationDispatcher shows 100%,")
    print("there's likely a bug in the ConversationDispatcher implementation.")


if __name__ == "__main__":
    asyncio.run(debug_profile_completion_issue())
