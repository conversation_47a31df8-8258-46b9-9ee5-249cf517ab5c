#!/usr/bin/env python3
"""
Test script to validate forced wheel generation functionality.

This script tests that the conversation dispatcher properly bypasses profile completion
when the forced_wheel_generation flag is set to True.
"""

import os
import sys
import django
import asyncio
import json
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher


async def test_forced_wheel_generation():
    """Test that forced wheel generation bypasses profile completion."""
    print("🧪 Testing forced wheel generation...")
    
    # Test with user ID 2 (PhiPhi) - should have incomplete profile
    dispatcher = ConversationDispatcher('2', 'test_session_forced', fail_fast_on_errors=True)
    
    # Create a message with forced wheel generation
    user_message = {
        'text': 'Generate a personalized activity wheel',
        'metadata': {
            'forced_wheel_generation': True,
            'energy_level': 100,
            'time_available_minutes': 120,
            'requested_workflow': 'wheel_generation'
        }
    }
    
    try:
        print(f"📤 Sending message: {user_message['text']}")
        print(f"🔧 Metadata: {user_message['metadata']}")
        
        result = await dispatcher.process_message(user_message)
        
        print("\n✅ Test Results:")
        print(f"   Workflow type: {result.get('workflow_type')}")
        print(f"   Status: {result.get('status')}")
        print(f"   Confidence: {result.get('confidence')}")
        
        # Check if wheel generation was selected
        if result.get('workflow_type') == 'wheel_generation':
            print("🎯 SUCCESS: Forced wheel generation worked!")
            print("   Profile completion was bypassed as expected.")
            return True
        else:
            print(f"❌ FAILED: Expected 'wheel_generation', got '{result.get('workflow_type')}'")
            print("   Forced wheel generation did not work.")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_normal_wheel_generation():
    """Test normal wheel generation (should go to onboarding for incomplete profile)."""
    print("\n🧪 Testing normal wheel generation (without forced flag)...")
    
    dispatcher = ConversationDispatcher('2', 'test_session_normal', fail_fast_on_errors=True)
    
    # Create a message without forced wheel generation
    user_message = {
        'text': 'Generate a personalized activity wheel',
        'metadata': {
            'energy_level': 100,
            'time_available_minutes': 120,
            'requested_workflow': 'wheel_generation'
        }
    }
    
    try:
        print(f"📤 Sending message: {user_message['text']}")
        print(f"🔧 Metadata: {user_message['metadata']}")
        
        result = await dispatcher.process_message(user_message)
        
        print("\n✅ Test Results:")
        print(f"   Workflow type: {result.get('workflow_type')}")
        print(f"   Status: {result.get('status')}")
        print(f"   Confidence: {result.get('confidence')}")
        
        # Check if onboarding was selected (expected for incomplete profile)
        if result.get('workflow_type') == 'onboarding':
            print("🎯 SUCCESS: Normal behavior works!")
            print("   Incomplete profile correctly routed to onboarding.")
            return True
        elif result.get('workflow_type') == 'wheel_generation':
            print("⚠️  UNEXPECTED: Got wheel generation without forced flag.")
            print("   This might indicate the profile is actually complete.")
            return True  # Still valid, just unexpected
        else:
            print(f"❌ FAILED: Expected 'onboarding' or 'wheel_generation', got '{result.get('workflow_type')}'")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting forced wheel generation tests...")
    print(f"⏰ Test started at: {datetime.now().isoformat()}")
    print("=" * 60)
    
    # Test 1: Forced wheel generation
    test1_result = await test_forced_wheel_generation()
    
    # Test 2: Normal wheel generation
    test2_result = await test_normal_wheel_generation()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print(f"   Forced wheel generation test: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Normal wheel generation test: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 ALL TESTS PASSED!")
        print("   The forced wheel generation feature is working correctly.")
        return True
    else:
        print("\n💥 SOME TESTS FAILED!")
        print("   The forced wheel generation feature needs debugging.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
