#!/usr/bin/env python3
"""
Agent Modal Integration Test

This test validates the complete agent evaluation modal integration including:
- API endpoint correctness
- Modal loading and rendering
- JavaScript function availability
- Enhanced debugging data display

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_agent_modal_integration.py
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import RequestFactory, Client
from django.contrib.auth.models import User
from apps.user.models import UserProfile
from apps.main.models import BenchmarkRun, GenericAgent
from apps.admin_tools.benchmark.views import QuickBenchmarkView
from apps.admin_tools.views import BenchmarkRunView

logger = logging.getLogger(__name__)

class AgentModalIntegrationTest:
    """Comprehensive test for agent modal integration."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Agent Modal Integration Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
        self.factory = RequestFactory()
        self.client = Client()
        
    def log_test_result(self, test_name: str, success: bool, details: dict = None, error: str = None):
        """Log the result of a test."""
        self.results['tests'][test_name] = {
            'success': success,
            'details': details or {},
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results['summary']['total_tests'] += 1
        if success:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: {error}")
            if error:
                self.results['summary']['errors'].append(f"{test_name}: {error}")
    
    def test_api_endpoint_exists(self):
        """Test that the detail API endpoint exists and returns correct data."""
        try:
            # Get a benchmark run
            benchmark_run = BenchmarkRun.objects.first()
            if not benchmark_run:
                self.log_test_result('API Endpoint Exists', False, error="No benchmark runs found")
                return False
            
            # Create a staff user
            user, created = User.objects.get_or_create(
                username='test_modal_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            
            # Test the detail API endpoint
            request = self.factory.get(f'/admin/benchmarks/api/run/{benchmark_run.id}/')
            request.user = user
            
            view = BenchmarkRunView()
            response = view.get(request, run_id=str(benchmark_run.id))
            
            details = {
                'status_code': response.status_code,
                'run_id': str(benchmark_run.id),
                'content_type': response.get('Content-Type', 'unknown')
            }
            
            if response.status_code == 200:
                try:
                    response_data = json.loads(response.content)
                    details.update({
                        'is_json': True,
                        'has_id': 'id' in response_data,
                        'has_raw_results': 'raw_results' in response_data,
                        'has_enhanced_debugging': bool(response_data.get('raw_results', {}).get('enhanced_debugging_data', {}).get('enabled', False)),
                        'data_keys': list(response_data.keys())
                    })
                    success = True
                    error = None
                except json.JSONDecodeError as e:
                    success = False
                    error = f"Response is not valid JSON: {str(e)}"
                    details['response_preview'] = response.content.decode('utf-8')[:200]
            else:
                success = False
                error = f"HTTP {response.status_code}"
                details['response_preview'] = response.content.decode('utf-8')[:200]
            
            self.log_test_result('API Endpoint Exists', success, details, error)
            return success, benchmark_run if success else None
            
        except Exception as e:
            self.log_test_result('API Endpoint Exists', False, error=str(e))
            return False, None
    
    def test_modal_html_structure(self):
        """Test that the modal HTML structure is correct."""
        try:
            # Create a staff user and login
            user, created = User.objects.get_or_create(
                username='test_modal_html_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark management page
            response = self.client.get('/admin/benchmarks/manage/')
            
            details = {
                'status_code': response.status_code,
                'content_type': response.get('Content-Type', 'unknown')
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for modal elements
                modal_elements = {
                    'agent_details_modal': 'id="agent-details-modal"' in content,
                    'agent_modal_body': 'id="agent-modal-body"' in content,
                    'copy_run_data_btn': 'id="copy-run-data-btn"' in content,
                    'refresh_modal_btn': 'id="refresh-modal-btn"' in content,
                    'llm_interactions_content': 'id="llm-interactions-content"' in content,
                    'enhanced_tool_calls_section': 'id="enhanced-tool-calls-section"' in content
                }
                
                # Check for JavaScript functions
                js_functions = {
                    'renderAgentDetails': 'function renderAgentDetails' in content or 'renderAgentDetails =' in content,
                    'renderLLMInteractions': 'function renderLLMInteractions' in content or 'renderLLMInteractions =' in content,
                    'renderEnhancedToolCalls': 'function renderEnhancedToolCalls' in content or 'renderEnhancedToolCalls =' in content,
                    'setupCopyRunDataButton': 'function setupCopyRunDataButton' in content or 'setupCopyRunDataButton =' in content
                }
                
                details.update({
                    'modal_elements': modal_elements,
                    'js_functions': js_functions,
                    'all_modal_elements_present': all(modal_elements.values()),
                    'all_js_functions_present': all(js_functions.values())
                })
                
                success = all(modal_elements.values()) and all(js_functions.values())
                error = None if success else "Missing modal elements or JavaScript functions"
                
            else:
                success = False
                error = f"Could not access benchmark management page: HTTP {response.status_code}"
            
            self.log_test_result('Modal HTML Structure', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Modal HTML Structure', False, error=str(e))
            return False
    
    def test_quick_benchmark_integration(self):
        """Test the complete quick benchmark to modal integration."""
        try:
            # Get required data
            user_profile = UserProfile.objects.filter(is_real=True).first()
            agent = GenericAgent.objects.filter(role='mentor').first()
            
            if not user_profile or not agent:
                self.log_test_result('Quick Benchmark Integration', False, 
                                   error="Missing user profile or agent")
                return False
            
            # Create a staff user
            user, created = User.objects.get_or_create(
                username='test_integration_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            
            # Run a quick benchmark
            request_data = {
                'agent_name': agent.role,
                'user_profile_id': str(user_profile.id),
                'evaluation_template': 'mentor_helpfulness',
                'scenario_context': {'user_input': 'Hello, I need help with integration testing'},
                'use_real_tools': True,
                'use_real_db': True
            }
            
            request = self.factory.post(
                '/admin/benchmarks/api/quick-benchmark/',
                data=json.dumps(request_data),
                content_type='application/json'
            )
            request.user = user
            
            # Execute the benchmark
            view = QuickBenchmarkView()
            response = view.post(request)
            
            details = {
                'benchmark_status_code': response.status_code,
                'request_data': request_data
            }
            
            if response.status_code in [200, 201]:
                response_data = json.loads(response.content)
                benchmark_run_id = response_data.get('benchmark_run_id')
                
                if benchmark_run_id:
                    # Test the detail API with the new run
                    detail_request = self.factory.get(f'/admin/benchmarks/api/run/{benchmark_run_id}/')
                    detail_request.user = user
                    
                    detail_view = BenchmarkRunView()
                    detail_response = detail_view.get(detail_request, run_id=benchmark_run_id)
                    
                    details.update({
                        'benchmark_run_id': benchmark_run_id,
                        'detail_status_code': detail_response.status_code
                    })
                    
                    if detail_response.status_code == 200:
                        try:
                            detail_data = json.loads(detail_response.content)
                            details.update({
                                'detail_has_enhanced_debugging': bool(detail_data.get('raw_results', {}).get('enhanced_debugging_data', {}).get('enabled', False)),
                                'detail_llm_interactions': len(detail_data.get('raw_results', {}).get('enhanced_debugging_data', {}).get('llm_interactions', [])),
                                'detail_tool_calls': len(detail_data.get('raw_results', {}).get('enhanced_debugging_data', {}).get('tool_calls', [])),
                                'detail_data_structure_valid': all(key in detail_data for key in ['id', 'agent_role', 'scenario'])
                            })
                            success = True
                            error = None
                        except json.JSONDecodeError as e:
                            success = False
                            error = f"Detail API returned invalid JSON: {str(e)}"
                    else:
                        success = False
                        error = f"Detail API failed: HTTP {detail_response.status_code}"
                else:
                    success = False
                    error = "No benchmark run ID returned"
            else:
                success = False
                error = f"Quick benchmark failed: HTTP {response.status_code}"
            
            self.log_test_result('Quick Benchmark Integration', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Quick Benchmark Integration', False, error=str(e))
            return False
    
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'agent_modal_integration_{timestamp}.json'
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Results saved to: {filepath}")
        return filepath
    
    def run_all_tests(self):
        """Run all tests in sequence."""
        print("🚀 Starting Agent Modal Integration Test")
        print("=" * 60)
        
        # Test 1: API Endpoint
        api_success, benchmark_run = self.test_api_endpoint_exists()
        
        # Test 2: Modal HTML Structure
        modal_success = self.test_modal_html_structure()
        
        # Test 3: Complete Integration
        integration_success = self.test_quick_benchmark_integration()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.results['summary']['total_tests']}")
        print(f"Passed: {self.results['summary']['passed']}")
        print(f"Failed: {self.results['summary']['failed']}")
        
        if self.results['summary']['errors']:
            print("\n❌ ERRORS:")
            for error in self.results['summary']['errors']:
                print(f"  - {error}")
        
        success_rate = (self.results['summary']['passed'] / self.results['summary']['total_tests']) * 100
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("✅ Agent Modal Integration is working perfectly!")
        elif success_rate >= 70:
            print("⚠️ Agent Modal Integration has some issues")
        else:
            print("❌ Agent Modal Integration needs significant fixes")
        
        return self.save_results()

def main():
    """Main test execution."""
    test = AgentModalIntegrationTest()
    results_file = test.run_all_tests()
    
    print(f"\n🔗 View detailed results: {results_file}")
    print("\n🎯 Next Steps:")
    print("1. If tests passed: Test the UI manually at /admin/benchmarks/manage/")
    print("2. If tests failed: Check the errors above and fix the integration issues")
    print("3. Run the enhanced agent benchmarking test to verify data capture")

if __name__ == '__main__':
    main()
