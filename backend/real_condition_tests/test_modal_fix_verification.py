#!/usr/bin/env python3
"""
Modal Fix Verification Test

This test verifies that the modal functionality is working after the fixes.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_modal_fix_verification.py
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.main.models import BenchmarkRun

logger = logging.getLogger(__name__)

class ModalFixVerificationTest:
    """Test that the modal fixes are working."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Modal Fix Verification Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
        self.client = Client()
        
    def log_test_result(self, test_name: str, success: bool, details: dict = None, error: str = None):
        """Log the result of a test."""
        self.results['tests'][test_name] = {
            'success': success,
            'details': details or {},
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results['summary']['total_tests'] += 1
        if success:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: {error}")
            if error:
                self.results['summary']['errors'].append(f"{test_name}: {error}")
    
    def test_javascript_syntax_fix(self):
        """Test that the JavaScript syntax error is fixed."""
        try:
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_syntax_fix_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark history page
            response = self.client.get('/admin/benchmarks/history/')
            
            details = {
                'status_code': response.status_code
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for the fixed syntax issues
                syntax_checks = {
                    'renderAgentDetails_global_defined': 'window.renderAgentDetails = async function(' in content,
                    'openAgentEvaluationModal_defined': 'window.openAgentEvaluationModal = function(' in content,
                    'debug_function_defined': 'function checkModalFunctions()' in content,
                    'no_extra_closing_braces': content.count('}') - content.count('{') <= 5,  # Allow some tolerance
                    'modal_elements_present': 'id="agent-details-modal"' in content and 'id="agent-modal-body"' in content
                }
                
                # Check for error handling improvements
                error_handling_checks = {
                    'better_error_messages': 'Function Not Available' in content,
                    'debug_logging': 'console.log(\'Checking modal function availability:\')' in content,
                    'function_availability_check': 'typeof window.renderAgentDetails' in content,
                    'graceful_degradation': 'alert(\'Agent evaluation modal functions not loaded\')' in content
                }
                
                details.update({
                    'syntax_checks': syntax_checks,
                    'error_handling_checks': error_handling_checks,
                    'all_syntax_fixed': all(syntax_checks.values()),
                    'error_handling_improved': all(error_handling_checks.values())
                })
                
                success = details['all_syntax_fixed'] and details['error_handling_improved']
                error = None if success else "JavaScript syntax or error handling issues remain"
                
            else:
                success = False
                error = f"Could not access benchmark history page: HTTP {response.status_code}"
            
            self.log_test_result('JavaScript Syntax Fix', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('JavaScript Syntax Fix', False, error=str(e))
            return False
    
    def test_api_endpoint_functionality(self):
        """Test that the API endpoint is working correctly."""
        try:
            # Get a benchmark run for testing
            benchmark_run = BenchmarkRun.objects.first()
            if not benchmark_run:
                self.log_test_result('API Endpoint Functionality', False, 
                                   error="No benchmark runs found for testing")
                return False
            
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_api_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Test the API endpoint
            response = self.client.get(f'/admin/benchmarks/api/run/{benchmark_run.id}/')
            
            details = {
                'status_code': response.status_code,
                'benchmark_run_id': str(benchmark_run.id)
            }
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # Check for required fields
                    required_fields = ['id', 'scenario', 'raw_results']
                    field_checks = {field: field in data for field in required_fields}
                    
                    # Check for enhanced debugging data
                    enhanced_data = data.get('raw_results', {}).get('enhanced_debugging_data', {})
                    enhanced_checks = {
                        'enhanced_debugging_enabled': enhanced_data.get('enabled', False),
                        'has_llm_interactions': len(enhanced_data.get('llm_interactions', [])) > 0,
                        'has_agents_data': len(enhanced_data.get('agents', [])) > 0
                    }
                    
                    details.update({
                        'is_json': True,
                        'field_checks': field_checks,
                        'enhanced_checks': enhanced_checks,
                        'all_required_fields': all(field_checks.values()),
                        'enhanced_data_available': any(enhanced_checks.values())
                    })
                    
                    success = details['all_required_fields']
                    error = None if success else f"Missing required fields: {[k for k, v in field_checks.items() if not v]}"
                    
                except json.JSONDecodeError as e:
                    success = False
                    error = f"API returned invalid JSON: {str(e)}"
                    details['response_preview'] = response.content.decode('utf-8')[:200]
            else:
                success = False
                error = f"API endpoint failed: HTTP {response.status_code}"
                details['response_preview'] = response.content.decode('utf-8')[:200]
            
            self.log_test_result('API Endpoint Functionality', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('API Endpoint Functionality', False, error=str(e))
            return False
    
    def test_benchmark_management_page_integration(self):
        """Test that the benchmark management page has proper modal integration."""
        try:
            # Create and login user
            user, created = User.objects.get_or_create(
                username='test_management_user',
                defaults={'email': '<EMAIL>', 'is_staff': True, 'is_active': True}
            )
            self.client.force_login(user)
            
            # Get the benchmark management page
            response = self.client.get('/admin/benchmarks/manage/')
            
            details = {
                'status_code': response.status_code
            }
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Check for modal integration
                integration_checks = {
                    'agent_modal_included': 'id="agent-details-modal"' in content,
                    'quick_benchmark_js_loaded': 'quick_benchmark.js' in content,
                    'openAgentEvaluationModal_function': 'function openAgentEvaluationModal' in content,
                    'view_detailed_results_button': 'id="view-detailed-results-btn"' in content,
                    'renderAgentDetails_global': 'window.renderAgentDetails = async function(' in content
                }
                
                details.update({
                    'integration_checks': integration_checks,
                    'all_integration_working': all(integration_checks.values())
                })
                
                success = details['all_integration_working']
                error = None if success else f"Missing integration: {[k for k, v in integration_checks.items() if not v]}"
                
            else:
                success = False
                error = f"Could not access benchmark management page: HTTP {response.status_code}"
            
            self.log_test_result('Benchmark Management Page Integration', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Benchmark Management Page Integration', False, error=str(e))
            return False
    
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'modal_fix_verification_{timestamp}.json'
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Results saved to: {filepath}")
        return filepath
    
    def run_all_tests(self):
        """Run all tests in sequence."""
        print("🚀 Starting Modal Fix Verification Test")
        print("=" * 60)
        
        # Test 1: JavaScript Syntax Fix
        self.test_javascript_syntax_fix()
        
        # Test 2: API Endpoint Functionality
        self.test_api_endpoint_functionality()
        
        # Test 3: Benchmark Management Page Integration
        self.test_benchmark_management_page_integration()
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.results['summary']['total_tests']}")
        print(f"Passed: {self.results['summary']['passed']}")
        print(f"Failed: {self.results['summary']['failed']}")
        
        if self.results['summary']['errors']:
            print("\n❌ ERRORS:")
            for error in self.results['summary']['errors']:
                print(f"  - {error}")
        
        success_rate = (self.results['summary']['passed'] / self.results['summary']['total_tests']) * 100
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("✅ Modal fixes are working correctly!")
            print("🎉 The renderAgentDetails error should now be resolved!")
        elif success_rate >= 70:
            print("⚠️ Modal fixes are partially working")
        else:
            print("❌ Modal fixes need more work")
        
        return self.save_results()

def main():
    """Main test execution."""
    test = ModalFixVerificationTest()
    results_file = test.run_all_tests()
    
    print(f"\n🔗 View detailed results: {results_file}")
    print("\n🎯 Next Steps:")
    print("1. If tests passed: Try the modal functionality manually")
    print("2. If tests failed: Check the specific issues reported")
    print("3. Manual testing: Open http://localhost:8000/admin/benchmarks/history/")
    print("4. Click 'View Details' on a benchmark run")

if __name__ == '__main__':
    main()
