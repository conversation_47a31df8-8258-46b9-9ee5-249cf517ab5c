#!/usr/bin/env python3
"""
Test Modular Web Components System

This test validates the modular web components system for agent evaluation,
ensuring that components load correctly, register properly, and provide
the expected functionality for displaying agent benchmark results.

Test Coverage:
- Component loading and initialization
- Component registry functionality
- Agent-specific component creation
- Data visualization components
- Error handling and fallback mechanisms
- Integration with existing modal system
"""

import os
import sys
import json
import asyncio
import logging
from pathlib import Path
from datetime import datetime

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import TestCase
from django.contrib.auth.models import User
# Note: Benchmarking models not needed for file structure tests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ModularWebComponentsTest:
    """Test suite for modular web components system"""
    
    def __init__(self):
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'test_name': 'Modular Web Components System',
            'tests': [],
            'summary': {
                'total': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
        
        # Component file paths (adjust for container environment)
        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        self.component_files = [
            f'{base_path}/static/admin/js/components/BaseModalComponent.js',
            f'{base_path}/static/admin/js/components/BaseAgentComponent.js',
            f'{base_path}/static/admin/js/components/ComponentRegistry.js',
            f'{base_path}/static/admin/js/components/ResourceAgentComponent.js',
            f'{base_path}/static/admin/js/components/MentorAgentComponent.js',
            f'{base_path}/static/admin/js/components/EngagementAgentComponent.js',
            f'{base_path}/static/admin/js/components/GenericAgentComponent.js',
            f'{base_path}/static/admin/js/components/DataVisualizationComponent.js',
            f'{base_path}/static/admin/js/components/ComponentLoader.js'
        ]

        # Template files
        self.template_files = [
            f'{base_path}/templates/admin_tools/modals/enhanced_agent_evaluation_modal.html'
        ]
    
    def run_all_tests(self):
        """Run all component tests"""
        logger.info("🧪 Starting Modular Web Components System Tests")
        
        try:
            # File existence tests
            self.test_component_files_exist()
            self.test_template_files_exist()
            
            # Component structure tests
            self.test_base_modal_component_structure()
            self.test_base_component_structure()
            self.test_component_registry_structure()
            self.test_enhanced_resource_component_structure()
            self.test_resource_component_structure()
            self.test_mentor_component_structure()
            self.test_engagement_component_structure()
            self.test_generic_component_structure()
            self.test_data_visualization_structure()
            self.test_component_loader_structure()
            
            # Template structure tests
            self.test_enhanced_modal_structure()
            
            # Integration tests
            self.test_component_dependencies()
            self.test_component_registration()
            self.test_modal_integration()
            
            # Functionality tests
            self.test_resource_agent_data_handling()
            self.test_data_visualization_features()
            self.test_error_handling()
            
            # Performance tests
            self.test_component_loading_performance()
            
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            self.add_test_result('test_execution', False, str(e))
        
        self.generate_report()
        return self.test_results
    
    def test_component_files_exist(self):
        """Test that all component files exist"""
        logger.info("📁 Testing component file existence...")
        
        missing_files = []
        for file_path in self.component_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            self.add_test_result(
                'component_files_exist',
                False,
                f"Missing files: {missing_files}"
            )
        else:
            self.add_test_result(
                'component_files_exist',
                True,
                f"All {len(self.component_files)} component files exist"
            )
    
    def test_template_files_exist(self):
        """Test that template files exist"""
        logger.info("📄 Testing template file existence...")
        
        missing_files = []
        for file_path in self.template_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            self.add_test_result(
                'template_files_exist',
                False,
                f"Missing template files: {missing_files}"
            )
        else:
            self.add_test_result(
                'template_files_exist',
                True,
                f"All {len(self.template_files)} template files exist"
            )

    def test_base_modal_component_structure(self):
        """Test BaseModalComponent structure"""
        logger.info("🏗️ Testing BaseModalComponent structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/BaseModalComponent.js'

        try:
            with open(file_path, 'r') as f:
                content = f.read()

            required_elements = [
                'class BaseModalComponent extends HTMLElement',
                'connectedCallback()',
                'attributeChangedCallback(',
                'getTemplate()',
                'getStyles()',
                'render()',
                'handleAction(',
                'createSection(',
                'formatValue(',
                'window.BaseModalComponent = BaseModalComponent'
            ]

            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)

            if missing_elements:
                self.add_test_result(
                    'base_modal_component_structure',
                    False,
                    f"Missing elements: {missing_elements}"
                )
            else:
                self.add_test_result(
                    'base_modal_component_structure',
                    True,
                    "BaseModalComponent has all required methods and structure"
                )

        except Exception as e:
            self.add_test_result(
                'base_modal_component_structure',
                False,
                f"Error reading file: {e}"
            )

    def test_base_component_structure(self):
        """Test BaseAgentComponent structure"""
        logger.info("🏗️ Testing BaseAgentComponent structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/BaseAgentComponent.js'
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            required_elements = [
                'class BaseAgentComponent extends HTMLElement',
                'connectedCallback()',
                'disconnectedCallback()',
                'attributeChangedCallback(',
                'getTemplate()',
                'getStyles()',
                'render()',
                'handleAction(',
                'window.BaseAgentComponent = BaseAgentComponent'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                self.add_test_result(
                    'base_component_structure',
                    False,
                    f"Missing elements: {missing_elements}"
                )
            else:
                self.add_test_result(
                    'base_component_structure',
                    True,
                    "BaseAgentComponent has all required methods and structure"
                )
                
        except Exception as e:
            self.add_test_result(
                'base_component_structure',
                False,
                f"Error reading file: {e}"
            )
    
    def test_component_registry_structure(self):
        """Test ComponentRegistry structure"""
        logger.info("📋 Testing ComponentRegistry structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/ComponentRegistry.js'
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            required_elements = [
                'class ComponentRegistry',
                'registerComponent(',
                'createComponent(',
                'destroyComponent(',
                'getComponent(',
                'getAllComponents(',
                'window.componentRegistry = new ComponentRegistry()'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                self.add_test_result(
                    'component_registry_structure',
                    False,
                    f"Missing elements: {missing_elements}"
                )
            else:
                self.add_test_result(
                    'component_registry_structure',
                    True,
                    "ComponentRegistry has all required methods"
                )
                
        except Exception as e:
            self.add_test_result(
                'component_registry_structure',
                False,
                f"Error reading file: {e}"
            )

    def test_enhanced_resource_component_structure(self):
        """Test enhanced ResourceAgentComponent structure with new features"""
        logger.info("🎒 Testing enhanced ResourceAgentComponent structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/ResourceAgentComponent.js'

        try:
            with open(file_path, 'r') as f:
                content = f.read()

            # Check for inheritance from BaseModalComponent
            if 'extends BaseModalComponent' in content:
                inheritance_check = True
                inheritance_type = 'BaseModalComponent'
            elif 'extends BaseAgentComponent' in content:
                inheritance_check = True
                inheritance_type = 'BaseAgentComponent'
            else:
                inheritance_check = False
                inheritance_type = 'None'

            # Check for new enhanced features
            enhanced_features = [
                'renderToolCallsSection(',
                'renderPerformanceMetrics(',
                'tool_call_details',
                'tool_breakdown',
                'exportInventory(',
                'analyzeLimitations(',
                'convertInventoryToCSV(',
                'generateRecommendations(',
                'getSeverityClass('
            ]

            missing_features = []
            for feature in enhanced_features:
                if feature not in content:
                    missing_features.append(feature)

            # Check for CSS styles for new features
            style_features = [
                'tool-calls-details',
                'performance-metrics',
                'tool-metric',
                'metric-card',
                'recommendation-item'
            ]

            missing_styles = []
            for style in style_features:
                if style not in content:
                    missing_styles.append(style)

            if not inheritance_check:
                self.add_test_result(
                    'enhanced_resource_component_inheritance',
                    False,
                    "ResourceAgentComponent should extend BaseModalComponent or BaseAgentComponent"
                )
            else:
                self.add_test_result(
                    'enhanced_resource_component_inheritance',
                    True,
                    f"ResourceAgentComponent extends {inheritance_type}"
                )

            if missing_features:
                self.add_test_result(
                    'enhanced_resource_component_features',
                    False,
                    f"Missing enhanced features: {missing_features}"
                )
            else:
                self.add_test_result(
                    'enhanced_resource_component_features',
                    True,
                    "All enhanced features present (tool calls, performance metrics, etc.)"
                )

            if missing_styles:
                self.add_test_result(
                    'enhanced_resource_component_styles',
                    False,
                    f"Missing CSS styles: {missing_styles}"
                )
            else:
                self.add_test_result(
                    'enhanced_resource_component_styles',
                    True,
                    "All enhanced CSS styles present"
                )

        except Exception as e:
            self.add_test_result(
                'enhanced_resource_component_structure',
                False,
                f"Error reading file: {e}"
            )

    def test_resource_component_structure(self):
        """Test ResourceAgentComponent structure"""
        logger.info("🎒 Testing ResourceAgentComponent structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/ResourceAgentComponent.js'
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            required_elements = [
                'class ResourceAgentComponent extends BaseAgentComponent',
                'renderResourceSummary(',
                'renderInventoryDetails(',
                'renderCapabilitiesDetails(',
                'renderLimitationsDetails(',
                'renderEnvironmentDetails(',
                'exportResourceData(',
                'window.ResourceAgentComponent = ResourceAgentComponent'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                self.add_test_result(
                    'resource_component_structure',
                    False,
                    f"Missing elements: {missing_elements}"
                )
            else:
                self.add_test_result(
                    'resource_component_structure',
                    True,
                    "ResourceAgentComponent has all required methods"
                )
                
        except Exception as e:
            self.add_test_result(
                'resource_component_structure',
                False,
                f"Error reading file: {e}"
            )

    def test_mentor_component_structure(self):
        """Test MentorAgentComponent structure"""
        logger.info("🧠 Testing MentorAgentComponent structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/MentorAgentComponent.js'

        try:
            with open(file_path, 'r') as f:
                content = f.read()

            required_elements = [
                'class MentorAgentComponent extends BaseAgentComponent',
                'extractMentorData(',
                'calculateResponseQuality(',
                'analyzePhilosophicalFraming(',
                'analyzeConversationFlow(',
                'renderMentorSummary(',
                'renderConversationAnalysis(',
                'renderTrustProgression(',
                'window.MentorAgentComponent = MentorAgentComponent'
            ]

            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)

            if missing_elements:
                self.add_test_result(
                    'mentor_component_structure',
                    False,
                    f"Missing elements: {missing_elements}"
                )
            else:
                self.add_test_result(
                    'mentor_component_structure',
                    True,
                    "MentorAgentComponent has all required methods"
                )

        except Exception as e:
            self.add_test_result(
                'mentor_component_structure',
                False,
                f"Error reading file: {e}"
            )

    def test_engagement_component_structure(self):
        """Test EngagementAgentComponent structure"""
        logger.info("🎯 Testing EngagementAgentComponent structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/EngagementAgentComponent.js'

        try:
            with open(file_path, 'r') as f:
                content = f.read()

            required_elements = [
                'class EngagementAgentComponent extends BaseAgentComponent',
                'extractEngagementData(',
                'analyzeTemporalPatterns(',
                'analyzePreferenceConsistency(',
                'renderEngagementSummary(',
                'renderDomainAnalysis(',
                'renderBehavioralPatterns(',
                'renderTemporalAnalysis(',
                'window.EngagementAgentComponent = EngagementAgentComponent'
            ]

            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)

            if missing_elements:
                self.add_test_result(
                    'engagement_component_structure',
                    False,
                    f"Missing elements: {missing_elements}"
                )
            else:
                self.add_test_result(
                    'engagement_component_structure',
                    True,
                    "EngagementAgentComponent has all required methods"
                )

        except Exception as e:
            self.add_test_result(
                'engagement_component_structure',
                False,
                f"Error reading file: {e}"
            )

    def test_generic_component_structure(self):
        """Test GenericAgentComponent structure"""
        logger.info("🤖 Testing GenericAgentComponent structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/GenericAgentComponent.js'

        try:
            with open(file_path, 'r') as f:
                content = f.read()

            required_elements = [
                'class GenericAgentComponent extends BaseAgentComponent',
                'analyzeAgentData(',
                'extractToolCalls(',
                'analyzeDataStructure(',
                'extractKeyMetrics(',
                'renderSummaryView(',
                'renderDetailedView(',
                'renderRawView(',
                'window.GenericAgentComponent = GenericAgentComponent'
            ]

            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)

            if missing_elements:
                self.add_test_result(
                    'generic_component_structure',
                    False,
                    f"Missing elements: {missing_elements}"
                )
            else:
                self.add_test_result(
                    'generic_component_structure',
                    True,
                    "GenericAgentComponent has all required methods"
                )

        except Exception as e:
            self.add_test_result(
                'generic_component_structure',
                False,
                f"Error reading file: {e}"
            )

    def test_data_visualization_structure(self):
        """Test DataVisualizationComponent structure"""
        logger.info("📊 Testing DataVisualizationComponent structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/DataVisualizationComponent.js'
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            required_elements = [
                'class DataVisualizationComponent extends BaseAgentComponent',
                'loadChartLibrary(',
                'createChart(',
                'prepareChartData(',
                'getChartOptions(',
                'exportChart(',
                'updateChartData(',
                'window.DataVisualizationComponent = DataVisualizationComponent'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                self.add_test_result(
                    'data_visualization_structure',
                    False,
                    f"Missing elements: {missing_elements}"
                )
            else:
                self.add_test_result(
                    'data_visualization_structure',
                    True,
                    "DataVisualizationComponent has all required methods"
                )
                
        except Exception as e:
            self.add_test_result(
                'data_visualization_structure',
                False,
                f"Error reading file: {e}"
            )
    
    def test_component_loader_structure(self):
        """Test ComponentLoader structure"""
        logger.info("🔄 Testing ComponentLoader structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/ComponentLoader.js'
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            required_elements = [
                'class ComponentLoader',
                'loadAllComponents(',
                'loadComponent(',
                'sortComponentsByDependencies(',
                'verifyComponentLoaded(',
                'onLoadComplete(',
                'onLoadError(',
                'window.componentLoader = new ComponentLoader()'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                self.add_test_result(
                    'component_loader_structure',
                    False,
                    f"Missing elements: {missing_elements}"
                )
            else:
                self.add_test_result(
                    'component_loader_structure',
                    True,
                    "ComponentLoader has all required methods"
                )
                
        except Exception as e:
            self.add_test_result(
                'component_loader_structure',
                False,
                f"Error reading file: {e}"
            )
    
    def test_enhanced_modal_structure(self):
        """Test enhanced modal template structure"""
        logger.info("🖼️ Testing enhanced modal template structure...")

        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/templates/admin_tools/modals/enhanced_agent_evaluation_modal.html'
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            required_elements = [
                'id="enhanced-agent-details-modal"',
                'window.renderEnhancedAgentDetails',
                'window.openEnhancedAgentEvaluationModal',
                'loadAgentComponents(',
                'loadResourceAgentComponents(',
                'loadSharedComponents(',
                'switchToLegacyModal('
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                self.add_test_result(
                    'enhanced_modal_structure',
                    False,
                    f"Missing elements: {missing_elements}"
                )
            else:
                self.add_test_result(
                    'enhanced_modal_structure',
                    True,
                    "Enhanced modal template has all required elements"
                )
                
        except Exception as e:
            self.add_test_result(
                'enhanced_modal_structure',
                False,
                f"Error reading file: {e}"
            )
    
    def test_component_dependencies(self):
        """Test component dependency definitions"""
        logger.info("🔗 Testing component dependencies...")
        
        try:
            # Check ComponentLoader component definitions
            base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
            loader_path = f'{base_path}/static/admin/js/components/ComponentLoader.js'
            with open(loader_path, 'r') as f:
                content = f.read()
            
            # Look for component definitions
            if 'this.components = [' in content:
                # Extract component definitions section
                start = content.find('this.components = [')
                end = content.find('];', start)
                components_section = content[start:end]
                
                # Check for required components
                required_components = [
                    'BaseAgentComponent',
                    'ComponentRegistry',
                    'DataVisualizationComponent',
                    'ResourceAgentComponent',
                    'MentorAgentComponent',
                    'EngagementAgentComponent',
                    'GenericAgentComponent'
                ]
                
                missing_components = []
                for comp in required_components:
                    if comp not in components_section:
                        missing_components.append(comp)
                
                if missing_components:
                    self.add_test_result(
                        'component_dependencies',
                        False,
                        f"Missing component definitions: {missing_components}"
                    )
                else:
                    self.add_test_result(
                        'component_dependencies',
                        True,
                        "All required components are defined with dependencies"
                    )
            else:
                self.add_test_result(
                    'component_dependencies',
                    False,
                    "Component definitions not found in ComponentLoader"
                )
                
        except Exception as e:
            self.add_test_result(
                'component_dependencies',
                False,
                f"Error checking dependencies: {e}"
            )
    
    def test_component_registration(self):
        """Test component registration patterns"""
        logger.info("📝 Testing component registration patterns...")
        
        registration_patterns = [
            ('ResourceAgentComponent', 'window.componentRegistry.registerComponent(\'ResourceAgent\''),
            ('MentorAgentComponent', 'window.componentRegistry.registerComponent(\'MentorAgent\''),
            ('EngagementAgentComponent', 'window.componentRegistry.registerComponent(\'EngagementAgent\''),
            ('GenericAgentComponent', 'window.componentRegistry.registerComponent(\'GenericAgent\''),
            ('DataVisualizationComponent', 'window.componentRegistry.registerComponent(\'DataVisualization\'')
        ]
        
        all_registered = True
        missing_registrations = []
        
        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        for component_name, pattern in registration_patterns:
            file_path = f'{base_path}/static/admin/js/components/{component_name}.js'
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                if pattern not in content:
                    all_registered = False
                    missing_registrations.append(component_name)
                    
            except Exception as e:
                all_registered = False
                missing_registrations.append(f"{component_name} (error: {e})")
        
        if all_registered:
            self.add_test_result(
                'component_registration',
                True,
                "All components have proper registration patterns"
            )
        else:
            self.add_test_result(
                'component_registration',
                False,
                f"Missing registrations: {missing_registrations}"
            )
    
    def test_modal_integration(self):
        """Test modal integration points"""
        logger.info("🔌 Testing modal integration...")
        
        # Check if enhanced modal can be integrated with existing system
        try:
            # Look for integration points in quick_benchmark.js
            base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
            quick_benchmark_path = f'{base_path}/static/admin/js/quick_benchmark.js'
            
            integration_points = []
            
            if os.path.exists(quick_benchmark_path):
                with open(quick_benchmark_path, 'r') as f:
                    content = f.read()
                
                # Check for modal opening function
                if 'openAgentEvaluationModal' in content:
                    integration_points.append('Legacy modal function exists')
                
                # Check for view results button
                if 'viewResultsBtn' in content:
                    integration_points.append('View results button handling exists')
            
            if len(integration_points) >= 2:
                self.add_test_result(
                    'modal_integration',
                    True,
                    f"Modal integration points found: {integration_points}"
                )
            else:
                self.add_test_result(
                    'modal_integration',
                    False,
                    f"Insufficient integration points: {integration_points}"
                )
                
        except Exception as e:
            self.add_test_result(
                'modal_integration',
                False,
                f"Error checking modal integration: {e}"
            )
    
    def test_resource_agent_data_handling(self):
        """Test resource agent data handling capabilities"""
        logger.info("🎒 Testing resource agent data handling...")
        
        # Create sample resource agent data
        sample_data = {
            'agent_role': 'resource',
            'raw_results': {
                'last_output': {
                    'resource_context': {
                        'resources': {
                            'inventory_count': 11,
                            'capabilities_summary': {'total_skills': 10},
                            'limitations_count': 7,
                            'available_inventory': [
                                {
                                    'code': 'test_item',
                                    'name': 'Test Item',
                                    'category': 'Test Category',
                                    'location': 'Test Location',
                                    'ownership': 'Test Owner'
                                }
                            ]
                        },
                        'environment': {
                            'analyzed_type': 'test_environment',
                            'privacy_level': 90
                        }
                    }
                }
            }
        }
        
        # Test data structure validation
        try:
            resource_context = sample_data['raw_results']['last_output']['resource_context']
            
            required_fields = [
                'resources',
                'environment'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in resource_context:
                    missing_fields.append(field)
            
            if missing_fields:
                self.add_test_result(
                    'resource_agent_data_handling',
                    False,
                    f"Missing required fields: {missing_fields}"
                )
            else:
                self.add_test_result(
                    'resource_agent_data_handling',
                    True,
                    "Resource agent data structure is valid"
                )
                
        except Exception as e:
            self.add_test_result(
                'resource_agent_data_handling',
                False,
                f"Error validating data structure: {e}"
            )
    
    def test_data_visualization_features(self):
        """Test data visualization features"""
        logger.info("📊 Testing data visualization features...")
        
        # Test chart type support
        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        file_path = f'{base_path}/static/admin/js/components/DataVisualizationComponent.js'
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            chart_types = ['bar', 'line', 'pie', 'radar', 'doughnut']
            supported_types = []
            
            for chart_type in chart_types:
                if f'value="{chart_type}"' in content:
                    supported_types.append(chart_type)
            
            if len(supported_types) >= 4:
                self.add_test_result(
                    'data_visualization_features',
                    True,
                    f"Supports {len(supported_types)} chart types: {supported_types}"
                )
            else:
                self.add_test_result(
                    'data_visualization_features',
                    False,
                    f"Insufficient chart type support: {supported_types}"
                )
                
        except Exception as e:
            self.add_test_result(
                'data_visualization_features',
                False,
                f"Error checking visualization features: {e}"
            )
    
    def test_error_handling(self):
        """Test error handling mechanisms"""
        logger.info("⚠️ Testing error handling...")
        
        error_handling_patterns = [
            'try {',
            'catch (',
            'handleError(',
            'console.error(',
            'throw new Error('
        ]
        
        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        files_to_check = [
            f'{base_path}/static/admin/js/components/BaseAgentComponent.js',
            f'{base_path}/static/admin/js/components/ComponentLoader.js',
            f'{base_path}/templates/admin_tools/modals/enhanced_agent_evaluation_modal.html'
        ]
        
        error_handling_score = 0
        total_checks = len(files_to_check) * len(error_handling_patterns)
        
        for file_path in files_to_check:
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                for pattern in error_handling_patterns:
                    if pattern in content:
                        error_handling_score += 1
                        
            except Exception as e:
                logger.warning(f"Could not check error handling in {file_path}: {e}")
        
        if error_handling_score >= total_checks * 0.6:  # 60% threshold
            self.add_test_result(
                'error_handling',
                True,
                f"Good error handling coverage: {error_handling_score}/{total_checks}"
            )
        else:
            self.add_test_result(
                'error_handling',
                False,
                f"Insufficient error handling: {error_handling_score}/{total_checks}"
            )
    
    def test_component_loading_performance(self):
        """Test component loading performance considerations"""
        logger.info("⚡ Testing component loading performance...")
        
        performance_features = [
            ('Async loading', 'script.async = true'),
            ('Timeout handling', 'setTimeout('),
            ('Dependency management', 'dependencies'),
            ('Lazy loading', 'loadComponent('),
            ('Error recovery', 'retry')
        ]
        
        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        loader_path = f'{base_path}/static/admin/js/components/ComponentLoader.js'
        
        try:
            with open(loader_path, 'r') as f:
                content = f.read()
            
            found_features = []
            for feature_name, pattern in performance_features:
                if pattern in content:
                    found_features.append(feature_name)
            
            if len(found_features) >= 3:
                self.add_test_result(
                    'component_loading_performance',
                    True,
                    f"Performance features found: {found_features}"
                )
            else:
                self.add_test_result(
                    'component_loading_performance',
                    False,
                    f"Insufficient performance features: {found_features}"
                )
                
        except Exception as e:
            self.add_test_result(
                'component_loading_performance',
                False,
                f"Error checking performance features: {e}"
            )
    
    def add_test_result(self, test_name, passed, details):
        """Add a test result"""
        result = {
            'test': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        
        self.test_results['tests'].append(result)
        self.test_results['summary']['total'] += 1
        
        if passed:
            self.test_results['summary']['passed'] += 1
            logger.info(f"✅ {test_name}: {details}")
        else:
            self.test_results['summary']['failed'] += 1
            self.test_results['summary']['errors'].append(f"{test_name}: {details}")
            logger.error(f"❌ {test_name}: {details}")
    
    def generate_report(self):
        """Generate final test report"""
        summary = self.test_results['summary']
        
        logger.info("\n" + "="*80)
        logger.info("🧪 MODULAR WEB COMPONENTS SYSTEM TEST REPORT")
        logger.info("="*80)
        logger.info(f"Total Tests: {summary['total']}")
        logger.info(f"Passed: {summary['passed']}")
        logger.info(f"Failed: {summary['failed']}")
        logger.info(f"Success Rate: {(summary['passed']/summary['total']*100):.1f}%")
        
        if summary['errors']:
            logger.info("\n❌ FAILED TESTS:")
            for error in summary['errors']:
                logger.info(f"  - {error}")
        
        logger.info("\n📊 COMPONENT SYSTEM STATUS:")
        if summary['failed'] == 0:
            logger.info("🟢 All components are properly structured and ready for use")
        elif summary['failed'] <= 2:
            logger.info("🟡 Minor issues found - system should work with some limitations")
        else:
            logger.info("🔴 Significant issues found - system may not work properly")
        
        # Save detailed results
        base_path = '/usr/src/app' if os.path.exists('/usr/src/app') else '.'
        results_dir = Path(f'{base_path}/real_condition_tests/results')
        results_dir.mkdir(parents=True, exist_ok=True)
        
        results_file = results_dir / f'modular_components_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        logger.info(f"\n📄 Detailed results saved to: {results_file}")
        logger.info("="*80)

def main():
    """Main test execution"""
    test_suite = ModularWebComponentsTest()
    results = test_suite.run_all_tests()
    
    # Return appropriate exit code
    if results['summary']['failed'] == 0:
        return 0
    else:
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
