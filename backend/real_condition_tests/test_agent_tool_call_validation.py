#!/usr/bin/env python3
"""
Agent <PERSON>l Call Validation Test

This test validates that agents are properly calling tools and that tool calls
are being tracked and displayed in the benchmarking system.

Focus: ResourceAgent tool call behavior and benchmarking system integration
"""

import os
import sys
import django
import asyncio
import json
from datetime import datetime

# Setup Django
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.agents.resource_agent import ResourceAgent
from apps.main.agents.benchmarking import AgentBenchmarkImproved
from apps.main.services.agent_communication_tracker import Agent<PERSON>ommunicationTracker
from apps.user.models import UserProfile
from apps.main.agents.tools.tools_util import execute_tool

class AgentToolCallValidator:
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {},
            'recommendations': []
        }
        
    def log(self, message, level='INFO'):
        """Log a message with timestamp"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    async def run_validation(self):
        """Run comprehensive tool call validation"""
        self.log("🔍 Starting agent tool call validation...")
        
        try:
            # Test 1: Direct tool execution
            await self.test_direct_tool_execution()
            
            # Test 2: ResourceAgent tool calls
            await self.test_resource_agent_tool_calls()
            
            # Test 3: Benchmark tool call tracking
            await self.test_benchmark_tool_tracking()
            
            # Test 4: PhiPhi inventory validation
            await self.test_phiphi_inventory()
            
            # Generate summary
            self.generate_summary()
            
        except Exception as e:
            self.log(f"Critical error in validation: {e}", 'ERROR')
            self.results['critical_error'] = str(e)
            return False
            
        return True
        
    async def test_direct_tool_execution(self):
        """Test direct tool execution"""
        self.log("Testing direct tool execution...")
        
        test_result = {
            'success': False,
            'details': {},
            'issues': []
        }
        
        try:
            # Test get_available_resources tool directly
            tool_input = {
                'input_data': {
                    'user_profile_id': '2',  # PhiPhi's ID
                    'context': {'test': 'direct_execution'}
                }
            }
            
            result = await execute_tool('get_available_resources', tool_input)
            
            test_result['details']['tool_result'] = result
            test_result['details']['has_inventory'] = bool(result.get('inventory', []))
            test_result['details']['inventory_count'] = len(result.get('inventory', []))
            test_result['details']['has_capabilities'] = bool(result.get('capabilities', {}))
            
            if result.get('inventory'):
                test_result['success'] = True
                self.log(f"✅ Direct tool execution successful - found {len(result['inventory'])} inventory items")
            else:
                test_result['issues'].append('Tool returned empty inventory')
                self.log("❌ Direct tool execution returned empty inventory")
                
        except Exception as e:
            test_result['issues'].append(f'Tool execution failed: {str(e)}')
            self.log(f"❌ Direct tool execution failed: {e}")
            
        self.results['tests']['direct_tool_execution'] = test_result
        
    async def test_resource_agent_tool_calls(self):
        """Test ResourceAgent tool call behavior"""
        self.log("Testing ResourceAgent tool calls...")

        test_result = {
            'success': False,
            'details': {},
            'issues': []
        }

        try:
            # Create ResourceAgent instance (no run_id parameter)
            agent = ResourceAgent(
                user_profile_id='2'  # PhiPhi
            )

            # Set run_id manually with a valid UUID
            import uuid
            agent.run_id = str(uuid.uuid4())

            # Ensure agent is loaded
            await agent._ensure_loaded()

            test_result['details']['agent_loaded'] = agent.agent_definition is not None
            test_result['details']['tools_count'] = len(agent.available_tools)

            # Test resource analysis (which should call tools)
            context_packet = {
                'user_input': 'Hello, I need help',
                'test_mode': True
            }

            resource_context = await agent._analyze_resources(context_packet)

            test_result['details']['resource_context'] = resource_context
            test_result['details']['has_inventory'] = bool(resource_context.get('available_inventory', []))
            test_result['details']['inventory_count'] = len(resource_context.get('available_inventory', []))

            if resource_context.get('available_inventory'):
                test_result['success'] = True
                self.log(f"✅ ResourceAgent tool calls successful - found {len(resource_context['available_inventory'])} items")
            else:
                test_result['issues'].append('ResourceAgent returned empty inventory')
                self.log("❌ ResourceAgent returned empty inventory")

        except Exception as e:
            test_result['issues'].append(f'ResourceAgent test failed: {str(e)}')
            self.log(f"❌ ResourceAgent test failed: {e}")

        self.results['tests']['resource_agent_tool_calls'] = test_result
        
    async def test_benchmark_tool_tracking(self):
        """Test benchmark tool call tracking"""
        self.log("Testing benchmark tool call tracking...")

        test_result = {
            'success': False,
            'details': {},
            'issues': []
        }

        try:
            # Create communication tracker with workflow_id
            tracker = AgentCommunicationTracker(workflow_id='test-workflow-validation')
            tracker.enabled = True

            # Create benchmark instance
            benchmark = AgentBenchmarkImproved(
                agent_role='resource',
                user_profile_id='2',
                runs=1,
                use_real_tools=True,
                use_real_db=True,
                comm_tracker=tracker
            )

            # Run benchmark
            results = await benchmark.run_benchmark()

            test_result['details']['benchmark_success'] = results.get('success', False)
            test_result['details']['tool_calls_total'] = results.get('tool_call_details', {}).get('total_calls', 0)
            test_result['details']['tool_calls_real'] = results.get('tool_call_details', {}).get('real_calls', 0)
            test_result['details']['tool_calls_mocked'] = results.get('tool_call_details', {}).get('mocked_calls', 0)

            # Check enhanced debugging data
            enhanced_data = results.get('enhanced_debugging_data', {})
            test_result['details']['has_enhanced_data'] = bool(enhanced_data)
            test_result['details']['tool_calls_in_enhanced'] = len(enhanced_data.get('tool_calls', []))

            if results.get('tool_call_details', {}).get('total_calls', 0) > 0:
                test_result['success'] = True
                self.log(f"✅ Benchmark tool tracking successful - {results['tool_call_details']['total_calls']} tool calls")
            else:
                test_result['issues'].append('No tool calls detected in benchmark')
                self.log("❌ No tool calls detected in benchmark")

        except Exception as e:
            test_result['issues'].append(f'Benchmark tracking test failed: {str(e)}')
            self.log(f"❌ Benchmark tracking test failed: {e}")

        self.results['tests']['benchmark_tool_tracking'] = test_result
        
    async def test_phiphi_inventory(self):
        """Test PhiPhi's inventory data availability"""
        self.log("Testing PhiPhi's inventory data...")

        test_result = {
            'success': False,
            'details': {},
            'issues': []
        }

        try:
            from asgiref.sync import sync_to_async

            # Get PhiPhi's profile using sync_to_async
            phiphi = await sync_to_async(UserProfile.objects.get)(id=2)
            test_result['details']['profile_name'] = phiphi.profile_name
            test_result['details']['is_real'] = phiphi.is_real

            # Check environments
            environments = await sync_to_async(list)(phiphi.user_environments.all())
            test_result['details']['environments_count'] = len(environments)

            # Check resources
            total_resources = 0
            for env in environments:
                resources = await sync_to_async(list)(env.user_resources.all())
                total_resources += len(resources)

            test_result['details']['total_resources'] = total_resources

            if total_resources > 0:
                test_result['success'] = True
                self.log(f"✅ PhiPhi has {total_resources} resources across {len(environments)} environments")
            else:
                test_result['issues'].append('PhiPhi has no resources in database')
                self.log("❌ PhiPhi has no resources in database")

        except Exception as e:
            test_result['issues'].append(f'PhiPhi inventory test failed: {str(e)}')
            self.log(f"❌ PhiPhi inventory test failed: {e}")

        self.results['tests']['phiphi_inventory'] = test_result
        
    def generate_summary(self):
        """Generate test summary and recommendations"""
        total_tests = len(self.results['tests'])
        successful_tests = sum(1 for test in self.results['tests'].values() if test['success'])
        
        self.results['summary'] = {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': successful_tests / total_tests if total_tests > 0 else 0,
            'overall_status': 'PASS' if successful_tests == total_tests else 'FAIL'
        }
        
        # Generate recommendations
        if not self.results['tests'].get('direct_tool_execution', {}).get('success'):
            self.results['recommendations'].append('Fix direct tool execution - check tool registration and database connectivity')
            
        if not self.results['tests'].get('resource_agent_tool_calls', {}).get('success'):
            self.results['recommendations'].append('Fix ResourceAgent tool call integration - check _call_tool method')
            
        if not self.results['tests'].get('benchmark_tool_tracking', {}).get('success'):
            self.results['recommendations'].append('Fix benchmark tool call tracking - check AgentCommunicationTracker integration')
            
        if not self.results['tests'].get('phiphi_inventory', {}).get('success'):
            self.results['recommendations'].append('Fix PhiPhi inventory data - run seed_db_phiphi command')
            
        self.log(f"📊 Summary: {successful_tests}/{total_tests} tests passed")
        
    def save_results(self):
        """Save results to file"""
        results_file = '/usr/src/app/real_condition_tests/results/agent_tool_call_validation.json'
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
            
        self.log(f"📄 Results saved to {results_file}")

async def main():
    """Main test execution"""
    validator = AgentToolCallValidator()
    
    try:
        success = await validator.run_validation()
        validator.save_results()
        
        if success:
            print("\n✅ Agent tool call validation completed successfully")
        else:
            print("\n❌ Agent tool call validation failed")
            
        return success
        
    except Exception as e:
        print(f"\n💥 Critical error: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(main())
