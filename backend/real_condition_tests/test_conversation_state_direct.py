#!/usr/bin/env python3
"""
Direct test of ConversationDispatcher conversation state handling.

This test directly calls the ConversationDispatcher to verify:
1. First message sets conversation state to awaiting_profile_info
2. Second message with conversation state metadata triggers onboarding workflow
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.user.models import UserProfile
from apps.main.services.conversation_dispatcher import ConversationDispatcher
from django.contrib.auth import get_user_model
from asgiref.sync import sync_to_async

User = get_user_model()

class ConversationStateDirectTester:
    def __init__(self):
        self.user_id = None
        self.session_id = f"test_direct_{int(asyncio.get_event_loop().time())}"
        
    async def setup_test_user(self):
        """Create a test user for the conversation."""
        try:
            # Create Django user first (sync)
            django_user = await sync_to_async(User.objects.create_user)(
                username=f"test_user_{self.session_id}",
                email=f"test_{self.session_id}@example.com"
            )
            
            # Create UserProfile (sync)
            user_profile = await sync_to_async(UserProfile.objects.create)(
                user=django_user,
                profile_name=f"Test User {self.session_id}",
                is_real=False  # Mark as test profile
            )
            self.user_id = str(user_profile.id)
            print(f"✅ Created test user: ID={self.user_id}")
            return True
        except Exception as e:
            print(f"❌ Failed to create test user: {e}")
            return False
    
    async def test_conversation_flow(self):
        """Test the complete conversation state flow."""
        print("🚀 Starting Direct ConversationDispatcher Test")
        print("=" * 60)
        
        # Step 1: Setup
        if not await self.setup_test_user():
            return False
        
        # Step 2: Test first message (should set conversation state)
        print("\n1️⃣ Testing first message: 'make me a wheel'")
        
        dispatcher1 = ConversationDispatcher(
            user_profile_id=self.user_id,
            user_ws_session_name=f"client_session_{self.session_id}"
        )
        
        result1 = await dispatcher1.process_message({
            'text': 'make me a wheel',
            'timestamp': '2025-06-18T20:00:00.000Z',
            'metadata': {}
        })
        
        print(f"   📊 Result keys: {list(result1.keys())}")
        print(f"   🔄 Workflow type: {result1.get('workflow_type')}")
        print(f"   💬 Has direct response: {'✅' if result1.get('direct_response') else '❌'}")
        print(f"   🔄 Has conversation state update: {'✅' if result1.get('conversation_state_update') else '❌'}")
        
        if result1.get('conversation_state_update'):
            conversation_state = result1['conversation_state_update']
            print(f"   📋 Conversation state: {conversation_state}")
        else:
            print("   ❌ No conversation state update found!")
            return False
        
        # Step 3: Test second message with conversation state metadata
        print("\n2️⃣ Testing second message with conversation state metadata")
        
        dispatcher2 = ConversationDispatcher(
            user_profile_id=self.user_id,
            user_ws_session_name=f"client_session_{self.session_id}"
        )
        
        # Include conversation state in metadata (simulating frontend behavior)
        metadata = {
            'conversation_phase': conversation_state.get('phase', 'initial'),
            'awaiting_response_type': conversation_state.get('awaiting_response_type'),
            'session_context': {}
        }
        
        print(f"   📋 Sending metadata: {metadata}")
        
        result2 = await dispatcher2.process_message({
            'text': "I'm a 25-year-old software developer from Berlin. I love hiking, reading sci-fi books, and learning new programming languages. My main goal is to improve my work-life balance.",
            'timestamp': '2025-06-18T20:01:00.000Z',
            'metadata': metadata
        })
        
        print(f"   📊 Result keys: {list(result2.keys())}")
        print(f"   🔄 Workflow type: {result2.get('workflow_type')}")
        print(f"   ⚙️ Should launch workflow: {'✅' if result2.get('workflow_type') != 'direct_response_only' else '❌'}")
        
        # Step 4: Analysis
        print("\n3️⃣ Analysis and Diagnosis")
        print("=" * 40)
        
        if result1.get('workflow_type') != 'direct_response_only':
            print("❌ ISSUE: First message should return 'direct_response_only'")
            return False
        elif not result1.get('conversation_state_update'):
            print("❌ ISSUE: First message should include conversation_state_update")
            return False
        elif result2.get('workflow_type') == 'direct_response_only':
            print("❌ ISSUE: Second message should launch a workflow, not return 'direct_response_only'")
            print("   This suggests the conversation state metadata is not being processed correctly")
            return False
        elif result2.get('workflow_type') != 'onboarding':
            print(f"❌ ISSUE: Second message should launch 'onboarding' workflow, got '{result2.get('workflow_type')}'")
            return False
        else:
            print("✅ SUCCESS: Conversation state flow is working correctly!")
            print(f"   - First message: {result1.get('workflow_type')} with state update")
            print(f"   - Second message: {result2.get('workflow_type')} workflow launched")
            return True

async def main():
    """Main test function."""
    tester = ConversationStateDirectTester()
    try:
        success = await tester.test_conversation_flow()
        if success:
            print("\n🎉 Test completed successfully!")
        else:
            print("\n❌ Test failed - conversation state flow has issues")
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
