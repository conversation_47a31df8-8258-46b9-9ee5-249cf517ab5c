#!/usr/bin/env python3
"""
Profile Completion Debug Test

This test reproduces the issue where the profile completion graph
inappropriately calls create_user_belief and create_user_goal tools
in a loop instead of asking questions to gather missing information.

The test simulates a new user with low profile completion requesting
a wheel and validates that the system should ask questions, not
extract data in loops.
"""

import asyncio
import sys
import os
import logging
from typing import Dict, Any

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.graphs.profile_completion_graph import run_profile_completion_workflow
from apps.user.models import UserProfile
from apps.main.models import AgentRun
from django.contrib.auth.models import User

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProfileCompletionDebugTest:
    """Test class to debug profile completion flow issues."""
    
    def __init__(self):
        self.test_user_id = None
        self.test_user_profile_id = None
        
    async def setup_test_user(self) -> str:
        """Create a test user with minimal profile completion."""
        try:
            # Create test user
            from asgiref.sync import sync_to_async
            
            @sync_to_async
            def create_test_user():
                # Clean up any existing test user
                User.objects.filter(username='profile_debug_test_user').delete()
                
                user = User.objects.create_user(
                    username='profile_debug_test_user',
                    email='<EMAIL>'
                )
                
                # Create minimal user profile (should be < 25% complete)
                user_profile = UserProfile.objects.create(
                    user=user,
                    # Minimal data - should trigger profile completion
                )
                
                return str(user_profile.id)
            
            self.test_user_profile_id = await create_test_user()
            logger.info(f"✅ Created test user with profile ID: {self.test_user_profile_id}")
            return self.test_user_profile_id
            
        except Exception as e:
            logger.error(f"❌ Failed to create test user: {e}")
            raise
    
    async def test_wheel_request_from_new_user(self):
        """Test the problematic scenario: new user requests wheel."""
        try:
            logger.info("🧪 Testing wheel request from new user...")
            
            # Create conversation dispatcher
            dispatcher = ConversationDispatcher(
                user_profile_id=self.test_user_profile_id,
                user_ws_session_name="test_session_profile_debug"
            )
            
            # Simulate user message requesting wheel
            user_message = {
                "text": "make me a wheel",
                "type": "user_message",
                "metadata": {}
            }
            
            logger.info("📤 Sending wheel request message...")
            
            # Process the message - this should trigger profile completion
            result = await dispatcher.process_message(user_message)
            
            logger.info(f"📥 Received result: {result}")
            
            # Check if it correctly routes to onboarding/profile completion
            workflow_type = result.get('workflow_type')
            if workflow_type == 'onboarding':
                logger.info("✅ Correctly routed to onboarding workflow")
            else:
                logger.warning(f"⚠️ Unexpected workflow type: {workflow_type}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            raise
    
    async def test_profile_completion_workflow_directly(self):
        """Test the profile completion workflow directly to see the tool call issue."""
        try:
            logger.info("🧪 Testing profile completion workflow directly...")
            
            # Create context packet simulating a wheel request
            context_packet = {
                'user_id': self.test_user_profile_id,
                'user_profile_id': self.test_user_profile_id,
                'session_timestamp': '2025-06-19T08:57:45.969Z',
                'reported_mood': 'neutral',
                'reported_environment': 'home',
                'reported_time_availability': 'medium',
                'reported_focus': 'medium',
                'reported_satisfaction': 'medium',
                'extraction_confidence': 0.8,
                'entities': [],
                'user_ws_session_name': 'test_session_profile_debug',
                'mentor_context': {},
                'workflow_metadata': {
                    'original_message': 'make me a wheel',
                    'intent': 'wheel_request'
                },
                'system_metadata': {}
            }
            
            logger.info("🚀 Starting profile completion workflow...")
            
            # This should reproduce the issue with inappropriate tool calls
            result = await run_profile_completion_workflow(
                user_profile_id=self.test_user_profile_id,
                context_packet=context_packet
            )
            
            logger.info(f"📥 Profile completion result: {result}")
            
            # Check the output for inappropriate tool usage
            output_data = result.get('output_data', {})
            user_response = output_data.get('user_response', '')
            
            if 'create_user_belief' in str(result) or 'create_user_goal' in str(result):
                logger.warning("⚠️ ISSUE DETECTED: Inappropriate tool calls found in result")
            
            if user_response and len(user_response) > 0:
                logger.info(f"✅ Generated user response: {user_response[:200]}...")
            else:
                logger.warning("⚠️ No user response generated")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Profile completion workflow test failed: {e}")
            raise
    
    async def analyze_agent_runs(self):
        """Analyze recent agent runs to see what tools were called."""
        try:
            from asgiref.sync import sync_to_async

            @sync_to_async
            def get_recent_runs():
                return list(AgentRun.objects.filter(
                    user_profile_id=self.test_user_profile_id
                ).order_by('-created_at')[:5])

            runs = await get_recent_runs()

            logger.info(f"📊 Found {len(runs)} recent agent runs")

            tool_calls = []
            for run in runs:
                if hasattr(run, 'output_data') and run.output_data:
                    output_data = run.output_data
                    if 'tool_calls' in str(output_data) or 'create_user_' in str(output_data):
                        tool_calls.append({
                            'run_id': str(run.id),
                            'agent_role': run.agent_definition.role if run.agent_definition else 'unknown',
                            'output_data': output_data
                        })

            if tool_calls:
                logger.info(f"🔧 Tool calls detected in agent runs: {len(tool_calls)}")
                for i, tool_call in enumerate(tool_calls):
                    logger.info(f"  {i+1}. Agent: {tool_call['agent_role']}, Run: {tool_call['run_id']}")
            else:
                logger.info("ℹ️ No tool calls found in recent agent runs")

            return tool_calls

        except Exception as e:
            logger.error(f"❌ Failed to analyze agent runs: {e}")
            return []
    
    async def run_debug_test(self):
        """Run the complete debug test."""
        try:
            logger.info("🚀 Starting Profile Completion Debug Test")
            logger.info("=" * 60)
            
            # Setup
            await self.setup_test_user()
            
            # Test 1: Conversation dispatcher routing
            logger.info("\n📋 Test 1: Conversation Dispatcher Routing")
            dispatcher_result = await self.test_wheel_request_from_new_user()
            
            # Test 2: Profile completion workflow directly
            logger.info("\n📋 Test 2: Profile Completion Workflow Direct")
            workflow_result = await self.test_profile_completion_workflow_directly()
            
            # Test 3: Analyze agent runs
            logger.info("\n📋 Test 3: Agent Runs Analysis")
            tool_calls = await self.analyze_agent_runs()
            
            # Summary
            logger.info("\n" + "=" * 60)
            logger.info("📊 DEBUG TEST SUMMARY")
            logger.info("=" * 60)
            
            logger.info(f"✅ Dispatcher routing: {dispatcher_result.get('workflow_type', 'unknown')}")
            logger.info(f"✅ Workflow completion: {workflow_result.get('completed', False)}")
            logger.info(f"✅ Tool calls detected: {len(tool_calls)}")
            
            # Check for the specific issue
            if any('create_user_belief' in str(call) or 'create_user_goal' in str(call) for call in tool_calls):
                logger.warning("🚨 ISSUE CONFIRMED: Inappropriate data extraction tool calls detected!")
                logger.warning("   The system should ask questions, not extract data in loops.")
            else:
                logger.info("✅ No inappropriate tool calls detected")
            
            return {
                'dispatcher_result': dispatcher_result,
                'workflow_result': workflow_result,
                'tool_calls': tool_calls,
                'issue_detected': any('create_user_belief' in str(call) or 'create_user_goal' in str(call) for call in tool_calls)
            }
            
        except Exception as e:
            logger.error(f"❌ Debug test failed: {e}")
            raise

async def main():
    """Main test execution."""
    test = ProfileCompletionDebugTest()
    result = await test.run_debug_test()
    
    if result['issue_detected']:
        logger.error("🚨 ISSUE DETECTED: Profile completion needs fixing!")
        sys.exit(1)
    else:
        logger.info("✅ Profile completion working correctly!")
        sys.exit(0)

if __name__ == "__main__":
    asyncio.run(main())
