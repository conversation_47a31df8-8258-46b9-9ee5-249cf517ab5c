#!/usr/bin/env python3
"""
Test ResourceAgent Modal Display - Comprehensive validation of ResourceAgent evaluation modal

This test validates that the ResourceAgent evaluation modal properly displays:
- Rich inventory data with categorization
- Capabilities matrix with skill levels
- Limitations analysis with severity indicators
- Environment context and activity support
- Tool usage analysis (internal processing)
- LLM interactions from enhanced debugging data

The test creates a ResourceAgent benchmark run and verifies the modal displays all data correctly.
"""

import os
import sys
import json
import asyncio
import django
from datetime import datetime

# Setup Django
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.models import BenchmarkRun, GenericAgent
from apps.user.models import UserProfile


def test_resource_agent_modal_display():
    """Test ResourceAgent modal display with comprehensive data validation"""
    
    print("🧪 Testing ResourceAgent Modal Display")
    print("=" * 60)
    
    try:
        # Step 1: Find or create a ResourceAgent benchmark run
        print("📊 Step 1: Finding ResourceAgent benchmark run...")
        
        # Look for recent ResourceAgent runs
        resource_runs = BenchmarkRun.objects.filter(
            raw_results__agent_role='ResourceAgent'
        ).order_by('-execution_date')[:5]
        
        if resource_runs.exists():
            run = resource_runs.first()
            print(f"✅ Found existing ResourceAgent run: {run.id}")
        else:
            print("❌ No ResourceAgent runs found. Please run a ResourceAgent benchmark first.")
            print("💡 Run: docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_real_agent_benchmark.py")
            return False
        
        # Step 2: Validate data structure
        print(f"\n📋 Step 2: Validating data structure for run {run.id}...")
        
        raw_results = run.raw_results
        if not raw_results:
            print("❌ No raw_results found")
            return False
        
        print(f"✅ Raw results keys: {list(raw_results.keys())}")
        
        # Check agent role
        agent_role = raw_results.get('agent_role')
        print(f"✅ Agent role: {agent_role}")
        
        # Check enhanced debugging data
        enhanced_data = raw_results.get('enhanced_debugging_data', {})
        if enhanced_data:
            print(f"✅ Enhanced debugging data available")
            print(f"   - Agents: {len(enhanced_data.get('agents', []))}")
            print(f"   - LLM interactions: {len(enhanced_data.get('llm_interactions', []))}")
            print(f"   - Tool calls: {len(enhanced_data.get('tool_calls', []))}")
        else:
            print("⚠️ No enhanced debugging data")
        
        # Check resource context
        resource_context = None
        if 'last_output' in raw_results and 'resource_context' in raw_results['last_output']:
            resource_context = raw_results['last_output']['resource_context']
            print("✅ Resource context found in last_output")
        elif enhanced_data.get('agents') and enhanced_data['agents'][0].get('output', {}).get('resource_context'):
            resource_context = enhanced_data['agents'][0]['output']['resource_context']
            print("✅ Resource context found in enhanced_debugging_data")
        else:
            print("❌ No resource context found")
            return False
        
        # Step 3: Validate ResourceAgent data completeness
        print(f"\n🎒 Step 3: Validating ResourceAgent data completeness...")
        
        # Check feasibility score
        feasibility = resource_context.get('feasibility_score', 0)
        print(f"✅ Feasibility score: {feasibility:.1%}")
        
        # Check resources
        resources = resource_context.get('resources', {})
        inventory_count = resources.get('inventory_count', 0)
        print(f"✅ Inventory items: {inventory_count}")
        
        # Check capabilities
        capabilities = resources.get('capabilities', {})
        total_skills = resources.get('capabilities_summary', {}).get('total_skills', 0)
        print(f"✅ Total skills: {total_skills}")
        
        # Check limitations
        limitations = resources.get('reported_limitations', [])
        print(f"✅ Limitations: {len(limitations)}")
        
        # Check environment
        environment = resource_context.get('environment', {})
        env_type = environment.get('analyzed_type', 'Unknown')
        print(f"✅ Environment type: {env_type}")
        
        # Check analysis summary
        analysis = resource_context.get('analysis_summary', {})
        opportunities = analysis.get('key_opportunities', [])
        constraints = analysis.get('primary_constraints', [])
        print(f"✅ Opportunities: {len(opportunities)}, Constraints: {len(constraints)}")
        
        # Step 4: Test modal API endpoint
        print(f"\n🌐 Step 4: Testing modal API endpoint...")
        
        from django.test import Client
        from django.contrib.auth.models import User
        
        # Create test client
        client = Client()
        
        # Create admin user for testing
        admin_user, created = User.objects.get_or_create(
            username='test_admin',
            defaults={'is_staff': True, 'is_superuser': True}
        )
        
        # Login as admin
        client.force_login(admin_user)
        
        # Test API endpoint
        response = client.get(f'/admin/benchmarks/api/run/{run.id}/')
        
        if response.status_code == 200:
            api_data = response.json()
            print("✅ API endpoint accessible")
            print(f"   - Response keys: {list(api_data.keys())}")
            
            # Validate API data structure
            if 'raw_results' in api_data and 'agent_role' in api_data['raw_results']:
                print(f"   - Agent role in API: {api_data['raw_results']['agent_role']}")
            
            if 'enhanced_debugging_data' in api_data['raw_results']:
                enhanced_api = api_data['raw_results']['enhanced_debugging_data']
                print(f"   - Enhanced data in API: {len(enhanced_api.get('agents', []))} agents")
        else:
            print(f"❌ API endpoint failed: {response.status_code}")
            return False
        
        # Step 5: Generate modal test instructions
        print(f"\n🎯 Step 5: Modal Test Instructions")
        print("=" * 40)
        print(f"✅ ResourceAgent benchmark run ready: ID {run.id}")
        print(f"✅ Data validation complete - all required data present")
        print(f"✅ API endpoint working - data accessible via /admin/benchmarks/api/run/{run.id}/")
        print()
        print("🔧 Manual Testing Steps:")
        print(f"1. Open: http://localhost:8000/admin/benchmarks/history/")
        print(f"2. Find run ID {run.id} (ResourceAgent, recent timestamp)")
        print(f"3. Click 'View Detailed Results' button")
        print(f"4. Verify modal shows:")
        print(f"   - ✅ ResourceAgent Analysis Complete header")
        print(f"   - ✅ Feasibility: {feasibility:.1%}")
        print(f"   - ✅ Inventory: {inventory_count} items")
        print(f"   - ✅ Skills: {total_skills}")
        print(f"   - ✅ Limitations: {len(limitations)}")
        print(f"   - ✅ Rich inventory display with categories")
        print(f"   - ✅ Skills matrix with progress bars")
        print(f"   - ✅ Limitations with severity indicators")
        print(f"   - ✅ Environment analysis")
        print(f"   - ✅ Tool usage showing internal processing")
        print(f"   - ✅ LLM interactions (should show 1 call)")
        print()
        print("🚨 Issues to verify are FIXED:")
        print("   - ❌ 'No Agent Response Found' should NOT appear")
        print("   - ❌ 'Semantic Evaluation Was Skipped' should NOT appear")
        print("   - ❌ 'No tool usage available' should NOT appear")
        print("   - ❌ 'ANALYZING...' should NOT hang indefinitely")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test execution"""
    print("🚀 ResourceAgent Modal Display Test")
    print("=" * 60)
    
    # Run the test
    result = test_resource_agent_modal_display()
    
    if result:
        print("\n🎉 ResourceAgent Modal Display Test PASSED")
        print("✅ All data validation successful")
        print("✅ API endpoint working")
        print("✅ Ready for manual modal testing")
        return 0
    else:
        print("\n❌ ResourceAgent Modal Display Test FAILED")
        return 1


if __name__ == "__main__":
    exit(main())
