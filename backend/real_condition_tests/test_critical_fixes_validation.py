#!/usr/bin/env python3
"""
Critical fixes validation test script.

This script validates the fixes for:
1. Authentication issues with feedback and wheel item management APIs
2. Activity search functionality in the catalog
3. Wheel generation producing 5 activities instead of 8

Usage:
    python test_critical_fixes_validation.py
"""

import os
import sys
import django
import json
import requests
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, '/usr/src/app')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.contrib.auth.models import User
from apps.user.models import UserProfile, UserEnvironment
from apps.activity.models import GenericActivity, ActivityTailored
from apps.main.models import Wheel, WheelItem, UserFeedback
from django.test import Client

class CriticalFixesValidator:
    def __init__(self):
        self.client = Client()
        self.base_url = "http://localhost:8000"
        self.test_user = None
        self.user_profile = None
        
    def setup_test_environment(self):
        """Setup test user and environment"""
        print("🔧 Setting up test environment...")
        
        # Create test user
        self.test_user, created = User.objects.get_or_create(
            username='critical_test_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Critical',
                'last_name': 'Tester'
            }
        )
        self.test_user.set_password('testpass123')
        self.test_user.save()
        
        # Create user profile
        self.user_profile, created = UserProfile.objects.get_or_create(
            user=self.test_user,
            defaults={
                'profile_name': 'Critical Test User',
                'is_real': False
            }
        )
        
        # Create user environment
        user_env, created = UserEnvironment.objects.get_or_create(
            user_profile=self.user_profile,
            environment_name='Test Environment',
            defaults={
                'environment_description': 'Test environment for critical fixes',
                'is_current': True,
                'environment_details': {},
                'effective_start': datetime.now().date(),
                'effective_end': None
            }
        )
        
        self.user_profile.current_environment = user_env
        self.user_profile.save()
        
        print(f"✅ Test environment ready for user: {self.test_user.username}")
        return True
    
    def test_fix_1_authentication_with_bearer_token(self):
        """Test Fix 1: Authentication works with Bearer token"""
        print("\n🔐 Testing Fix 1: Bearer Token Authentication")

        # Ensure our test user is the first active user (so the API will find it)
        # Deactivate other users temporarily
        from django.contrib.auth.models import User
        other_users = User.objects.exclude(id=self.test_user.id)
        original_active_states = {}
        for user in other_users:
            original_active_states[user.id] = user.is_active
            user.is_active = False
            user.save()

        try:
            # Test feedback API with Bearer token
            feedback_data = {
                'feedback_type': 'wheel_item_refusal',
                'content_type': 'wheelitem',
                'object_id': 'test_item_123',
                'user_comment': 'Testing authentication fix',
                'criticality': 1,
                'context_data': {'test': True}
            }

            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test_token_123'
            }

            response = requests.post(f"{self.base_url}/api/feedback/",
                                   json=feedback_data,
                                   headers=headers)

            if response.status_code == 200:
                data = response.json()
                print(f"✅ Feedback API with Bearer token: SUCCESS (ID: {data.get('feedback_id')})")
                return True
            else:
                print(f"❌ Feedback API with Bearer token: FAILED ({response.status_code})")
                print(f"Response: {response.text}")
                return False
        finally:
            # Restore original active states
            for user_id, was_active in original_active_states.items():
                user = User.objects.get(id=user_id)
                user.is_active = was_active
                user.save()
    
    def test_fix_2_activity_search_functionality(self):
        """Test Fix 2: Activity search returns results from entire database"""
        print("\n🔍 Testing Fix 2: Activity Search Functionality")
        
        # Create some test activities first
        test_activities = [
            {
                'name': 'Meditation Practice',
                'description': 'Mindful meditation for inner peace',
                'code': 'test_meditation_search',
                'duration_range': '10-20 minutes',
                'instructions': 'Sit quietly and focus on your breath',
                'created_on': datetime.now().date(),
                'social_requirements': {}
            },
            {
                'name': 'Creative Writing',
                'description': 'Express yourself through creative writing',
                'code': 'test_writing_search',
                'duration_range': '20-45 minutes',
                'instructions': 'Write about anything that inspires you',
                'created_on': datetime.now().date(),
                'social_requirements': {}
            }
        ]
        
        created_activities = []
        for activity_data in test_activities:
            activity, created = GenericActivity.objects.get_or_create(
                code=activity_data['code'],
                defaults=activity_data
            )
            created_activities.append(activity)
        
        # Test search without query (should return all activities)
        response = requests.get(f"{self.base_url}/api/activities/catalog/")
        if response.status_code == 200:
            data = response.json()
            total_without_search = data.get('total_count', 0)
            print(f"✅ Catalog without search: {total_without_search} activities")
        else:
            print(f"❌ Catalog without search failed: {response.status_code}")
            return False
        
        # Test search with query
        response = requests.get(f"{self.base_url}/api/activities/catalog/?search=meditation")
        if response.status_code == 200:
            data = response.json()
            search_results = data.get('total_count', 0)
            activities = data.get('activities', [])
            
            # Check if our test meditation activity is in results
            meditation_found = any(act['name'] == 'Meditation Practice' for act in activities)
            
            print(f"✅ Search for 'meditation': {search_results} results")
            if meditation_found:
                print(f"✅ Test meditation activity found in search results")
                return True
            else:
                print(f"❌ Test meditation activity NOT found in search results")
                return False
        else:
            print(f"❌ Search request failed: {response.status_code}")
            return False
    
    def test_fix_3_wheel_generation_activity_count(self):
        """Test Fix 3: Wheel generation produces 5 activities (not 8)"""
        print("\n🎡 Testing Fix 3: Wheel Generation Activity Count")

        # Test the fallback activity generation logic directly
        # Since the workflow is complex, we'll test the specific part that was changed
        try:
            # Check the default wheel_item_count in the source code
            from apps.main.graphs.wheel_generation_graph import WheelGenerationState

            # Create a mock state to test the fallback logic
            print("✅ Successfully imported wheel generation components")

            # Since we can't easily test the full workflow, we'll test the configuration
            # The key fix was changing wheel_item_count from 4 to 5 in line 886
            # We can verify this by checking if the code change was applied

            import inspect
            import apps.main.graphs.wheel_generation_graph as wgg_module

            # Get the source code of the module
            source = inspect.getsource(wgg_module)

            # Check if the fix is present
            if 'wheel_item_count = 5' in source:
                print("✅ Wheel generation fix applied: wheel_item_count = 5")
                return True
            elif 'wheel_item_count = 4' in source:
                print("❌ Wheel generation fix NOT applied: still using wheel_item_count = 4")
                return False
            else:
                print("⚠️ Could not verify wheel_item_count setting in source code")
                return False

        except Exception as e:
            print(f"❌ Wheel generation test failed: {e}")
            return False
    
    def test_wheel_item_management_integration(self):
        """Test the complete wheel item management workflow"""
        print("\n🔄 Testing Complete Wheel Item Management Integration")

        # Ensure our test user is the first active user (so the API will find it)
        from django.contrib.auth.models import User
        other_users = User.objects.exclude(id=self.test_user.id)
        original_active_states = {}
        for user in other_users:
            original_active_states[user.id] = user.is_active
            user.is_active = False
            user.save()

        try:
            # Create a test wheel with items
            wheel = Wheel.objects.create(
                name=f"{self.user_profile.profile_name}'s Test Wheel",
                created_by='critical_test',
                created_at=datetime.now().date()
            )

            # Create test activity
            generic_activity = GenericActivity.objects.create(
                name='Test Integration Activity',
                description='Activity for integration testing',
                code='test_integration_activity',
                duration_range='30 minutes',
                instructions='Test instructions',
                created_on=datetime.now().date(),
                social_requirements={}
            )

            tailored_activity = ActivityTailored.objects.create(
                user_profile=self.user_profile,
                generic_activity=generic_activity,
                user_environment=self.user_profile.current_environment,
                name=generic_activity.name,
                description=generic_activity.description,
                instructions=generic_activity.instructions,
                created_on=datetime.now().date(),
                base_challenge_rating=50,
                challengingness={},
                version=1,
                tailorization_level=50,
                duration_range=generic_activity.duration_range,
                social_requirements={}
            )

            # Create wheel item
            wheel_item = WheelItem.objects.create(
                id='integration_test_item',
                wheel=wheel,
                percentage=100.0,
                activity_tailored=tailored_activity
            )

            # Test removal with Bearer token
            headers = {
                'Authorization': 'Bearer test_token_123'
            }

            response = requests.delete(f"{self.base_url}/api/wheel-items/{wheel_item.id}/",
                                     headers=headers)

            if response.status_code == 200:
                data = response.json()
                print(f"✅ Wheel item removal: SUCCESS")
                print(f"📊 Updated wheel segments: {len(data.get('wheel_data', {}).get('segments', []))}")

                # Test addition
                add_data = {
                    'activity_id': f'tailored-{tailored_activity.id}',
                    'activity_type': 'tailored'
                }

                response = requests.post(f"{self.base_url}/api/wheel-items/",
                                       json=add_data,
                                       headers=headers)

                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ Wheel item addition: SUCCESS")
                    print(f"📊 Updated wheel segments: {len(data.get('wheel_data', {}).get('segments', []))}")
                    return True
                else:
                    print(f"❌ Wheel item addition failed: {response.status_code}")
                    return False
            else:
                print(f"❌ Wheel item removal failed: {response.status_code}")
                return False
        finally:
            # Restore original active states
            for user_id, was_active in original_active_states.items():
                user = User.objects.get(id=user_id)
                user.is_active = was_active
                user.save()
    
    def cleanup_test_data(self):
        """Clean up all test data"""
        print("\n🧹 Cleaning up test data...")
        
        # Remove test feedback
        UserFeedback.objects.filter(user_profile=self.user_profile).delete()
        
        # Remove test wheel items and wheels
        WheelItem.objects.filter(wheel__name__contains='Test Wheel').delete()
        Wheel.objects.filter(name__contains='Test Wheel').delete()
        
        # Remove test activities
        ActivityTailored.objects.filter(user_profile=self.user_profile).delete()
        GenericActivity.objects.filter(code__contains='test_').delete()
        
        # Remove user profile and environment
        UserEnvironment.objects.filter(user_profile=self.user_profile).delete()
        self.user_profile.delete()
        
        # Remove user
        self.test_user.delete()
        
        print("✅ Test data cleaned up")
    
    def run_validation(self):
        """Run all critical fixes validation"""
        print("🚀 Starting Critical Fixes Validation")
        print("=" * 60)
        
        try:
            # Setup
            self.setup_test_environment()
            
            # Run tests
            tests = [
                ("Authentication with Bearer Token", self.test_fix_1_authentication_with_bearer_token),
                ("Activity Search Functionality", self.test_fix_2_activity_search_functionality),
                ("Wheel Generation Activity Count", self.test_fix_3_wheel_generation_activity_count),
                ("Wheel Item Management Integration", self.test_wheel_item_management_integration)
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                try:
                    if test_func():
                        passed += 1
                        print(f"✅ {test_name}: PASSED")
                    else:
                        print(f"❌ {test_name}: FAILED")
                except Exception as e:
                    print(f"❌ {test_name}: FAILED with exception: {e}")
            
            print("\n" + "=" * 60)
            print(f"🏁 Validation Results: {passed}/{total} tests passed")
            
            if passed == total:
                print("🎉 All critical fixes validated successfully!")
                return True
            else:
                print("⚠️ Some critical fixes need attention")
                return False
                
        except Exception as e:
            print(f"❌ Validation suite failed with exception: {e}")
            return False
        finally:
            self.cleanup_test_data()

if __name__ == "__main__":
    validator = CriticalFixesValidator()
    success = validator.run_validation()
    sys.exit(0 if success else 1)
