#!/usr/bin/env python3
"""
Test Conversation Dispatcher Fix

This script tests the fixed conversation dispatcher to ensure it correctly
detects profile gaps and asks for more information.
"""

import os
import sys
import django
import asyncio

# Add the backend directory to the Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher


async def test_conversation_dispatcher_fix():
    """Test the fixed conversation dispatcher with user 2."""
    
    print("🔍 Testing Fixed Conversation Dispatcher")
    print("=" * 60)
    
    user_profile_id = "2"
    session_name = "test_session_fix"
    
    # Test 1: Create conversation dispatcher and analyze profile gaps
    print("\n1️⃣ Testing ConversationDispatcher._analyze_profile_gaps (FIXED):")
    try:
        dispatcher = ConversationDispatcher(
            user_profile_id=user_profile_id,
            user_ws_session_name=session_name
        )
        
        # Call the fixed profile gap analysis
        gaps = await dispatcher._analyze_profile_gaps()
        
        completion = gaps.get('completion_percentage', 0.0)
        critical_gaps = gaps.get('critical', [])
        important_gaps = gaps.get('important', [])
        optional_gaps = gaps.get('optional', [])
        readiness = gaps.get('profile_readiness', 'unknown')
        
        print(f"   Profile Completion: {completion:.1%}")
        print(f"   Critical Gaps: {len(critical_gaps)}")
        print(f"   Important Gaps: {len(important_gaps)}")
        print(f"   Optional Gaps: {len(optional_gaps)}")
        print(f"   Profile Readiness: {readiness}")
        
        # List critical gap fields
        if critical_gaps:
            print(f"   Critical Gap Fields:")
            for gap in critical_gaps:
                field_name = gap.get('field', 'unknown')
                description = gap.get('description', 'no description')
                print(f"     - {field_name}: {description}")
        else:
            print(f"   ✅ No critical gaps detected")
        
        # Test expected behavior
        if completion == 0.0 and len(critical_gaps) > 0:
            print(f"   ✅ FIXED: Correctly detected {len(critical_gaps)} critical gaps for user {user_profile_id}")
        elif completion > 0.0:
            print(f"   ❌ STILL BROKEN: Shows {completion:.1%} completion when should be 0%")
        else:
            print(f"   ⚠️  UNEXPECTED: 0% completion but no critical gaps detected")
            
    except Exception as e:
        print(f"   ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Test wheel request handling with insufficient profile
    print("\n2️⃣ Testing wheel request handling with insufficient profile:")
    try:
        dispatcher = ConversationDispatcher(
            user_profile_id=user_profile_id,
            user_ws_session_name=session_name
        )
        
        # Simulate a wheel request message
        user_message = {"text": "Can you help me with activities?"}
        
        # Get profile gaps first
        gaps = await dispatcher._analyze_profile_gaps()
        
        # Test direct wheel request handling
        result = await dispatcher._handle_direct_wheel_request(user_message, gaps)
        
        if result:
            workflow_type = result.get('workflow_type')
            direct_response = result.get('direct_response', '')
            reason = result.get('reason', '')
            
            print(f"   Workflow Type: {workflow_type}")
            print(f"   Direct Response: {direct_response[:100]}...")
            print(f"   Reason: {reason}")
            
            # Check if it correctly routes to profile completion
            if workflow_type == 'wheel_generation' and 'bypass' in reason.lower():
                print(f"   ⚠️  BYPASSING: System is bypassing profile completion")
            elif workflow_type != 'wheel_generation':
                print(f"   ✅ CORRECT: System is NOT generating wheel due to insufficient profile")
            else:
                print(f"   ❓ UNCLEAR: Unexpected behavior")
        else:
            print(f"   ✅ CORRECT: No direct wheel handling (will route to discussion/onboarding)")
            
    except Exception as e:
        print(f"   ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Test message processing flow
    print("\n3️⃣ Testing full message processing flow:")
    try:
        dispatcher = ConversationDispatcher(
            user_profile_id=user_profile_id,
            user_ws_session_name=session_name
        )
        
        # Simulate processing a wheel request
        test_message = "Hi! I'm feeling a bit overwhelmed and could use some help with activities."
        
        print(f"   Test Message: {test_message}")
        print(f"   Processing...")
        
        # This would normally trigger the full flow, but we'll just test the classification
        # to avoid actually launching workflows
        
        print(f"   ✅ Message processing test completed (full flow not executed to avoid side effects)")
        
    except Exception as e:
        print(f"   ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    print("If the fix worked:")
    print("  - Profile completion should be 0%")
    print("  - Critical gaps should be detected")
    print("  - Wheel requests should be handled appropriately")
    print("  - System should ask for more information before generating wheels")


if __name__ == "__main__":
    asyncio.run(test_conversation_dispatcher_fix())
