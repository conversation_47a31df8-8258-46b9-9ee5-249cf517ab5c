#!/usr/bin/env python3
"""
Test ActivityTailored-UserEnvironment Relationship and Activity Reuse Logic

This test validates the new architecture where:
1. ActivityTailored objects are linked to specific UserEnvironments
2. Activities are properly reused when the same GenericActivity + UserEnvironment combination is requested
3. WheelItem can reference the same ActivityTailored multiple times (ForeignKey instead of OneToOneField)
4. Database constraints prevent violations during wheel generation

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_activity_environment_relationship.py
"""

import os
import sys
import django
import logging
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.db import transaction
from apps.user.models import UserProfile, UserEnvironment, GenericEnvironment
from apps.activity.models import GenericActivity, ActivityTailored
from apps.main.models import Wheel, WheelItem
from apps.main.agents.tools.tools import generate_wheel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ActivityEnvironmentRelationshipTest:
    """Test suite for ActivityTailored-UserEnvironment relationship and activity reuse logic."""
    
    def __init__(self):
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': []
        }
        
    def log_test_result(self, test_name: str, passed: bool, details: str = ""):
        """Log the result of a test."""
        self.test_results['total_tests'] += 1
        if passed:
            self.test_results['passed_tests'] += 1
            logger.info(f"✅ {test_name}: PASSED")
        else:
            self.test_results['failed_tests'] += 1
            logger.error(f"❌ {test_name}: FAILED - {details}")
        
        self.test_results['test_details'].append({
            'test_name': test_name,
            'passed': passed,
            'details': details
        })
    
    def setup_test_data(self):
        """Create test data for the tests."""
        logger.info("🔧 Setting up test data...")
        
        try:
            # Get or create test user (PhiPhi)
            self.user_profile = UserProfile.objects.filter(id=2).first()
            if not self.user_profile:
                # Create a test user if PhiPhi doesn't exist
                from django.contrib.auth.models import User
                user = User.objects.create_user(username='test_user', email='<EMAIL>')
                self.user_profile = UserProfile.objects.create(
                    user=user,
                    profile_name='Test User',
                    is_real=False
                )
            
            # Create test environments
            self.home_env = UserEnvironment.objects.create(
                user_profile=self.user_profile,
                environment_name="Home Office",
                environment_description="Quiet home office environment",
                effective_start=datetime.now().date(),
                is_current=True
            )

            self.work_env = UserEnvironment.objects.create(
                user_profile=self.user_profile,
                environment_name="Work Office",
                environment_description="Busy work office environment",
                effective_start=datetime.now().date(),
                is_current=False
            )
            
            # Create test generic activities
            self.generic_activity_1 = GenericActivity.objects.create(
                code="test_mindful_walk",
                name="Mindful Walking",
                description="A mindful walking exercise",
                duration_range="15-30 minutes",
                instructions="Walk mindfully for 15-30 minutes",
                social_requirements={"interaction_level": "solo"}
            )
            
            self.generic_activity_2 = GenericActivity.objects.create(
                code="test_breathing_exercise",
                name="Breathing Exercise",
                description="A calming breathing exercise",
                duration_range="5-10 minutes",
                instructions="Practice deep breathing for 5-10 minutes",
                social_requirements={"interaction_level": "solo"}
            )
            
            logger.info("✅ Test data setup complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup test data: {str(e)}")
            return False
    
    def test_activity_environment_relationship(self):
        """Test that ActivityTailored objects are properly linked to UserEnvironments."""
        test_name = "ActivityTailored-UserEnvironment Relationship"
        
        try:
            # Create tailored activity for home environment
            home_activity = ActivityTailored.objects.create(
                name="Home Mindful Walking",
                description="Mindful walking adapted for home environment",
                user_profile=self.user_profile,
                user_environment=self.home_env,
                generic_activity=self.generic_activity_1,
                base_challenge_rating=40,
                challengingness={"openness": 60, "conscientiousness": 50},
                version=1,
                tailorization_level=75
            )
            
            # Create tailored activity for work environment
            work_activity = ActivityTailored.objects.create(
                name="Work Mindful Walking",
                description="Mindful walking adapted for work environment",
                user_profile=self.user_profile,
                user_environment=self.work_env,
                generic_activity=self.generic_activity_1,
                base_challenge_rating=45,
                challengingness={"openness": 65, "conscientiousness": 55},
                version=1,
                tailorization_level=80
            )
            
            # Verify relationships
            assert home_activity.user_environment == self.home_env
            assert work_activity.user_environment == self.work_env
            assert home_activity.generic_activity == self.generic_activity_1
            assert work_activity.generic_activity == self.generic_activity_1
            
            # Verify unique constraint allows same user+activity in different environments
            home_activities = ActivityTailored.objects.filter(
                user_profile=self.user_profile,
                user_environment=self.home_env,
                generic_activity=self.generic_activity_1
            )
            work_activities = ActivityTailored.objects.filter(
                user_profile=self.user_profile,
                user_environment=self.work_env,
                generic_activity=self.generic_activity_1
            )
            
            assert home_activities.count() == 1
            assert work_activities.count() == 1
            assert home_activities.first().id != work_activities.first().id
            
            self.log_test_result(test_name, True)
            return True
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))
            return False
    
    def test_activity_reuse_logic(self):
        """Test that activities are properly reused when the same combination is requested."""
        test_name = "Activity Reuse Logic"
        
        try:
            # Clear any existing tailored activities for clean test
            ActivityTailored.objects.filter(
                user_profile=self.user_profile,
                user_environment=self.home_env,
                generic_activity=self.generic_activity_2
            ).delete()
            
            # First wheel generation should create new activities
            wheel_1 = Wheel.objects.create(
                name="Test Wheel 1",
                created_by="test_agent",
                created_at=datetime.now().date()
            )
            
            # Create first tailored activity
            activity_1 = ActivityTailored.objects.create(
                name="First Breathing Exercise",
                description="First version of breathing exercise",
                user_profile=self.user_profile,
                user_environment=self.home_env,
                generic_activity=self.generic_activity_2,
                base_challenge_rating=30,
                challengingness={"openness": 50, "conscientiousness": 60},
                version=1,
                tailorization_level=70
            )
            
            # Create wheel item
            wheel_item_1 = WheelItem.objects.create(
                id="test_item_1",
                wheel=wheel_1,
                percentage=50.0,
                activity_tailored=activity_1
            )
            
            # Second wheel generation should reuse the same activity (within 24 hours)
            wheel_2 = Wheel.objects.create(
                name="Test Wheel 2",
                created_by="test_agent",
                created_at=datetime.now().date()
            )
            
            # Create second wheel item referencing the same activity
            wheel_item_2 = WheelItem.objects.create(
                id="test_item_2",
                wheel=wheel_2,
                percentage=30.0,
                activity_tailored=activity_1  # Reusing the same activity
            )
            
            # Verify both wheel items reference the same activity
            assert wheel_item_1.activity_tailored.id == wheel_item_2.activity_tailored.id
            
            # Verify only one tailored activity exists for this combination
            tailored_activities = ActivityTailored.objects.filter(
                user_profile=self.user_profile,
                user_environment=self.home_env,
                generic_activity=self.generic_activity_2
            )
            assert tailored_activities.count() == 1
            
            # Verify the activity has multiple wheel items
            assert activity_1.wheel_items.count() == 2
            
            self.log_test_result(test_name, True)
            return True
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))
            return False
    
    def test_wheel_generation_no_violations(self):
        """Test that wheel generation doesn't cause database constraint violations."""
        test_name = "Wheel Generation No DB Violations"
        
        try:
            # Set current environment for the user
            self.user_profile.current_environment = self.home_env
            self.user_profile.save()
            
            # Test the generate_wheel function with multiple activities
            strategy_framework = {
                "domains": {
                    "mindfulness": {"name": "Mindfulness", "weight": 0.4},
                    "physical": {"name": "Physical", "weight": 0.3},
                    "creative": {"name": "Creative", "weight": 0.3}
                },
                "challenge_calibration": {
                    "base_level": 40,
                    "adjustment_factors": {"trust_phase": "foundation"}
                }
            }
            
            # Generate wheel - this should not cause any database violations
            result = generate_wheel({
                "user_profile_id": self.user_profile.id,
                "strategy_framework": strategy_framework,
                "activity_count": 4
            })
            
            # Verify wheel was created successfully
            assert "wheel" in result
            assert "error" not in result
            
            wheel_data = result["wheel"]
            assert "items" in wheel_data
            assert len(wheel_data["items"]) > 0
            
            # Verify all wheel items were created without violations
            wheel = Wheel.objects.get(id=wheel_data["id"])
            wheel_items = wheel.items.all()
            assert wheel_items.count() > 0
            
            # Verify all activities are properly linked to the environment
            for item in wheel_items:
                assert item.activity_tailored.user_environment == self.home_env
                assert item.activity_tailored.user_profile == self.user_profile
            
            self.log_test_result(test_name, True)
            return True
            
        except Exception as e:
            self.log_test_result(test_name, False, str(e))
            return False
    
    def cleanup_test_data(self):
        """Clean up test data."""
        logger.info("🧹 Cleaning up test data...")
        
        try:
            # Delete in proper order to avoid foreign key constraints
            WheelItem.objects.filter(wheel__name__startswith="Test Wheel").delete()
            Wheel.objects.filter(name__startswith="Test Wheel").delete()
            ActivityTailored.objects.filter(user_profile=self.user_profile).delete()
            UserEnvironment.objects.filter(user_profile=self.user_profile).delete()
            
            # Only delete test user if it was created for testing
            if self.user_profile.profile_name == 'Test User':
                self.user_profile.user.delete()
                self.user_profile.delete()
            
            GenericActivity.objects.filter(code__startswith="test_").delete()
            
            logger.info("✅ Test data cleanup complete")
            
        except Exception as e:
            logger.error(f"❌ Failed to cleanup test data: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests in the test suite."""
        logger.info("🚀 Starting ActivityTailored-UserEnvironment Relationship Tests")
        logger.info("=" * 80)
        
        # Setup
        if not self.setup_test_data():
            logger.error("❌ Failed to setup test data. Aborting tests.")
            return False
        
        try:
            # Run tests
            with transaction.atomic():
                self.test_activity_environment_relationship()
                self.test_activity_reuse_logic()
                self.test_wheel_generation_no_violations()
                
                # Rollback transaction to avoid affecting the database
                transaction.set_rollback(True)
                
        except Exception as e:
            logger.error(f"❌ Test execution failed: {str(e)}")
        
        finally:
            # Cleanup (in case rollback didn't work)
            self.cleanup_test_data()
        
        # Print results
        logger.info("=" * 80)
        logger.info("📊 TEST RESULTS SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Total Tests: {self.test_results['total_tests']}")
        logger.info(f"Passed: {self.test_results['passed_tests']}")
        logger.info(f"Failed: {self.test_results['failed_tests']}")
        
        success_rate = (self.test_results['passed_tests'] / self.test_results['total_tests']) * 100 if self.test_results['total_tests'] > 0 else 0
        logger.info(f"Success Rate: {success_rate:.1f}%")
        
        if self.test_results['failed_tests'] > 0:
            logger.info("\n❌ FAILED TESTS:")
            for test in self.test_results['test_details']:
                if not test['passed']:
                    logger.info(f"  - {test['test_name']}: {test['details']}")
        
        logger.info("=" * 80)
        
        return self.test_results['failed_tests'] == 0

def main():
    """Main function to run the tests."""
    test_suite = ActivityEnvironmentRelationshipTest()
    success = test_suite.run_all_tests()
    
    if success:
        logger.info("🎉 All tests passed! ActivityTailored-UserEnvironment relationship is working correctly.")
        sys.exit(0)
    else:
        logger.error("💥 Some tests failed. Please check the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
