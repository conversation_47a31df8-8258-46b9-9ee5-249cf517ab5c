#!/usr/bin/env python3
"""
Test to validate that the frontend fix is working correctly.

This test simulates the exact message flow that should happen after the frontend fix:
1. First message: "make me a wheel" (should set conversation state)
2. Second message: profile info WITH conversation state metadata (should launch workflow)
"""

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.user.models import UserProfile
from apps.main.services.conversation_dispatcher import ConversationDispatcher
from django.contrib.auth import get_user_model
from asgiref.sync import sync_to_async

User = get_user_model()

class FrontendFixValidator:
    def __init__(self):
        self.user_id = None
        self.session_id = f"test_frontend_fix_{int(asyncio.get_event_loop().time())}"
        
    async def setup_test_user(self):
        """Create a test user for the conversation."""
        try:
            # Create Django user first (sync)
            django_user = await sync_to_async(User.objects.create_user)(
                username=f"test_user_{self.session_id}",
                email=f"test_{self.session_id}@example.com"
            )
            
            # Create UserProfile (sync)
            user_profile = await sync_to_async(UserProfile.objects.create)(
                user=django_user,
                profile_name=f"Test User {self.session_id}",
                is_real=False  # Mark as test profile
            )
            self.user_id = str(user_profile.id)
            print(f"✅ Created test user: ID={self.user_id}")
            return True
        except Exception as e:
            print(f"❌ Failed to create test user: {e}")
            return False
    
    async def test_frontend_fix(self):
        """Test the frontend fix with proper conversation state flow."""
        print("🚀 Testing Frontend Fix - Conversation State Flow")
        print("=" * 60)
        
        # Step 1: Setup
        if not await self.setup_test_user():
            return False
        
        # Step 2: Test first message (should set conversation state)
        print("\n1️⃣ Testing first message: 'make me a wheel'")
        
        dispatcher1 = ConversationDispatcher(
            user_profile_id=self.user_id,
            user_ws_session_name=f"client_session_{self.session_id}"
        )
        
        result1 = await dispatcher1.process_message({
            'text': 'make me a wheel',
            'timestamp': '2025-06-18T20:00:00.000Z',
            'metadata': {}  # No conversation state initially
        })
        
        print(f"   🔄 Workflow type: {result1.get('workflow_type')}")
        print(f"   🔄 Has conversation state update: {'✅' if result1.get('conversation_state_update') else '❌'}")
        
        if result1.get('conversation_state_update'):
            conversation_state = result1['conversation_state_update']
            print(f"   📋 Conversation state: {conversation_state}")
        else:
            print("   ❌ No conversation state update found!")
            return False
        
        # Step 3: Test second message WITH conversation state metadata (simulating fixed frontend)
        print("\n2️⃣ Testing second message WITH conversation state metadata (FIXED FRONTEND)")
        
        dispatcher2 = ConversationDispatcher(
            user_profile_id=self.user_id,
            user_ws_session_name=f"client_session_{self.session_id}"
        )
        
        # Include conversation state in metadata (simulating FIXED frontend behavior)
        metadata_with_state = {
            'conversation_phase': conversation_state.get('phase', 'initial'),
            'awaiting_response_type': conversation_state.get('awaiting_response_type'),
            'session_context': conversation_state.get('context', {})
        }
        
        print(f"   📋 Sending metadata WITH conversation state: {metadata_with_state}")
        
        result2 = await dispatcher2.process_message({
            'text': "I'm a 25-year-old software developer from Berlin. I love hiking, reading sci-fi books, and learning new programming languages. My main goal is to improve my work-life balance.",
            'timestamp': '2025-06-18T20:01:00.000Z',
            'metadata': metadata_with_state  # CRITICAL: Include conversation state
        })
        
        print(f"   🔄 Workflow type: {result2.get('workflow_type')}")
        print(f"   ⚙️ Should launch workflow: {'✅' if result2.get('workflow_type') != 'direct_response_only' else '❌'}")
        
        # Step 4: Test second message WITHOUT conversation state metadata (simulating broken frontend)
        print("\n3️⃣ Testing second message WITHOUT conversation state metadata (BROKEN FRONTEND)")
        
        dispatcher3 = ConversationDispatcher(
            user_profile_id=self.user_id,
            user_ws_session_name=f"client_session_{self.session_id}"
        )
        
        # NO conversation state in metadata (simulating broken frontend behavior)
        metadata_without_state = {}
        
        print(f"   📋 Sending metadata WITHOUT conversation state: {metadata_without_state}")
        
        result3 = await dispatcher3.process_message({
            'text': "I'm a 25-year-old software developer from Berlin. I love hiking, reading sci-fi books, and learning new programming languages. My main goal is to improve my work-life balance.",
            'timestamp': '2025-06-18T20:02:00.000Z',
            'metadata': metadata_without_state  # NO conversation state
        })
        
        print(f"   🔄 Workflow type: {result3.get('workflow_type')}")
        print(f"   ⚙️ Should launch workflow: {'❌' if result3.get('workflow_type') == 'direct_response_only' else '✅'}")
        
        # Step 5: Analysis
        print("\n4️⃣ Analysis and Validation")
        print("=" * 40)
        
        if result1.get('workflow_type') != 'direct_response_only':
            print("❌ ISSUE: First message should return 'direct_response_only'")
            return False
        elif not result1.get('conversation_state_update'):
            print("❌ ISSUE: First message should include conversation_state_update")
            return False
        elif result2.get('workflow_type') == 'direct_response_only':
            print("❌ ISSUE: Second message WITH conversation state should launch a workflow")
            print("   This suggests the conversation state metadata is still not being processed correctly")
            return False
        elif result3.get('workflow_type') != 'direct_response_only':
            print("❌ ISSUE: Second message WITHOUT conversation state should NOT launch a workflow")
            print("   This suggests the system is not properly checking for conversation state")
            return False
        else:
            print("✅ SUCCESS: Frontend fix validation passed!")
            print(f"   - First message: {result1.get('workflow_type')} with state update")
            print(f"   - Second message WITH state: {result2.get('workflow_type')} workflow launched")
            print(f"   - Second message WITHOUT state: {result3.get('workflow_type')} (no workflow)")
            print("\n🎉 The frontend fix should resolve the missing Celery task issue!")
            return True

async def main():
    """Main test function."""
    validator = FrontendFixValidator()
    try:
        success = await validator.test_frontend_fix()
        if success:
            print("\n🎉 Frontend fix validation completed successfully!")
        else:
            print("\n❌ Frontend fix validation failed")
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
