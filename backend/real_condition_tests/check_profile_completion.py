#!/usr/bin/env python3
"""
Quick script to check the current profile completion for user 39
"""

import os
import sys
import django
import asyncio

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Configure Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.agents.tools.tools_util import execute_tool

async def check_profile_completion():
    """Check the current profile completion for user 39."""
    try:
        result = await execute_tool(
            tool_code="get_user_profile",
            tool_input={"input_data": {"user_profile_id": "40"}},
            user_profile_id="40",
            session_id="test_session_40"
        )
        
        user_profile_data = result.get("user_profile", {})
        completion = user_profile_data.get("profile_completion", 0.0)
        
        print(f"User 40 Profile Completion: {completion:.1%}")
        print(f"Above 50% threshold: {completion >= 0.5}")
        
        # Show what components are present
        print("\nProfile Components:")
        print(f"  Demographics: {'✓' if user_profile_data.get('demographics') else '✗'}")
        print(f"  Traits: {len(user_profile_data.get('traits', []))} records")
        print(f"  Goals: {len(user_profile_data.get('goals', []))} records")
        print(f"  Beliefs: {len(user_profile_data.get('beliefs', []))} records")
        print(f"  Trust Level: {'✓' if user_profile_data.get('trust_level') else '✗'}")
        print(f"  Current Mood: {'✓' if user_profile_data.get('current_mood') else '✗'}")
        
        return completion
        
    except Exception as e:
        print(f"Error checking profile completion: {e}")
        return 0.0

if __name__ == "__main__":
    asyncio.run(check_profile_completion())
