#!/usr/bin/env python3

"""
Phase 3 Profile Completion Refactoring Test

Tests the new data processing workflow that focuses purely on extracting,
validating, and storing profile information without conversation management.

This test validates that the refactored workflow:
1. Processes informative messages about user profile
2. Extracts structured data using LLM-based tools
3. Validates extracted data for completeness
4. Stores data in database without conversation loops
5. Integrates with ConversationDispatcher flow
"""

import asyncio
import sys
import os
import django
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.graphs.profile_completion_graph import run_profile_completion_workflow
from apps.user.models import UserProfile, Demographics, Preference, UserGoal
from django.contrib.auth.models import User
from asgiref.sync import sync_to_async


class Phase3ProfileDataProcessingTest:
    """Test the new Phase 3 data processing workflow."""
    
    def __init__(self):
        self.test_user_id = None
        self.session_name = f"phase3_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    async def setup_test_user(self):
        """Create a test user with minimal profile data."""
        print("🔧 Setting up test user...")
        
        @sync_to_async
        def create_user():
            # Create Django user first
            django_user, _ = User.objects.get_or_create(
                username="phase3_test_user",
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Phase3',
                    'last_name': 'TestUser'
                }
            )

            # Create or get test user profile
            user_profile, created = UserProfile.objects.get_or_create(
                profile_name="phase3_test_user",
                defaults={
                    'user': django_user,
                    'is_real': False  # Mark as test user
                }
            )
            
            # Clear any existing profile data for clean test
            Demographics.objects.filter(user_profile=user_profile).delete()
            Preference.objects.filter(user_profile=user_profile).delete()
            UserGoal.objects.filter(user_profile=user_profile).delete()
            
            return str(user_profile.id)
        
        self.test_user_id = await create_user()
        print(f"✅ Test user created: {self.test_user_id}")
        
    async def test_data_processing_workflow(self):
        """Test the new data processing workflow with informative user message."""
        print("\n📊 Testing Data Processing Workflow")
        print("-" * 50)
        
        # Simulate informative message about user profile (what ConversationDispatcher would send)
        profile_data_packet = {
            'text': """Hi, I'm a 22-year-old student living in Berlin. I'm currently dealing with ADHD 
                      and have upcoming exams that are making me really stressed. I need help focusing 
                      and managing my time better. I really enjoy creative activities like drawing and 
                      music, but I also need quiet spaces to concentrate. I want to improve my study 
                      habits and learn better stress management techniques.""",
            'user_ws_session_name': self.session_name,
            'source': 'conversation_dispatcher',
            'processing_type': 'profile_enrichment'
        }
        
        try:
            # Run the new data processing workflow
            print(f"🔄 Running data processing workflow for user {self.test_user_id}")
            result = await run_profile_completion_workflow(
                user_profile_id=self.test_user_id,
                initial_input=profile_data_packet
            )
            
            print(f"✅ Workflow completed: {result.get('completed', False)}")
            print(f"📊 Processing summary: {result.get('processing_summary', {})}")
            
            # Validate results
            await self._validate_data_processing_results(result)
            
            return result
            
        except Exception as e:
            print(f"❌ Data processing workflow failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    async def _validate_data_processing_results(self, result):
        """Validate that data was properly extracted and stored."""
        print("\n🔍 Validating Data Processing Results")
        print("-" * 40)
        
        processing_summary = result.get('processing_summary', {})
        
        # Check processing completion
        if not processing_summary.get('completed', False):
            print(f"⚠️  Workflow not completed: {result.get('error', 'Unknown error')}")
            return False
        
        # Check items processed
        items_processed = processing_summary.get('items_processed', 0)
        print(f"📊 Items processed: {items_processed}")
        
        # Check data categories updated
        categories_updated = processing_summary.get('data_categories_updated', [])
        print(f"📂 Data categories updated: {categories_updated}")
        
        # Validate database storage
        await self._validate_database_storage(categories_updated)
        
        return True
    
    async def _validate_database_storage(self, expected_categories):
        """Validate that data was actually stored in the database."""
        print("\n💾 Validating Database Storage")
        print("-" * 35)

        # Add a small delay to ensure database transactions are committed
        import asyncio
        await asyncio.sleep(0.5)

        @sync_to_async
        def check_database():
            from django.db import transaction

            # Force a database connection refresh to ensure we see committed data
            with transaction.atomic():
                results = {
                    'demographics': 0,
                    'goals': 0,
                    'preferences': 0,
                    'details': {}
                }

                # Check demographics with detailed info
                demographics_qs = Demographics.objects.filter(user_profile_id=self.test_user_id)
                results['demographics'] = demographics_qs.count()
                if results['demographics'] > 0:
                    demo = demographics_qs.first()
                    results['details']['demographics'] = {
                        'age': demo.age,
                        'location': demo.location,
                        'occupation': demo.occupation
                    }

                # Check goals with detailed info
                goals_qs = UserGoal.objects.filter(user_profile_id=self.test_user_id)
                results['goals'] = goals_qs.count()
                if results['goals'] > 0:
                    results['details']['goals'] = [goal.title for goal in goals_qs[:3]]

                # Check preferences with detailed info
                preferences_qs = Preference.objects.filter(user_profile_id=self.test_user_id)
                results['preferences'] = preferences_qs.count()
                if results['preferences'] > 0:
                    results['details']['preferences'] = [pref.pref_name for pref in preferences_qs[:3]]

                return results

        db_results = await check_database()

        # Validate storage results with detailed reporting
        success = True
        for category, count in db_results.items():
            if category == 'details':
                continue

            if category in expected_categories:
                if count > 0:
                    print(f"✅ {category.capitalize()}: {count} records stored")
                    # Show sample data if available
                    if category in db_results['details']:
                        sample_data = db_results['details'][category]
                        if isinstance(sample_data, list):
                            print(f"   📋 Sample items: {', '.join(sample_data[:2])}")
                        else:
                            print(f"   📋 Sample data: {sample_data}")
                else:
                    print(f"❌ {category.capitalize()}: No records found (expected some)")
                    success = False
            else:
                if count > 0:
                    print(f"ℹ️  {category.capitalize()}: {count} records (not expected in this test)")

        return success
    
    async def test_integration_with_conversation_dispatcher(self):
        """Test that the workflow integrates properly with ConversationDispatcher."""
        print("\n🔗 Testing ConversationDispatcher Integration")
        print("-" * 45)
        
        # Test that the workflow returns proper data for ConversationDispatcher
        profile_data = {
            'text': 'I am 25 years old and work as a software engineer in San Francisco.',
            'user_ws_session_name': self.session_name
        }
        
        result = await run_profile_completion_workflow(
            user_profile_id=self.test_user_id,
            initial_input=profile_data
        )
        
        # Check that result contains information ConversationDispatcher needs
        required_fields = ['processing_summary', 'completed']
        missing_fields = [field for field in required_fields if field not in result]
        
        if missing_fields:
            print(f"❌ Missing required fields for ConversationDispatcher: {missing_fields}")
            return False
        
        print("✅ ConversationDispatcher integration fields present")
        return True
    
    async def cleanup(self):
        """Clean up test data."""
        print("\n🧹 Cleaning up test data...")
        
        @sync_to_async
        def cleanup_data():
            if self.test_user_id:
                try:
                    user_profile = UserProfile.objects.get(id=self.test_user_id)
                    Demographics.objects.filter(user_profile=user_profile).delete()
                    Preference.objects.filter(user_profile=user_profile).delete()
                    UserGoal.objects.filter(user_profile=user_profile).delete()
                    user_profile.delete()
                    print("✅ Test data cleaned up")
                except UserProfile.DoesNotExist:
                    print("ℹ️  Test user already deleted")
        
        await cleanup_data()
    
    async def run_all_tests(self):
        """Run all Phase 3 tests."""
        print("🚀 Starting Phase 3 Profile Data Processing Tests")
        print("=" * 60)
        
        try:
            await self.setup_test_user()
            
            # Test 1: Data processing workflow
            result1 = await self.test_data_processing_workflow()
            
            # Test 2: ConversationDispatcher integration
            result2 = await self.test_integration_with_conversation_dispatcher()
            
            # Summary
            print("\n📋 Test Summary")
            print("-" * 20)
            print(f"✅ Data Processing Workflow: {'PASS' if result1 else 'FAIL'}")
            print(f"✅ ConversationDispatcher Integration: {'PASS' if result2 else 'FAIL'}")
            
            overall_success = bool(result1 and result2)
            print(f"\n🎯 Overall Result: {'SUCCESS' if overall_success else 'FAILURE'}")
            
            return overall_success
            
        except Exception as e:
            print(f"❌ Test suite failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            await self.cleanup()


async def main():
    """Main test execution."""
    test_suite = Phase3ProfileDataProcessingTest()
    success = await test_suite.run_all_tests()
    
    if success:
        print("\n🎉 Phase 3 Profile Data Processing Tests PASSED!")
        sys.exit(0)
    else:
        print("\n💥 Phase 3 Profile Data Processing Tests FAILED!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
