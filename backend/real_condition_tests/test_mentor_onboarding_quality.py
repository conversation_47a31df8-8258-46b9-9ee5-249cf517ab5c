#!/usr/bin/env python3
"""
Mentor Agent Quality Testing - Onboarding Workflow Focus

This script tests the quality delivered by the Mentor agent during the onboarding workflow,
focusing on user profile enrichment and UX progression for ADHD students.

Test Scenario:
- 21-year-old female student in Berlin
- ADHD characteristics, upcoming exams
- Initial message: "hello, what do you propose ?"
- Monitor profile enrichment and Mentor quality
"""

import os
import sys
import django
import asyncio
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.contrib.auth.models import User
from apps.user.models import (
    UserProfile, Demographics, UserEnvironment,
    UserTraitInclination, Preference, TrustLevel,
    CurrentMood, Belief, UserGoal
)
from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.agents.tools.tools_util import execute_tool
from asgiref.sync import sync_to_async

class MentorOnboardingQualityTest:
    """Test Mentor agent quality during onboarding workflow."""
    
    def __init__(self):
        self.test_id = str(uuid.uuid4())[:8]
        self.test_user_id = None
        self.test_profile_id = None
        self.results = {
            'test_id': self.test_id,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'test_scenario': 'ADHD student onboarding',
            'phases': {},
            'measurements': {},
            'quality_assessment': {},
            'recommendations': []
        }
        
    async def run_complete_test(self):
        """Execute the complete onboarding quality test."""
        print(f"🎯 Starting Mentor Onboarding Quality Test - ID: {self.test_id}")
        print("=" * 60)
        
        try:
            # Phase 1: Setup test user profile
            await self._phase_1_setup_test_user()
            
            # Phase 2: Baseline profile assessment
            await self._phase_2_baseline_assessment()
            
            # Phase 3: Execute onboarding workflow
            await self._phase_3_execute_onboarding()
            
            # Phase 4: Validate profile enrichment
            await self._phase_4_validate_enrichment()
            
            # Phase 5: Quality assessment
            await self._phase_5_quality_assessment()
            
            # Phase 6: Generate recommendations
            await self._phase_6_generate_recommendations()
            
            # Save results
            await self._save_results()
            
            print("\n🎉 Test completed successfully!")
            print(f"📊 Results saved to: results/mentor_onboarding_quality_{self.test_id}.json")
            
        except Exception as e:
            print(f"❌ Test failed: {str(e)}")
            self.results['error'] = str(e)
            await self._save_results()
            raise
            
    async def _phase_1_setup_test_user(self):
        """Phase 1: Create test user profile with minimal data."""
        print("\n📋 Phase 1: Setting up test user profile")
        
        phase_start = datetime.now()
        
        try:
            # Create Django user (wrapped for async)
            username = f"test_adhd_student_{self.test_id}"
            user, created = await sync_to_async(User.objects.get_or_create)(
                username=username,
                defaults={
                    'email': f"{username}@test.goali.com",
                    'first_name': 'Emma',
                    'last_name': 'Test'
                }
            )
            self.test_user_id = user.id

            # Create minimal UserProfile (wrapped for async)
            profile, created = await sync_to_async(UserProfile.objects.get_or_create)(
                user=user,
                defaults={
                    'profile_name': 'Emma',
                    'is_real': False,  # Test profile
                }
            )
            self.test_profile_id = str(profile.id)

            # Create minimal Demographics (21-year-old female student in Berlin)
            demographics, created = await sync_to_async(Demographics.objects.get_or_create)(
                user_profile=profile,
                defaults={
                    'full_name': 'Emma Test',
                    'age': 21,
                    'gender': 'female',
                    'location': 'Berlin, Germany',
                    'language': 'English',
                    'occupation': 'Student'
                }
            )

            # Create preferences as separate objects (replacing personal_prefs_json)
            if created:
                from datetime import date, timedelta
                preferences_data = [
                    ("Psychology Studies", "Currently studying Psychology", 85),
                    ("ADHD Management", "Managing ADHD while studying", 70),
                    ("Exam Preparation", "Preparing for upcoming exams", 90)
                ]

                for pref_name, description, strength in preferences_data:
                    await sync_to_async(Preference.objects.create)(
                        user_profile=profile,
                        pref_name=pref_name,
                        pref_description=description,
                        pref_strength=strength,
                        user_awareness=80,
                        effective_start=date.today(),
                        duration_estimate="6 months",
                        effective_end=date.today() + timedelta(days=180)
                    )
            
            self.results['phases']['phase_1'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'user_id': self.test_user_id,
                'profile_id': self.test_profile_id,
                'demographics_created': created,
                'test_characteristics': {
                    'age': 21,
                    'gender': 'female',
                    'location': 'Berlin, Germany',
                    'occupation': 'Student',
                    'special_needs': 'ADHD',
                    'context': 'upcoming exams'
                }
            }
            
            print(f"✅ Test user created: {username} (Profile ID: {self.test_profile_id})")
            
        except Exception as e:
            self.results['phases']['phase_1'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            raise
            
    async def _phase_2_baseline_assessment(self):
        """Phase 2: Assess baseline profile completion."""
        print("\n📊 Phase 2: Baseline profile assessment")
        
        phase_start = datetime.now()
        
        try:
            # Get initial profile completion
            profile_result = await execute_tool(
                tool_code="get_user_profile",
                tool_input={"input_data": {"user_profile_id": self.test_profile_id}},
                user_profile_id=self.test_profile_id
            )
            
            baseline_completion = profile_result.get("user_profile", {}).get("profile_completion", 0.0)
            
            # Count existing database records (wrapped for async)
            profile = await sync_to_async(UserProfile.objects.get)(id=self.test_profile_id)
            baseline_counts = {
                'demographics': await sync_to_async(Demographics.objects.filter(user_profile=profile).count)(),
                'environments': await sync_to_async(UserEnvironment.objects.filter(user_profile=profile).count)(),
                'traits': await sync_to_async(UserTraitInclination.objects.filter(user_profile=profile).count)(),
                'preferences': await sync_to_async(Preference.objects.filter(user_profile=profile).count)(),
                'beliefs': await sync_to_async(Belief.objects.filter(user_profile=profile).count)(),
                'goals': await sync_to_async(UserGoal.objects.filter(user_profile=profile).count)(),
                'trust_levels': await sync_to_async(TrustLevel.objects.filter(user_profile=profile).count)(),
                'moods': await sync_to_async(CurrentMood.objects.filter(user_profile=profile).count)()
            }
            
            self.results['phases']['phase_2'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'baseline_completion': baseline_completion,
                'baseline_record_counts': baseline_counts,
                'total_baseline_records': sum(baseline_counts.values())
            }
            
            print(f"✅ Baseline completion: {baseline_completion:.2%}")
            print(f"📈 Baseline records: {sum(baseline_counts.values())} total")
            
        except Exception as e:
            self.results['phases']['phase_2'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            raise
            
    async def _phase_3_execute_onboarding(self):
        """Phase 3: Execute onboarding workflow with test message."""
        print("\n🚀 Phase 3: Executing onboarding workflow")
        
        phase_start = datetime.now()
        
        try:
            # Create conversation dispatcher
            dispatcher = ConversationDispatcher(
                user_profile_id=self.test_profile_id,
                user_ws_session_name=f"test_session_{self.test_id}",
                fail_fast_on_errors=True  # Fail fast for testing
            )
            
            # Prepare test message (ADHD student, upcoming exams)
            test_message = {
                'text': 'hello, what do you propose ?',
                'metadata': {
                    'test_scenario': 'adhd_student_onboarding',
                    'user_context': 'stressed about upcoming exams'
                },
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            print(f"📤 Sending message: '{test_message['text']}'")
            
            # Process the message
            workflow_start = datetime.now()
            response = await dispatcher.process_message(test_message)
            workflow_duration = (datetime.now() - workflow_start).total_seconds()
            
            self.results['phases']['phase_3'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'workflow_duration': workflow_duration,
                'test_message': test_message,
                'dispatcher_response': response,
                'workflow_classification': {
                    'workflow_type': response.get('workflow_type'),
                    'confidence': response.get('confidence'),
                    'status': response.get('status')
                }
            }
            
            print(f"✅ Workflow executed: {response.get('workflow_type')} (confidence: {response.get('confidence', 0):.2f})")
            print(f"⏱️  Execution time: {workflow_duration:.2f}s")
            
            # Wait for workflow completion
            print("⏳ Waiting for workflow completion...")
            await asyncio.sleep(10)  # Allow time for async workflow execution
            
        except Exception as e:
            self.results['phases']['phase_3'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            raise

    async def _phase_4_validate_enrichment(self):
        """Phase 4: Validate profile enrichment after onboarding."""
        print("\n📈 Phase 4: Validating profile enrichment")

        phase_start = datetime.now()

        try:
            # Get updated profile completion
            profile_result = await execute_tool(
                tool_code="get_user_profile",
                tool_input={"input_data": {"user_profile_id": self.test_profile_id}},
                user_profile_id=self.test_profile_id
            )

            updated_completion = profile_result.get("user_profile", {}).get("profile_completion", 0.0)
            baseline_completion = self.results['phases']['phase_2']['baseline_completion']
            completion_improvement = updated_completion - baseline_completion

            # Count updated database records (wrapped for async)
            profile = await sync_to_async(UserProfile.objects.get)(id=self.test_profile_id)
            updated_counts = {
                'demographics': await sync_to_async(Demographics.objects.filter(user_profile=profile).count)(),
                'environments': await sync_to_async(UserEnvironment.objects.filter(user_profile=profile).count)(),
                'traits': await sync_to_async(UserTraitInclination.objects.filter(user_profile=profile).count)(),
                'preferences': await sync_to_async(Preference.objects.filter(user_profile=profile).count)(),
                'beliefs': await sync_to_async(Belief.objects.filter(user_profile=profile).count)(),
                'goals': await sync_to_async(UserGoal.objects.filter(user_profile=profile).count)(),
                'trust_levels': await sync_to_async(TrustLevel.objects.filter(user_profile=profile).count)(),
                'moods': await sync_to_async(CurrentMood.objects.filter(user_profile=profile).count)()
            }

            baseline_counts = self.results['phases']['phase_2']['baseline_record_counts']
            record_improvements = {
                key: updated_counts[key] - baseline_counts[key]
                for key in updated_counts.keys()
            }

            total_new_records = sum(record_improvements.values())

            self.results['phases']['phase_4'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'updated_completion': updated_completion,
                'completion_improvement': completion_improvement,
                'updated_record_counts': updated_counts,
                'record_improvements': record_improvements,
                'total_new_records': total_new_records,
                'enrichment_success': completion_improvement > 0 or total_new_records > 0
            }

            print(f"✅ Updated completion: {updated_completion:.2%} (Δ: {completion_improvement:+.2%})")
            print(f"📊 New records created: {total_new_records}")
            for key, improvement in record_improvements.items():
                if improvement > 0:
                    print(f"   • {key}: +{improvement}")

        except Exception as e:
            self.results['phases']['phase_4'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            raise

    async def _phase_5_quality_assessment(self):
        """Phase 5: Assess Mentor agent response quality."""
        print("\n🎯 Phase 5: Quality assessment")

        phase_start = datetime.now()

        try:
            # Get conversation history to analyze Mentor responses
            history_result = await execute_tool(
                tool_code="get_conversation_history",
                tool_input={"user_profile_id": self.test_profile_id, "limit": 10},
                user_profile_id=self.test_profile_id
            )

            conversation_history = []
            if isinstance(history_result, list):
                conversation_history = history_result
            elif isinstance(history_result, dict) and 'history' in history_result:
                conversation_history = history_result['history']

            # Analyze response quality
            quality_metrics = {
                'response_count': len([msg for msg in conversation_history if msg.get('role') == 'assistant']),
                'response_length_avg': 0,
                'contextual_appropriateness': 'unknown',
                'adhd_considerations': 'unknown',
                'onboarding_guidance': 'unknown'
            }

            assistant_responses = [msg for msg in conversation_history if msg.get('role') == 'assistant']
            if assistant_responses:
                total_length = sum(len(msg.get('content', '')) for msg in assistant_responses)
                quality_metrics['response_length_avg'] = total_length / len(assistant_responses)

                # Analyze first response for quality indicators
                first_response = assistant_responses[0].get('content', '') if assistant_responses else ''

                # Check for ADHD-friendly characteristics
                adhd_indicators = [
                    'clear', 'simple', 'step', 'one thing', 'focus', 'break down',
                    'manageable', 'small', 'easy', 'start with'
                ]
                adhd_score = sum(1 for indicator in adhd_indicators if indicator in first_response.lower())
                quality_metrics['adhd_considerations'] = 'good' if adhd_score >= 2 else 'needs_improvement'

                # Check for onboarding guidance
                onboarding_indicators = [
                    'welcome', 'help you', 'get started', 'learn about', 'understand',
                    'tell me', 'share', 'explore', 'discover'
                ]
                onboarding_score = sum(1 for indicator in onboarding_indicators if indicator in first_response.lower())
                quality_metrics['onboarding_guidance'] = 'good' if onboarding_score >= 2 else 'needs_improvement'

                # Overall contextual appropriateness
                if quality_metrics['adhd_considerations'] == 'good' and quality_metrics['onboarding_guidance'] == 'good':
                    quality_metrics['contextual_appropriateness'] = 'excellent'
                elif quality_metrics['adhd_considerations'] == 'good' or quality_metrics['onboarding_guidance'] == 'good':
                    quality_metrics['contextual_appropriateness'] = 'good'
                else:
                    quality_metrics['contextual_appropriateness'] = 'needs_improvement'

            self.results['phases']['phase_5'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'conversation_history': conversation_history,
                'quality_metrics': quality_metrics,
                'first_response_analysis': {
                    'content': assistant_responses[0].get('content', '') if assistant_responses else '',
                    'length': len(assistant_responses[0].get('content', '')) if assistant_responses else 0,
                    'adhd_friendly': quality_metrics['adhd_considerations'],
                    'onboarding_appropriate': quality_metrics['onboarding_guidance']
                }
            }

            print(f"✅ Response quality: {quality_metrics['contextual_appropriateness']}")
            print(f"📝 Responses analyzed: {quality_metrics['response_count']}")
            print(f"🧠 ADHD considerations: {quality_metrics['adhd_considerations']}")
            print(f"🎓 Onboarding guidance: {quality_metrics['onboarding_guidance']}")

        except Exception as e:
            self.results['phases']['phase_5'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            raise

    async def _phase_6_generate_recommendations(self):
        """Phase 6: Generate recommendations for improvement."""
        print("\n💡 Phase 6: Generating recommendations")

        phase_start = datetime.now()

        try:
            recommendations = []

            # Analyze enrichment results
            enrichment_data = self.results['phases']['phase_4']
            if enrichment_data['completion_improvement'] <= 0:
                recommendations.append({
                    'category': 'profile_enrichment',
                    'priority': 'high',
                    'issue': 'No profile completion improvement detected',
                    'recommendation': 'Enhance MentorAgent to actively gather user information during onboarding',
                    'implementation': 'Add progressive information gathering prompts to mentor instructions'
                })

            if enrichment_data['total_new_records'] == 0:
                recommendations.append({
                    'category': 'database_updates',
                    'priority': 'high',
                    'issue': 'No new database records created during onboarding',
                    'recommendation': 'Implement database update mechanisms in onboarding workflow',
                    'implementation': 'Add tool calls to create user preferences, traits, and goals'
                })

            # Analyze quality results
            quality_data = self.results['phases']['phase_5']
            quality_metrics = quality_data['quality_metrics']

            if quality_metrics['adhd_considerations'] == 'needs_improvement':
                recommendations.append({
                    'category': 'adhd_support',
                    'priority': 'medium',
                    'issue': 'Responses not optimized for ADHD users',
                    'recommendation': 'Enhance mentor instructions for ADHD-friendly communication',
                    'implementation': 'Add clear, concise, step-by-step guidance patterns'
                })

            if quality_metrics['onboarding_guidance'] == 'needs_improvement':
                recommendations.append({
                    'category': 'onboarding_ux',
                    'priority': 'medium',
                    'issue': 'Insufficient onboarding guidance in responses',
                    'recommendation': 'Improve mentor onboarding instructions and prompts',
                    'implementation': 'Add welcome messages and progressive information gathering'
                })

            # Workflow classification analysis
            workflow_data = self.results['phases']['phase_3']
            if workflow_data['workflow_classification']['workflow_type'] != 'onboarding':
                recommendations.append({
                    'category': 'workflow_classification',
                    'priority': 'high',
                    'issue': f"Wrong workflow triggered: {workflow_data['workflow_classification']['workflow_type']}",
                    'recommendation': 'Fix profile completion assessment or classification logic',
                    'implementation': 'Review get_user_profile tool and classification thresholds'
                })

            # Performance analysis
            if workflow_data['workflow_duration'] > 30:
                recommendations.append({
                    'category': 'performance',
                    'priority': 'low',
                    'issue': f"Slow workflow execution: {workflow_data['workflow_duration']:.2f}s",
                    'recommendation': 'Optimize workflow execution time',
                    'implementation': 'Review agent instructions and tool call efficiency'
                })

            self.results['phases']['phase_6'] = {
                'status': 'success',
                'duration_seconds': (datetime.now() - phase_start).total_seconds(),
                'recommendations': recommendations,
                'recommendation_count': len(recommendations),
                'high_priority_count': len([r for r in recommendations if r['priority'] == 'high']),
                'medium_priority_count': len([r for r in recommendations if r['priority'] == 'medium']),
                'low_priority_count': len([r for r in recommendations if r['priority'] == 'low'])
            }

            print(f"✅ Generated {len(recommendations)} recommendations")
            for rec in recommendations:
                priority_emoji = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}[rec['priority']]
                print(f"   {priority_emoji} {rec['category']}: {rec['issue']}")

        except Exception as e:
            self.results['phases']['phase_6'] = {
                'status': 'error',
                'error': str(e),
                'duration_seconds': (datetime.now() - phase_start).total_seconds()
            }
            raise

    async def _save_results(self):
        """Save test results to file."""
        try:
            # Ensure results directory exists
            os.makedirs('/usr/src/app/real_condition_tests/results', exist_ok=True)

            # Calculate overall metrics
            self.results['measurements'] = {
                'total_test_duration': sum(
                    phase.get('duration_seconds', 0)
                    for phase in self.results['phases'].values()
                ),
                'phases_completed': len([
                    phase for phase in self.results['phases'].values()
                    if phase.get('status') == 'success'
                ]),
                'phases_failed': len([
                    phase for phase in self.results['phases'].values()
                    if phase.get('status') == 'error'
                ]),
                'overall_success': all(
                    phase.get('status') == 'success'
                    for phase in self.results['phases'].values()
                )
            }

            # Save to file
            filename = f'/usr/src/app/real_condition_tests/results/mentor_onboarding_quality_{self.test_id}.json'
            with open(filename, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)

            print(f"💾 Results saved to: {filename}")

        except Exception as e:
            print(f"❌ Failed to save results: {str(e)}")


async def main():
    """Main test execution."""
    test = MentorOnboardingQualityTest()
    await test.run_complete_test()


if __name__ == "__main__":
    asyncio.run(main())
