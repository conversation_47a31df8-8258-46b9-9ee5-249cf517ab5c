#!/usr/bin/env python3
"""
Quick Benchmark Synchronous Test
Tests the quick benchmark system without async complications.

This test validates:
1. System setup and configuration
2. API endpoint functionality
3. Basic service operations
4. UI integration readiness

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_quick_benchmark_sync.py
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model

from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
from apps.main.services.simple_evaluation_adapter import SimpleEvaluationAdapter
from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from apps.main.models import GenericAgent

class QuickBenchmarkSyncTester:
    """Synchronous tester for quick benchmark system."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Quick Benchmark Synchronous Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {},
            'recommendations': []
        }
        self.client = Client()
        self.User = get_user_model()
        
    def log(self, message, level='INFO'):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        filename = f"quick_benchmark_sync_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        self.log(f"Results saved to: {filepath}")
        return filepath

    def test_system_setup(self):
        """Test system setup and configuration."""
        test_name = 'system_setup'
        self.log("Testing system setup...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Test 1: Check if agents exist
            agents = GenericAgent.objects.filter(is_active=True)
            test_result['details']['active_agents'] = agents.count()
            
            if agents.count() == 0:
                test_result['errors'].append("No active agents found")
                self.log("WARNING: No active agents found", 'WARN')
            else:
                self.log(f"Found {agents.count()} active agents")
            
            # Test 2: Check benchmark profiles
            try:
                profile = BenchmarkProfileFactory.get_or_create_benchmark_profile('anxious_new_user')
                test_result['details']['profile_creation'] = True
                test_result['details']['profile_name'] = profile.profile_name
                self.log(f"Profile ready: {profile.profile_name}")
            except Exception as e:
                test_result['errors'].append(f"Profile creation failed: {e}")
                test_result['details']['profile_creation'] = False
            
            # Test 3: Check evaluation adapter
            adapter = SimpleEvaluationAdapter()
            templates = adapter.get_available_templates()
            test_result['details']['evaluation_templates'] = len(templates)
            self.log(f"Found {len(templates)} evaluation templates")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"System setup test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['tests'][test_name] = test_result

    def test_service_functionality(self):
        """Test service functionality without async operations."""
        test_name = 'service_functionality'
        self.log("Testing service functionality...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            service = QuickBenchmarkService()
            
            # Test 1: Get available options
            options = service.get_available_options()
            test_result['details']['options_available'] = True
            test_result['details']['agents_count'] = len(options.get('available_agents', []))
            test_result['details']['profiles_count'] = len(options.get('profile_templates', []))
            test_result['details']['evaluations_count'] = len(options.get('evaluation_templates', []))
            
            self.log(f"Service options: {test_result['details']['agents_count']} agents, "
                    f"{test_result['details']['profiles_count']} profiles, "
                    f"{test_result['details']['evaluations_count']} evaluations")
            
            # Test 2: Validate option structure
            if options.get('available_agents'):
                sample_agent = options['available_agents'][0]
                test_result['details']['agent_structure'] = {
                    'has_role': 'role' in sample_agent,
                    'has_description': 'description' in sample_agent
                }
            
            if options.get('profile_templates'):
                sample_profile = options['profile_templates'][0]
                test_result['details']['profile_structure'] = {
                    'has_template_name': 'template_name' in sample_profile,
                    'has_profile_name': 'profile_name' in sample_profile,
                    'has_description': 'description' in sample_profile
                }
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"Service functionality test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['tests'][test_name] = test_result

    def test_api_integration(self):
        """Test API integration."""
        test_name = 'api_integration'
        self.log("Testing API integration...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Create admin user
            admin_user, created = self.User.objects.get_or_create(
                username='test_admin_sync',
                defaults={
                    'email': '<EMAIL>',
                    'is_staff': True,
                    'is_superuser': True
                }
            )
            
            # Login
            self.client.force_login(admin_user)
            
            # Test API endpoint
            response = self.client.get('/admin/benchmarks/api/quick-benchmark/')
            test_result['details']['api_status_code'] = response.status_code
            test_result['details']['api_accessible'] = response.status_code == 200
            
            if response.status_code == 200:
                data = response.json()
                test_result['details']['api_response'] = {
                    'has_success': data.get('success', False),
                    'has_options': 'options' in data,
                    'agents_in_response': len(data.get('options', {}).get('available_agents', [])),
                    'profiles_in_response': len(data.get('options', {}).get('profile_templates', [])),
                    'evaluations_in_response': len(data.get('options', {}).get('evaluation_templates', []))
                }
                self.log("API endpoint working correctly")
            else:
                test_result['errors'].append(f"API endpoint failed: {response.status_code}")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"API integration test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['tests'][test_name] = test_result

    def test_ui_readiness(self):
        """Test UI readiness."""
        test_name = 'ui_readiness'
        self.log("Testing UI readiness...")
        
        start_time = time.time()
        test_result = {
            'status': 'PASS',
            'details': {},
            'errors': [],
            'execution_time': 0
        }
        
        try:
            # Test benchmark management page
            response = self.client.get('/admin/benchmarks/manage/')
            test_result['details']['page_status_code'] = response.status_code
            test_result['details']['page_accessible'] = response.status_code == 200
            
            if response.status_code == 200:
                content = response.content.decode()
                ui_elements = {
                    'has_quick_benchmark_tab': 'quick-benchmark' in content,
                    'has_quick_benchmark_form': 'quick-benchmark-form' in content,
                    'has_api_url': 'QUICK_BENCHMARK_API_URL' in content,
                    'has_form_fields': all(field in content for field in [
                        'quick-agent-name',
                        'quick-profile-template',
                        'quick-evaluation-template'
                    ]),
                    'has_javascript': 'initializeQuickBenchmark' in content
                }
                
                test_result['details']['ui_elements'] = ui_elements
                
                all_elements_present = all(ui_elements.values())
                test_result['details']['ui_ready'] = all_elements_present
                
                if all_elements_present:
                    self.log("UI is ready for quick benchmark functionality")
                else:
                    missing = [k for k, v in ui_elements.items() if not v]
                    test_result['errors'].append(f"Missing UI elements: {missing}")
            else:
                test_result['errors'].append(f"Page not accessible: {response.status_code}")
            
        except Exception as e:
            test_result['status'] = 'FAIL'
            test_result['errors'].append(str(e))
            self.log(f"UI readiness test failed: {e}", 'ERROR')
        
        test_result['execution_time'] = time.time() - start_time
        self.results['tests'][test_name] = test_result

    def run_all_tests(self):
        """Run all tests and generate summary."""
        self.log("Starting Quick Benchmark Synchronous Test")
        self.log("=" * 60)
        
        # Run all tests
        self.test_system_setup()
        self.test_service_functionality()
        self.test_api_integration()
        self.test_ui_readiness()
        
        # Generate summary
        total_tests = len(self.results['tests'])
        passed_tests = sum(1 for test in self.results['tests'].values() if test['status'] == 'PASS')
        failed_tests = total_tests - passed_tests
        
        self.results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': f"{(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
            'total_execution_time': sum(test['execution_time'] for test in self.results['tests'].values())
        }
        
        # Generate recommendations
        if failed_tests > 0:
            self.results['recommendations'].append("Some tests failed - check error details and fix issues")
        
        # Check specific conditions
        system_setup = self.results['tests'].get('system_setup', {})
        if system_setup.get('details', {}).get('active_agents', 0) == 0:
            self.results['recommendations'].append("No active agents found - create agents for benchmarking")
        
        ui_readiness = self.results['tests'].get('ui_readiness', {})
        if not ui_readiness.get('details', {}).get('ui_ready', False):
            self.results['recommendations'].append("UI not fully ready - check missing elements")
        
        if failed_tests == 0:
            self.results['recommendations'].append("All tests passed - system ready for quick benchmarking")
        
        # Log summary
        self.log("=" * 60)
        self.log(f"Test Summary: {passed_tests}/{total_tests} tests passed ({self.results['summary']['success_rate']})")
        self.log(f"Total execution time: {self.results['summary']['total_execution_time']:.2f}s")
        
        if failed_tests == 0:
            self.log("✅ All tests passed! Quick benchmark system is ready for use.", 'SUCCESS')
        else:
            self.log(f"❌ {failed_tests} tests failed. Check results for details.", 'ERROR')
        
        return self.save_results()

def main():
    """Main test execution."""
    tester = QuickBenchmarkSyncTester()
    results_file = tester.run_all_tests()
    
    print(f"\n📊 Detailed results saved to: {results_file}")
    print("\n🚀 Next steps:")
    print("1. Review test results for any failures")
    print("2. Test the UI functionality manually")
    print("3. Run actual benchmarks to validate end-to-end flow")
    print("\n💡 Manual Testing:")
    print("   1. Open: http://localhost:8000/admin/benchmarks/manage/")
    print("   2. Click 'Quick Benchmark' tab")
    print("   3. Select agent, profile, and evaluation template")
    print("   4. Submit benchmark and verify results")

if __name__ == '__main__':
    main()
