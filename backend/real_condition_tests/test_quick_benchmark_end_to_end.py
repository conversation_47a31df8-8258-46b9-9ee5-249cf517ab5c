#!/usr/bin/env python3
"""
Quick Benchmark End-to-End Real Condition Test
Tests complete benchmark execution from profile creation to result display.

This test validates:
1. Complete benchmark workflow execution
2. Real LLM integration and evaluation
3. Result storage and retrieval
4. Performance measurement
5. Quality assessment
6. UI data preparation

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_quick_benchmark_end_to_end.py
"""

import os
import sys
import json
import asyncio
import time
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from django.db import transaction

from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
from apps.main.models import GenericAgent, BenchmarkRun, BenchmarkScenario
from apps.user.models import UserProfile

class QuickBenchmarkEndToEndTester:
    """End-to-end tester for quick benchmark system."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Quick Benchmark End-to-End Real Condition Test',
            'timestamp': datetime.now().isoformat(),
            'benchmarks_executed': [],
            'performance_metrics': {},
            'quality_assessment': {},
            'summary': {},
            'recommendations': []
        }
        self.client = Client()
        self.User = get_user_model()
        
    def log(self, message, level='INFO'):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        filename = f"quick_benchmark_e2e_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        self.log(f"Results saved to: {filepath}")
        return filepath

    def setup_test_environment(self):
        """Set up test environment with required data."""
        self.log("Setting up test environment...")

        # Ensure benchmark profiles exist (synchronous call)
        try:
            from django.db import transaction
            with transaction.atomic():
                profile = BenchmarkProfileFactory.get_or_create_benchmark_profile('anxious_new_user')
                self.log(f"Benchmark profile ready: {profile.profile_name}")
        except Exception as e:
            self.log(f"Failed to create benchmark profile: {e}", 'ERROR')
            return False
        
        # Check for available agents
        agents = GenericAgent.objects.all()
        if not agents.exists():
            self.log("No agents available - creating test agent", 'WARN')
            try:
                agent = GenericAgent.objects.create(
                    name='test_mentor',
                    role='mentor',
                    description='Test mentor agent for benchmarking'
                )
                self.log(f"Created test agent: {agent.name}")
            except Exception as e:
                self.log(f"Failed to create test agent: {e}", 'ERROR')
                return False
        
        return True

    async def run_benchmark_test(self, agent_name, profile_template, evaluation_template, test_name):
        """Run a single benchmark test."""
        self.log(f"Running benchmark: {test_name}")
        
        start_time = time.time()
        benchmark_result = {
            'test_name': test_name,
            'agent_name': agent_name,
            'profile_template': profile_template,
            'evaluation_template': evaluation_template,
            'status': 'PASS',
            'execution_time': 0,
            'benchmark_run_id': None,
            'results': {},
            'errors': []
        }
        
        try:
            service = QuickBenchmarkService()
            
            # Execute benchmark
            benchmark_run = await service.run_quick_benchmark(
                agent_name=agent_name,
                profile_template=profile_template,
                evaluation_template=evaluation_template,
                scenario_context={
                    'user_input': 'Hello, I need help creating a life wheel that fits my current situation.',
                    'workflow_type': 'discussion'
                }
            )
            
            benchmark_result['benchmark_run_id'] = str(benchmark_run.id)
            benchmark_result['results'] = {
                'agent_output_length': len(benchmark_run.agent_output or ''),
                'execution_time': benchmark_run.execution_time,
                'total_tokens': benchmark_run.total_tokens,
                'estimated_cost': str(benchmark_run.estimated_cost) if benchmark_run.estimated_cost else None,
                'has_semantic_evaluation': bool(benchmark_run.semantic_evaluation),
                'scenario_created': bool(benchmark_run.scenario)
            }
            
            # Validate results
            if benchmark_run.agent_output:
                benchmark_result['results']['output_quality'] = {
                    'has_content': len(benchmark_run.agent_output.strip()) > 0,
                    'reasonable_length': 50 < len(benchmark_run.agent_output) < 2000,
                    'contains_helpful_content': any(word in benchmark_run.agent_output.lower() 
                                                  for word in ['help', 'wheel', 'activity', 'goal'])
                }
            
            self.log(f"Benchmark completed: {benchmark_run.id}")
            
        except Exception as e:
            benchmark_result['status'] = 'FAIL'
            benchmark_result['errors'].append(str(e))
            self.log(f"Benchmark failed: {e}", 'ERROR')
        
        benchmark_result['execution_time'] = time.time() - start_time
        return benchmark_result

    async def run_comprehensive_benchmark_suite(self):
        """Run comprehensive benchmark test suite."""
        self.log("Running comprehensive benchmark suite...")
        
        # Get available options
        service = QuickBenchmarkService()
        options = service.get_available_options()
        
        agents = options.get('available_agents', [])
        if not agents:
            self.log("No agents available for testing", 'ERROR')
            return
        
        # Test configurations
        test_configs = [
            {
                'agent_name': agents[0]['name'],
                'profile_template': 'anxious_new_user',
                'evaluation_template': 'mentor_helpfulness',
                'test_name': 'Anxious User - Mentor Helpfulness'
            },
            {
                'agent_name': agents[0]['name'],
                'profile_template': 'confident_adhd_user',
                'evaluation_template': 'agent_accuracy',
                'test_name': 'ADHD User - Agent Accuracy'
            },
            {
                'agent_name': agents[0]['name'],
                'profile_template': 'stressed_professional',
                'evaluation_template': 'mentor_helpfulness',
                'test_name': 'Stressed Professional - Mentor Helpfulness'
            }
        ]
        
        # Run benchmarks
        for config in test_configs:
            try:
                result = await self.run_benchmark_test(**config)
                self.results['benchmarks_executed'].append(result)
            except Exception as e:
                self.log(f"Failed to run benchmark {config['test_name']}: {e}", 'ERROR')

    def analyze_performance_metrics(self):
        """Analyze performance metrics from benchmark results."""
        self.log("Analyzing performance metrics...")
        
        if not self.results['benchmarks_executed']:
            self.log("No benchmarks executed - skipping performance analysis", 'WARN')
            return
        
        execution_times = []
        token_counts = []
        successful_runs = 0
        
        for benchmark in self.results['benchmarks_executed']:
            if benchmark['status'] == 'PASS':
                successful_runs += 1
                execution_times.append(benchmark['execution_time'])
                
                if benchmark['results'].get('total_tokens'):
                    token_counts.append(benchmark['results']['total_tokens'])
        
        self.results['performance_metrics'] = {
            'total_benchmarks': len(self.results['benchmarks_executed']),
            'successful_runs': successful_runs,
            'success_rate': f"{(successful_runs/len(self.results['benchmarks_executed']))*100:.1f}%",
            'average_execution_time': sum(execution_times) / len(execution_times) if execution_times else 0,
            'max_execution_time': max(execution_times) if execution_times else 0,
            'min_execution_time': min(execution_times) if execution_times else 0,
            'average_token_usage': sum(token_counts) / len(token_counts) if token_counts else 0
        }
        
        self.log(f"Performance analysis complete: {successful_runs}/{len(self.results['benchmarks_executed'])} successful")

    def assess_quality_metrics(self):
        """Assess quality metrics from benchmark results."""
        self.log("Assessing quality metrics...")
        
        quality_scores = []
        output_quality_checks = []
        
        for benchmark in self.results['benchmarks_executed']:
            if benchmark['status'] == 'PASS' and 'output_quality' in benchmark['results']:
                quality = benchmark['results']['output_quality']
                
                # Calculate quality score
                score = 0
                if quality.get('has_content'): score += 25
                if quality.get('reasonable_length'): score += 25
                if quality.get('contains_helpful_content'): score += 50
                
                quality_scores.append(score)
                output_quality_checks.append(quality)
        
        self.results['quality_assessment'] = {
            'benchmarks_with_quality_data': len(quality_scores),
            'average_quality_score': sum(quality_scores) / len(quality_scores) if quality_scores else 0,
            'quality_distribution': {
                'excellent': sum(1 for score in quality_scores if score >= 90),
                'good': sum(1 for score in quality_scores if 70 <= score < 90),
                'fair': sum(1 for score in quality_scores if 50 <= score < 70),
                'poor': sum(1 for score in quality_scores if score < 50)
            }
        }

    def generate_recommendations(self):
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Performance recommendations
        if self.results['performance_metrics'].get('average_execution_time', 0) > 60:
            recommendations.append("Average execution time exceeds 60s - consider optimizing LLM calls or workflow logic")
        
        if self.results['performance_metrics'].get('success_rate', '0%') != '100.0%':
            recommendations.append("Some benchmarks failed - investigate error logs and fix underlying issues")
        
        # Quality recommendations
        avg_quality = self.results['quality_assessment'].get('average_quality_score', 0)
        if avg_quality < 70:
            recommendations.append("Average quality score below 70% - review agent instructions and evaluation criteria")
        
        # System recommendations
        if not self.results['benchmarks_executed']:
            recommendations.append("No benchmarks executed successfully - check system setup and dependencies")
        
        if not recommendations:
            recommendations.append("All tests passed successfully - system ready for production use")
        
        self.results['recommendations'] = recommendations

    async def run_all_tests(self):
        """Run all end-to-end tests."""
        self.log("Starting Quick Benchmark End-to-End Test")
        self.log("=" * 60)
        
        # Setup
        if not self.setup_test_environment():
            self.log("Test environment setup failed", 'ERROR')
            return self.save_results()
        
        # Run benchmarks
        await self.run_comprehensive_benchmark_suite()
        
        # Analysis
        self.analyze_performance_metrics()
        self.assess_quality_metrics()
        self.generate_recommendations()
        
        # Summary
        total_benchmarks = len(self.results['benchmarks_executed'])
        successful_benchmarks = sum(1 for b in self.results['benchmarks_executed'] if b['status'] == 'PASS')
        
        self.results['summary'] = {
            'total_benchmarks_executed': total_benchmarks,
            'successful_benchmarks': successful_benchmarks,
            'overall_success_rate': f"{(successful_benchmarks/total_benchmarks)*100:.1f}%" if total_benchmarks > 0 else "0%",
            'test_completion_status': 'PASS' if successful_benchmarks == total_benchmarks else 'PARTIAL' if successful_benchmarks > 0 else 'FAIL'
        }
        
        # Log results
        self.log("=" * 60)
        self.log(f"End-to-End Test Summary:")
        self.log(f"  Benchmarks executed: {total_benchmarks}")
        self.log(f"  Successful: {successful_benchmarks}")
        self.log(f"  Success rate: {self.results['summary']['overall_success_rate']}")
        
        if self.results['performance_metrics']:
            self.log(f"  Average execution time: {self.results['performance_metrics']['average_execution_time']:.2f}s")
        
        if self.results['quality_assessment']:
            self.log(f"  Average quality score: {self.results['quality_assessment']['average_quality_score']:.1f}%")
        
        if successful_benchmarks == total_benchmarks:
            self.log("✅ All end-to-end tests passed! Quick benchmark system fully functional.", 'SUCCESS')
        else:
            self.log(f"❌ {total_benchmarks - successful_benchmarks} tests failed. Check results for details.", 'ERROR')
        
        return self.save_results()

def main():
    """Main test execution."""
    async def run_tests():
        tester = QuickBenchmarkEndToEndTester()
        return await tester.run_all_tests()
    
    results_file = asyncio.run(run_tests())
    
    print(f"\n📊 Detailed results saved to: {results_file}")
    print("\n🚀 Next steps:")
    print("1. Review benchmark execution results")
    print("2. Check performance and quality metrics")
    print("3. Test UI functionality with real data")
    print("\n💡 UI Validation:")
    print("   1. Open: http://localhost:8000/admin/benchmarks/manage/")
    print("   2. Go to 'Quick Benchmark' tab")
    print("   3. Run a benchmark and verify results display correctly")

if __name__ == '__main__':
    main()
