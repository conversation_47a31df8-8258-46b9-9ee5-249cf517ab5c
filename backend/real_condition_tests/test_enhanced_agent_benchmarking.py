#!/usr/bin/env python3
"""
Enhanced Agent Benchmarking System Test

This test validates the complete agent benchmarking pipeline including:
- LLM call interception and recording
- Tool call tracking
- Memory operation monitoring
- Enhanced debugging data capture
- Agent evaluation modal functionality

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_enhanced_agent_benchmarking.py
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from django.test import RequestFactory
from django.contrib.auth.models import User
from apps.user.models import UserProfile
from apps.main.models import BenchmarkRun, GenericAgent
from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from apps.admin_tools.benchmark.views import QuickBenchmarkView

logger = logging.getLogger(__name__)

class EnhancedAgentBenchmarkingTest:
    """Comprehensive test for the enhanced agent benchmarking system."""
    
    def __init__(self):
        self.results = {
            'test_name': 'Enhanced Agent Benchmarking System Test',
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': []
            }
        }
        self.factory = RequestFactory()
        
    def log_test_result(self, test_name: str, success: bool, details: dict = None, error: str = None):
        """Log the result of a test."""
        self.results['tests'][test_name] = {
            'success': success,
            'details': details or {},
            'error': error,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results['summary']['total_tests'] += 1
        if success:
            self.results['summary']['passed'] += 1
            print(f"✅ {test_name}")
        else:
            self.results['summary']['failed'] += 1
            print(f"❌ {test_name}: {error}")
            if error:
                self.results['summary']['errors'].append(f"{test_name}: {error}")
    
    def test_user_profile_setup(self):
        """Test that we have proper user profiles for benchmarking."""
        try:
            # Get a real user profile
            real_profiles = UserProfile.objects.filter(is_real=True)
            test_profiles = UserProfile.objects.filter(is_real=False)
            
            details = {
                'real_profiles_count': real_profiles.count(),
                'test_profiles_count': test_profiles.count(),
                'total_profiles': UserProfile.objects.count()
            }
            
            if real_profiles.exists():
                profile = real_profiles.first()
                details['sample_real_profile'] = {
                    'id': str(profile.id),
                    'name': profile.profile_name,
                    'is_real': profile.is_real
                }
                
            success = real_profiles.exists() and test_profiles.exists()
            error = None if success else "No real or test profiles found"
            
            self.log_test_result('User Profile Setup', success, details, error)
            return success, real_profiles.first() if success else None
            
        except Exception as e:
            self.log_test_result('User Profile Setup', False, error=str(e))
            return False, None
    
    def test_agent_availability(self):
        """Test that we have agents available for benchmarking."""
        try:
            agents = GenericAgent.objects.all()
            mentor_agents = GenericAgent.objects.filter(role='mentor')
            
            details = {
                'total_agents': agents.count(),
                'mentor_agents': mentor_agents.count(),
                'available_agents': [{'role': a.role, 'id': str(a.id)} for a in agents[:5]]
            }
            
            success = agents.exists() and mentor_agents.exists()
            error = None if success else "No agents found in database"
            
            self.log_test_result('Agent Availability', success, details, error)
            return success, mentor_agents.first() if success else None
            
        except Exception as e:
            self.log_test_result('Agent Availability', False, error=str(e))
            return False, None
    
    def test_quick_benchmark_service(self, user_profile, agent):
        """Test the QuickBenchmarkService functionality."""
        try:
            service = QuickBenchmarkService()
            
            # Test service initialization
            details = {
                'service_initialized': True,
                'user_profile_id': str(user_profile.id),
                'agent_role': agent.role
            }
            
            # Run a quick benchmark
            benchmark_run = service.run_quick_benchmark_sync(
                agent_name=agent.role,
                user_profile_id=str(user_profile.id),
                evaluation_template='mentor_helpfulness',
                scenario_context={'user_input': 'Hello, I need help with my goals'},
                use_real_tools=True,
                use_real_db=True
            )
            
            details.update({
                'benchmark_run_id': str(benchmark_run.id),
                'execution_successful': True,
                'mean_duration': benchmark_run.mean_duration,
                'total_input_tokens': benchmark_run.total_input_tokens,
                'total_output_tokens': benchmark_run.total_output_tokens,
                'has_enhanced_debugging': bool(benchmark_run.raw_results.get('enhanced_debugging_data', {}).get('enabled', False))
            })
            
            success = benchmark_run is not None
            error = None
            
            self.log_test_result('Quick Benchmark Service', success, details, error)
            return success, benchmark_run if success else None
            
        except Exception as e:
            self.log_test_result('Quick Benchmark Service', False, error=str(e))
            return False, None
    
    def test_enhanced_debugging_data(self, benchmark_run):
        """Test that enhanced debugging data is properly captured."""
        try:
            raw_results = benchmark_run.raw_results or {}
            enhanced_data = raw_results.get('enhanced_debugging_data', {})
            
            details = {
                'enhanced_debugging_enabled': enhanced_data.get('enabled', False),
                'has_llm_interactions': len(enhanced_data.get('llm_interactions', [])) > 0,
                'llm_interactions_count': len(enhanced_data.get('llm_interactions', [])),
                'has_tool_calls': len(enhanced_data.get('tool_calls', [])) > 0,
                'tool_calls_count': len(enhanced_data.get('tool_calls', [])),
                'has_agents_data': len(enhanced_data.get('agents', [])) > 0,
                'agents_count': len(enhanced_data.get('agents', []))
            }
            
            # Check for LLM interactions
            llm_interactions = enhanced_data.get('llm_interactions', [])
            if llm_interactions:
                sample_interaction = llm_interactions[0]
                details['sample_llm_interaction'] = {
                    'agent': sample_interaction.get('agent'),
                    'model': sample_interaction.get('model'),
                    'has_prompt': bool(sample_interaction.get('prompt')),
                    'has_response': bool(sample_interaction.get('response')),
                    'token_usage': sample_interaction.get('token_usage', {}),
                    'duration_ms': sample_interaction.get('duration_ms'),
                    'success': sample_interaction.get('success')
                }
            
            # Check for tool calls
            tool_calls = enhanced_data.get('tool_calls', [])
            if tool_calls:
                sample_tool_call = tool_calls[0]
                details['sample_tool_call'] = {
                    'tool_name': sample_tool_call.get('tool_name'),
                    'execution_mode': sample_tool_call.get('execution_mode'),
                    'has_input': bool(sample_tool_call.get('tool_input')),
                    'has_output': bool(sample_tool_call.get('tool_output')),
                    'duration_ms': sample_tool_call.get('duration_ms'),
                    'success': sample_tool_call.get('success')
                }
            
            # Success criteria: enhanced debugging enabled and some data captured
            success = (enhanced_data.get('enabled', False) and 
                      (len(llm_interactions) > 0 or len(tool_calls) > 0 or len(enhanced_data.get('agents', [])) > 0))
            
            error = None if success else "Enhanced debugging data not properly captured"
            
            self.log_test_result('Enhanced Debugging Data', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('Enhanced Debugging Data', False, error=str(e))
            return False
    
    def test_api_endpoint(self, user_profile, agent):
        """Test the quick benchmark API endpoint."""
        try:
            # Create a test user for the request
            user, created = User.objects.get_or_create(
                username='test_benchmark_user',
                defaults={'email': '<EMAIL>'}
            )
            
            # Prepare request data
            request_data = {
                'agent_name': agent.role,
                'user_profile_id': str(user_profile.id),
                'evaluation_template': 'mentor_helpfulness',
                'scenario_context': {'user_input': 'Hello, I need help'},
                'use_real_tools': True,
                'use_real_db': True
            }
            
            # Create request
            request = self.factory.post(
                '/admin/benchmarks/api/quick-benchmark/',
                data=json.dumps(request_data),
                content_type='application/json'
            )
            request.user = user
            
            # Call the API using the class-based view
            view = QuickBenchmarkView()
            response = view.post(request)
            
            details = {
                'status_code': response.status_code,
                'request_data': request_data
            }
            
            if response.status_code == 201:
                response_data = json.loads(response.content)
                details.update({
                    'response_success': response_data.get('success', False),
                    'benchmark_run_id': response_data.get('benchmark_run_id'),
                    'has_results_summary': 'results_summary' in response_data,
                    'results_summary': response_data.get('results_summary', {})
                })
                
                success = response_data.get('success', False)
                error = None
            else:
                try:
                    error_data = json.loads(response.content)
                    error = error_data.get('error', f'HTTP {response.status_code}')
                except:
                    error = f'HTTP {response.status_code}'
                success = False
            
            self.log_test_result('API Endpoint', success, details, error)
            return success
            
        except Exception as e:
            self.log_test_result('API Endpoint', False, error=str(e))
            return False
    
    def save_results(self):
        """Save test results to file."""
        results_dir = Path('/usr/src/app/real_condition_tests/results')
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'enhanced_agent_benchmarking_{timestamp}.json'
        filepath = results_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Results saved to: {filepath}")
        return filepath
    
    def run_all_tests(self):
        """Run all tests in sequence."""
        print("🚀 Starting Enhanced Agent Benchmarking System Test")
        print("=" * 60)
        
        # Test 1: User Profile Setup
        profile_success, user_profile = self.test_user_profile_setup()
        if not profile_success:
            print("❌ Cannot continue without user profiles")
            return self.save_results()
        
        # Test 2: Agent Availability
        agent_success, agent = self.test_agent_availability()
        if not agent_success:
            print("❌ Cannot continue without agents")
            return self.save_results()
        
        # Test 3: Quick Benchmark Service
        service_success, benchmark_run = self.test_quick_benchmark_service(user_profile, agent)
        
        # Test 4: Enhanced Debugging Data (only if service test passed)
        if service_success and benchmark_run:
            self.test_enhanced_debugging_data(benchmark_run)
        
        # Test 5: API Endpoint
        self.test_api_endpoint(user_profile, agent)
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.results['summary']['total_tests']}")
        print(f"Passed: {self.results['summary']['passed']}")
        print(f"Failed: {self.results['summary']['failed']}")
        
        if self.results['summary']['errors']:
            print("\n❌ ERRORS:")
            for error in self.results['summary']['errors']:
                print(f"  - {error}")
        
        success_rate = (self.results['summary']['passed'] / self.results['summary']['total_tests']) * 100
        print(f"\n🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("✅ Enhanced Agent Benchmarking System is working well!")
        elif success_rate >= 60:
            print("⚠️ Enhanced Agent Benchmarking System has some issues")
        else:
            print("❌ Enhanced Agent Benchmarking System needs significant fixes")
        
        return self.save_results()

def main():
    """Main test execution."""
    test = EnhancedAgentBenchmarkingTest()
    results_file = test.run_all_tests()
    
    print(f"\n🔗 View detailed results: {results_file}")
    print("\n🎯 Next Steps:")
    print("1. If tests passed: Test the admin UI at /admin/benchmarks/manage/")
    print("2. If tests failed: Check the errors above and fix the issues")
    print("3. Run the quick benchmark UI test: test_quick_benchmark_ui_integration.py")

if __name__ == '__main__':
    main()
