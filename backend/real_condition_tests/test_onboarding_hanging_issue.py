#!/usr/bin/env python3
"""
Onboarding Hanging Issue Test
Reproduces the exact user journey issue where users with low profile completion
request wheels and the system hangs instead of asking for more information.
"""

import os
import sys
import django
import asyncio
import time
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Configure Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.agents.tools.tools_util import execute_tool
from apps.user.models import UserProfile, Demographics, Preference, UserGoal, TrustLevel

class OnboardingHangingIssueTest:
    """
    Test class to reproduce and validate the fix for the onboarding hanging issue.
    
    This test reproduces the exact scenario from the celery logs:
    1. New user with ~25% profile completion
    2. User requests "make me a wheel"
    3. System should ask for more information instead of hanging
    4. System should respond within 10 seconds
    """

    def __init__(self):
        self.test_id = str(uuid.uuid4())[:8]
        self.start_time = time.time()
        self.user_profile_id = None
        self.session_name = f"onboarding_hang_test_{self.test_id}"
        self.test_results = []
        self.response_times = []

    async def run_hanging_issue_test(self):
        """Run the hanging issue reproduction test."""
        print("🔍 Onboarding Hanging Issue Test")
        print(f"   Test ID: {self.test_id}")
        print(f"   Session: {self.session_name}")
        print("   Scenario: New user with low profile completion requests wheel")
        print()

        try:
            # Phase 1: Setup new user with minimal profile data
            await self._setup_minimal_user()
            
            # Phase 2: Test the exact hanging scenario
            await self._test_wheel_request_hanging()
            
            # Phase 3: Test follow-up response handling
            await self._test_follow_up_response()
            
            # Phase 4: Validate complete flow
            await self._validate_complete_flow()
            
            # Phase 5: Generate report
            self._generate_test_report()

        except Exception as e:
            print(f"❌ Error in hanging issue test: {e}")
            import traceback
            traceback.print_exc()

    async def _setup_minimal_user(self):
        """Setup a user with minimal profile data (~25% completion)."""
        print("📋 Phase 1: Setting up minimal user profile")
        phase_start = time.time()

        try:
            # Create test user profile with minimal data
            user_profile = await self._create_minimal_user_profile()
            self.user_profile_id = str(user_profile.id)

            # Verify profile completion is low
            completion = await self._check_profile_completion()
            
            print(f"   ✓ Test user created: ID {self.user_profile_id}")
            print(f"   ✓ Profile completion: {completion:.1%}")
            
            if completion > 0.3:
                print(f"   ⚠️  Warning: Profile completion higher than expected ({completion:.1%})")

        except Exception as e:
            print(f"   ❌ Setup failed: {e}")
            raise

        duration = time.time() - phase_start
        print(f"   ✓ Setup completed in {duration:.2f}s")
        print()

    async def _create_minimal_user_profile(self):
        """Create a minimal user profile that triggers the hanging issue."""
        from django.contrib.auth.models import User
        from apps.user.models import UserProfile
        from asgiref.sync import sync_to_async

        @sync_to_async
        def create_user_and_profile():
            # Create Django user
            username = f"test_hanging_user_{self.test_id}"
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': f'{username}@test.com',
                    'first_name': 'Test',
                    'last_name': 'User'
                }
            )

            # Create minimal UserProfile (should have ~25% completion)
            user_profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'profile_name': f'Test Hanging User {self.test_id}',
                    'is_real': False,  # Mark as test profile
                }
            )

            return user_profile

        return await create_user_and_profile()

    async def _test_wheel_request_hanging(self):
        """Test the exact scenario that causes hanging."""
        print("🎯 Phase 2: Testing wheel request hanging scenario")
        phase_start = time.time()

        # Create conversation dispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=self.user_profile_id,
            user_ws_session_name=self.session_name,
            fail_fast_on_errors=True
        )

        # The exact message that causes hanging
        wheel_request_message = {
            "text": "make me a wheel",
            "metadata": {
                "test_context": "hanging_reproduction",
                "conversation_phase": "initial"
            }
        }

        print(f"   Sending wheel request: '{wheel_request_message['text']}'")
        step_start = time.time()

        try:
            # Set a timeout to detect hanging
            response = await asyncio.wait_for(
                dispatcher.process_message(wheel_request_message),
                timeout=15.0  # 15 second timeout
            )
            
            step_duration = time.time() - step_start
            self.response_times.append(step_duration)

            workflow_type = response.get('workflow_type')
            confidence = response.get('confidence', 0.0)
            direct_response = response.get('direct_response')
            conversation_state_update = response.get('conversation_state_update')

            print(f"   ✓ Response received in {step_duration:.2f}s")
            print(f"   ✓ Workflow: {workflow_type} (confidence: {confidence:.2f})")
            
            if direct_response:
                print(f"   ✓ Direct response: '{direct_response[:100]}...'")
            else:
                print(f"   ⚠️  No direct response provided")
                
            if conversation_state_update:
                print(f"   ✓ Conversation state update: {conversation_state_update}")
            else:
                print(f"   ⚠️  No conversation state update")

            # Validate response quality
            success = (
                step_duration < 10.0 and  # Response within 10 seconds
                workflow_type in ['onboarding', 'discussion'] and  # Appropriate workflow
                (direct_response or conversation_state_update)  # Some form of response
            )

            self.test_results.append({
                'phase': 'wheel_request_hanging',
                'workflow_type': workflow_type,
                'confidence': confidence,
                'duration': step_duration,
                'direct_response': direct_response,
                'conversation_state_update': conversation_state_update,
                'success': success,
                'response': response
            })

            if not success:
                print(f"   ❌ Test failed: Response took {step_duration:.2f}s or missing response")
            else:
                print(f"   ✓ Test passed: Quick response with appropriate workflow")

        except asyncio.TimeoutError:
            step_duration = time.time() - step_start
            print(f"   ❌ HANGING DETECTED: No response after {step_duration:.2f}s")
            
            self.test_results.append({
                'phase': 'wheel_request_hanging',
                'error': 'timeout',
                'duration': step_duration,
                'success': False,
                'hanging_detected': True
            })
            
            raise Exception("Hanging issue reproduced - system did not respond within timeout")

        except Exception as e:
            step_duration = time.time() - step_start
            print(f"   ❌ Error during wheel request: {e}")
            
            self.test_results.append({
                'phase': 'wheel_request_hanging',
                'error': str(e),
                'duration': step_duration,
                'success': False
            })
            
            raise

        total_duration = time.time() - phase_start
        print(f"   ✓ Wheel request test completed in {total_duration:.2f}s")
        print()

    async def _test_follow_up_response(self):
        """Test follow-up response handling after initial wheel request."""
        print("💬 Phase 3: Testing follow-up response handling")
        phase_start = time.time()

        # Simulate user providing requested information
        follow_up_message = {
            "text": "I'm a 22-year-old student in Berlin. I'm feeling stressed about exams and need some activities to help me relax and focus.",
            "metadata": {
                "test_context": "follow_up_response",
                "conversation_phase": "awaiting_profile_info",
                "awaiting_response_type": "profile_info"
            }
        }

        print(f"   Sending follow-up response...")
        step_start = time.time()

        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.user_profile_id,
                user_ws_session_name=f"{self.session_name}_followup",
                fail_fast_on_errors=True
            )

            response = await asyncio.wait_for(
                dispatcher.process_message(follow_up_message),
                timeout=15.0
            )
            
            step_duration = time.time() - step_start
            self.response_times.append(step_duration)

            workflow_type = response.get('workflow_type')
            
            print(f"   ✓ Follow-up response received in {step_duration:.2f}s")
            print(f"   ✓ Workflow: {workflow_type}")

            # Check if profile completion improved
            completion_after = await self._check_profile_completion()
            print(f"   ✓ Profile completion after follow-up: {completion_after:.1%}")

            self.test_results.append({
                'phase': 'follow_up_response',
                'workflow_type': workflow_type,
                'duration': step_duration,
                'profile_completion_after': completion_after,
                'success': step_duration < 10.0 and completion_after > 0.3,
                'response': response
            })

        except Exception as e:
            print(f"   ❌ Follow-up response failed: {e}")
            self.test_results.append({
                'phase': 'follow_up_response',
                'error': str(e),
                'success': False
            })

        total_duration = time.time() - phase_start
        print(f"   ✓ Follow-up response test completed in {total_duration:.2f}s")
        print()

    async def _validate_complete_flow(self):
        """Validate the complete flow works end-to-end."""
        print("🔄 Phase 4: Validating complete flow")

        # Check final profile completion
        final_completion = await self._check_profile_completion()
        print(f"   Final profile completion: {final_completion:.1%}")

        # If profile is still insufficient, simulate more profile completion
        if final_completion < 0.5:
            print("   📝 Simulating additional profile completion...")
            await self._simulate_profile_completion()
            final_completion = await self._check_profile_completion()
            print(f"   Updated profile completion: {final_completion:.1%}")

        # Test wheel generation
        if final_completion >= 0.3:  # Lower threshold for testing
            print("   ✓ Profile sufficient for wheel generation")
            await self._test_final_wheel_generation()
        else:
            print("   ⚠️  Profile still insufficient for wheel generation")

    async def _simulate_profile_completion(self):
        """Simulate additional profile completion steps."""
        # Simulate providing more detailed information
        additional_info_messages = [
            {
                "text": "I'm studying computer science at university. I have ADHD so I need activities that help me focus and manage stress. I prefer shorter activities that I can do between study sessions.",
                "metadata": {
                    "test_context": "additional_profile_info",
                    "conversation_phase": "awaiting_profile_info"
                }
            },
            {
                "text": "My goals are to improve my focus, reduce stress, and maintain a healthy work-life balance during exam period. I like creative activities and light physical exercise.",
                "metadata": {
                    "test_context": "goals_and_preferences",
                    "conversation_phase": "awaiting_profile_info"
                }
            }
        ]

        for i, message in enumerate(additional_info_messages):
            print(f"   Sending additional info {i+1}/2...")

            try:
                dispatcher = ConversationDispatcher(
                    user_profile_id=self.user_profile_id,
                    user_ws_session_name=f"{self.session_name}_additional_{i}",
                    fail_fast_on_errors=True
                )

                response = await asyncio.wait_for(
                    dispatcher.process_message(message),
                    timeout=15.0
                )

                print(f"   ✓ Additional info {i+1} processed")

                # Wait for profile processing
                await asyncio.sleep(2)

            except Exception as e:
                print(f"   ⚠️  Additional info {i+1} failed: {e}")

        # Wait for all profile updates to complete
        await asyncio.sleep(5)

    async def _test_final_wheel_generation(self):
        """Test final wheel generation after profile completion."""
        print("   🎯 Testing final wheel generation...")

        wheel_message = {
            "text": "Now I'm ready for my activity wheel! Please create one for me.",
            "metadata": {
                "test_context": "final_wheel_generation"
            }
        }

        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.user_profile_id,
                user_ws_session_name=f"{self.session_name}_final",
                fail_fast_on_errors=True
            )

            step_start = time.time()
            response = await asyncio.wait_for(
                dispatcher.process_message(wheel_message),
                timeout=30.0  # Longer timeout for wheel generation
            )
            step_duration = time.time() - step_start

            workflow_type = response.get('workflow_type')
            print(f"   ✓ Final wheel request: {workflow_type} ({step_duration:.2f}s)")

            if workflow_type == 'wheel_generation':
                print("   ✓ Successfully triggered wheel generation")

                # Wait for wheel generation to complete
                print("   ⏳ Waiting for wheel generation to complete...")
                await asyncio.sleep(15)  # Allow time for wheel creation

                # Test wheel retrieval
                await self._test_wheel_retrieval()

                self.test_results.append({
                    'phase': 'final_wheel_generation',
                    'workflow_type': workflow_type,
                    'duration': step_duration,
                    'success': True,
                    'response': response
                })

            else:
                print(f"   ⚠️  Unexpected workflow: {workflow_type}")
                self.test_results.append({
                    'phase': 'final_wheel_generation',
                    'workflow_type': workflow_type,
                    'duration': step_duration,
                    'success': False,
                    'error': f'Expected wheel_generation, got {workflow_type}'
                })

        except Exception as e:
            print(f"   ❌ Final wheel generation failed: {e}")
            self.test_results.append({
                'phase': 'final_wheel_generation',
                'error': str(e),
                'success': False
            })

    async def _test_wheel_retrieval(self):
        """Test retrieving the generated wheel data."""
        print("   🎡 Testing wheel retrieval...")

        try:
            # Use the get_user_wheels tool to retrieve wheel data
            result = await execute_tool(
                tool_code="get_user_wheels",
                tool_input={"input_data": {"user_profile_id": self.user_profile_id}},
                user_profile_id=self.user_profile_id,
                session_id=self.session_name
            )

            wheels = result.get("wheels", [])
            print(f"   ✓ Retrieved {len(wheels)} wheel(s)")

            if wheels:
                latest_wheel = wheels[0]  # Assuming most recent first
                activities = latest_wheel.get("activities", [])
                print(f"   ✓ Latest wheel has {len(activities)} activities")

                if len(activities) >= 4:
                    print("   ✓ Wheel has sufficient activities (≥4)")

                    # Check activity quality
                    activity_names = [activity.get("name", "Unknown") for activity in activities[:3]]
                    print(f"   ✓ Sample activities: {', '.join(activity_names)}")

                    self.test_results.append({
                        'phase': 'wheel_retrieval',
                        'wheel_count': len(wheels),
                        'activity_count': len(activities),
                        'sample_activities': activity_names,
                        'success': True
                    })
                else:
                    print(f"   ⚠️  Wheel has insufficient activities ({len(activities)})")
                    self.test_results.append({
                        'phase': 'wheel_retrieval',
                        'wheel_count': len(wheels),
                        'activity_count': len(activities),
                        'success': False,
                        'error': 'Insufficient activities in wheel'
                    })
            else:
                print("   ⚠️  No wheels found")
                self.test_results.append({
                    'phase': 'wheel_retrieval',
                    'wheel_count': 0,
                    'success': False,
                    'error': 'No wheels generated'
                })

        except Exception as e:
            print(f"   ❌ Wheel retrieval failed: {e}")
            self.test_results.append({
                'phase': 'wheel_retrieval',
                'error': str(e),
                'success': False
            })

    async def _check_profile_completion(self) -> float:
        """Check the user's profile completion status."""
        try:
            result = await execute_tool(
                tool_code="get_user_profile",
                tool_input={"input_data": {"user_profile_id": self.user_profile_id}},
                user_profile_id=self.user_profile_id,
                session_id=self.session_name
            )
            user_profile_data = result.get("user_profile", {})
            return user_profile_data.get("profile_completion", 0.0)
        except Exception as e:
            print(f"   Warning: Could not check profile completion: {e}")
            return 0.0

    def _generate_test_report(self):
        """Generate comprehensive test report."""
        print("📊 Comprehensive User Journey Test Report")
        print("=" * 60)

        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result.get('success', False))

        print(f"Total Test Phases: {total_tests}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {total_tests - successful_tests}")
        print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        print()

        # Response time analysis
        if self.response_times:
            avg_response_time = sum(self.response_times) / len(self.response_times)
            max_response_time = max(self.response_times)
            min_response_time = min(self.response_times)
            print(f"Response Time Analysis:")
            print(f"  Average: {avg_response_time:.2f}s")
            print(f"  Maximum: {max_response_time:.2f}s")
            print(f"  Minimum: {min_response_time:.2f}s")
            print(f"  Response Time Goal (<10s): {'✓' if max_response_time < 10.0 else '❌'}")
            print(f"  Hanging Prevention: {'✓' if max_response_time < 15.0 else '❌'}")

        print()
        print("Detailed Phase Results:")
        print("-" * 40)

        for result in self.test_results:
            phase = result.get('phase', 'unknown')
            success = result.get('success', False)
            duration = result.get('duration', 0)
            status = '✓' if success else '❌'

            print(f"{status} {phase.upper().replace('_', ' ')}")
            if duration > 0:
                print(f"    Duration: {duration:.2f}s")

            # Phase-specific details
            if phase == 'wheel_request_hanging':
                workflow_type = result.get('workflow_type')
                direct_response = result.get('direct_response')
                if workflow_type:
                    print(f"    Workflow: {workflow_type}")
                if direct_response:
                    print(f"    Direct Response: ✓")

            elif phase == 'follow_up_response':
                completion = result.get('profile_completion_after', 0)
                print(f"    Profile Completion After: {completion:.1%}")

            elif phase == 'final_wheel_generation':
                workflow_type = result.get('workflow_type')
                if workflow_type:
                    print(f"    Triggered Workflow: {workflow_type}")

            elif phase == 'wheel_retrieval':
                wheel_count = result.get('wheel_count', 0)
                activity_count = result.get('activity_count', 0)
                sample_activities = result.get('sample_activities', [])
                print(f"    Wheels Generated: {wheel_count}")
                print(f"    Activities in Latest Wheel: {activity_count}")
                if sample_activities:
                    print(f"    Sample Activities: {', '.join(sample_activities[:2])}")

            if result.get('error'):
                print(f"    ❌ Error: {result['error']}")
            if result.get('hanging_detected'):
                print(f"    ⚠️  HANGING ISSUE DETECTED")

            print()

        # Overall assessment
        print("Overall Assessment:")
        print("-" * 20)

        hanging_fixed = not any(result.get('hanging_detected', False) for result in self.test_results)
        profile_completion_working = any(result.get('profile_completion_after', 0) > 0 for result in self.test_results)
        wheel_generation_working = any(result.get('phase') == 'wheel_retrieval' and result.get('success', False) for result in self.test_results)

        print(f"✓ Hanging Issue Fixed: {'✓' if hanging_fixed else '❌'}")
        print(f"✓ Profile Completion Working: {'✓' if profile_completion_working else '❌'}")
        print(f"✓ Wheel Generation Working: {'✓' if wheel_generation_working else '❌'}")
        print(f"✓ Complete User Journey: {'✓' if hanging_fixed and wheel_generation_working else '❌'}")

        # Recommendations
        print()
        print("Recommendations:")
        print("-" * 15)

        if not hanging_fixed:
            print("• Fix hanging issue in onboarding workflow")
        if not profile_completion_working:
            print("• Investigate profile completion workflow")
        if not wheel_generation_working:
            print("• Debug wheel generation process")
        if hanging_fixed and wheel_generation_working:
            print("• ✅ System is working correctly for the tested user journey!")
            print("• Consider testing with different user personas")
            print("• Monitor response times in production")

async def main():
    """Main test execution."""
    test = OnboardingHangingIssueTest()
    await test.run_hanging_issue_test()

if __name__ == "__main__":
    asyncio.run(main())
