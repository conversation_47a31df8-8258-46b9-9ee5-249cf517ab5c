#!/usr/bin/env python3
"""
Test script to verify the placeholder injection system is working correctly.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.agents.utils.placeholder_injector import placeholder_injector

def test_placeholder_injection():
    """Test the placeholder injection system."""
    print("🧪 Testing Placeholder Injection System")
    print("=" * 60)
    
    # Test template with placeholders
    template = """
You are helping {{USER_NAME}} on {{LOCAL_DATE}} at {{LOCAL_TIME}}.

Current Context:
- Mood: {{CURRENT_MOOD}}
- Energy Level: {{ENERGY_LEVEL}}
- Environment: {{CURRENT_ENVIRONMENT}}
- Time Available: {{TIME_AVAILABLE}} minutes

User's Psychological Profile:
- Dominant Traits: {{DOMINANT_TRAITS}}
- Openness: {{TRAIT_OPENNESS}}
- Trust Phase: {{TRUST_PHASE}}

Please tailor your response considering these factors.
"""
    
    print("📋 Original Template:")
    print(template)
    print("\n" + "=" * 60)
    
    # Build context for user 2 (PhiPhi)
    print("🔧 Building context for user 2 (PhiPhi)...")
    try:
        context = placeholder_injector.build_context(
            user_profile_id=2,
            context_packet={
                'mood': 'excited',
                'energy_level': 'high',
                'time_available': 120,
                'environment': 'home office'
            },
            resource_context={
                'environment_analysis': {
                    'environment_description': 'comfortable home office',
                    'environment_type': 'home',
                    'privacy_level': 'private',
                    'space_size': 'medium',
                    'noise_level': 'quiet',
                    'social_context': 'alone'
                },
                'time_analysis': {
                    'duration_minutes': 120,
                    'flexibility_level': 'flexible'
                },
                'resource_analysis': {
                    'inventory_items': ['laptop', 'notebook', 'pen'],
                    'physical_limitations': [],
                    'cognitive_limitations': [],
                    'capabilities': ['writing', 'research', 'analysis']
                }
            }
        )
        
        print(f"✅ Context built successfully with {len(context)} placeholders")
        print("\n🔍 Context Preview:")
        for key, value in list(context.items())[:10]:  # Show first 10 items
            print(f"  {key}: {value}")
        if len(context) > 10:
            print(f"  ... and {len(context) - 10} more placeholders")
            
    except Exception as e:
        print(f"❌ Error building context: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    
    # Inject placeholders
    print("💉 Injecting placeholders...")
    try:
        result = placeholder_injector.inject_placeholders(template, context)
        
        print("✅ Placeholder injection completed!")
        print("\n📄 Final Result:")
        print(result)
        
    except Exception as e:
        print(f"❌ Error injecting placeholders: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    print("🎉 Placeholder injection test completed successfully!")
    return True

def test_wheel_activity_agent_instructions():
    """Test loading and contextualizing wheel activity agent instructions."""
    print("\n🎯 Testing Wheel Activity Agent Instructions")
    print("=" * 60)
    
    try:
        from apps.main.models import GenericAgent, AgentRole
        
        # Get the wheel activity agent
        agent = GenericAgent.objects.get(role=AgentRole.ACTIVITY)
        print(f"✅ Found agent: {agent.description}")
        
        # Show original instructions (first 500 chars)
        original_instructions = agent.system_instructions
        print(f"\n📋 Original Instructions (first 500 chars):")
        print(original_instructions[:500] + "..." if len(original_instructions) > 500 else original_instructions)
        
        # Build context and inject placeholders
        context = placeholder_injector.build_context(
            user_profile_id=2,
            context_packet={'mood': 'excited', 'energy_level': 'high'},
            resource_context={
                'environment_analysis': {
                    'environment_description': 'comfortable home office',
                    'privacy_level': 'private'
                }
            }
        )
        
        # Inject placeholders
        contextualized = placeholder_injector.inject_placeholders(original_instructions, context)
        
        print(f"\n💉 Contextualized Instructions (first 1000 chars):")
        print(contextualized[:1000] + "..." if len(contextualized) > 1000 else contextualized)
        
        print(f"\n📊 Statistics:")
        print(f"  Original length: {len(original_instructions)} chars")
        print(f"  Contextualized length: {len(contextualized)} chars")
        print(f"  Placeholders in context: {len(context)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing agent instructions: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Placeholder Injection Tests")
    print("=" * 80)
    
    success = True
    
    # Test basic placeholder injection
    if not test_placeholder_injection():
        success = False
    
    # Test wheel activity agent instructions
    if not test_wheel_activity_agent_instructions():
        success = False
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 ALL TESTS PASSED! Placeholder injection system is working correctly.")
    else:
        print("❌ SOME TESTS FAILED! Please check the errors above.")
    
    sys.exit(0 if success else 1)
